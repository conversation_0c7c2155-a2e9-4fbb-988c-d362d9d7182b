#!/usr/bin/env python3
"""
اختبار الاتصال مع قاعدة البيانات Supabase
"""

import os
import sys
import requests
import json
from datetime import datetime

def test_supabase_connection():
    """اختبار الاتصال مع قاعدة البيانات"""
    print("🔍 اختبار الاتصال مع قاعدة البيانات Supabase...")
    print("=" * 50)
    
    # قراءة إعدادات قاعدة البيانات من .env
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            env_content = f.read()
            
        # استخراج المتغيرات
        supabase_url = None
        supabase_key = None
        
        for line in env_content.split('\n'):
            if line.startswith('SUPABASE_URL='):
                supabase_url = line.split('=', 1)[1].strip()
            elif line.startswith('SUPABASE_KEY='):
                supabase_key = line.split('=', 1)[1].strip()
                
        if not supabase_url or not supabase_key:
            print("❌ لم يتم العثور على إعدادات قاعدة البيانات في ملف .env")
            return False
            
        print(f"📊 URL: {supabase_url}")
        print(f"🔑 Key: {supabase_key[:20]}...")
        print()
        
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف .env: {e}")
        return False
    
    # إعداد headers
    headers = {
        'apikey': supabase_key,
        'Authorization': f'Bearer {supabase_key}',
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
    }
    
    # اختبار 1: اختبار الاتصال الأساسي
    print("🧪 اختبار 1: الاتصال الأساسي...")
    try:
        url = f"{supabase_url}/rest/v1/"
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ الاتصال الأساسي ناجح")
        else:
            print(f"❌ فشل الاتصال الأساسي: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال الأساسي: {e}")
        return False
    
    # اختبار 2: اختبار جدول mods
    print("\n🧪 اختبار 2: جلب المودات...")
    try:
        url = f"{supabase_url}/rest/v1/mods"
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ تم جلب {len(data)} مود من قاعدة البيانات")
            
            if len(data) > 0:
                print(f"📋 مثال على أول مود:")
                first_mod = data[0]
                print(f"   - ID: {first_mod.get('id')}")
                print(f"   - Name: {first_mod.get('name', 'غير محدد')}")
                print(f"   - Category: {first_mod.get('category', 'غير محدد')}")
                print(f"   - Version: {first_mod.get('version', 'غير محدد')}")
            else:
                print("⚠️ لا توجد مودات في قاعدة البيانات")
                
        else:
            print(f"❌ فشل في جلب المودات: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في جلب المودات: {e}")
        return False
    
    # اختبار 3: اختبار إحصائيات
    print("\n🧪 اختبار 3: إحصائيات قاعدة البيانات...")
    try:
        # عدد المودات
        url = f"{supabase_url}/rest/v1/mods?select=id"
        headers_count = headers.copy()
        headers_count['Prefer'] = 'count=exact'
        
        response = requests.head(url, headers=headers_count, timeout=10)
        
        if response.status_code == 200:
            count_header = response.headers.get('Content-Range', '0')
            if '/' in count_header:
                total_count = int(count_header.split('/')[-1])
                print(f"📊 إجمالي المودات: {total_count}")
            else:
                print("📊 لا يمكن تحديد عدد المودات")
        else:
            print(f"❌ فشل في جلب الإحصائيات: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في جلب الإحصائيات: {e}")
    
    print("\n" + "=" * 50)
    print("✅ جميع اختبارات قاعدة البيانات نجحت!")
    print(f"🕒 وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار قاعدة البيانات Supabase")
    print("=" * 50)
    
    success = test_supabase_connection()
    
    if success:
        print("\n🎉 جميع الاختبارات نجحت! قاعدة البيانات تعمل بشكل صحيح.")
        sys.exit(0)
    else:
        print("\n❌ فشلت بعض الاختبارات. يرجى مراجعة الإعدادات.")
        sys.exit(1)

if __name__ == "__main__":
    main()
