-- جدول روابط التحميل المخصصة للمستخدمين
-- Custom Download Links Table for Users

CREATE TABLE IF NOT EXISTS custom_download_links (
    id SERIAL PRIMARY KEY,
    channel_id VARCHAR(255) NOT NULL UNIQUE,
    custom_link TEXT NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP NULL
);

-- إضافة تعليقات للجدول والأعمدة
COMMENT ON TABLE custom_download_links IS 'جدول لحفظ روابط التحميل المخصصة لكل قناة مستخدم';
COMMENT ON COLUMN custom_download_links.id IS 'المعرف الفريد للرابط';
COMMENT ON COLUMN custom_download_links.channel_id IS 'معرف القناة (فريد)';
COMMENT ON COLUMN custom_download_links.custom_link IS 'الرابط المخصص للتحميل';
COMMENT ON COLUMN custom_download_links.created_by IS 'معرف المستخدم الذي أنشأ الرابط';
COMMENT ON COLUMN custom_download_links.created_at IS 'تاريخ إنشاء الرابط';
COMMENT ON COLUMN custom_download_links.updated_at IS 'تاريخ آخر تحديث';
COMMENT ON COLUMN custom_download_links.is_active IS 'حالة الرابط (نشط/غير نشط)';
COMMENT ON COLUMN custom_download_links.usage_count IS 'عدد مرات استخدام الرابط';
COMMENT ON COLUMN custom_download_links.last_used_at IS 'تاريخ آخر استخدام للرابط';

-- فهارس لتحسين الأداء
CREATE INDEX idx_channel_id ON custom_download_links (channel_id);
CREATE INDEX idx_created_by ON custom_download_links (created_by);
CREATE INDEX idx_is_active ON custom_download_links (is_active);
CREATE INDEX idx_created_at ON custom_download_links (created_at);

-- إضافة قيود إضافية
ALTER TABLE custom_download_links 
ADD CONSTRAINT chk_custom_link_not_empty CHECK (LENGTH(TRIM(custom_link)) > 0),
ADD CONSTRAINT chk_usage_count_positive CHECK (usage_count >= 0);

-- إنشاء trigger لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_custom_download_links_updated_at_func()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_custom_download_links_updated_at
BEFORE UPDATE ON custom_download_links
FOR EACH ROW
EXECUTE FUNCTION update_custom_download_links_updated_at_func();

-- إنشاء view لعرض الروابط النشطة فقط
CREATE OR REPLACE VIEW active_custom_download_links AS
SELECT 
    id,
    channel_id,
    custom_link,
    created_by,
    created_at,
    updated_at,
    usage_count,
    last_used_at
FROM custom_download_links 
WHERE is_active = TRUE;

-- إنشاء stored procedure (function) لإحصائيات الاستخدام
CREATE OR REPLACE FUNCTION GetCustomLinkStats(p_channel_id VARCHAR(255))
RETURNS TABLE (
    channel_id VARCHAR,
    custom_link TEXT,
    usage_count INTEGER,
    created_at TIMESTAMP,
    last_used_at TIMESTAMP,
    days_since_created INTEGER,
    last_usage_text TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cdl.channel_id,
        cdl.custom_link,
        cdl.usage_count,
        cdl.created_at,
        cdl.last_used_at,
        (CURRENT_DATE - cdl.created_at::date)::INTEGER as days_since_created,
        CASE 
            WHEN cdl.last_used_at IS NULL THEN 'لم يستخدم بعد'
            WHEN (CURRENT_DATE - cdl.last_used_at::date)::INTEGER = 0 THEN 'اليوم'
            WHEN (CURRENT_DATE - cdl.last_used_at::date)::INTEGER = 1 THEN 'أمس'
            ELSE (CURRENT_DATE - cdl.last_used_at::date)::TEXT || ' أيام مضت'
        END as last_usage_text
    FROM custom_download_links cdl
    WHERE cdl.channel_id = p_channel_id AND cdl.is_active = TRUE;
END;
$$ LANGUAGE plpgsql;

-- إنشاء stored procedure (function) لتحديث عداد الاستخدام
CREATE OR REPLACE FUNCTION IncrementLinkUsage(p_channel_id VARCHAR(255))
RETURNS VOID AS $$
BEGIN
    UPDATE custom_download_links 
    SET 
        usage_count = usage_count + 1,
        last_used_at = CURRENT_TIMESTAMP
    WHERE channel_id = p_channel_id AND is_active = TRUE;
END;
$$ LANGUAGE plpgsql;

-- بيانات تجريبية (اختيارية)
-- INSERT INTO custom_download_links (channel_id, custom_link, created_by) VALUES
-- ('@test_channel', 'https://example.com/download', '123456789'),
-- ('-1001234567890', 'https://mysite.com/mods', '987654321');

-- استعلامات مفيدة للإدارة:

-- 1. عرض جميع الروابط المخصصة
-- SELECT * FROM custom_download_links ORDER BY created_at DESC;

-- 2. عرض الروابط الأكثر استخداماً
-- SELECT channel_id, custom_link, usage_count FROM custom_download_links 
-- WHERE is_active = TRUE ORDER BY usage_count DESC LIMIT 10;

-- 3. عرض الروابط غير المستخدمة
-- SELECT channel_id, custom_link, created_at FROM custom_download_links 
-- WHERE usage_count = 0 AND is_active = TRUE;

-- 4. حذف الروابط القديمة غير المستخدمة (أكثر من 30 يوم)
-- DELETE FROM custom_download_links 
-- WHERE usage_count = 0 AND (CURRENT_DATE - created_at::date)::INTEGER > 30;
