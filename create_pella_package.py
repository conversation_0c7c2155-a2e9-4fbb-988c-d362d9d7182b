#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تجميع ملفات البوت للاستضافة على Pella
Script to package bot files for Pella hosting
"""

import os
import shutil
import json
from pathlib import Path

def create_pella_package():
    """إنشاء حزمة الملفات للاستضافة على Pella"""
    
    # إنشاء مجلد الاستضافة
    hosting_dir = Path("pella_hosting")
    hosting_dir.mkdir(exist_ok=True)
    
    print("🚀 بدء تجميع ملفات البوت للاستضافة على Pella...")
    print("="*60)
    
    # قائمة الملفات الأساسية المطلوبة
    essential_files = [
        # ملفات Python الأساسية
        "main.py",
        "start_hosting.py", 
        "hosting_config.py",
        "supabase_client.py",
        "web_server.py",
        "telegram_web_app.py",
        "notifications.py",
        
        # ملفات الإعداد
        "requirements.txt",
        "Procfile",
        "config.json",
        "runtime.txt",
        
        # ملفات البيانات المهمة (إذا وجدت)
        "admin_settings.json",
        "mods.json",
        "all_users.json",
        "user_channels.json",
        "notification_templates.json",
        "custom_download_links.json",
        "pending_publications.json",
        "broadcast_history.json",
        "user_invitations.json",
        "user_mods_status.json",
        "user_blocked_mods.json",
        "user_feedback.json",
        "creators_database.json",
        "custom_formats.json",
        "user_monetization.json",
        "user_feature_activation.json",
        "user_subscriptions.json",
        "admin_processed_mods.json",
        "pending_publication.json"
    ]
    
    # ملفات اختيارية قد تكون مفيدة
    optional_files = [
        "optimization_config.py",
        "network_config.py", 
        "secure_config.py",
        "security_config.py",
        "app.json",
        "railway.json",
        "render.yaml",
        "nixpacks.toml"
    ]
    
    # نسخ الملفات الأساسية
    copied_files = []
    missing_files = []
    
    print("📁 نسخ الملفات الأساسية:")
    for file_name in essential_files:
        source_path = Path(file_name)
        dest_path = hosting_dir / file_name
        
        if source_path.exists():
            try:
                shutil.copy2(source_path, dest_path)
                copied_files.append(file_name)
                print(f"   ✅ {file_name}")
            except Exception as e:
                print(f"   ❌ فشل نسخ {file_name}: {e}")
                missing_files.append(file_name)
        else:
            missing_files.append(file_name)
            print(f"   ⚠️ {file_name} غير موجود")
    
    print(f"\n📁 نسخ الملفات الاختيارية:")
    for file_name in optional_files:
        source_path = Path(file_name)
        dest_path = hosting_dir / file_name
        
        if source_path.exists():
            try:
                shutil.copy2(source_path, dest_path)
                copied_files.append(file_name)
                print(f"   ✅ {file_name}")
            except Exception as e:
                print(f"   ❌ فشل نسخ {file_name}: {e}")
        else:
            print(f"   ⚠️ {file_name} غير موجود")
    
    # نسخ مجلد htdocs
    print(f"\n🌐 نسخ ملفات الويب:")
    htdocs_source = Path("htdocs")
    htdocs_dest = hosting_dir / "htdocs"
    
    if htdocs_source.exists():
        try:
            if htdocs_dest.exists():
                shutil.rmtree(htdocs_dest)
            shutil.copytree(htdocs_source, htdocs_dest)
            print(f"   ✅ تم نسخ مجلد htdocs بالكامل")
        except Exception as e:
            print(f"   ❌ فشل نسخ مجلد htdocs: {e}")
    else:
        print(f"   ⚠️ مجلد htdocs غير موجود")
    
    # نسخ مجلد uploaded_images إذا وجد
    print(f"\n🖼️ نسخ الصور المرفوعة:")
    images_source = Path("uploaded_images")
    images_dest = hosting_dir / "uploaded_images"
    
    if images_source.exists():
        try:
            if images_dest.exists():
                shutil.rmtree(images_dest)
            shutil.copytree(images_source, images_dest)
            print(f"   ✅ تم نسخ مجلد uploaded_images")
        except Exception as e:
            print(f"   ❌ فشل نسخ مجلد uploaded_images: {e}")
    else:
        print(f"   ⚠️ مجلد uploaded_images غير موجود")
    
    # إنشاء ملف runtime.txt إذا لم يكن موجود
    runtime_file = hosting_dir / "runtime.txt"
    if not runtime_file.exists():
        with open(runtime_file, 'w') as f:
            f.write("python-3.11.0\n")
        print(f"   ✅ تم إنشاء ملف runtime.txt")
    
    # إنشاء ملف .env للاستضافة
    env_file = hosting_dir / ".env"
    env_content = """# إعدادات البوت للاستضافة على Pella
BOT_TOKEN=7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4
ADMIN_CHAT_ID=7513880877
ADMIN_USERNAME=Kim880198
SUPABASE_URL=https://ytqxxodyecdeosnqoure.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4

# إعدادات التحسين للاستضافة المجانية
OPTIMIZATION_ENABLED=true
LOW_RESOURCE_MODE=true
REQUEST_TIMEOUT=30
"""
    
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(env_content)
    print(f"   ✅ تم إنشاء ملف .env")
    
    # إنشاء تقرير التجميع
    print(f"\n📊 تقرير التجميع:")
    print(f"   ✅ تم نسخ {len(copied_files)} ملف بنجاح")
    if missing_files:
        print(f"   ⚠️ {len(missing_files)} ملف مفقود: {', '.join(missing_files[:5])}{'...' if len(missing_files) > 5 else ''}")
    
    # حساب حجم المجلد
    total_size = sum(f.stat().st_size for f in hosting_dir.rglob('*') if f.is_file())
    size_mb = total_size / (1024 * 1024)
    print(f"   📦 حجم الحزمة: {size_mb:.2f} MB")
    
    print(f"\n🎉 تم تجميع ملفات البوت بنجاح في مجلد: {hosting_dir}")
    print(f"📁 يمكنك الآن رفع محتويات هذا المجلد إلى استضافة Pella")
    
    return hosting_dir, copied_files, missing_files

if __name__ == "__main__":
    create_pella_package()
