#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة إدارة مميزات المستخدمين من لوحة الأدمن
Test for admin user features management functionality
"""

import sys
import os
import json
from unittest.mock import Mock, AsyncMock

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_admin_functions_exist():
    """اختبار وجود دوال إدارة المميزات الجديدة"""
    print("🔍 اختبار وجود دوال إدارة المميزات...")
    
    try:
        from main import (
            # دوال إدارة المميزات الجديدة
            admin_manage_user_features_menu,
            admin_show_user_features,
            admin_toggle_user_feature,
            admin_grant_all_features,
            admin_revoke_all_features,
            admin_handle_user_features_input,
            
            # دوال مساعدة
            load_user_invitations,
            save_user_invitations,
            create_user_invitation,
            check_user_premium_feature,
            
            # إعدادات
            YOUR_CHAT_ID
        )
        print("✅ جميع الدوال موجودة")
        return True
    except ImportError as e:
        print(f"❌ دالة مفقودة: {e}")
        return False

def test_user_features_data_structure():
    """اختبار بنية بيانات مميزات المستخدمين"""
    print("\n🏗️ اختبار بنية بيانات المميزات...")
    
    try:
        from main import load_user_invitations, save_user_invitations, create_user_invitation
        
        # إنشاء مستخدم تجريبي
        test_user_id = "999888777"
        create_user_invitation(test_user_id)
        
        # جلب البيانات
        invitations = load_user_invitations()
        user_data = invitations.get(test_user_id, {})
        
        # التحقق من البنية المطلوبة
        required_keys = ['premium_features', 'total_invitations', 'invitation_code']
        missing_keys = []
        
        for key in required_keys:
            if key not in user_data:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"❌ مفاتيح مفقودة: {missing_keys}")
            return False
        
        # التحقق من بنية premium_features
        premium_features = user_data.get('premium_features', {})
        if not isinstance(premium_features, dict):
            print("❌ premium_features ليس dictionary")
            return False
        
        print("✅ بنية البيانات صحيحة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص بنية البيانات: {e}")
        return False

def test_feature_management_logic():
    """اختبار منطق إدارة المميزات"""
    print("\n🔧 اختبار منطق إدارة المميزات...")
    
    try:
        from main import (
            load_user_invitations, 
            save_user_invitations, 
            create_user_invitation,
            check_user_premium_feature
        )
        
        test_user_id = "999888777"
        
        # إنشاء مستخدم تجريبي
        create_user_invitation(test_user_id)
        
        # اختبار منح ميزة
        invitations = load_user_invitations()
        user_data = invitations[test_user_id]
        user_data['premium_features']['url_shortener_access'] = True
        invitations[test_user_id] = user_data
        save_user_invitations(invitations)
        
        # التحقق من منح الميزة
        has_feature = check_user_premium_feature(test_user_id, 'url_shortener_access')
        if not has_feature:
            print("❌ فشل في منح الميزة")
            return False
        
        print("✅ منح الميزة: نجح")
        
        # اختبار إلغاء ميزة
        invitations = load_user_invitations()
        user_data = invitations[test_user_id]
        user_data['premium_features']['url_shortener_access'] = False
        invitations[test_user_id] = user_data
        save_user_invitations(invitations)
        
        # التحقق من إلغاء الميزة
        has_feature = check_user_premium_feature(test_user_id, 'url_shortener_access')
        if has_feature:
            print("❌ فشل في إلغاء الميزة")
            return False
        
        print("✅ إلغاء الميزة: نجح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار منطق المميزات: {e}")
        return False

def test_all_features_list():
    """اختبار قائمة جميع المميزات المتاحة"""
    print("\n📋 اختبار قائمة المميزات المتاحة...")
    
    expected_features = {
        'unlimited_channels': '📺 قنوات غير محدودة',
        'url_shortener_access': '🔗 اختصار الروابط',
        'custom_download_links': '🔗 روابط تحميل مخصصة',
        'publish_intervals_extended': '⏰ أوقات نشر موسعة',
        'tasks_system_access': '📋 نظام المهام',
        'page_customization_vip': '🎨 تخصيص صفحات VIP',
        'ads_system_access': '💰 نظام الإعلانات'
    }
    
    try:
        # فحص الكود للتأكد من وجود جميع المميزات
        from main import admin_show_user_features
        import inspect
        
        source = inspect.getsource(admin_show_user_features)
        
        missing_features = []
        for feature_key, feature_name in expected_features.items():
            if f"'{feature_key}'" not in source:
                missing_features.append(feature_key)
        
        if missing_features:
            print(f"❌ مميزات مفقودة في الكود: {missing_features}")
            return False
        
        print("✅ جميع المميزات موجودة في الكود")
        print(f"📊 عدد المميزات المدعومة: {len(expected_features)}")
        
        for feature_key, feature_name in expected_features.items():
            print(f"  • {feature_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قائمة المميزات: {e}")
        return False

def test_admin_panel_integration():
    """اختبار تكامل الميزة مع لوحة الأدمن"""
    print("\n🎛️ اختبار تكامل لوحة الأدمن...")
    
    try:
        from main import admin_panel
        import inspect
        
        source = inspect.getsource(admin_panel)
        
        # التحقق من وجود زر إدارة المميزات
        if "admin_manage_user_features" not in source:
            print("❌ زر إدارة المميزات غير موجود في لوحة الأدمن")
            return False
        
        if "🎁 إدارة مميزات المستخدمين" not in source:
            print("❌ نص زر إدارة المميزات غير موجود")
            return False
        
        print("✅ زر إدارة المميزات موجود في لوحة الأدمن")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص تكامل لوحة الأدمن: {e}")
        return False

def test_callback_handlers():
    """اختبار معالجات الأحداث"""
    print("\n🔗 اختبار معالجات الأحداث...")
    
    expected_patterns = [
        "admin_manage_user_features",
        "admin_toggle_feature_",
        "admin_grant_all_features_",
        "admin_revoke_all_features_",
        "admin_refresh_user_features_"
    ]
    
    try:
        # قراءة ملف main.py للبحث عن المعالجات
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        missing_handlers = []
        for pattern in expected_patterns:
            if pattern not in content:
                missing_handlers.append(pattern)
        
        if missing_handlers:
            print(f"❌ معالجات مفقودة: {missing_handlers}")
            return False
        
        print("✅ جميع معالجات الأحداث موجودة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص معالجات الأحداث: {e}")
        return False

async def test_mock_admin_functions():
    """اختبار الدوال مع mock objects"""
    print("\n🧪 اختبار الدوال مع mock objects...")
    
    try:
        from main import admin_manage_user_features_menu, admin_show_user_features
        
        # إنشاء mock objects
        update = Mock()
        update.callback_query = Mock()
        update.callback_query.answer = AsyncMock()
        update.callback_query.from_user = Mock()
        update.callback_query.from_user.id = int(os.environ.get("ADMIN_CHAT_ID", "7513880877"))
        update.callback_query.message = Mock()
        update.callback_query.message.photo = None
        
        context = Mock()
        context.user_data = {}
        context.bot = AsyncMock()
        
        # اختبار دالة القائمة الرئيسية
        await admin_manage_user_features_menu(update, context)
        print("✅ دالة القائمة الرئيسية: نجحت")
        
        # اختبار دالة عرض المميزات
        test_user_id = "123456789"
        await admin_show_user_features(update, context, test_user_id)
        print("✅ دالة عرض المميزات: نجحت")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدوال: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار ميزة إدارة مميزات المستخدمين من لوحة الأدمن")
    print("=" * 80)
    
    tests = [
        ("وجود الدوال", test_admin_functions_exist),
        ("بنية البيانات", test_user_features_data_structure),
        ("منطق إدارة المميزات", test_feature_management_logic),
        ("قائمة المميزات", test_all_features_list),
        ("تكامل لوحة الأدمن", test_admin_panel_integration),
        ("معالجات الأحداث", test_callback_handlers),
        ("اختبار الدوال", test_mock_admin_functions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📝 {test_name}:")
        print("-" * 50)
        try:
            if test_func.__name__ == 'test_mock_admin_functions':
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 نتائج الاختبار النهائية:")
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت! ميزة إدارة مميزات المستخدمين جاهزة.")
        print("\n📋 الميزات المضافة:")
        print("• 🎁 زر إدارة مميزات المستخدمين في لوحة الأدمن")
        print("• 🔍 البحث عن المستخدمين بمعرف المستخدم")
        print("• 📊 عرض مميزات المستخدم الحالية")
        print("• ✅ منح مميزات محددة للمستخدمين")
        print("• 🚫 إلغاء مميزات محددة من المستخدمين")
        print("• 🎁 منح جميع المميزات دفعة واحدة")
        print("• 🗑️ إلغاء جميع المميزات دفعة واحدة")
        print("• 🔔 إشعار المستخدمين بالتغييرات")
        print("• 🔄 تحديث العرض في الوقت الفعلي")
    else:
        print(f"\n⚠️ هناك {total - passed} اختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
