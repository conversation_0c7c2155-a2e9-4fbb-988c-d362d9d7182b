-- جدول إعدادات الإعلانات للمستخدمين
-- User Ads Settings Table for Supabase (PostgreSQL)

CREATE TABLE IF NOT EXISTS user_ads_settings (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL UNIQUE,
    channel_id VARCHAR(255) NOT NULL,

    -- إعدادات الإعلان الأساسية
    ads_enabled BOOLEAN DEFAULT FALSE,
    ad_direct_link TEXT NULL,
    ad_network VARCHAR(100) DEFAULT 'custom', -- 'adstera', 'monetag', 'propellerads', 'hilltopads', 'custom'

    -- إعدادات عرض الإعلان
    ad_display_mode VARCHAR(50) DEFAULT 'on_download', -- 'on_download', 'after_page_load', 'after_delay'
    ad_delay_seconds INTEGER DEFAULT 3, -- للوضع after_delay

    -- إعدادات زر الإغلاق
    close_button_enabled BOOLEAN DEFAULT TRUE,
    close_button_delay INTEGER DEFAULT 5, -- بالثواني

    -- إحصائيات
    total_ad_views INTEGER DEFAULT 0,
    total_ad_clicks INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_ads_settings_user_id ON user_ads_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_ads_settings_channel_id ON user_ads_settings(channel_id);
CREATE INDEX IF NOT EXISTS idx_user_ads_settings_ads_enabled ON user_ads_settings(ads_enabled);
CREATE INDEX IF NOT EXISTS idx_user_ads_settings_created_at ON user_ads_settings(created_at);

-- إضافة تعليقات للجدول (PostgreSQL syntax)
COMMENT ON TABLE user_ads_settings IS 'جدول لحفظ إعدادات الإعلانات لكل مستخدم';

-- تعليقات الأعمدة (PostgreSQL syntax)
COMMENT ON COLUMN user_ads_settings.user_id IS 'معرف المستخدم في تليجرام';
COMMENT ON COLUMN user_ads_settings.channel_id IS 'معرف القناة المرتبطة';
COMMENT ON COLUMN user_ads_settings.ads_enabled IS 'هل الإعلانات مفعلة';
COMMENT ON COLUMN user_ads_settings.ad_direct_link IS 'رابط الإعلان المباشر';
COMMENT ON COLUMN user_ads_settings.ad_network IS 'شبكة الإعلانات المستخدمة';
COMMENT ON COLUMN user_ads_settings.ad_display_mode IS 'طريقة عرض الإعلان';
COMMENT ON COLUMN user_ads_settings.ad_delay_seconds IS 'تأخير عرض الإعلان بالثواني';
COMMENT ON COLUMN user_ads_settings.close_button_enabled IS 'هل زر الإغلاق مفعل';
COMMENT ON COLUMN user_ads_settings.close_button_delay IS 'تأخير ظهور زر الإغلاق بالثواني';
COMMENT ON COLUMN user_ads_settings.total_ad_views IS 'إجمالي مشاهدات الإعلانات';
COMMENT ON COLUMN user_ads_settings.total_ad_clicks IS 'إجمالي نقرات الإعلانات';

-- إنشاء دالة لتحديث updated_at تلقائياً
-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث updated_at
DROP TRIGGER IF EXISTS update_user_ads_settings_updated_at ON user_ads_settings;
CREATE TRIGGER update_user_ads_settings_updated_at
    BEFORE UPDATE ON user_ads_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- بيانات تجريبية (اختيارية)
-- INSERT INTO user_ads_settings (user_id, channel_id, ads_enabled, ad_direct_link, ad_network, ad_display_mode)
-- VALUES ('123456789', '@test_channel', TRUE, 'https://example-ad-network.com/redirect?id=12345', 'adstera', 'on_download');
