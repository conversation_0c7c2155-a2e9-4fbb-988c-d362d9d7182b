<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحميل المود - Modetaris</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            text-align: center;
            max-width: 500px;
            width: 100%;
            animation: bounceIn 0.8s ease-out;
        }
        
        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .download-icon {
            font-size: 5em;
            color: #4CAF50;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2em;
        }
        
        .countdown {
            font-size: 3em;
            color: #4CAF50;
            font-weight: bold;
            margin: 20px 0;
        }
        
        .message {
            color: #666;
            font-size: 1.1em;
            line-height: 1.6;
            margin: 20px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 30px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }
        
        .manual-download {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #e0e0e0;
        }
        
        .download-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
            margin: 10px;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
        }
        
        .back-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
        }
        
        .instructions {
            background: #e3f2fd;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            text-align: right;
            color: #333;
        }
        
        .instructions li {
            margin: 10px 0;
            line-height: 1.5;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .download-icon {
                font-size: 4em;
            }
            
            h1 {
                font-size: 1.5em;
            }
            
            .countdown {
                font-size: 2.5em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="download-icon">📥</div>
        <h1>جاري تحضير التحميل...</h1>
        
        <div class="countdown" id="countdown">5</div>
        <div class="message">سيبدأ التحميل تلقائياً خلال <span id="seconds">5</span> ثواني</div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress"></div>
        </div>
        
        <div class="manual-download" id="manual-download" style="display: none;">
            <h3>لم يبدأ التحميل تلقائياً؟</h3>
            <p>اضغط على الزر أدناه لتحميل المود يدوياً:</p>
            <a href="#" id="manual-link" class="download-btn">
                📥 تحميل المود الآن
            </a>
        </div>
        
        <div class="instructions">
            <h3>📋 تعليمات التثبيت:</h3>
            <ol>
                <li>قم بتحميل ملف المود (.mcpack أو .mcaddon)</li>
                <li>افتح الملف باستخدام Minecraft PE</li>
                <li>سيتم تثبيت المود تلقائياً</li>
                <li>ابدأ عالم جديد أو افتح عالم موجود</li>
                <li>فعل المود من إعدادات العالم</li>
            </ol>
        </div>
        
        <a href="/" class="back-btn">🏠 العودة للرئيسية</a>
        <a href="#" id="mod-page-link" class="back-btn">📄 صفحة المود</a>
    </div>
    
    <script>
        // استخراج معرف المود من الرابط
        function getModId() {
            const path = window.location.pathname;
            const parts = path.split('/');
            return parts[parts.length - 1] || '1';
        }
        
        // روابط تحميل تجريبية
        const downloadLinks = {
            '1': 'https://example.com/download/lucky-block.mcpack',
            '2': 'https://example.com/download/dragon-mounts.mcpack'
        };
        
        // بدء العد التنازلي
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        const secondsElement = document.getElementById('seconds');
        const progressElement = document.getElementById('progress');
        const manualDownload = document.getElementById('manual-download');
        const manualLink = document.getElementById('manual-link');
        const modPageLink = document.getElementById('mod-page-link');
        
        const modId = getModId();
        const downloadUrl = downloadLinks[modId] || 'https://example.com/download/sample.mcpack';
        
        // تحديث روابط
        manualLink.href = downloadUrl;
        modPageLink.href = `/api/mod/${modId}`;
        
        // دالة العد التنازلي
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            secondsElement.textContent = countdown;
            
            // تحديث شريط التقدم
            const progress = ((5 - countdown) / 5) * 100;
            progressElement.style.width = progress + '%';
            
            if (countdown <= 0) {
                clearInterval(timer);
                startDownload();
            }
        }, 1000);
        
        // بدء التحميل
        function startDownload() {
            // تحديث النص
            document.querySelector('h1').textContent = '✅ بدء التحميل!';
            document.querySelector('.message').textContent = 'إذا لم يبدأ التحميل، استخدم الزر أدناه';
            countdownElement.style.display = 'none';
            
            // إظهار التحميل اليدوي
            manualDownload.style.display = 'block';
            
            // محاولة بدء التحميل التلقائي
            try {
                // إنشاء رابط مخفي للتحميل
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = `mod_${modId}.mcpack`;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                // تحديث النص بعد محاولة التحميل
                setTimeout(() => {
                    document.querySelector('h1').textContent = '🎉 تم بدء التحميل!';
                    document.querySelector('.message').textContent = 'تحقق من مجلد التحميلات في جهازك';
                }, 1000);
                
            } catch (error) {
                console.error('خطأ في التحميل التلقائي:', error);
                document.querySelector('h1').textContent = '⚠️ استخدم التحميل اليدوي';
                document.querySelector('.message').textContent = 'اضغط على زر التحميل أدناه';
            }
        }
        
        // إضافة مستمع للنقر على التحميل اليدوي
        manualLink.addEventListener('click', function(e) {
            e.preventDefault();
            
            // محاولة فتح الرابط في نافذة جديدة
            window.open(downloadUrl, '_blank');
            
            // تحديث النص
            document.querySelector('h1').textContent = '🎉 تم بدء التحميل!';
            document.querySelector('.message').textContent = 'تحقق من مجلد التحميلات في جهازك';
        });
        
        // تحديث شريط التقدم إلى 100% عند التحميل
        progressElement.style.width = '0%';
    </script>
</body>
</html>
