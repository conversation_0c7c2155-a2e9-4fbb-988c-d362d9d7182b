#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل البوت النهائي مع جميع الإصلاحات
Final Bot Startup with All Fixes Applied
"""

import os
import sys
import asyncio
import logging
import subprocess
import time
import platform
from pathlib import Path

# إصلاح مشكلة Unicode في Windows
if platform.system() == "Windows":
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)

logger = logging.getLogger(__name__)

def run_pre_startup_checks():
    """تشغيل فحوصات ما قبل البدء"""
    print("🔍 تشغيل فحوصات ما قبل البدء...")
    
    try:
        # تشغيل الإصلاح النهائي
        result = subprocess.run([sys.executable, "final_connection_fix.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ تم تطبيق جميع الإصلاحات بنجاح")
            return True
        else:
            print(f"⚠️ بعض الإصلاحات فشلت: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ انتهت مهلة الإصلاحات، المتابعة...")
        return False
    except FileNotFoundError:
        print("ℹ️ ملف الإصلاح غير موجود، المتابعة بدون إصلاحات...")
        return False
    except Exception as e:
        print(f"⚠️ خطأ في الإصلاحات: {e}")
        return False

def check_environment():
    """فحص البيئة"""
    print("🔍 فحص البيئة...")
    
    # فحص Python
    print(f"🐍 Python: {sys.version}")
    
    # فحص الملفات المطلوبة
    required_files = ["main.py", ".env", "supabase_client.py"]
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    
    # فحص متغيرات البيئة
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        required_vars = ["BOT_TOKEN", "ADMIN_CHAT_ID", "SUPABASE_URL", "SUPABASE_KEY"]
        missing_vars = []
        
        for var in required_vars:
            if not os.environ.get(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ متغيرات بيئة مفقودة: {', '.join(missing_vars)}")
            return False
        
        print("✅ جميع متغيرات البيئة موجودة")
        
    except ImportError:
        print("⚠️ مكتبة python-dotenv غير مثبتة")
    
    return True

def test_connections():
    """اختبار الاتصالات"""
    print("🌐 اختبار الاتصالات...")
    
    try:
        import socket
        
        # اختبار الإنترنت
        try:
            socket.create_connection(("8.8.8.8", 53), timeout=10)
            print("✅ الاتصال بالإنترنت يعمل")
        except:
            print("❌ لا يوجد اتصال بالإنترنت")
            return False
        
        # اختبار Telegram
        try:
            socket.create_connection(("api.telegram.org", 443), timeout=10)
            print("✅ الاتصال مع Telegram يعمل")
        except:
            print("⚠️ مشكلة في الاتصال مع Telegram")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصالات: {e}")
        return False

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 فحص وتثبيت المتطلبات...")
    
    try:
        # فحص وجود ملف requirements.txt
        if not os.path.exists("requirements.txt"):
            print("⚠️ ملف requirements.txt غير موجود")
            return True
        
        # تثبيت المتطلبات
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ تم تثبيت المتطلبات بنجاح")
            return True
        else:
            print(f"⚠️ مشاكل في تثبيت المتطلبات: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ انتهت مهلة تثبيت المتطلبات")
        return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def start_web_servers():
    """تشغيل خوادم الويب"""
    print("🌐 تشغيل خوادم الويب...")

    try:
        import threading
        import time

        # تشغيل خادم الويب الأساسي
        def run_main_server():
            try:
                from web_server import run_web_server
                run_web_server(5000)
            except Exception as e:
                print(f"❌ خطأ في خادم الويب الأساسي: {e}")

        # تشغيل خادم Telegram Web App
        def run_telegram_server():
            try:
                from telegram_web_app import run_telegram_web_app
                run_telegram_web_app(5001)
            except Exception as e:
                print(f"❌ خطأ في خادم Telegram Web App: {e}")

        # تشغيل الخوادم في threads منفصلة
        main_server_thread = threading.Thread(target=run_main_server, daemon=True)
        telegram_server_thread = threading.Thread(target=run_telegram_server, daemon=True)

        main_server_thread.start()
        telegram_server_thread.start()

        # انتظار قصير للتأكد من بدء الخوادم
        time.sleep(3)

        print("✅ تم تشغيل خوادم الويب")
        return True

    except Exception as e:
        print(f"❌ خطأ في تشغيل خوادم الويب: {e}")
        return False

def start_ngrok():
    """تشغيل ngrok - معطل لأن البوت يستخدم موقع مستضاف"""
    print("ℹ️ تم تعطيل ngrok - البوت يستخدم موقع مستضاف: https://sendaddons.fwh.is")
    return True  # نعيد True لأن البوت لا يحتاج ngrok بعد الآن

async def start_bot():
    """تشغيل البوت"""
    print("🚀 بدء تشغيل البوت...")

    try:
        # محاولة استيراد وتشغيل البوت
        if os.path.exists("run_bot_fixed.py"):
            print("📱 استخدام run_bot_fixed.py...")
            from run_bot_fixed import run_bot_with_retry
            await run_bot_with_retry()
        else:
            print("📱 استخدام main.py...")
            from main import main
            await main()

    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البوت: {e}")
        raise

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🤖 تشغيل بوت نشر مودات ماين كرافت - الإصدار النهائي")
    print("🚀 Minecraft Mods Bot - Final Edition Startup")
    print("=" * 60)
    
    # الخطوات
    steps = [
        ("🔧 تطبيق الإصلاحات", run_pre_startup_checks),
        ("🔍 فحص البيئة", check_environment),
        ("🌐 اختبار الاتصالات", test_connections),
        ("📦 تثبيت المتطلبات", install_requirements),
        ("🌐 تشغيل خوادم الويب", start_web_servers),
        ("✅ التحقق من الموقع المستضاف", start_ngrok),  # تم تغيير الاسم فقط
    ]
    
    # تنفيذ الخطوات
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        try:
            if not step_func():
                print(f"❌ فشل في {step_name}")
                print("🔧 يرجى مراجعة الأخطاء أعلاه وإصلاحها")
                return
        except Exception as e:
            print(f"❌ خطأ في {step_name}: {e}")
            return
    
    print("\n" + "=" * 60)
    print("✅ جميع الفحوصات نجحت!")
    print("🚀 بدء تشغيل البوت...")
    print("⏹️ اضغط Ctrl+C لإيقاف البوت")
    print("=" * 60)
    
    # تشغيل البوت
    try:
        asyncio.run(start_bot())
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف البوت بأمان")
    except Exception as e:
        print(f"\n\n❌ خطأ في تشغيل البوت: {e}")
        print("\n🔧 نصائح لحل المشكلة:")
        print("   1. تأكد من صحة إعدادات .env")
        print("   2. تأكد من استقرار الاتصال بالإنترنت")
        print("   3. جرب إعادة تشغيل الكمبيوتر")
        print("   4. راجع ملف TROUBLESHOOTING.md")

if __name__ == "__main__":
    main()
