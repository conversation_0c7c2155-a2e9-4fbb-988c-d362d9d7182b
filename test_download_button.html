<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر التحميل المحسن - Enhanced Download Button Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .pixel-button {
            image-rendering: pixelated;
            background-color: #FFA500;
            color: white;
            border: 2px solid #FFD700;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            cursor: pointer;
            box-shadow: inset -4px -4px 0 0 #d27e00,
                       inset 4px 4px 0 0 #ffcb6b,
                       0 0 10px #FFD700,
                       0 0 20px #FFD700;
            transition: all 0.1s;
            position: relative;
            overflow: hidden;
            animation: pulse-animation 1.5s infinite;
            min-width: 200px;
            min-height: 50px;
        }

        .pixel-button.downloading {
            background-color: #4CAF50;
            border-color: #45a049;
            animation: none;
            cursor: not-allowed;
        }

        .pixel-button.downloaded {
            background-color: #2196F3;
            border-color: #1976D2;
            animation: none;
        }

        .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease, background-color 0.3s ease;
            z-index: 1;
        }

        .download-icon {
            display: inline-block;
            margin-right: 8px;
            font-size: 18px;
        }

        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes pulse-animation {
            0% { box-shadow: inset -4px -4px 0 0 #d27e00, inset 4px 4px 0 0 #ffcb6b, 0 0 10px #FFD700, 0 0 20px #FFD700; }
            50% { box-shadow: inset -4px -4px 0 0 #d27e00, inset 4px 4px 0 0 #ffcb6b, 0 0 15px #FFD700, 0 0 30px #FFD700; }
            100% { box-shadow: inset -4px -4px 0 0 #d27e00, inset 4px 4px 0 0 #ffcb6b, 0 0 10px #FFD700, 0 0 20px #FFD700; }
        }

        .download-success-animation {
            animation: downloadSuccess 0.6s ease-out;
        }

        @keyframes downloadSuccess {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); box-shadow: 0 0 20px #4CAF50; }
            100% { transform: scale(1); }
        }

        .pixel-button:hover {
            transform: translateY(-2px);
            box-shadow: inset -4px -4px 0 0 #d27e00,
                       inset 4px 4px 0 0 #ffcb6b,
                       0 0 15px #FFD700,
                       0 0 25px #FFD700;
        }

        .pixel-button.downloading:hover {
            transform: none;
        }

        .pixel-button.downloaded:hover {
            box-shadow: 0 0 15px #2196F3,
                       0 0 25px #2196F3;
        }

        .notification-enter {
            animation: slideInRight 0.3s ease-out;
        }

        .notification-exit {
            animation: slideOutRight 0.3s ease-in;
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen flex items-center justify-center">
    <div class="max-w-md mx-auto p-6 bg-gray-800 rounded-lg shadow-lg">
        <h1 class="text-2xl font-bold text-center mb-6">اختبار زر التحميل المحسن</h1>
        
        <div class="mb-4 text-center">
            <h2 class="text-lg font-semibold mb-2">مود ماين كرافت التجريبي</h2>
            <p class="text-gray-400 text-sm">حجم الملف: 25.5 ميجابايت</p>
            <p class="text-gray-400 text-sm">النوع: .mcaddon</p>
        </div>

        <!-- Download Button -->
        <div class="flex justify-center mb-6">
            <button id="download-button" class="pixel-button text-xl py-3 px-10" onclick="handleDownload()">
                <div class="progress-bar" id="progress-bar"></div>
                <span class="download-icon" id="download-icon">📥</span>
                <span id="download-text">تحميل المود</span>
            </button>
        </div>

        <!-- Control Panel -->
        <div class="bg-gray-700 p-4 rounded-lg">
            <h3 class="font-semibold mb-3">لوحة التحكم</h3>
            <div class="space-y-2">
                <button onclick="resetDownloadState()" class="w-full bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded">
                    إعادة تعيين الحالة
                </button>
                <button onclick="simulateError()" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded">
                    محاكاة خطأ
                </button>
                <button onclick="showStats()" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
                    عرض الإحصائيات
                </button>
            </div>
        </div>

        <!-- Stats Display -->
        <div id="stats-display" class="mt-4 bg-gray-700 p-4 rounded-lg hidden">
            <h3 class="font-semibold mb-2">الإحصائيات</h3>
            <div id="stats-content" class="text-sm text-gray-300"></div>
        </div>
    </div>

    <script>
        // متغيرات حالة التحميل
        let isDownloading = false;
        let isDownloaded = false;
        let downloadProgress = 0;
        let modFileSize = 25.5;
        let downloadedFileName = 'test_mod.mcaddon';
        
        // معلومات المود
        const modId = 'test_mod_123';
        const modTitle = 'مود ماين كرافت التجريبي';
        const modDownloadUrl = 'https://example.com/test_mod.mcaddon';

        function handleDownload() {
            if (isDownloading || isDownloaded) {
                if (isDownloaded) {
                    openDownloadedFile();
                }
                return;
            }
            startDownload();
        }

        function startDownload() {
            isDownloading = true;
            updateDownloadButton();
            simulateDownload();
        }

        function simulateDownload() {
            const progressBar = document.getElementById('progress-bar');
            const downloadText = document.getElementById('download-text');
            const downloadIcon = document.getElementById('download-icon');

            const downloadSpeed = Math.random() * 5 + 2;
            const totalTime = (modFileSize / downloadSpeed) * 1000;

            let startTime = Date.now();

            // إظهار إشعار بدء التحميل
            showDownloadStartNotification();

            const updateProgress = () => {
                const elapsed = Date.now() - startTime;
                downloadProgress = Math.min((elapsed / totalTime) * 100, 100);

                progressBar.style.width = downloadProgress + '%';

                if (downloadProgress < 100) {
                    const downloadedMB = (modFileSize * downloadProgress / 100).toFixed(1);
                    const speedMBps = downloadSpeed.toFixed(1);

                    // حساب الوقت المتبقي
                    const remainingMB = modFileSize - (modFileSize * downloadProgress / 100);
                    const remainingSeconds = Math.ceil(remainingMB / downloadSpeed);
                    const remainingTime = remainingSeconds > 60 ?
                        `${Math.ceil(remainingSeconds / 60)}د` :
                        `${remainingSeconds}ث`;

                    downloadText.textContent = `جاري التحميل... ${Math.round(downloadProgress)}%`;

                    // إضافة معلومات إضافية في tooltip
                    const button = document.getElementById('download-button');
                    button.title = `المحمل: ${downloadedMB}/${modFileSize.toFixed(1)} MB\nالسرعة: ${speedMBps} MB/s\nالوقت المتبقي: ${remainingTime}`;

                    if (downloadProgress < 30) {
                        downloadIcon.innerHTML = '<div class="spinner"></div>';
                    } else if (downloadProgress < 70) {
                        downloadIcon.textContent = '⬇️';
                    } else {
                        downloadIcon.textContent = '📦';
                    }

                    requestAnimationFrame(updateProgress);
                } else {
                    // حساب إحصائيات التحميل
                    const downloadEndTime = Date.now();
                    const totalDownloadTime = (downloadEndTime - startTime) / 1000;
                    const averageSpeed = (modFileSize / totalDownloadTime).toFixed(1);

                    const downloadStats = {
                        fileSize: modFileSize,
                        downloadTime: totalDownloadTime,
                        averageSpeed: averageSpeed,
                        fileName: downloadedFileName,
                        completedAt: new Date().toISOString()
                    };

                    completeDownload(downloadStats);
                }
            };

            updateProgress();
        }

        function showDownloadStartNotification() {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 left-4 bg-blue-500 text-white p-3 rounded-lg shadow-lg z-50 notification-enter';
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <span class="text-lg">📥</span>
                    <span>بدء التحميل...</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.classList.remove('notification-enter');
                    notification.classList.add('notification-exit');
                    setTimeout(() => {
                        if (document.body.contains(notification)) {
                            document.body.removeChild(notification);
                        }
                    }, 300);
                }
            }, 2000);
        }

        function completeDownload(stats = null) {
            isDownloading = false;
            isDownloaded = true;
            downloadProgress = 100;

            updateDownloadButton();
            saveDownloadState();
            showDownloadCompleteNotification(stats);
        }

        function updateDownloadButton() {
            const button = document.getElementById('download-button');
            const downloadText = document.getElementById('download-text');
            const downloadIcon = document.getElementById('download-icon');
            const progressBar = document.getElementById('progress-bar');
            
            if (isDownloading) {
                button.classList.add('downloading');
                button.classList.remove('downloaded');
                downloadText.textContent = `جاري التحميل... ${Math.round(downloadProgress)}%`;
                progressBar.style.width = downloadProgress + '%';
            } else if (isDownloaded) {
                button.classList.remove('downloading');
                button.classList.add('downloaded');
                downloadText.textContent = 'فتح المود';
                downloadIcon.textContent = '📂';
                progressBar.style.width = '100%';
                progressBar.style.backgroundColor = '#2196F3';
            } else {
                button.classList.remove('downloading', 'downloaded');
                downloadText.textContent = 'تحميل المود';
                downloadIcon.textContent = '📥';
                progressBar.style.width = '0%';
                progressBar.style.backgroundColor = '#4CAF50';
            }
        }

        function openDownloadedFile() {
            const minecraftUrl = `minecraft://import?url=${encodeURIComponent(modDownloadUrl)}`;
            
            try {
                window.open(minecraftUrl, '_blank');
                showMinecraftInstructions();
            } catch (error) {
                console.error('Error opening Minecraft:', error);
                showMinecraftInstructions();
            }
        }

        function showDownloadCompleteNotification(stats = null) {
            const button = document.getElementById('download-button');
            button.classList.add('download-success-animation');

            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50 notification-enter max-w-sm';

            let statsHtml = '';
            if (stats) {
                const timeText = stats.downloadTime < 60 ?
                    `${stats.downloadTime.toFixed(1)}ث` :
                    `${(stats.downloadTime / 60).toFixed(1)}د`;

                statsHtml = `
                    <div class="text-xs opacity-75 mt-2 space-y-1">
                        <div>الحجم: ${stats.fileSize.toFixed(1)} MB</div>
                        <div>الوقت: ${timeText}</div>
                        <div>السرعة: ${stats.averageSpeed} MB/s</div>
                    </div>
                `;
            }

            notification.innerHTML = `
                <div class="flex items-start space-x-2">
                    <span class="text-2xl">✅</span>
                    <div class="flex-1">
                        <h4 class="font-bold">تم التحميل بنجاح!</h4>
                        <p class="text-sm">${downloadedFileName}</p>
                        <p class="text-xs opacity-75 mt-1">انقر على الزر لفتح المود</p>
                        ${statsHtml}
                    </div>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                button.classList.remove('download-success-animation');
            }, 600);

            setTimeout(() => {
                notification.classList.remove('notification-enter');
                notification.classList.add('notification-exit');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 7000);
        }

        function showMinecraftInstructions() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center';
            modal.innerHTML = `
                <div class="bg-white text-black rounded-lg p-6 max-w-md mx-4 text-center">
                    <div class="text-6xl mb-4">🎮</div>
                    <h3 class="text-lg font-bold mb-4">تعليمات التثبيت</h3>
                    <div class="text-sm text-gray-600 mb-6 text-right">
                        إذا لم يفتح ماين كرافت تلقائياً:
                        <br><br>
                        1. افتح ماين كرافت
                        <br>
                        2. اذهب إلى الإعدادات
                        <br>
                        3. اختر "التخزين"
                        <br>
                        4. اختر "استيراد"
                        <br>
                        5. اختر الملف المحمل
                    </div>
                    <div class="flex space-x-2 justify-center">
                        <button onclick="tryOpenMinecraftAgain()" class="bg-green-500 text-white px-4 py-2 rounded">
                            فتح ماين كرافت
                        </button>
                        <button onclick="closeInstructionsModal()" class="bg-gray-500 text-white px-4 py-2 rounded">
                            إغلاق
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            window.currentInstructionsModal = modal;
        }

        function tryOpenMinecraftAgain() {
            const minecraftUrl = `minecraft://import?url=${encodeURIComponent(modDownloadUrl)}`;
            window.open(minecraftUrl, '_blank');
            closeInstructionsModal();
        }

        function closeInstructionsModal() {
            if (window.currentInstructionsModal) {
                document.body.removeChild(window.currentInstructionsModal);
                window.currentInstructionsModal = null;
            }
        }

        function saveDownloadState() {
            const state = {
                isDownloaded: isDownloaded,
                downloadedFileName: downloadedFileName,
                timestamp: Date.now()
            };
            localStorage.setItem(`mod_download_${modId}`, JSON.stringify(state));
        }

        function loadDownloadState() {
            try {
                const saved = localStorage.getItem(`mod_download_${modId}`);
                if (saved) {
                    const state = JSON.parse(saved);
                    const hoursPassed = (Date.now() - state.timestamp) / (1000 * 60 * 60);
                    if (hoursPassed < 24) {
                        isDownloaded = state.isDownloaded;
                        downloadedFileName = state.downloadedFileName;
                        if (isDownloaded) {
                            updateDownloadButton();
                        }
                    }
                }
            } catch (error) {
                console.error('Error loading download state:', error);
            }
        }

        function resetDownloadState() {
            isDownloading = false;
            isDownloaded = false;
            downloadProgress = 0;
            downloadedFileName = 'test_mod.mcaddon';
            localStorage.removeItem(`mod_download_${modId}`);
            updateDownloadButton();
            
            // إشعار إعادة التعيين
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 left-4 bg-red-500 text-white p-3 rounded-lg shadow-lg z-50';
            notification.textContent = 'تم إعادة تعيين حالة التحميل';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 2000);
        }

        function simulateError() {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 left-4 bg-red-500 text-white p-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <span>❌</span>
                    <span>خطأ في التحميل - يرجى المحاولة مرة أخرى</span>
                </div>
            `;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        function showStats() {
            const statsDisplay = document.getElementById('stats-display');
            const statsContent = document.getElementById('stats-content');
            
            const stats = {
                'حالة التحميل': isDownloaded ? 'مكتمل' : (isDownloading ? 'جاري التحميل' : 'لم يبدأ'),
                'نسبة التقدم': Math.round(downloadProgress) + '%',
                'حجم الملف': modFileSize + ' ميجابايت',
                'اسم الملف': downloadedFileName,
                'معرف المود': modId,
                'وقت الحفظ': localStorage.getItem(`mod_download_${modId}`) ? 'محفوظ' : 'غير محفوظ'
            };
            
            statsContent.innerHTML = Object.entries(stats)
                .map(([key, value]) => `<div><strong>${key}:</strong> ${value}</div>`)
                .join('');
            
            statsDisplay.classList.toggle('hidden');
        }

        // تحميل الحالة عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            loadDownloadState();
        });
    </script>
</body>
</html>
