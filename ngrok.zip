<!DOCTYPE html><html lang="en-US" dir="ltr" class="h-full" data-applied-theme="system" data-theme="system"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><script async="" defer="" src="https://www.googletagmanager.com/gtm.js?id=GTM-P4F37ZW"></script><script async="" defer="" src="https://global.ketchcdn.com/web/v2/config/ngrok/ngrok_ketch_tag/boot.js"></script><link rel="modulepreload" href="https://frontend-downloads.vercel.app/assets/entry.client-DNywe9BI.js"/><link rel="modulepreload" href="https://frontend-downloads.vercel.app/assets/jsx-runtime-D_zvdyIk.js"/><link rel="modulepreload" href="https://frontend-downloads.vercel.app/assets/chunk-NL6KNZEE-DMLPV0TN.js"/><link rel="modulepreload" href="https://frontend-downloads.vercel.app/assets/index-nz3DOmyz.js"/><link rel="modulepreload" href="https://frontend-downloads.vercel.app/assets/root-tntjzWUS.js"/><link rel="modulepreload" href="https://frontend-downloads.vercel.app/assets/root-CcwH-qPw.js"/><link rel="modulepreload" href="https://frontend-downloads.vercel.app/assets/chunk-XN5RN6JW-ChTOcMGD.js"/><link rel="modulepreload" href="https://frontend-downloads.vercel.app/assets/tiny-invariant-BCXflckp.js"/><link rel="modulepreload" href="https://frontend-downloads.vercel.app/assets/downloads-BLfir-MM.js"/><link rel="modulepreload" href="https://frontend-downloads.vercel.app/assets/types-DqvH84Ze.js"/><link rel="modulepreload" href="https://frontend-downloads.vercel.app/assets/downloads._platform-DZQhy5sB.js"/><link rel="preconnect" href="https://assets.ngrok.com"/><link rel="preload" href="https://assets.ngrok.com/fonts/euclid-square/EuclidSquare-Regular-WebS.woff" as="font" type="font/woff" crossorigin="anonymous"/><link rel="preload" href="https://assets.ngrok.com/fonts/euclid-square/EuclidSquare-RegularItalic-WebS.woff" as="font" type="font/woff" crossorigin="anonymous"/><link rel="preload" href="https://assets.ngrok.com/fonts/euclid-square/EuclidSquare-Medium-WebS.woff" as="font" type="font/woff" crossorigin="anonymous"/><link rel="preload" href="https://assets.ngrok.com/fonts/euclid-square/EuclidSquare-Semibold-WebS.woff" as="font" type="font/woff" crossorigin="anonymous"/><link rel="preload" href="https://assets.ngrok.com/fonts/euclid-square/EuclidSquare-MediumItalic-WebS.woff" as="font" type="font/woff" crossorigin="anonymous"/><link rel="preload" href="https://assets.ngrok.com/fonts/ibm-plex-mono/IBMPlexMono-Text.woff" as="font" type="font/woff" crossorigin="anonymous"/><link rel="preload" href="https://assets.ngrok.com/fonts/ibm-plex-mono/IBMPlexMono-TextItalic.woff" as="font" type="font/woff" crossorigin="anonymous"/><link rel="preload" href="https://assets.ngrok.com/fonts/ibm-plex-mono/IBMPlexMono-SemiBold.woff" as="font" type="font/woff" crossorigin="anonymous"/><link rel="preload" href="https://assets.ngrok.com/fonts/ibm-plex-mono/IBMPlexMono-SemiBoldItalic.woff" as="font" type="font/woff" crossorigin="anonymous"/><meta name="author" content="ngrok"/><title>Download ngrok</title><link href="https://frontend-downloads.vercel.app/favicon.ico" rel="shortcut icon" type="image/x-icon"/><meta name="app-version" content="bd78730f63a82aea5503c8ae9a33b9eb1a186fed"/><meta name="description" content="Download ngrok"/><meta name="og:title" property="og:title" content="Download ngrok"/><meta name="og:description" property="og:description" content="Download ngrok"/><meta name="twitter:title" property="twitter:title" content="Download ngrok"/><meta name="og:image" property="og:image" content="/og-image.png"/><meta name="twitter:image" property="twitter:image" content="/og-image.png"/><meta name="robots" content="index, follow"/><link rel="canonical" href="https://ngrok.com/downloads/windows"/><meta name="og:url" property="og:url" content="https://ngrok.com/downloads/windows"/><meta name="google-site-verification" content="nUUUGlWJUPeQDItn5CtyeeZA0fXnjbvwf_eO-Am6Jv8"/><link rel="preconnect" href="https://www.googletagmanager.com"/><link rel="preconnect" href="https://www.google-analytics.com"/><link rel="preconnect" href="https://global.ketchcdn.com"/><script>(function() {
	const themes = ["system","light","dark","light-high-contrast","dark-high-contrast"];
	const isTheme = (value) => typeof value === "string" && themes.includes(value);
	const fallbackTheme = "system" ?? "system";
	let maybeStoredTheme = null;
	try {
		maybeStoredTheme = "localStorage" in window ? window.localStorage.getItem("mantle-ui-theme") : null;
	} catch (_) {}
	const hasStoredTheme = isTheme(maybeStoredTheme);
	if (!hasStoredTheme && "localStorage" in window) {
		try {
			window.localStorage.setItem("mantle-ui-theme", fallbackTheme);
		} catch (_) {}
	}
	const themePreference = hasStoredTheme ? maybeStoredTheme : fallbackTheme;
	const prefersDarkMode = window.matchMedia("(prefers-color-scheme: dark)").matches;
	const prefersHighContrast = window.matchMedia("(prefers-contrast: more)").matches;
	let initialTheme = themePreference;
	if (initialTheme === "system") {
		if (prefersHighContrast) {
			initialTheme = prefersDarkMode ? "dark-high-contrast" : "light-high-contrast";
		} else {
			initialTheme = prefersDarkMode ? "dark" : "light";
		}
	}
	const htmlElement = document.documentElement;
	htmlElement.classList.remove(...themes);
	htmlElement.classList.add(initialTheme);
	htmlElement.dataset.appliedTheme = initialTheme;
	htmlElement.dataset.theme = themePreference;
})();</script><link rel="stylesheet" href="https://frontend-downloads.vercel.app/assets/root-D5oZZc3d.css"/><script>window.semaphore = window.semaphore || [];
window.ketch = function () {
	window.semaphore.push(arguments);
};</script></head><body class="bg-base text-body h-full min-h-full overflow-y-scroll text-sm"><div class="h-full pb-12 md:pb-20"><a class="sr-only" href="/downloads#main" data-discover="true">Skip to main content</a><header class="relative bg-[#22125B]" style="background:radial-gradient(72.52% 42.88% at 73.32% 82.63%, rgba(170, 220, 189, 0.24) 0%, rgba(255, 255, 255, 0) 100%),
radial-gradient(43.33% 47.76% at 50.77% 72.86%, rgba(234, 196, 33, 0.05) 0%, rgba(255, 255, 255, 0) 100%),
radial-gradient(65.71% 43.42% at 22.18% 90.23%, rgba(255, 154, 0, 0.15) 35%, rgba(255, 255, 255, 0) 100%),
#22125B"><div class="max-w-5xl px-4 py-8 text-[#ffffff] md:mx-auto md:pb-40 md:pt-16"><div class="flex items-center justify-between"><a href="https://ngrok.com/" class="inline-flex"><span class="sr-only">ngrok logo</span><svg viewBox="0 0 94 36" fill="currentColor" class="h-9 shrink-0"><path d="M32.272 12.011c-1.298-1.466-2.904-2.205-4.812-2.205-1.176 0-2.26.233-3.255.7a7.995 7.995 0 0 0-2.581 1.906 9.205 9.205 0 0 0-1.715 2.853 9.773 9.773 0 0 0-.628 3.546c0 1.25.194 2.39.58 3.419.362.98.918 1.877 1.635 2.636A7.543 7.543 0 0 0 24 26.584c.965.41 2.025.617 3.176.617.522 0 1.005-.041 1.445-.116.439-.075.858-.2 1.26-.37.4-.175.79-.398 1.18-.664.385-.27.792-.612 1.21-1.018v4.353h-.005v.421h-5.33l-4.005 4.64v.798h15.037v-24.98h-5.697v1.746Zm-.014 7.979a4.25 4.25 0 0 1-.786 1.215 3.555 3.555 0 0 1-2.592 1.1 3.627 3.627 0 0 1-1.464-.292 3.508 3.508 0 0 1-1.166-.808 3.93 3.93 0 0 1-1.054-2.72c0-.519.097-1.006.298-1.457a3.77 3.77 0 0 1 .804-1.181 4.114 4.114 0 0 1 1.162-.808 3.484 3.484 0 0 1 2.817-.016c.448.19.844.463 1.181.81.336.347.6.743.804 1.194.202.452.298.95.298 1.493 0 .505-.104 1.005-.302 1.47Zm-16.261-7.708a6.173 6.173 0 0 0-2.06-1.602 4.875 4.875 0 0 0-.57-.22 6.383 6.383 0 0 0-.923-.216H8.383L5.697 13.39v-3.082H.002v16.61h5.695V15.712h5.35l.444-.01v11.214h5.697V16.528c0-.885-.084-1.674-.25-2.366a4.655 4.655 0 0 0-.941-1.877v-.003Zm38.367-2.018h-6.213l-2.47 2.863v-2.864h-5.7v16.61h5.71l.004-11.117h4.144l4.526-5.26-.001-.232Zm31.051 7.672 7.79-7.392v-.281H85.7l-5.975 5.991V0h-5.696v26.87h5.696v-6.766l6.262 6.763h7.663v-.316l-8.233-8.617-.002.002Zm-16.11-5.78a9.436 9.436 0 0 0-3.085-1.842 10.953 10.953 0 0 0-3.855-.664c-1.407 0-2.705.226-3.884.678a9.611 9.611 0 0 0-3.072 1.858 8.488 8.488 0 0 0-2.016 2.788 8.281 8.281 0 0 0-.722 3.449c0 1.362.24 2.596.722 3.707a8.52 8.52 0 0 0 2.002 2.862c.85.798 1.86 1.415 3.036 1.847 1.177.432 2.455.647 3.842.647 1.406 0 2.707-.215 3.919-.647 1.204-.431 2.24-1.04 3.098-1.833a8.583 8.583 0 0 0 2.031-2.816c.493-1.09.742-2.29.742-3.611 0-1.316-.244-2.52-.722-3.612a8.424 8.424 0 0 0-2.035-2.81Zm-3.558 7.864c-.2.461-.463.869-.786 1.215a3.573 3.573 0 0 1-2.592 1.1c-.502 0-.981-.096-1.434-.291a3.44 3.44 0 0 1-1.16-.809 4.155 4.155 0 0 1-.788-1.215 3.825 3.825 0 0 1-.297-1.537c0-.517.098-1.004.297-1.456.201-.451.46-.849.787-1.194a3.579 3.579 0 0 1 2.597-1.1c.502 0 .98.096 1.43.29.448.19.839.461 1.16.81.328.345.586.752.786 1.214.2.461.297.954.297 1.471 0 .538-.096 1.04-.297 1.502Z"></path></svg></a><button type="button" id="radix-«R9j5»" aria-haspopup="menu" aria-expanded="false" data-state="closed" class="text-on-filled ring-focus-accent hover:bg-filled-accent-hover active:bg-filled-accent-active flex shrink-0 cursor-pointer items-center justify-center rounded-full p-1.5 focus:outline-none focus-visible:ring-4 dark-high-contrast:text-black dark-high-contrast:hover:text-white size-9"><span class="sr-only">Change Theme</span><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 256 256" class="size-5"><path d="M116,36V20a12,12,0,0,1,24,0V36a12,12,0,0,1-24,0Zm80,92a68,68,0,1,1-68-68A68.07,68.07,0,0,1,196,128Zm-24,0a44,44,0,1,0-44,44A44.05,44.05,0,0,0,172,128ZM51.51,68.49a12,12,0,1,0,17-17l-12-12a12,12,0,0,0-17,17Zm0,119-12,12a12,12,0,0,0,17,17l12-12a12,12,0,1,0-17-17ZM196,72a12,12,0,0,0,8.49-3.51l12-12a12,12,0,0,0-17-17l-12,12A12,12,0,0,0,196,72Zm8.49,115.51a12,12,0,0,0-17,17l12,12a12,12,0,0,0,17-17ZM48,128a12,12,0,0,0-12-12H20a12,12,0,0,0,0,24H36A12,12,0,0,0,48,128Zm80,80a12,12,0,0,0-12,12v16a12,12,0,0,0,24,0V220A12,12,0,0,0,128,208Zm108-92H220a12,12,0,0,0,0,24h16a12,12,0,0,0,0-24Z"></path></svg></button></div><h1 class="mt-10 text-3xl font-light leading-normal md:mt-20 md:text-[3.8125rem]">Download ngrok</h1><p class="max-w-[28.25rem] font-mono text-sm font-normal leading-tight text-[hsl(255_100%_100%_/_.85)] md:text-[1.0625rem]">ngrok is your app’s front door—and the fastest way to put anything on the internet.</p></div></header><div class="mx-auto flex max-w-5xl gap-14 md:px-4"><nav class="text-muted mt-11 hidden min-w-44 shrink-0 text-sm md:block"><ul><li class="text-strong mb-3 ml-2 text-xs font-medium uppercase tracking-wider">Agents</li><li><ul><li><a class="ring-focus-accent flex items-center gap-1.5 rounded-md px-2 py-1 font-normal focus-visible:ring-4 focus:outline-none hover:bg-popover-hover text-muted hover:text-strong [&amp;&gt;svg]:text-body" href="/downloads/mac-os" data-discover="true"><svg viewBox="0 0 32 32" fill="currentColor" class="shrink-0 size-5"><path d="M16.21 8.05c-1.1 0-2.8-1.26-4.6-1.22a6.78 6.78 0 0 0-5.74 3.52c-2.46 4.29-.64 10.62 1.76 14.1 1.17 1.7 2.56 3.6 4.4 3.55 1.76-.08 2.42-1.15 4.56-1.15 2.12 0 2.72 1.15 4.59 1.1 1.9-.03 3.1-1.72 4.26-3.44a15.27 15.27 0 0 0 1.93-3.98 6.17 6.17 0 0 1-.73-10.99 6.5 6.5 0 0 0-5.09-2.77c-2.31-.18-4.26 1.28-5.34 1.28Zm3.92-3.58A6.15 6.15 0 0 0 21.57 0c-1.4.06-3.09.94-4.1 2.12A5.88 5.88 0 0 0 16 6.45c1.55.13 3.15-.8 4.12-1.98"></path></svg>macOS</a></li><li><a aria-current="page" class="ring-focus-accent flex items-center gap-1.5 rounded-md px-2 py-1 focus-visible:ring-4 focus:outline-none bg-filled-accent text-on-filled [&amp;&gt;svg]:text-on-filled font-medium" href="/downloads/windows" data-discover="true"><svg viewBox="0 0 32 32" class="shrink-0 size-5"><path fill="currentColor" d="m4 7.32 9.8-1.34.01 9.46-9.8.06L4 7.32Z"></path><path fill="currentColor" d="m15 5.8 13-1.9v11.42l-13 .1V5.81Z"></path><path fill="currentColor" d="m13.8 16.53.01 9.47-9.8-1.35v-8.18l9.8.06Z"></path><path fill="currentColor" d="M28 16.62v11.36l-13-1.83-.03-9.55 13.03.02Z"></path></svg>Windows</a></li><li><a class="ring-focus-accent flex items-center gap-1.5 rounded-md px-2 py-1 font-normal focus-visible:ring-4 focus:outline-none hover:bg-popover-hover text-muted hover:text-strong [&amp;&gt;svg]:text-body" href="/downloads/linux" data-discover="true"><svg viewBox="0 0 32 32" fill="currentColor" class="shrink-0 size-5"><path d="M15.34 9.12c.05.03.1.09.16.09.05 0 .14-.02.15-.08 0-.07-.1-.12-.17-.15a.33.33 0 0 0-.29 0c-.02 0-.04.03-.03.05.02.07.12.06.18.1Zm-1.14.09c.06 0 .1-.06.15-.09.06-.03.17-.02.19-.08 0-.02-.01-.05-.03-.06-.09-.05-.2-.03-.3 0-.06.04-.17.08-.16.16 0 .05.1.08.15.07Zm11.5 14.52c-.18-.2-.27-.6-.37-1.03-.1-.42-.2-.87-.55-1.16a1.4 1.4 0 0 0-.42-.26c.48-1.42.3-2.84-.2-4.12a13.9 13.9 0 0 0-2.41-3.87c-.9-1.12-1.76-2.18-1.74-3.75.02-2.4.26-6.83-3.95-6.84-5.33-.01-4 5.38-4.06 7.04a6.04 6.04 0 0 1-1.17 3.37 16.63 16.63 0 0 0-3.03 5.04 5.99 5.99 0 0 0-.32 2.77c-.34.3-.6.77-.86 1.05-.22.23-.54.31-.89.44-.35.12-.73.3-.96.75-.11.2-.15.42-.15.65 0 .2.03.4.06.61.07.42.13.82.05 1.09-.28.75-.31 1.27-.12 1.65.2.38.6.54 1.05.64.9.18 2.12.14 3.09.65 1.03.54 2.07.73 2.9.54.61-.14 1.1-.5 1.36-1.05.65 0 1.37-.28 2.51-.35.78-.06 1.75.28 2.87.22a2.09 2.09 0 0 0 2.23 1.55 3.8 3.8 0 0 0 2.52-1.45c.7-.86 1.87-1.21 2.65-1.68.38-.24.7-.53.72-.95.02-.43-.23-.9-.8-1.55ZM15.5 7.25c.51-1.16 1.78-1.14 2.3-.02.33.74.18 1.6-.23 2.1-.09-.04-.31-.14-.66-.26.06-.06.16-.14.2-.24.25-.61 0-1.4-.47-1.42-.38-.02-.72.57-.61 1.2a3.7 3.7 0 0 0-.68-.23c-.05-.36-.02-.76.15-1.13Zm-2.12-.6c.53 0 1.08.74 1 1.74-.19.05-.37.13-.54.24.07-.46-.17-1.04-.5-1.02-.43.04-.5 1.1-.09 1.46.05.05.1 0-.3.3-.82-.77-.55-2.72.43-2.72Zm-.7 3.16.73-.55c.24-.23.7-.74 1.45-.74.37 0 .81.12 1.35.47.33.2.59.23 1.18.48.43.18.7.5.54.95-.13.37-.57.75-1.18.94-.58.19-1.03.83-1.99.78-.2-.01-.36-.06-.5-.11-.42-.18-.63-.54-1.04-.78-.45-.25-.69-.55-.77-.8-.07-.26 0-.47.22-.64Zm.16 17.4c-.14 1.82-2.28 1.78-3.92.93-1.55-.82-3.57-.34-3.98-1.14-.13-.24-.13-.66.13-1.37v-.02c.13-.4.04-.83-.03-1.24-.06-.4-.1-.78.05-1.04.18-.35.44-.48.77-.59.54-.2.61-.18 1.02-.52.29-.3.5-.67.75-.93.26-.29.52-.42.92-.36.42.06.78.35 1.14.83l1.02 1.85c.5 1.04 2.24 2.53 2.13 3.6Zm-.07-1.36c-.21-.34-.5-.7-.75-1.02.37 0 .74-.11.87-.46.12-.32 0-.78-.38-1.3-.7-.94-2-1.69-2-1.69a2.8 2.8 0 0 1-1.28-1.56A3.53 3.53 0 0 1 9.2 18c.27-1.2.97-2.35 1.42-3.08.12-.1.04.16-.45 1.08-.45.84-1.27 2.78-.14 4.3a9 9 0 0 1 .72-3.21c.63-1.43 1.94-3.9 2.05-5.87.05.04.24.17.32.21.24.14.42.35.66.54.64.52 1.48.48 2.2.06.33-.18.59-.39.83-.47.52-.16.93-.44 1.16-.78a26.9 26.9 0 0 0 1.94 4.99c.32.59.95 1.85 1.23 3.36.17 0 .37.02.57.07.72-1.86-.61-3.86-1.22-4.42-.24-.24-.25-.34-.13-.34a6.43 6.43 0 0 1 1.83 3.08c.15.6.17 1.23.02 1.86.86.35 1.87.93 1.6 1.8h-.22c.17-.52-.2-.9-1.18-1.35-1.02-.45-1.88-.45-2 .65-.63.22-.95.76-1.11 1.42a11.5 11.5 0 0 0-.23 2.08c-.03.4-.19.94-.36 1.5-1.67 1.2-4 1.72-5.95.38Zm13.4-.6c-.04.88-2.14 1.04-3.29 2.43A3.48 3.48 0 0 1 20.6 29a1.7 1.7 0 0 1-1.75-1c-.25-.58-.13-1.2.06-1.89.19-.74.48-1.5.51-2.11.04-.8.09-1.49.22-2.02.14-.54.34-.9.71-1.1l.06-.03c.04.7.38 1.39.98 1.54.65.17 1.6-.39 2-.85.46-.01.81-.04 1.17.27.52.44.37 1.58.9 2.16.54.6.72 1.02.7 1.29Zm-13.3-14.8c.1.1.25.23.42.36.35.27.82.56 1.42.56s1.18-.31 1.66-.57c.25-.13.57-.36.77-.54.2-.18.3-.33.16-.34-.14-.02-.13.13-.31.26-.23.17-.5.39-.72.51-.39.22-1.02.54-1.56.54s-.98-.25-1.3-.51c-.16-.13-.3-.26-.4-.36-.08-.07-.1-.24-.22-.25-.08-.01-.1.19.09.33Z"></path></svg>Linux</a></li><li><a class="ring-focus-accent flex items-center gap-1.5 rounded-md px-2 py-1 font-normal focus-visible:ring-4 focus:outline-none hover:bg-popover-hover text-muted hover:text-strong [&amp;&gt;svg]:text-body" href="/downloads/free-bsd" data-discover="true"><svg viewBox="0 0 32 32" class="shrink-0 size-5"><path fill="currentColor" d="M20.27 7.3c.6-.6 6.19-4.18 7.46-2.89 1.27 1.29-2.26 6.97-2.85 7.57-.6.6-2.11.05-3.38-1.24-1.28-1.3-1.83-2.83-1.23-3.44ZM9.89 5.78C7.94 4.66 5.18 3.4 4.29 4.3c-.88.9.39 3.77 1.5 5.75 1-1.75 2.4-3.23 4.1-4.28Zm15.9 5.76c.17.61.14 1.12-.15 1.42-1.09 1.1-4.69-1.47-5.85-3.82-.97-1.75-.6-2.9.8-2.65.3-.2.65-.4 1.05-.63a11.44 11.44 0 0 0-5.34-1.32A11.64 11.64 0 0 0 4.75 16.27C4.75 22.74 9.92 28 16.3 28c6.39 0 11.55-5.25 11.55-11.73 0-2.09-.54-4.05-1.48-5.76-.2.39-.4.73-.58 1.03Z"></path></svg>FreeBSD</a></li><li><a class="ring-focus-accent flex items-center gap-1.5 rounded-md px-2 py-1 font-normal focus-visible:ring-4 focus:outline-none hover:bg-popover-hover text-muted hover:text-strong [&amp;&gt;svg]:text-body" href="/downloads/docker" data-discover="true"><svg viewBox="0 0 32 32" class="shrink-0 size-5"><path fill="currentColor" d="M16.4 4.98h3.04v3.04h-3.03V4.98Zm0 3.82h3.04v3.03h-3.03V8.8Zm3.04 3.81h-3.03v3.04h3.03v-3.04ZM12.72 8.8h3.03v3.03h-3.03V8.8Zm3.03 3.81h-3.03v3.04h3.03v-3.04ZM9.03 8.8h3.03v3.03H9.03V8.8Zm3.03 3.81H9.03v3.04h3.03v-3.04Zm-6.68 0h3.04v3.04H5.38v-3.04Zm17.75 0H20.1v3.04h3.04v-3.04Z"></path><path fill="currentColor" d="M3.71 16.63s-1.3 9.85 9.89 9.85c11.55 0 13.93-9.5 13.93-9.5s3.64.17 4.47-2.52c-1.48-1.35-3.86-.57-3.86-.57s.09-2-2.26-3.34c-2.08 2.78-.3 5.08-.3 5.08s-.04 1-3.2 1H3.72Z"></path></svg>Docker</a></li><li><a class="ring-focus-accent flex items-center gap-1.5 rounded-md px-2 py-1 font-normal focus-visible:ring-4 focus:outline-none hover:bg-popover-hover text-muted hover:text-strong [&amp;&gt;svg]:text-body" href="/downloads/raspberry-pi" data-discover="true"><svg viewBox="0 0 32 32" fill="currentColor" class="shrink-0 size-5"><path fill-rule="evenodd" d="M9.25 2.34c.36-.33.77-.27 1.2-.1a.78.78 0 0 1 .46-.2c.18 0 .34.06.5.14l.25.1c.45-.16.64-.04.83.07.07.04.13.08.2.1.6-.12.83.11 1.1.4l.09.1.37-.02a4.2 4.2 0 0 1 1.67 2.4 4.2 4.2 0 0 1 1.66-2.4l.37.01.1-.1c.27-.28.49-.51 1.08-.38l.21-.1c.2-.12.38-.24.84-.09.08-.02.16-.06.24-.1.29-.12.57-.25.97.06.42-.16.83-.22 1.2.11.56-.07.74.08.88.26h.06c.22-.02.9-.08 1.24.42.92-.11 1.22.54.89 1.15v.01c.19.3.38.58-.06 1.14.15.3.06.64-.31 1.05.1.44-.1.75-.44.99.06.55-.46.89-.68 1.04-.02 0-.04.02-.05.03-.07.35-.22.68-.92.86-.1.5-.5.6-.89.7l-.05.02c1.35.78 2.5 1.8 2.5 4.33l.19.36a4.28 4.28 0 0 1 .76 6.4c-.11.6-.28 1.08-.45 1.55l-.14.38a4.7 4.7 0 0 1-2.93 3.75c-.8.61-1.65 1.19-2.8 1.6a4.65 4.65 0 0 1-3.45 1.54h-.05a4.65 4.65 0 0 1-3.45-1.55c-1.15-.4-2-.98-2.8-1.59a4.7 4.7 0 0 1-2.93-3.75l-.14-.38c-.17-.47-.34-.94-.45-1.54a4.28 4.28 0 0 1 .76-6.41l.2-.36a4.42 4.42 0 0 1 2.5-4.33l-.06-.02c-.4-.1-.78-.2-.9-.7-.7-.18-.84-.51-.9-.86l-.06-.03c-.22-.15-.74-.49-.68-1.04-.34-.24-.53-.55-.44-.99-.37-.4-.46-.74-.3-1.05-.44-.56-.26-.85-.07-1.14-.32-.62-.03-1.27.9-1.16.33-.5 1.01-.44 1.24-.43h.05c.14-.17.32-.32.89-.25Zm13.72 2.28C20.32 6 18.78 7.1 17.94 8.03c.43 1.73 2.68 1.81 3.5 1.76-.16-.07-.3-.17-.35-.31.08-.06.26-.08.48-.1.3-.01.67-.04.97-.2-.2-.05-.29-.09-.38-.23l.21-.07a3.5 3.5 0 0 0 1.1-.47h-.08a.65.65 0 0 1-.46-.12l.3-.15c.33-.17.68-.34.96-.62h-.07c-.2 0-.4-.01-.47-.09.4-.24.73-.51 1-.81-.3.04-.44 0-.51-.05l.27-.26c.23-.2.46-.42.59-.69-.24.09-.45.12-.6 0 .04-.1.13-.17.25-.26.17-.14.39-.32.54-.64h-.05c-.23.02-.46.05-.51 0 .1-.45.3-.7.47-.96l.03-.04h-.42c-.44 0-.89 0-.86-.04l.32-.33a2.56 2.56 0 0 0-1.41.14c-.17-.13 0-.3.2-.48-.43.06-.82.16-1.18.3-.14-.13 0-.26.15-.4.05-.03.1-.07.13-.12-.67.13-.96.31-1.24.49-.18-.17-.05-.32.07-.47l.06-.07c-.48.18-.74.4-1 .64l-.05.03-.04-.05c-.08-.1-.17-.2-.02-.47a2.6 2.6 0 0 0-.83.73c-.17-.11-.16-.25-.14-.4V3.1c-.26.22-.46.45-.66.68l-.24.27c-.05-.05-.1-.19-.15-.42-.9.87-2.15 3.05-.32 3.91a17.17 17.17 0 0 1 5.47-2.92Zm-9.02 3.41c-.84-.94-2.38-2.04-5.03-3.4 2.06.7 3.92 1.63 5.48 2.91 1.82-.86.56-3.04-.33-3.9-.04.22-.1.36-.15.4l-.24-.26c-.2-.23-.4-.46-.66-.68v.13c.02.14.03.28-.14.39a2.6 2.6 0 0 0-.83-.73c.15.26.06.37-.02.47l-.04.05-.05-.03a2.93 2.93 0 0 0-1-.64l.06.07c.12.15.25.3.07.47a3.03 3.03 0 0 0-1.24-.49l.13.13c.15.13.3.26.15.39A5.14 5.14 0 0 0 8.92 3c.21.18.39.35.22.48-.39-.12-.9-.28-1.42-.14l.32.33c.03.03-.41.04-.86.04h-.42l.03.04c.18.26.36.51.47.96-.05.05-.28.02-.5 0H6.7c.15.32.36.5.54.64.11.09.2.17.24.26-.15.12-.36.09-.6 0 .13.27.36.48.6.7l.26.25c-.07.05-.2.09-.52.05.28.3.61.57 1.01.81-.06.08-.27.08-.47.09H7.7c.28.28.62.45.96.62l.29.15a.65.65 0 0 1-.46.12h-.08c.26.21.68.34 1.1.47l.21.07c-.09.14-.18.18-.38.22.3.17.67.2.97.22.22.01.4.03.48.09-.05.14-.19.24-.36.31.83.05 3.08-.03 3.51-1.76Zm2.03 17.14a3.3 3.3 0 0 0 2.24-.86c.6-.55.93-1.3.93-2.07a2.8 2.8 0 0 0-.93-2.06c-.6-.55-1.4-.86-2.24-.86-.84 0-1.65.3-2.24.86a2.8 2.8 0 0 0-.93 2.06c0 .78.33 1.52.93 2.07.6.55 1.4.86 2.24.86Zm-.7-9.36c-.1-.81-.5-1.5-1.13-1.92a2.66 2.66 0 0 0-2.23-.28 3.7 3.7 0 0 0-2 1.52c-.5.75-.71 1.62-.61 2.44.1.82.5 1.5 1.14 1.92.63.41 1.43.52 2.22.28a3.7 3.7 0 0 0 2-1.52c.5-.74.72-1.62.62-2.44Zm2.41-2.07a2.63 2.63 0 0 0-1.14 1.92c-.1.82.12 1.7.61 2.43a3.7 3.7 0 0 0 2 1.53c.8.23 1.6.13 2.23-.29a2.63 2.63 0 0 0 1.14-1.92c.1-.81-.12-1.69-.61-2.43a3.7 3.7 0 0 0-2-1.53c-.8-.23-1.6-.13-2.23.29ZM7.6 15.3c1.4-.38.47 5.87-.68 5.35a3.38 3.38 0 0 1 .67-5.35Zm15.96-1.04c-.08 1.02-5.3-3.54-4.4-3.7 2.45-.4 4.49 1.05 4.4 3.7Zm-4.75-2.5c0-.92-1.6-1.9-2.9-1.89-1.46-.04-2.86 1.09-2.86 1.74 0 .79 1.15 1.6 2.88 1.62 1.75.01 2.87-.65 2.88-1.47Zm-2.89 17.29c1.52.03 3.1-1.33 3.07-1.93 0-.62-1.71-1.08-2.99-1.03-1.26-.03-3.2.5-3.18 1.2-.02.47 1.53 1.83 3.1 1.76ZM11.85 26c.76-.57.35-2.49-.56-3.58-1.04-1.2-2.4-1.93-3.27-1.4-.58.45-.69 1.97.14 3.46 1.24 1.77 2.98 1.95 3.7 1.52Zm8.67-3.93c-.98 1.15-1.53 3.25-.81 ********** 2.52.45 3.87-1.43.99-1.26.66-3.38.1-3.94-.84-.64-2.04.18-3.16 1.45ZM12.39 10.5c-2.45-.41-4.48 1.04-4.4 3.7.08 1.01 5.3-3.55 4.4-3.7Zm11.58 4.73c-1.42-.38-.48 5.87.68 5.36a3.39 3.39 0 0 0-.68-5.36Z" clip-rule="evenodd"></path></svg> Raspberry Pi</a></li></ul></li><li class="text-strong mb-3 ml-2 mt-8 text-xs font-medium uppercase tracking-wider">SDKS</li><li><ul><li><a class="ring-focus-accent flex items-center gap-1.5 rounded-md px-2 py-1 font-normal focus-visible:ring-4 focus:outline-none hover:bg-popover-hover text-muted hover:text-strong [&amp;&gt;svg]:text-body" href="/downloads/go" data-discover="true"><span class="sr-only">Golang</span><svg viewBox="0 0 32 32" class="shrink-0 size-5"><path fill="currentColor" d="M18.4 12.28a150.26 150.26 0 0 0-2.98.76l-.03.01c-.23.06-.26.07-.48-.18-.26-.3-.46-.49-.83-.66a3.12 3.12 0 0 0-3.2.26 3.68 3.68 0 0 0-1.8 3.3 2.74 2.74 0 0 0 2.38 2.73 3.3 3.3 0 0 0 3-1.15c.13-.14.24-.29.36-.46l.13-.18h-3.42c-.37 0-.45-.23-.33-.52.23-.54.65-1.44.9-1.9.05-.1.18-.27.44-.27h5.69c.26-.8.67-1.55 1.22-2.26a7.43 7.43 0 0 1 4.95-2.9c1.8-.32 3.5-.14 5.04.88a5.4 5.4 0 0 1 2.5 3.88 6.64 6.64 0 0 1-2.04 5.89 8.14 8.14 0 0 1-5.66 2.38 6.7 6.7 0 0 1-4.31-1.53 5.45 5.45 0 0 1-1.77-2.7c-.2.4-.45.8-.74 1.17a7.39 7.39 0 0 1-5.04 2.95 6.34 6.34 0 0 1-4.75-1.14 5.44 5.44 0 0 1-2.25-3.86c-.23-1.9.34-3.6 1.5-5.1a7.86 7.86 0 0 1 4.96-3c1.66-.3 3.25-.1 4.68.85.94.61 1.6 1.45 2.05 2.45.1.16.04.25-.18.3Zm10.05 2.59-.01-.19-.04-.38a2.91 2.91 0 0 0-3.6-2.32 3.83 3.83 0 0 0-3.1 3.03 2.85 2.85 0 0 0 1.62 3.28c.98.42 1.95.37 2.88-.1a3.8 3.8 0 0 0 2.25-3.32ZM1.1 17.1l.48-.9c.06-.11.2-.22.33-.22h2.35c.14 0 .2.1.2.25l.06.83c0 .12-.14.25-.24.25H1.21c-.14 0-.17-.1-.1-.21Zm1.1-5.54.7-.97c.07-.1.24-.18.38-.18h3.25c.14 0 .17.1.1.22l-.73.92c-.08.1-.23.21-.33.21H2.3c-.13 0-.17-.09-.1-.2ZM.25 14.2c-.09.12-.07.22.06.22h4.07c.13 0 .26-.07.3-.22l.27-.86c.03-.11-.04-.22-.17-.22H1.1c-.14 0-.3.07-.37.18l-.49.9Z"></path></svg>Go</a></li><li><a class="ring-focus-accent flex items-center gap-1.5 rounded-md px-2 py-1 font-normal focus-visible:ring-4 focus:outline-none hover:bg-popover-hover text-muted hover:text-strong [&amp;&gt;svg]:text-body" href="/downloads/node-js" data-discover="true"><svg viewBox="0 0 32 32" class="shrink-0 size-5"><path fill="currentColor" d="M16 29.32c-.37 0-.72-.1-1.04-.27l-3.3-1.94c-.5-.27-.26-.37-.1-.41.66-.23.79-.27 1.48-.67.08-.05.18-.02.25.03l2.54 1.5c.1.04.22.04.3 0l9.9-5.67c.1-.05.16-.14.16-.27V10.33c0-.12-.05-.22-.15-.27l-9.92-5.63c-.1-.05-.22-.05-.3 0l-9.9 5.63a.32.32 0 0 0-.16.27v11.3c0 .1.05.21.15.26l2.71 1.55c1.47.73 2.39-.13 2.39-.98V11.3c0-.15.12-.3.3-.3h1.26c.15 0 .3.13.3.3v11.15c0 1.93-1.07 3.06-2.93 3.06-.57 0-1.02 0-2.29-.61l-2.6-1.47A2.06 2.06 0 0 1 4 21.64V10.36c0-.73.4-1.42 1.04-1.78l9.92-5.66a2.25 2.25 0 0 1 2.08 0l9.92 5.66A2.06 2.06 0 0 1 28 10.35v11.3c0 .73-.4 1.42-1.04 1.79l-9.92 5.66c-.32.14-.7.22-1.04.22Zm3.06-7.77c-4.35 0-5.25-1.96-5.25-3.63a.3.3 0 0 1 .3-.29h1.3c.14 0 .27.1.27.25.2 1.3.77 1.93 3.4 1.93 2.09 0 2.98-.46 2.98-1.57 0-.63-.25-1.1-3.5-1.42-2.7-.27-4.4-.86-4.4-2.99 0-1.98 1.7-3.16 4.52-3.16 3.18 0 4.75 1.08 4.95 3.43a.4.4 0 0 1-.08.22c-.05.05-.12.1-.2.1h-1.29a.29.29 0 0 1-.27-.22c-.3-1.35-1.07-1.79-3.1-1.79-2.3 0-2.57.79-2.57 1.37 0 .71.33.94 3.4 1.33 3.06.39 4.5.95 4.5 3.06-.02 2.16-1.8 3.38-4.96 3.38Z"></path></svg>Node.js</a></li><li><a class="ring-focus-accent flex items-center gap-1.5 rounded-md px-2 py-1 font-normal focus-visible:ring-4 focus:outline-none hover:bg-popover-hover text-muted hover:text-strong [&amp;&gt;svg]:text-body" href="/downloads/rust" data-discover="true"><svg fill="currentColor" viewBox="0 0 32 32" class="shrink-0 size-5"><path d="m29.8 15.65-1.17-.72c0-.12-.02-.23-.03-.35l1-.94a.4.4 0 0 0-.13-.67l-1.29-.49a9.7 9.7 0 0 0-.1-.33l.8-1.12a.4.4 0 0 0-.26-.63l-1.36-.22-.16-.31.57-1.26a.4.4 0 0 0-.18-.52.4.4 0 0 0-.2-.05l-1.38.05a7.93 7.93 0 0 0-.22-.26L26 6.48a.4.4 0 0 0-.5-.49l-1.34.32-.26-.22.05-1.38a.4.4 0 0 0-.58-.38l-1.25.57-.3-.16-.23-1.37a.4.4 0 0 0-.63-.26l-1.12.8a9.9 9.9 0 0 0-.34-.1l-.48-1.29a.4.4 0 0 0-.67-.13L17.4 3.4l-.34-.03-.73-1.18a.4.4 0 0 0-.68 0l-.73 1.18a16 16 0 0 0-.34.03l-.95-1.01a.4.4 0 0 0-.67.13l-.48 1.3-.34.1-1.12-.81a.4.4 0 0 0-.63.26l-.22 1.37-.3.16-1.26-.57a.4.4 0 0 0-.58.38l.05 1.38-.26.22L6.48 6a.4.4 0 0 0-.49.49l.32 1.35-.22.26-1.38-.05a.4.4 0 0 0-.38.57l.57 1.26-.16.3-1.36.23a.4.4 0 0 0-.27.63l.8 1.12-.1.33-1.28.49a.4.4 0 0 0-.14.67l1.01.94-.03.35-1.18.72a.4.4 0 0 0 0 .7l1.18.72.03.34-1 .94a.4.4 0 0 0 .13.68l1.29.48.1.34-.8 1.11a.4.4 0 0 0 .26.64l1.36.22.16.3-.57 1.26a.4.4 0 0 0 .38.57l1.38-.04.22.26L6 25.52a.4.4 0 0 0 .48.48l1.35-.31.26.22-.04 1.38a.4.4 0 0 0 .57.38l1.25-.57.3.16.23 1.36a.4.4 0 0 0 .64.27l1.11-.8.34.1.48 1.28a.4.4 0 0 0 .67.14l.95-1.01.34.03.73 1.18a.4.4 0 0 0 .68 0l.73-1.18c.12 0 .23-.02.34-.03l.95 1a.4.4 0 0 0 .67-.13l.48-1.29.34-.1 1.12.8a.4.4 0 0 0 .63-.26l.22-1.36.3-.16 1.26.57a.4.4 0 0 0 .58-.38l-.05-1.38.26-.22 1.35.31a.4.4 0 0 0 .48-.48l-.31-1.35.21-.26 1.39.04a.4.4 0 0 0 .38-.57l-.57-1.25.16-.3 1.36-.23a.4.4 0 0 0 .27-.64l-.8-1.11.1-.34 1.28-.48a.4.4 0 0 0 .14-.68l-1.01-.94.03-.34 1.18-.73a.4.4 0 0 0 0-.69Zm-7.86 9.75a.83.83 0 1 1 .65-1.5.83.83 0 0 1-.65 1.5Zm-.4-2.7a.76.76 0 0 0-.9.59l-.41 1.94a10.18 10.18 0 0 1-8.54-.04l-.41-1.94a.76.76 0 0 0-.9-.59l-1.72.37c-.32-.33-.62-.68-.9-1.04h8.37c.1 0 .16-.02.16-.1v-2.97c0-.08-.06-.1-.16-.1H13.7v-1.88h2.64c.24 0 1.3.07 1.63 1.41.1.42.33 1.76.5 2.19.15.48.79 1.45 1.47 1.45h4.17l.15-.02c-.29.4-.6.76-.95 1.11l-1.76-.38ZM9.98 25.36a.83.83 0 1 1-.38-1.62.83.83 0 0 1 .38 1.62ZM6.8 12.5a.83.83 0 1 1-1.5.72.83.83 0 0 1 1.5-.72Zm-.97 2.3 1.79-.79a.76.76 0 0 0 .38-1l-.36-.83h1.45v6.53H6.16a10.3 10.3 0 0 1-.33-3.9Zm7.86-.63v-1.92h3.45c.18 0 1.26.2 1.26 1.01 0 .67-.83.91-1.51.91h-3.2Zm12.55 1.74-.03.76h-1.05c-.1 0-.15.07-.15.17v.48c0 1.14-.64 1.38-1.2 1.45-.53.06-1.13-.23-1.2-.55-.31-1.78-.84-2.16-1.67-2.8 1.03-.66 2.1-1.63 2.1-2.92 0-1.39-.95-2.27-1.6-2.7a4.55 4.55 0 0 0-2.2-.72H8.38a10.23 10.23 0 0 1 5.72-3.23l1.28 1.34a.76.76 0 0 0 1.08.02l1.43-1.37a10.24 10.24 0 0 1 7 5l-.98 2.21a.76.76 0 0 0 .39 1l1.88.84c.04.34.05.67.05 1.02ZM15.39 4.7a.83.83 0 1 1 1.14 1.2.83.83 0 0 1-1.14-1.2Zm9.72 7.83a.83.83 0 0 1 1.47-.11.83.83 0 1 1-1.47.1Z"></path></svg>Rust</a></li><li><a class="ring-focus-accent flex items-center gap-1.5 rounded-md px-2 py-1 font-normal focus-visible:ring-4 focus:outline-none hover:bg-popover-hover text-muted hover:text-strong [&amp;&gt;svg]:text-body" href="/downloads/python" data-discover="true"><svg viewBox="0 0 32 32" class="shrink-0 size-5"><path fill="currentColor" d="M15.85 2.5c-1.08 0-2.1.1-3 .26-2.66.48-3.14 1.49-3.14 3.34v2.45h6.27v.81H7.36c-1.82 0-3.42 1.13-3.92 3.26-.57 2.45-.6 3.97 0 6.53.45 1.9 1.51 3.26 3.33 3.26h2.16v-2.94c0-2.12 1.8-3.99 3.92-3.99h6.26a3.2 3.2 0 0 0 3.14-3.26V6.1c0-1.74-1.44-3.05-3.14-3.34a19.1 19.1 0 0 0-3.26-.26Zm-3.4 1.97c.65 0 1.18.55 1.18 1.23a1.2 1.2 0 0 1-1.18 1.22 1.2 1.2 0 0 1-1.17-1.22c0-.68.52-1.23 1.17-1.23Z"></path><path fill="currentColor" d="M23.03 9.36v2.86a4.07 4.07 0 0 1-3.92 4.07h-6.26a3.24 3.24 0 0 0-3.14 3.26v6.12c0 1.74 1.48 2.76 3.14 3.26 1.98.6 3.89.7 6.26 0 1.58-.46 3.14-1.4 3.14-3.26v-2.45h-6.27v-.81h9.4c1.83 0 2.5-1.3 3.14-3.26.65-2.01.63-3.95 0-6.53-.45-1.86-1.31-3.26-3.14-3.26h-2.35Zm-3.52 15.5c.65 0 1.17.54 1.17 1.21a1.2 1.2 0 0 1-1.17 1.23 1.2 1.2 0 0 1-1.18-1.23c0-.67.53-1.22 1.18-1.22Z"></path><defs><linearGradient id="«Rchr5»" x1="1.22" x2="13.45" y1="1.42" y2="11.6" gradientUnits="userSpaceOnUse"><stop stop-color="#5A9FD4"></stop><stop offset="1" stop-color="#306998"></stop></linearGradient><linearGradient id="«Rchr5H1»" x1="14.78" x2="10.34" y1="20.8" y2="14.72" gradientUnits="userSpaceOnUse"><stop stop-color="#FFD43B"></stop><stop offset="1" stop-color="#FFE873"></stop></linearGradient></defs></svg> Python</a></li><li><a class="ring-focus-accent flex items-center gap-1.5 rounded-md px-2 py-1 font-normal focus-visible:ring-4 focus:outline-none hover:bg-popover-hover text-muted hover:text-strong [&amp;&gt;svg]:text-body" href="/downloads/java" data-discover="true"><svg viewBox="0 0 32 32" class="shrink-0 size-5"><path fill="currentColor" d="M12 23.68s-1.14.68.78.88c2.3.3 3.52.24 6.07-.25.51.32 1.05.58 1.62.79-5.73 2.45-12.98-.15-8.48-1.42Zm-.74-3.19s-1.23.93.68 1.13c2.5.25 4.46.3 7.84-.39.33.33.73.59 1.18.73-6.9 2.06-14.65.2-9.7-1.47Zm13.52 5.59s.83.68-.93 1.22c-3.28.98-13.77 1.28-16.7 0-1.03-.44.93-1.08 1.55-1.17.64-.15.98-.15.98-.15-1.12-.78-7.49 1.62-3.23 2.3 11.7 1.91 21.36-.83 18.32-2.2Zm-12.26-8.92s-5.34 1.28-1.9 1.72c1.46.2 4.35.14 7.04-.05 2.2-.2 4.41-.59 4.41-.59s-.78.34-1.32.69c-5.43 1.42-15.87.78-12.88-.69 2.55-1.22 4.65-1.08 4.65-1.08Zm9.55 5.34c5.49-2.84 2.94-5.58 1.18-5.24-.44.1-.64.2-.64.2s.15-.3.5-.4c3.47-1.22 6.21 3.68-1.14 5.59 0 0 .05-.05.1-.15Zm-9 7.74c5.28.34 13.36-.2 13.56-2.7 0 0-.4.99-4.36 1.72-4.5.83-10.09.73-13.37.2 0 0 .69.58 4.16.78Z"></path><path fill="currentColor" d="M18.75 1s3.04 3.09-2.89 7.74c-4.75 3.77-1.07 5.92 0 8.37-2.79-2.5-4.8-4.7-3.43-6.75 2.01-3.04 7.55-4.5 6.32-9.36ZM17.2 15.06c1.42 1.61-.39 3.08-.39 3.08s3.63-1.86 1.96-4.16c-1.52-2.2-2.7-3.28 3.67-6.96 0 0-10.04 2.5-5.24 8.04Z"></path></svg>Java</a></li></ul></li><li class="text-strong mb-3 ml-2 mt-8 text-xs font-medium uppercase tracking-wider">Infrastructure</li><li><ul><li><a class="ring-focus-accent flex items-center gap-1.5 rounded-md px-2 py-1 font-normal focus-visible:ring-4 focus:outline-none hover:bg-popover-hover text-muted hover:text-strong [&amp;&gt;svg]:text-body" href="/downloads/kubernetes" data-discover="true"><svg viewBox="0 0 32 32" class="shrink-0 size-5"><path fill="currentColor" d="M18.46 17.5a.46.46 0 0 0-.29.03.48.48 0 0 0-.23.62l1.1 2.65a5.58 5.58 0 0 0 2.25-2.84l-2.82-.47Zm-4.38.37a.48.48 0 0 0-.55-.36v-.01l-2.8.47a5.6 5.6 0 0 0 2.25 2.82l1.08-2.62c.03-.1.04-.2.02-.3Zm2.34 1.02a.48.48 0 0 0-.84 0l-1.38 2.49a5.6 5.6 0 0 0 3.6 0l-1.37-2.49h-.01Zm4.35-5.77-2.12 1.9a.48.48 0 0 0 .2.82v.01l2.73.8a5.68 5.68 0 0 0-.81-3.53Zm-3.93.22a.48.48 0 0 0 .76.36l2.32-1.64a5.62 5.62 0 0 0-3.24-1.56l.16 2.84Zm-2.45.37a.48.48 0 0 0 .76-.36l.01-.01.16-2.84a5.72 5.72 0 0 0-3.26 1.56l2.33 1.65Zm-1.24 2.15a.48.48 0 0 0 .19-.82v-.02l-2.12-1.9a5.57 5.57 0 0 0-.8 3.53l2.73-.79Zm2.07.83.78.38.78-.38.2-.84-.55-.68h-.87l-.54.68.2.84Z"></path><path fill="currentColor" d="m28.96 18.35-2.24-9.7a1.74 1.74 0 0 0-.93-1.16l-9.04-4.32a1.74 1.74 0 0 0-1.5 0L6.2 7.49a1.74 1.74 0 0 0-.94 1.16l-2.23 9.7a1.7 1.7 0 0 0 .33 1.46l6.26 7.78.09.1a1.72 1.72 0 0 0 1.26.54h10.04a1.65 1.65 0 0 0 .61-.12 1.76 1.76 0 0 0 .74-.53l6.25-7.78a1.69 1.69 0 0 0 .34-.7c.05-.25.05-.5 0-.75Zm-9.38 3.78.09.21a.43.43 0 0 0-.04.32c.13.3.3.6.48.86l.29.43.07.15a.54.54 0 1 1-.98.46l-.07-.14a4.52 4.52 0 0 1-.15-.5c-.1-.3-.22-.61-.37-.9a.43.43 0 0 0-.28-.17l-.12-.2a7 7 0 0 1-4.99-.02l-.12.22a.44.44 0 0 0-.24.12c-.18.3-.32.63-.42.97-.04.17-.09.33-.15.5l-.07.13a.54.54 0 1 1-.98-.46l.07-.15c.1-.15.19-.29.3-.43.18-.28.35-.57.48-.88.02-.1 0-.21-.04-.3l.1-.24a7.04 7.04 0 0 1-3.13-3.9l-.23.04a.6.6 0 0 0-.31-.1c-.33.06-.64.15-.95.27l-.48.19-.14.03h-.02a.54.54 0 1 1-.24-1.05h.01l.15-.04c.17-.02.34-.04.51-.04.33-.02.66-.08.97-.16.1-.06.18-.14.24-.23l.22-.07a7 7 0 0 1 1.1-4.88L9.98 12a.6.6 0 0 0-.1-.3 4.87 4.87 0 0 0-.8-.57 4.25 4.25 0 0 1-.46-.27l-.11-.09a.57.57 0 0 1-.14-.8.52.52 0 0 1 .43-.2c.14.01.27.06.38.15l.12.1c.12.12.24.24.35.38a5 5 0 0 0 .73.65c.1.06.22.07.32.04l.2.13a6.96 6.96 0 0 1 4.51-2.17l.02-.23a.59.59 0 0 0 .17-.28c.01-.32 0-.65-.06-.98a4.55 4.55 0 0 1-.07-.51V6.9a.54.54 0 1 1 1.08 0v.15a4.5 4.5 0 0 1-.07.51c-.05.33-.08.66-.06.98.02.11.08.21.17.28l.02.24a7.1 7.1 0 0 1 4.49 2.17l.2-.15a.6.6 0 0 0 .33-.04c.27-.2.51-.41.73-.66.11-.13.23-.25.36-.37l.12-.1a.54.54 0 1 1 .67.85l-.12.1a5.06 5.06 0 0 0-1.25.83.4.4 0 0 0-.1.3l-.2.17a7.03 7.03 0 0 1 1.13 4.87l.22.06c.06.1.14.18.23.24.32.08.65.14.98.16.17 0 .34.01.51.04.05 0 .12.03.16.04a.54.54 0 1 1-.24 1.05h-.02l-.14-.03a4.5 4.5 0 0 1-.48-.2c-.3-.11-.62-.2-.95-.27a.43.43 0 0 0-.3.11 8.22 8.22 0 0 0-.23-.04 7.04 7.04 0 0 1-3.12 3.93Z"></path></svg> Kubernetes</a></li></ul></li></ul></nav><div class="min-w-0 flex-1 md:-mt-16"><main id="main"><div class="divide-card-muted border-card bg-card relative divide-y border rounded-none border-x-0 shadow-2xl md:rounded-xl md:border-x"><div class="p-6"><form class="mb-5 block md:hidden"><label class="cursor-pointer peer-disabled:cursor-default has-[:disabled]:cursor-default aria-disabled:cursor-default text-strong mb-1.5 block text-sm" for="platform">Select a Platform</label><button type="button" role="combobox" aria-controls="radix-«R4mr5»" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" aria-invalid="false" class="h-9 text-sm border-form bg-form text-strong placeholder:text-placeholder hover:bg-form-hover hover:text-strong flex w-full items-center justify-between gap-1.5 rounded-md border px-3 py-2 disabled:pointer-events-none disabled:opacity-50 [&amp;&gt;span]:line-clamp-1 [&amp;&gt;span]:text-left hover:border-neutral-400 focus:outline-none focus:ring-4 aria-expanded:ring-4 focus:border-accent-600 focus:ring-focus-accent aria-expanded:border-accent-600 aria-expanded:ring-focus-accent data-validation-success:border-success-600 data-validation-success:focus:border-success-600 data-validation-success:focus:ring-focus-success data-validation-success:aria-expanded:border-success-600 data-validation-success:aria-expanded:ring-focus-success data-validation-warning:border-warning-600 data-validation-warning:focus:border-warning-600 data-validation-warning:focus:ring-focus-warning data-validation-warning:aria-expanded:border-warning-600 data-validation-warning:aria-expanded:ring-focus-warning data-validation-error:border-danger-600 data-validation-error:focus:border-danger-600 data-validation-error:focus:ring-focus-danger data-validation-error:aria-expanded:border-danger-600 data-validation-error:aria-expanded:ring-focus-danger" id="platform"><span class="sr-only">Select a Platform</span><span style="pointer-events:none"></span><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 256 256" aria-hidden="true" class="shrink-0 size-4"><path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z"></path></svg></button><select aria-hidden="true" tabindex="-1" name="platform" style="position:absolute;border:0;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;word-wrap:normal"></select></form><div><div class="flex mb-10 items-center gap-4"><div class="shrink-0 leading-none border-popover bg-popover grid size-20 place-content-center rounded-xl border bg-gradient-to-b from-white to-gray-100 shadow-md dark:bg-gradient-to-t dark:from-gray-50 dark:to-gray-100"><svg viewBox="0 0 32 32" class="shrink-0 size-14"><path fill="#F8682C" d="m4 7.32 9.8-1.34.01 9.46-9.8.06L4 7.32Z"></path><path fill="#91C300" d="m15 5.8 13-1.9v11.42l-13 .1V5.81Z"></path><path fill="#00B4F1" d="m13.8 16.53.01 9.47-9.8-1.35v-8.18l9.8.06Z"></path><path fill="#FFC300" d="M28 16.62v11.36l-13-1.83-.03-9.55 13.03.02Z"></path></svg></div><div class="min-w-0 flex-1"><span class="inline-flex w-fit shrink-0 cursor-default items-center gap-1 rounded px-1.5 py-0.5 font-medium text-xs bg-neutral-500/20 text-neutral-700">Agent</span><h2 class="text-strong text-3xl font-medium">Windows</h2></div></div><h3 class="text-strong mb-5 text-xl font-medium">Installation</h3><div dir="ltr" data-orientation="horizontal" class="flex gap-4 flex-col mb-4"><div role="tablist" aria-orientation="horizontal" class="flex border-gray-200 flex-row items-center gap-6 border-b" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-«R76r5»-content-install" data-state="active" id="radix-«R76r5»-trigger-install" class="group/tab-trigger relative flex cursor-pointer items-center gap-1 whitespace-nowrap py-3 text-sm font-medium text-gray-600 rounded-tl-md rounded-tr-md ring-focus-accent outline-none aria-disabled:cursor-default aria-disabled:opacity-50 focus-visible:ring-4 [&amp;&gt;svg]:shrink-0 [&amp;&gt;svg]:size-5 not-aria-disabled:hover:text-gray-900 not-aria-disabled:hover:data-state-active:text-blue-600 data-state-active:text-blue-600" tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span aria-hidden="true" class="group-data-state-active/tab-trigger:bg-blue-600 absolute z-0 -bottom-px left-0 right-0 h-[0.1875rem]"></span>Chocolatey</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R76r5»-content-download" data-state="inactive" id="radix-«R76r5»-trigger-download" class="group/tab-trigger relative flex cursor-pointer items-center gap-1 whitespace-nowrap py-3 text-sm font-medium text-gray-600 rounded-tl-md rounded-tr-md ring-focus-accent outline-none aria-disabled:cursor-default aria-disabled:opacity-50 focus-visible:ring-4 [&amp;&gt;svg]:shrink-0 [&amp;&gt;svg]:size-5 not-aria-disabled:hover:text-gray-900 not-aria-disabled:hover:data-state-active:text-blue-600 data-state-active:text-blue-600" tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span aria-hidden="true" class="group-data-state-active/tab-trigger:bg-blue-600 absolute z-0 -bottom-px left-0 right-0 h-[0.1875rem]"></span>Download</button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R76r5»-trigger-install" id="radix-«R76r5»-content-install" tabindex="0" class="focus-visible:ring-focus-accent outline-none focus-visible:ring-4" style="animation-duration:0s"><div class="space-y-4"><p>Install ngrok via Chocolatey with the following command:</p><div class="text-size-mono overflow-hidden rounded-md border border-gray-300 bg-gray-50 font-mono [&amp;_svg]:shrink-0"><div class="relative"><button type="button" class="focus-visible:border-accent-600 focus-visible:ring-focus-accent absolute right-2.5 top-2.5 z-10 flex size-7 items-center justify-center rounded border border-gray-300 bg-gray-50 shadow-[-1rem_0_0.75rem_-0.375rem_hsl(var(--gray-50)),1rem_0_0_-0.25rem_hsl(var(--gray-50))] hover:border-gray-400 hover:bg-gray-200 focus-visible:outline-none focus-visible:ring-4 get-started-copy-windows-3"><span class="sr-only">Copy code</span><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 256 256" class="shrink-0 size-5 -ml-px"><path d="M216,32H88a8,8,0,0,0-8,8V80H40a8,8,0,0,0-8,8V216a8,8,0,0,0,8,8H168a8,8,0,0,0,8-8V176h40a8,8,0,0,0,8-8V40A8,8,0,0,0,216,32ZM160,208H48V96H160Zm48-48H176V88a8,8,0,0,0-8-8H96V48H208Z"></path></svg></button><pre class="scrollbar firefox:after:mr-[3.375rem] firefox:after:inline-block firefox:after:content-[&#x27;&#x27;] overflow-x-auto overflow-y-hidden p-4 pr-14 text-size-mono m-0 font-mono aria-collapsed:max-h-[13.6rem] language-sh" data-lang="sh" id="«R2l76r5»" style="tab-size:2;-moz-tab-size:2px" tabindex="-1"><code class="text-size-inherit language-sh">choco install ngrok</code></pre></div></div></div></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R76r5»-trigger-download" hidden="" id="radix-«R76r5»-content-download" tabindex="0" class="focus-visible:ring-focus-accent outline-none focus-visible:ring-4"></div></div><h3 class="text-strong mb-4 mt-8 text-xl font-medium">Configure and run</h3><p>Add your authtoken:</p><p class="text-muted mb-4 text-xs">Don’t have an authtoken?<!-- --> <a class="cursor-pointer rounded bg-transparent text-accent-600 hover:underline focus:outline-none focus-visible:ring focus-visible:ring-focus-accent get-started-signup-windows-1" href="https://ngrok.com/signup?ref=downloads">Sign up</a> <!-- -->for a free account.</p><div class="text-size-mono overflow-hidden rounded-md border border-gray-300 bg-gray-50 font-mono [&amp;_svg]:shrink-0 mb-4"><div class="relative"><button type="button" class="focus-visible:border-accent-600 focus-visible:ring-focus-accent absolute right-2.5 top-2.5 z-10 flex size-7 items-center justify-center rounded border border-gray-300 bg-gray-50 shadow-[-1rem_0_0.75rem_-0.375rem_hsl(var(--gray-50)),1rem_0_0_-0.25rem_hsl(var(--gray-50))] hover:border-gray-400 hover:bg-gray-200 focus-visible:outline-none focus-visible:ring-4 get-started-copy-windows-1"><span class="sr-only">Copy code</span><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 256 256" class="shrink-0 size-5 -ml-px"><path d="M216,32H88a8,8,0,0,0-8,8V80H40a8,8,0,0,0-8,8V216a8,8,0,0,0,8,8H168a8,8,0,0,0,8-8V176h40a8,8,0,0,0,8-8V40A8,8,0,0,0,216,32ZM160,208H48V96H160Zm48-48H176V88a8,8,0,0,0-8-8H96V48H208Z"></path></svg></button><pre class="scrollbar firefox:after:mr-[3.375rem] firefox:after:inline-block firefox:after:content-[&#x27;&#x27;] overflow-x-auto overflow-y-hidden p-4 pr-14 text-size-mono m-0 font-mono aria-collapsed:max-h-[13.6rem] language-sh" data-lang="sh" id="«R2f6r5»" style="tab-size:2;-moz-tab-size:2px" tabindex="-1"><code class="text-size-inherit language-sh">ngrok config add-authtoken &lt;token&gt;</code></pre></div></div><p class="mb-4">Start an endpoint:</p><div class="text-size-mono overflow-hidden rounded-md border border-gray-300 bg-gray-50 font-mono [&amp;_svg]:shrink-0 mb-4"><div class="relative"><button type="button" class="focus-visible:border-accent-600 focus-visible:ring-focus-accent absolute right-2.5 top-2.5 z-10 flex size-7 items-center justify-center rounded border border-gray-300 bg-gray-50 shadow-[-1rem_0_0.75rem_-0.375rem_hsl(var(--gray-50)),1rem_0_0_-0.25rem_hsl(var(--gray-50))] hover:border-gray-400 hover:bg-gray-200 focus-visible:outline-none focus-visible:ring-4 get-started-copy-windows-2"><span class="sr-only">Copy code</span><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 256 256" class="shrink-0 size-5 -ml-px"><path d="M216,32H88a8,8,0,0,0-8,8V80H40a8,8,0,0,0-8,8V216a8,8,0,0,0,8,8H168a8,8,0,0,0,8-8V176h40a8,8,0,0,0,8-8V40A8,8,0,0,0,216,32ZM160,208H48V96H160Zm48-48H176V88a8,8,0,0,0-8-8H96V48H208Z"></path></svg></button><pre class="scrollbar firefox:after:mr-[3.375rem] firefox:after:inline-block firefox:after:content-[&#x27;&#x27;] overflow-x-auto overflow-y-hidden p-4 pr-14 text-size-mono m-0 font-mono aria-collapsed:max-h-[13.6rem] language-sh" data-lang="sh" id="«R2j6r5»" style="tab-size:2;-moz-tab-size:2px" tabindex="-1"><code class="text-size-inherit language-sh">ngrok http 80</code></pre></div></div><p class="mb-8">Congratulations, you have an endpoint online!</p><p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 256 256" class="shrink-0 size-5 -mt-0.5 inline"><path d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Zm16-40a8,8,0,0,1-8,8,16,16,0,0,1-16-16V128a8,8,0,0,1,0-16,16,16,0,0,1,16,16v40A8,8,0,0,1,144,176ZM112,84a12,12,0,1,1,12,12A12,12,0,0,1,112,84Z"></path></svg> You can run ngrok as a<!-- --> <a class="cursor-pointer rounded bg-transparent text-accent-600 hover:underline focus:outline-none focus-visible:ring focus-visible:ring-focus-accent" href="https://ngrok.com/docs/agent/#background-service">background service</a> <!-- -->on a Windows machine.</p></div></div></div></main><aside class="mt-16"><div class="grid grid-cols-1 gap-14 lg:grid-cols-2"><div class="flex flex-col"><div class="mb-4 flex min-w-0 items-center justify-between gap-4 px-5 md:px-0"><h3 class="text-strong text-lg font-medium md:ml-2">What’s new?</h3><a href="https://ngrok.com/docs/whats-new/" aria-disabled="false" class="inline-flex cursor-pointer items-center justify-center gap-1.5 whitespace-nowrap rounded-md focus-within:outline-none focus-visible:ring-4 disabled:cursor-default disabled:opacity-50 [&amp;&gt;*]:focus-within:outline-none h-9 border px-3 text-sm font-medium border-form bg-form text-strong focus-visible:border-accent-600 focus-visible:ring-focus-accent not-disabled:hover:border-neutral-400 not-disabled:hover:bg-form-hover not-disabled:hover:text-strong not-disabled:active:border-neutral-400 not-disabled:active:bg-neutral-500/10 not-disabled:active:text-strong focus-visible:not-disabled:active:border-accent-600 md:rounded-lg" data-loading="false"><span class="inline-flex items-center gap-1.5 focus-within:outline-none focus-visible:outline-none"><span class="xs:inline xs:not-sr-only sr-only">View changelog</span><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 256 256" class="shrink-0 size-5 xs:hidden"><path d="M168,128a8,8,0,0,1-8,8H96a8,8,0,0,1,0-16h64A8,8,0,0,1,168,128Zm-8,24H96a8,8,0,0,0,0,16h64a8,8,0,0,0,0-16ZM216,40V200a32,32,0,0,1-32,32H72a32,32,0,0,1-32-32V40a8,8,0,0,1,8-8H72V24a8,8,0,0,1,16,0v8h32V24a8,8,0,0,1,16,0v8h32V24a8,8,0,0,1,16,0v8h24A8,8,0,0,1,216,40Zm-16,8H184v8a8,8,0,0,1-16,0V48H136v8a8,8,0,0,1-16,0V48H88v8a8,8,0,0,1-16,0V48H56V200a16,16,0,0,0,16,16H184a16,16,0,0,0,16-16Z"></path></svg></span></a></div><div class="divide-card-muted border-card bg-card relative divide-y border flex-1 rounded-none border-x-0 shadow-2xl md:rounded-xl md:border-x"><div class="p-6"><h4 class="text-strong mb-2 text-base font-medium">Features, improvements, and fixes</h4><p>At ngrok, we deploy our SaaS platform multiple times a day across our global network. We are constantly improving and adding new features.</p></div></div></div><div class="flex flex-col"><div class="mb-4 flex min-w-0 items-center justify-between gap-4 px-5 md:px-0"><h3 class="text-strong text-lg font-medium md:ml-2">Upcoming</h3><a href="https://ngrok.com/resources/office-hours" aria-disabled="false" class="inline-flex cursor-pointer items-center justify-center gap-1.5 whitespace-nowrap rounded-md focus-within:outline-none focus-visible:ring-4 disabled:cursor-default disabled:opacity-50 [&amp;&gt;*]:focus-within:outline-none h-9 border px-3 text-sm font-medium border-form bg-form text-strong focus-visible:border-accent-600 focus-visible:ring-focus-accent not-disabled:hover:border-neutral-400 not-disabled:hover:bg-form-hover not-disabled:hover:text-strong not-disabled:active:border-neutral-400 not-disabled:active:bg-neutral-500/10 not-disabled:active:text-strong focus-visible:not-disabled:active:border-accent-600 md:rounded-lg" data-loading="false"><span class="inline-flex items-center gap-1.5 focus-within:outline-none focus-visible:outline-none"><span class="xs:inline xs:not-sr-only sr-only">Register now</span><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 256 256" class="shrink-0 size-5 xs:hidden"><path d="M208,32H184V24a8,8,0,0,0-16,0v8H88V24a8,8,0,0,0-16,0v8H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM72,48v8a8,8,0,0,0,16,0V48h80v8a8,8,0,0,0,16,0V48h24V80H48V48ZM208,208H48V96H208V208Zm-38.34-85.66a8,8,0,0,1,0,11.32l-48,48a8,8,0,0,1-11.32,0l-24-24a8,8,0,0,1,11.32-11.32L116,164.69l42.34-42.35A8,8,0,0,1,169.66,122.34Z"></path></svg></span></a></div><div class="divide-card-muted border-card bg-card divide-y border relative flex-1 rounded-none border-x-0 shadow-2xl md:rounded-xl md:border-x"><div class="p-6"><div class="flex gap-4"><div class="shrink-0 leading-none -left-7 md:absolute"><svg xmlns="http://www.w3.org/2000/svg" width="105" height="68" viewBox="0 0 105 68" fill="none"><path d="M95.5273 28.9774C95.492 28.8252 95.3745 28.7081 95.2275 28.6612L93.9283 28.2748V8.21481C93.9283 5.32819 91.5709 2.98025 88.6727 2.98025H8.56957L4.77193 1.85605C4.61908 1.80921 4.45448 1.85605 4.33691 1.96144L1.95603 4.29182C0.868472 5.25207 0.180664 6.65147 0.180664 8.20896V28.7139C0.180664 31.6005 2.53802 33.9485 5.43622 33.9485H88.6727C90.3364 33.9485 91.8237 33.1756 92.7878 31.9694L95.4156 29.3931C95.5273 29.2819 95.5685 29.1238 95.5332 28.9774H95.5273ZM4.76605 2.76946L5.47737 2.98025H5.43034C5.09526 2.98025 4.77193 3.01538 4.45448 3.07393L4.76605 2.76946ZM93.8284 29.7152C93.8636 29.5395 93.8871 29.3639 93.9048 29.1823L94.2634 29.2877L93.8284 29.7093V29.7152Z" fill="url(#paint0_linear_31_850)"></path><path d="M91.6645 0.439087H8.42794C5.76888 0.439087 3.61328 2.58607 3.61328 5.23451V25.7395C3.61328 28.3879 5.76888 30.5349 8.42794 30.5349H91.6645C94.3235 30.5349 96.4791 28.3879 96.4791 25.7395V5.23451C96.4791 2.58607 94.3235 0.439087 91.6645 0.439087Z" fill="hsl(var(--white))"></path><path d="M91.6649 30.9741H8.42841C5.53021 30.9741 3.17285 28.6262 3.17285 25.7395V5.23456C3.17285 2.34794 5.53021 0 8.42841 0H91.6649C94.5631 0 96.9205 2.34794 96.9205 5.23456V25.7395C96.9205 28.6262 94.5631 30.9741 91.6649 30.9741ZM8.42841 0.878282C6.01814 0.878282 4.05466 2.83392 4.05466 5.23456V25.7395C4.05466 28.1402 6.01814 30.0958 8.42841 30.0958H91.6649C94.0752 30.0958 96.0387 28.1402 96.0387 25.7395V5.23456C96.0387 2.83392 94.0752 0.878282 91.6649 0.878282H8.42841Z" fill="url(#paint1_linear_31_850)"></path><path d="M18.4509 23.6082C17.6337 23.6082 16.9048 23.4735 16.2522 23.2042C15.5997 22.9349 15.053 22.5484 14.6003 22.0507C14.1477 21.553 13.8008 20.9499 13.5598 20.2356C13.3188 19.5271 13.1953 18.725 13.1953 17.8467C13.1953 16.9684 13.3188 16.1662 13.5598 15.4577C13.8008 14.7493 14.1477 14.1403 14.6003 13.6426C15.053 13.1449 15.5997 12.7585 16.2522 12.4892C16.9048 12.2198 17.6337 12.0851 18.4509 12.0851C19.268 12.0851 19.997 12.2198 20.6495 12.4892C21.302 12.7585 21.8488 13.1449 22.3014 13.6426C22.7541 14.1403 23.095 14.7434 23.3419 15.4577C23.583 16.1721 23.7064 16.9684 23.7064 17.8467C23.7064 18.725 23.583 19.5271 23.3419 20.2356C23.1009 20.9499 22.7541 21.553 22.3014 22.0507C21.8488 22.5484 21.302 22.9349 20.6495 23.2042C19.997 23.4735 19.268 23.6082 18.4509 23.6082ZM18.4509 21.7755C19.3092 21.7755 19.9911 21.5179 20.4967 20.9968C21.0022 20.4757 21.255 19.7028 21.255 18.684V17.0211C21.255 15.9964 21.0022 15.2235 20.4967 14.7083C19.9911 14.1872 19.3092 13.9295 18.4509 13.9295C17.5926 13.9295 16.9107 14.1872 16.4051 14.7083C15.8995 15.2294 15.6467 16.0023 15.6467 17.0211V18.684C15.6467 19.7086 15.8995 20.4815 16.4051 20.9968C16.9107 21.5179 17.5926 21.7755 18.4509 21.7755Z" fill="hsl(var(--gray-700))"></path><path d="M26.2527 21.4535H29.7858V14.2399H26.0352V12.3428H29.7858V10.3579C29.7858 9.53229 30.015 8.85894 30.4736 8.34369C30.9321 7.82257 31.6434 7.56494 32.6134 7.56494H36.3405V9.46203H32.1196V12.3428H36.3405V14.2399H32.1196V21.4535H35.8467V23.3506H26.2468V21.4535H26.2527Z" fill="hsl(var(--gray-700))"></path><path d="M39.2615 21.4535H42.7946V14.2399H39.0439V12.3428H42.7946V10.3579C42.7946 9.53229 43.0238 8.85894 43.4824 8.34369C43.9409 7.82257 44.6522 7.56494 45.6222 7.56494H49.3493V9.46203H45.1284V12.3428H49.3493V14.2399H45.1284V21.4535H48.8555V23.3506H39.2556V21.4535H39.2615Z" fill="hsl(var(--gray-700))"></path><path d="M52.3721 21.4535H56.1462V14.2399H52.3721V12.3428H58.48V21.4535H62.0131V23.3506H52.3721V21.4535ZM57.3219 10.3344C56.7223 10.3344 56.3049 10.2115 56.0698 9.97142C55.8346 9.73135 55.7171 9.43274 55.7171 9.07557V8.62472C55.7171 8.26755 55.8346 7.96893 56.0698 7.72887C56.3049 7.4888 56.7164 7.36584 57.3043 7.36584C57.8922 7.36584 58.3213 7.4888 58.5565 7.72887C58.7916 7.96893 58.9092 8.26755 58.9092 8.62472V9.07557C58.9092 9.43274 58.7916 9.73135 58.5565 9.97142C58.3213 10.2115 57.9098 10.3344 57.3219 10.3344Z" fill="hsl(var(--gray-700))"></path><path d="M69.5719 23.6082C68.743 23.6082 68.0023 23.4735 67.3557 23.2042C66.7031 22.9349 66.1564 22.5484 65.7038 22.0507C65.2511 21.553 64.9101 20.9499 64.675 20.2356C64.4398 19.5271 64.3223 18.725 64.3223 17.8467C64.3223 16.9684 64.4398 16.1662 64.675 15.4577C64.9101 14.7493 65.2511 14.1403 65.7038 13.6426C66.1564 13.1449 66.7031 12.7585 67.3557 12.4892C68.0082 12.2198 68.7372 12.0851 69.5543 12.0851C70.6948 12.0851 71.6119 12.3252 72.3055 12.8053C72.9992 13.2855 73.5166 13.9061 73.8575 14.6673L72.0351 15.651C71.8352 15.1416 71.5296 14.7317 71.1239 14.4331C70.7183 14.1345 70.1892 13.9822 69.5484 13.9822C68.6607 13.9822 67.9788 14.2516 67.4909 14.7844C67.003 15.3231 66.7619 16.0316 66.7619 16.9216V18.7659C66.7619 19.6384 67.003 20.3469 67.4909 20.8914C67.9788 21.4359 68.6784 21.7053 69.5896 21.7053C70.2774 21.7053 70.8359 21.5472 71.2709 21.2368C71.7059 20.9265 72.0528 20.4991 72.3114 19.9546L74.028 21.0026C73.6694 21.7697 73.1286 22.3903 72.3996 22.8763C71.6706 23.3623 70.73 23.6024 69.5719 23.6024V23.6082Z" fill="hsl(var(--gray-700))"></path><path d="M81.9874 23.6082C81.1468 23.6082 80.3884 23.4735 79.7182 23.2042C79.0481 22.9349 78.4837 22.5484 78.0252 22.0507C77.5666 21.553 77.2198 20.9499 76.9729 20.2473C76.7319 19.5447 76.6084 18.7484 76.6084 17.8701C76.6084 16.9918 76.7319 16.1897 76.9846 15.4812C77.2315 14.7727 77.5843 14.1637 78.0369 13.6543C78.4896 13.1508 79.0363 12.7643 79.6771 12.4892C80.3179 12.2198 81.0351 12.0851 81.8169 12.0851C82.5988 12.0851 83.2866 12.214 83.9156 12.4774C84.5446 12.7409 85.0796 13.1157 85.5205 13.5958C85.9614 14.0818 86.3024 14.6556 86.5375 15.3231C86.7727 15.9906 86.8902 16.7342 86.8902 17.5422V18.4146H78.9599V18.7777C78.9599 19.6384 79.2303 20.341 79.777 20.8855C80.3179 21.4301 81.0703 21.6994 82.0286 21.6994C82.7399 21.6994 83.3513 21.5472 83.8627 21.2427C84.3683 20.9382 84.7798 20.5342 85.0972 20.0248L86.5552 21.3071C86.1848 21.9512 85.6146 22.4899 84.8503 22.9349C84.0861 23.3799 83.1338 23.5965 81.9874 23.5965V23.6082ZM81.8169 13.8534C81.4054 13.8534 81.0233 13.9237 80.6706 14.0642C80.3179 14.2047 80.018 14.4097 79.7711 14.6731C79.5184 14.9366 79.3244 15.247 79.1833 15.6041C79.0422 15.9613 78.9716 16.3536 78.9716 16.7752V16.9274H84.5211V16.7166C84.5211 15.85 84.2742 15.1533 83.7804 14.638C83.2866 14.1169 82.6341 13.8593 81.8169 13.8593V13.8534Z" fill="hsl(var(--gray-700))"></path><path d="M103.045 62.4282C103.01 62.2818 102.904 62.1647 102.757 62.112L101.222 61.5968V41.8881C101.222 39.0015 98.8651 36.6535 95.9669 36.6535H27.2978L24.3937 35.6757C24.235 35.623 24.0586 35.664 23.941 35.7811L22.0598 37.6606C20.7489 38.615 19.8965 40.155 19.8965 41.8881V62.3931C19.8965 65.2797 22.2538 67.6276 25.152 67.6276H95.9669C97.5247 67.6276 98.9239 66.9484 99.888 65.8769L102.927 62.8381C103.033 62.7327 103.08 62.5746 103.045 62.4282Z" fill="url(#paint2_linear_31_850)"></path><path d="M98.9644 34.1124H28.1496C25.4906 34.1124 23.335 36.2594 23.335 38.9078V59.4128C23.335 62.0613 25.4906 64.2082 28.1496 64.2082H98.9644C101.624 64.2082 103.779 62.0613 103.779 59.4128V38.9078C103.779 36.2594 101.624 34.1124 98.9644 34.1124Z" fill="hsl(var(--white))"></path><path d="M98.9708 64.6474H28.1559C25.2577 64.6474 22.9004 62.2995 22.9004 59.4129V38.9079C22.9004 36.0213 25.2577 33.6733 28.1559 33.6733H98.9708C101.869 33.6733 104.226 36.0213 104.226 38.9079V59.4129C104.226 62.2995 101.869 64.6474 98.9708 64.6474ZM28.1559 34.5516C25.7457 34.5516 23.7822 36.5073 23.7822 38.9079V59.4129C23.7822 61.8135 25.7457 63.7692 28.1559 63.7692H98.9708C101.381 63.7692 103.345 61.8135 103.345 59.4129V38.9079C103.345 36.5073 101.381 34.5516 98.9708 34.5516H28.1559Z" fill="url(#paint3_linear_31_850)"></path><path d="M33.0469 41.1329H35.3807V47.7903H35.4865C35.5982 47.5034 35.7452 47.2341 35.9274 46.9706C36.1038 46.7071 36.3213 46.4787 36.58 46.2855C36.8386 46.0923 37.1385 45.9401 37.4794 45.8288C37.8204 45.7176 38.2201 45.659 38.6787 45.659C39.7956 45.659 40.6951 46.022 41.377 46.7481C42.0648 47.4741 42.4058 48.5047 42.4058 49.8396V56.9245H40.0719V50.1792C40.0719 48.4285 39.3077 47.5561 37.7792 47.5561C37.4794 47.5561 37.1855 47.5971 36.9033 47.6732C36.6152 47.7493 36.3625 47.8664 36.1332 48.0245C35.9039 48.1826 35.7217 48.3817 35.5865 48.6218C35.4513 48.8618 35.3807 49.1429 35.3807 49.4532V56.9245H33.0469V41.1329Z" fill="hsl(var(--gray-700))"></path><path d="M50.7243 57.1821C49.9072 57.1821 49.1782 57.0474 48.5257 56.7781C47.8731 56.5087 47.3264 56.1223 46.8738 55.6246C46.4211 55.1269 46.0743 54.5238 45.8332 53.8095C45.5922 53.101 45.4688 52.2988 45.4688 51.4205C45.4688 50.5422 45.5922 49.7401 45.8332 49.0316C46.0743 48.3231 46.4211 47.7142 46.8738 47.2165C47.3264 46.7188 47.8731 46.3323 48.5257 46.063C49.1782 45.7937 49.9072 45.659 50.7243 45.659C51.5414 45.659 52.2704 45.7937 52.9229 46.063C53.5755 46.3323 54.1222 46.7188 54.5749 47.2165C55.0275 47.7142 55.3685 48.3173 55.6154 49.0316C55.8564 49.7459 55.9799 50.5422 55.9799 51.4205C55.9799 52.2988 55.8564 53.101 55.6154 53.8095C55.3744 54.5238 55.0275 55.1269 54.5749 55.6246C54.1222 56.1223 53.5755 56.5087 52.9229 56.7781C52.2704 57.0474 51.5414 57.1821 50.7243 57.1821ZM50.7243 55.3494C51.5826 55.3494 52.2645 55.0917 52.7701 54.5706C53.2757 54.0495 53.5284 53.2766 53.5284 52.2578V50.5949C53.5284 49.5703 53.2757 48.7974 52.7701 48.2821C52.2645 47.761 51.5826 47.5034 50.7243 47.5034C49.866 47.5034 49.1841 47.761 48.6785 48.2821C48.173 48.8032 47.9202 49.5761 47.9202 50.5949V52.2578C47.9202 53.2825 48.173 54.0554 48.6785 54.5706C49.1841 55.0917 49.866 55.3494 50.7243 55.3494Z" fill="hsl(var(--gray-700))"></path><path d="M66.068 55.0508H65.9622C65.8505 55.3377 65.7035 55.607 65.5213 55.8705C65.3449 56.134 65.1274 56.3623 64.8688 56.5555C64.6101 56.7488 64.3103 56.901 63.9693 57.0123C63.6284 57.1235 63.2286 57.1821 62.7701 57.1821C61.6531 57.1821 60.7537 56.819 60.0717 56.093C59.3898 55.3669 59.043 54.3364 59.043 52.9956V45.9108H61.3768V52.656C61.3768 54.4067 62.141 55.2791 63.6695 55.2791C63.9693 55.2791 64.2633 55.2381 64.5454 55.162C64.8335 55.0859 65.0863 54.9688 65.3155 54.8107C65.5448 54.6526 65.7271 54.4594 65.8623 54.2252C65.9975 53.991 66.068 53.7099 66.068 53.382V45.9108H68.4019V56.9244H66.068V55.0449V55.0508Z" fill="hsl(var(--gray-700))"></path><path d="M71.5049 55.0273H74.4384V47.8137H71.5049V45.9166H76.7781V48.8384H76.9074C77.0073 48.4519 77.1484 48.0831 77.3366 47.7259C77.5247 47.3687 77.7598 47.0584 78.042 46.7949C78.3301 46.5314 78.6769 46.3206 79.0943 46.1567C79.5058 45.9927 79.9937 45.9108 80.5522 45.9108H81.8808V48.1299H79.6939C78.8356 48.1299 78.1361 48.3817 77.5952 48.8852C77.0544 49.3888 76.7781 50.068 76.7781 50.917V55.0215H80.7874V56.9186H71.5108V55.0215L71.5049 55.0273Z" fill="hsl(var(--gray-700))"></path><path d="M89.2065 57.1821C88.0072 57.1821 86.9726 56.9889 86.1084 56.6083C85.2442 56.2219 84.5447 55.7125 84.0215 55.0742L85.4559 53.792C85.9438 54.3189 86.4905 54.7229 87.096 54.9981C87.7015 55.2733 88.4188 55.4139 89.2476 55.4139C90.006 55.4139 90.6174 55.285 91.0818 55.0274C91.5462 54.7698 91.7755 54.3716 91.7755 53.8329C91.7755 53.6046 91.7343 53.4114 91.6462 53.2591C91.558 53.101 91.4404 52.9722 91.2817 52.8727C91.1229 52.7732 90.9407 52.697 90.735 52.6385C90.5292 52.5799 90.3117 52.5389 90.0824 52.5097L88.3247 52.2345C87.8955 52.1759 87.4605 52.0822 87.0196 51.9593C86.5787 51.8305 86.179 51.6489 85.8321 51.4147C85.4794 51.1805 85.1972 50.8761 84.9738 50.5072C84.7504 50.1383 84.6387 49.6699 84.6387 49.0961C84.6387 47.9719 85.0679 47.117 85.9379 46.5373C86.8021 45.9518 87.9426 45.6649 89.3535 45.6649C90.3822 45.6649 91.2699 45.8172 92.0224 46.1216C92.7749 46.4261 93.398 46.8535 93.8977 47.3922L92.5926 48.7565C92.3046 48.4286 91.8989 48.1241 91.3699 47.8489C90.8408 47.5737 90.1412 47.4332 89.2712 47.4332C87.7133 47.4332 86.9373 47.925 86.9373 48.9087C86.9373 49.3771 87.1078 49.6992 87.4429 49.8807C87.778 50.0563 88.1718 50.1734 88.6304 50.232L90.3881 50.5072C90.829 50.5774 91.2699 50.6711 91.7049 50.7824C92.14 50.8936 92.5338 51.0693 92.8807 51.3035C93.2334 51.5377 93.5156 51.8422 93.739 52.211C93.9624 52.5799 94.0741 53.0483 94.0741 53.6222C94.0741 54.7346 93.639 55.6012 92.7749 56.2336C91.9107 56.8659 90.7232 57.1821 89.2065 57.1821Z" fill="hsl(var(--gray-700))"></path><defs><linearGradient id="paint0_linear_31_850" x1="0.180664" y1="17.8993" x2="95.5391" y2="17.8993" gradientUnits="userSpaceOnUse"><stop stop-color="#E58080"></stop><stop offset="0.52" stop-color="#FFC662"></stop><stop offset="1" stop-color="#64EAF4"></stop></linearGradient><linearGradient id="paint1_linear_31_850" x1="3.17285" y1="15.487" x2="96.9205" y2="15.487" gradientUnits="userSpaceOnUse"><stop stop-color="#E58080"></stop><stop offset="0.52" stop-color="#FFC662"></stop><stop offset="1" stop-color="#64EAF4"></stop></linearGradient><linearGradient id="paint2_linear_31_850" x1="19.9024" y1="51.637" x2="103.057" y2="51.637" gradientUnits="userSpaceOnUse"><stop stop-color="#E58080"></stop><stop offset="0.52" stop-color="#FFC662"></stop><stop offset="1" stop-color="#64EAF4"></stop></linearGradient><linearGradient id="paint3_linear_31_850" x1="22.9004" y1="49.1604" x2="104.22" y2="49.1604" gradientUnits="userSpaceOnUse"><stop stop-color="#E58080"></stop><stop offset="0.52" stop-color="#FFC662"></stop><stop offset="1" stop-color="#64EAF4"></stop></linearGradient></defs></svg></div><div class="min-w-0 flex-1 md:pl-[4.625rem]"><h4 class="text-strong mb-2 text-base font-medium">Join us for Office Hours</h4><p>Just got started and want to learn how it works in your development environment or with a production use case? Our door is open!</p></div></div></div></div></div></div><p class="my-12 text-center sm:my-20">Want to stay in touch?<!-- --> <a class="cursor-pointer rounded bg-transparent text-accent-600 hover:underline focus:outline-none focus-visible:ring focus-visible:ring-focus-accent" href="https://ngrok.com/newsletter">Join our newsletter →</a></p></aside></div></div></div><script>window.__reactRouterContext = {"basename":"/","future":{"unstable_middleware":false,"unstable_optimizeDeps":false,"unstable_splitRouteModules":false,"unstable_subResourceIntegrity":false,"unstable_viteEnvironmentApi":false},"routeDiscovery":{"mode":"lazy","manifestPath":"/__manifest"},"ssr":true,"isSpaMode":false};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());</script><script type="module" async="">;
import * as route0 from "https://frontend-downloads.vercel.app/assets/root-tntjzWUS.js";
import * as route1 from "https://frontend-downloads.vercel.app/assets/downloads-BLfir-MM.js";
import * as route2 from "https://frontend-downloads.vercel.app/assets/downloads._platform-DZQhy5sB.js";
  window.__reactRouterManifest = {
  "entry": {
    "module": "https://frontend-downloads.vercel.app/assets/entry.client-DNywe9BI.js",
    "imports": [
      "https://frontend-downloads.vercel.app/assets/jsx-runtime-D_zvdyIk.js",
      "https://frontend-downloads.vercel.app/assets/chunk-NL6KNZEE-DMLPV0TN.js",
      "https://frontend-downloads.vercel.app/assets/index-nz3DOmyz.js"
    ],
    "css": []
  },
  "routes": {
    "root": {
      "id": "root",
      "path": "",
      "hasAction": false,
      "hasLoader": true,
      "hasClientAction": false,
      "hasClientLoader": false,
      "hasClientMiddleware": false,
      "hasErrorBoundary": false,
      "module": "https://frontend-downloads.vercel.app/assets/root-tntjzWUS.js",
      "imports": [
        "https://frontend-downloads.vercel.app/assets/jsx-runtime-D_zvdyIk.js",
        "https://frontend-downloads.vercel.app/assets/chunk-NL6KNZEE-DMLPV0TN.js",
        "https://frontend-downloads.vercel.app/assets/index-nz3DOmyz.js",
        "https://frontend-downloads.vercel.app/assets/root-CcwH-qPw.js",
        "https://frontend-downloads.vercel.app/assets/chunk-XN5RN6JW-ChTOcMGD.js",
        "https://frontend-downloads.vercel.app/assets/tiny-invariant-BCXflckp.js"
      ],
      "css": [
        "https://frontend-downloads.vercel.app/assets/root-D5oZZc3d.css"
      ]
    },
    "routes/downloads": {
      "id": "routes/downloads",
      "parentId": "root",
      "path": "downloads",
      "hasAction": false,
      "hasLoader": true,
      "hasClientAction": false,
      "hasClientLoader": false,
      "hasClientMiddleware": false,
      "hasErrorBoundary": false,
      "module": "https://frontend-downloads.vercel.app/assets/downloads-BLfir-MM.js",
      "imports": [
        "https://frontend-downloads.vercel.app/assets/chunk-NL6KNZEE-DMLPV0TN.js",
        "https://frontend-downloads.vercel.app/assets/jsx-runtime-D_zvdyIk.js",
        "https://frontend-downloads.vercel.app/assets/types-DqvH84Ze.js",
        "https://frontend-downloads.vercel.app/assets/chunk-XN5RN6JW-ChTOcMGD.js",
        "https://frontend-downloads.vercel.app/assets/tiny-invariant-BCXflckp.js",
        "https://frontend-downloads.vercel.app/assets/index-nz3DOmyz.js"
      ],
      "css": []
    },
    "routes/downloads.$platform": {
      "id": "routes/downloads.$platform",
      "parentId": "routes/downloads",
      "path": ":platform",
      "hasAction": false,
      "hasLoader": true,
      "hasClientAction": false,
      "hasClientLoader": false,
      "hasClientMiddleware": false,
      "hasErrorBoundary": false,
      "module": "https://frontend-downloads.vercel.app/assets/downloads._platform-DZQhy5sB.js",
      "imports": [
        "https://frontend-downloads.vercel.app/assets/chunk-NL6KNZEE-DMLPV0TN.js",
        "https://frontend-downloads.vercel.app/assets/jsx-runtime-D_zvdyIk.js",
        "https://frontend-downloads.vercel.app/assets/types-DqvH84Ze.js",
        "https://frontend-downloads.vercel.app/assets/index-nz3DOmyz.js",
        "https://frontend-downloads.vercel.app/assets/tiny-invariant-BCXflckp.js"
      ],
      "css": []
    },
    "routes/_index": {
      "id": "routes/_index",
      "parentId": "root",
      "index": true,
      "hasAction": false,
      "hasLoader": true,
      "hasClientAction": false,
      "hasClientLoader": false,
      "hasClientMiddleware": false,
      "hasErrorBoundary": false,
      "module": "https://frontend-downloads.vercel.app/assets/_index-C7lo8XAs.js",
      "imports": [
        "https://frontend-downloads.vercel.app/assets/chunk-NL6KNZEE-DMLPV0TN.js"
      ],
      "css": []
    }
  },
  "url": "https://frontend-downloads.vercel.app/assets/manifest-6f9a7e32.js",
  "version": "6f9a7e32"
};
  window.__reactRouterRouteModules = {"root":route0,"routes/downloads":route1,"routes/downloads.$platform":route2};

import("https://frontend-downloads.vercel.app/assets/entry.client-DNywe9BI.js");</script><!--$?--><template id="B:0"></template><!--/$--><div hidden id="S:0"><script nonce="6a131620-1d7f-4966-9acf-9c12b0204b49">window.__reactRouterContext.streamController.enqueue("[{\"_1\":2,\"_14\":-5,\"_15\":-5},\"loaderData\",{\"_3\":4,\"_9\":-5,\"_10\":11},\"root\",{\"_5\":6,\"_7\":8},\"appVersion\",\"bd78730f63a82aea5503c8ae9a33b9eb1a186fed\",\"rumEnabled\",true,\"routes/downloads\",\"routes/downloads.$platform\",{\"_12\":13},\"platform\",\"windows\",\"actionData\",\"errors\"]\n");</script><!--$?--><template id="B:1"></template><!--/$--></div><script nonce="6a131620-1d7f-4966-9acf-9c12b0204b49">$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("B:0","S:0")</script><div hidden id="S:1"><script nonce="6a131620-1d7f-4966-9acf-9c12b0204b49">window.__reactRouterContext.streamController.close();</script></div><script nonce="6a131620-1d7f-4966-9acf-9c12b0204b49">$RC("B:1","S:1")</script></body></html>