#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أداة إعادة تعيين النظام للاختبار
Reset System for Testing Tool

هذه الأداة تسمح بإعادة تعيين حالة تفعيل المميزات للمستخدمين
لاختبار رسائل التحفيز مرة أخرى
"""

import json
import os
import sys
from datetime import datetime

# إضافة مجلد المشروع إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def reset_user_feature_activation(user_id=None, feature_type=None):
    """إعادة تعيين حالة تفعيل المميزات"""
    activation_file = "user_feature_activation.json"
    
    if not os.path.exists(activation_file):
        print("✅ ملف التفعيل غير موجود - لا حاجة لإعادة التعيين")
        return True
    
    try:
        with open(activation_file, 'r', encoding='utf-8') as f:
            activation_data = json.load(f)
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف التفعيل: {e}")
        return False
    
    if user_id and feature_type:
        # إعادة تعيين ميزة محددة لمستخدم محدد
        user_id_str = str(user_id)
        if user_id_str in activation_data and feature_type in activation_data[user_id_str]:
            del activation_data[user_id_str][feature_type]
            print(f"✅ تم إعادة تعيين الميزة '{feature_type}' للمستخدم {user_id}")
        else:
            print(f"⚠️ الميزة '{feature_type}' غير مفعلة للمستخدم {user_id}")
            
    elif user_id:
        # إعادة تعيين جميع مميزات مستخدم محدد
        user_id_str = str(user_id)
        if user_id_str in activation_data:
            activation_data[user_id_str] = {}
            print(f"✅ تم إعادة تعيين جميع المميزات للمستخدم {user_id}")
        else:
            print(f"⚠️ لا توجد مميزات مفعلة للمستخدم {user_id}")
            
    elif feature_type:
        # إعادة تعيين ميزة محددة لجميع المستخدمين
        reset_count = 0
        for user_id_str in activation_data:
            if feature_type in activation_data[user_id_str]:
                del activation_data[user_id_str][feature_type]
                reset_count += 1
        print(f"✅ تم إعادة تعيين الميزة '{feature_type}' لـ {reset_count} مستخدم")
        
    else:
        # إعادة تعيين جميع المميزات لجميع المستخدمين
        user_count = len(activation_data)
        activation_data = {}
        print(f"✅ تم إعادة تعيين جميع المميزات لـ {user_count} مستخدم")
    
    # حفظ التغييرات
    try:
        with open(activation_file, 'w', encoding='utf-8') as f:
            json.dump(activation_data, f, indent=2, ensure_ascii=False)
        print("💾 تم حفظ التغييرات بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في حفظ التغييرات: {e}")
        return False

def reset_user_subscriptions(user_id=None):
    """إعادة تعيين حالة اشتراكات المستخدمين"""
    subscriptions_file = "user_subscriptions.json"
    
    if not os.path.exists(subscriptions_file):
        print("✅ ملف الاشتراكات غير موجود - لا حاجة لإعادة التعيين")
        return True
    
    try:
        with open(subscriptions_file, 'r', encoding='utf-8') as f:
            subscriptions_data = json.load(f)
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف الاشتراكات: {e}")
        return False
    
    if user_id:
        # إعادة تعيين اشتراكات مستخدم محدد
        user_id_str = str(user_id)
        if user_id_str in subscriptions_data:
            del subscriptions_data[user_id_str]
            print(f"✅ تم إعادة تعيين اشتراكات المستخدم {user_id}")
        else:
            print(f"⚠️ لا توجد اشتراكات للمستخدم {user_id}")
    else:
        # إعادة تعيين جميع الاشتراكات
        user_count = len(subscriptions_data)
        subscriptions_data = {}
        print(f"✅ تم إعادة تعيين اشتراكات {user_count} مستخدم")
    
    # حفظ التغييرات
    try:
        with open(subscriptions_file, 'w', encoding='utf-8') as f:
            json.dump(subscriptions_data, f, indent=2, ensure_ascii=False)
        print("💾 تم حفظ تغييرات الاشتراكات بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في حفظ تغييرات الاشتراكات: {e}")
        return False

def show_current_status():
    """عرض الحالة الحالية للنظام"""
    print("\n📊 الحالة الحالية للنظام:")
    print("=" * 50)
    
    # حالة التفعيل
    activation_file = "user_feature_activation.json"
    if os.path.exists(activation_file):
        try:
            with open(activation_file, 'r', encoding='utf-8') as f:
                activation_data = json.load(f)
            
            total_users = len(activation_data)
            total_activations = sum(len(user_features) for user_features in activation_data.values())
            
            print(f"🔓 حالة تفعيل المميزات:")
            print(f"   • عدد المستخدمين: {total_users}")
            print(f"   • إجمالي التفعيلات: {total_activations}")
            
            if total_users > 0:
                print(f"   • تفاصيل المستخدمين:")
                for user_id, features in activation_data.items():
                    if features:
                        feature_list = ", ".join(features.keys())
                        print(f"     - {user_id}: {feature_list}")
                        
        except Exception as e:
            print(f"❌ خطأ في قراءة ملف التفعيل: {e}")
    else:
        print("🔓 حالة تفعيل المميزات: لا توجد تفعيلات")
    
    # حالة الاشتراكات
    subscriptions_file = "user_subscriptions.json"
    if os.path.exists(subscriptions_file):
        try:
            with open(subscriptions_file, 'r', encoding='utf-8') as f:
                subscriptions_data = json.load(f)
            
            total_subscribers = len(subscriptions_data)
            print(f"\n📺 حالة الاشتراكات:")
            print(f"   • عدد المشتركين: {total_subscribers}")
            
            if total_subscribers > 0:
                print(f"   • تفاصيل الاشتراكات:")
                for user_id, subscriptions in subscriptions_data.items():
                    subscription_list = ", ".join(subscriptions.keys())
                    print(f"     - {user_id}: {subscription_list}")
                    
        except Exception as e:
            print(f"❌ خطأ في قراءة ملف الاشتراكات: {e}")
    else:
        print("📺 حالة الاشتراكات: لا توجد اشتراكات")

def interactive_menu():
    """قائمة تفاعلية لإدارة النظام"""
    while True:
        print("\n🛠️ أداة إعادة تعيين النظام للاختبار")
        print("=" * 50)
        print("1. عرض الحالة الحالية")
        print("2. إعادة تعيين جميع التفعيلات")
        print("3. إعادة تعيين جميع الاشتراكات")
        print("4. إعادة تعيين تفعيلات مستخدم محدد")
        print("5. إعادة تعيين ميزة محددة لجميع المستخدمين")
        print("6. إعادة تعيين ميزة محددة لمستخدم محدد")
        print("7. إعادة تعيين كامل للنظام")
        print("0. خروج")
        
        choice = input("\nاختر رقم العملية: ").strip()
        
        if choice == "0":
            print("👋 وداعاً!")
            break
            
        elif choice == "1":
            show_current_status()
            
        elif choice == "2":
            confirm = input("⚠️ هل أنت متأكد من إعادة تعيين جميع التفعيلات؟ (y/N): ").strip().lower()
            if confirm == 'y':
                reset_user_feature_activation()
            else:
                print("❌ تم إلغاء العملية")
                
        elif choice == "3":
            confirm = input("⚠️ هل أنت متأكد من إعادة تعيين جميع الاشتراكات؟ (y/N): ").strip().lower()
            if confirm == 'y':
                reset_user_subscriptions()
            else:
                print("❌ تم إلغاء العملية")
                
        elif choice == "4":
            user_id = input("أدخل معرف المستخدم: ").strip()
            if user_id:
                reset_user_feature_activation(user_id=user_id)
                reset_user_subscriptions(user_id=user_id)
            else:
                print("❌ معرف المستخدم مطلوب")
                
        elif choice == "5":
            feature_type = input("أدخل اسم الميزة: ").strip()
            if feature_type:
                reset_user_feature_activation(feature_type=feature_type)
            else:
                print("❌ اسم الميزة مطلوب")
                
        elif choice == "6":
            user_id = input("أدخل معرف المستخدم: ").strip()
            feature_type = input("أدخل اسم الميزة: ").strip()
            if user_id and feature_type:
                reset_user_feature_activation(user_id=user_id, feature_type=feature_type)
            else:
                print("❌ معرف المستخدم واسم الميزة مطلوبان")
                
        elif choice == "7":
            confirm = input("⚠️ هل أنت متأكد من إعادة تعيين النظام بالكامل؟ (y/N): ").strip().lower()
            if confirm == 'y':
                print("🔄 جاري إعادة تعيين النظام بالكامل...")
                reset_user_feature_activation()
                reset_user_subscriptions()
                print("✅ تم إعادة تعيين النظام بالكامل")
            else:
                print("❌ تم إلغاء العملية")
                
        else:
            print("❌ خيار غير صحيح، حاول مرة أخرى")
        
        input("\nاضغط Enter للمتابعة...")

def main():
    """الدالة الرئيسية"""
    print("🚀 أداة إعادة تعيين النظام للاختبار")
    print("=" * 60)
    print("هذه الأداة تسمح بإعادة تعيين حالة تفعيل المميزات والاشتراكات")
    print("لاختبار رسائل التحفيز مرة أخرى")
    print("=" * 60)
    
    # التحقق من وجود الملفات
    files_to_check = [
        "user_feature_activation.json",
        "user_subscriptions.json"
    ]
    
    existing_files = [f for f in files_to_check if os.path.exists(f)]
    
    if not existing_files:
        print("ℹ️ لا توجد ملفات نظام للإعادة تعيين")
        print("النظام في حالة نظيفة بالفعل")
        return
    
    print(f"📁 تم العثور على {len(existing_files)} ملف نظام:")
    for file in existing_files:
        print(f"   • {file}")
    
    # بدء القائمة التفاعلية
    interactive_menu()

if __name__ == "__main__":
    main()
