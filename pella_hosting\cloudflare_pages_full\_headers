# Headers للأمان والأداء
# Security and Performance Headers

/*
  # Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  
  # Performance Headers
  Cache-Control: public, max-age=3600
  
  # CORS Headers for API
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization

# Static Assets Caching
/style.css
  Cache-Control: public, max-age=31536000
  Content-Type: text/css

/style-templates.css
  Cache-Control: public, max-age=31536000
  Content-Type: text/css

/script.js
  Cache-Control: public, max-age=31536000
  Content-Type: application/javascript

# Images Caching
/*.jpg
  Cache-Control: public, max-age=31536000
/*.jpeg
  Cache-Control: public, max-age=31536000
/*.png
  Cache-Control: public, max-age=31536000
/*.gif
  Cache-Control: public, max-age=31536000
/*.webp
  Cache-Control: public, max-age=31536000
/*.svg
  Cache-Control: public, max-age=31536000

# API Endpoints
/api/*
  Cache-Control: no-cache, no-store, must-revalidate
  X-Robots-Tag: noindex

# Fonts
/*.woff
  Cache-Control: public, max-age=31536000
/*.woff2
  Cache-Control: public, max-age=31536000
/*.ttf
  Cache-Control: public, max-age=31536000
/*.eot
  Cache-Control: public, max-age=31536000

# HTML Pages
/index.html
  Cache-Control: public, max-age=300
  Content-Type: text/html; charset=utf-8
