# 🎮 Minecraft Mods Bot - Enhanced Version

## 📱 نظام عرض المودات المحسن مع Telegram Web App

بوت تيليجرام متقدم لنشر مودات ماين كرافت مع نظام عرض تفاصيل محسن يمكن الوصول إليه من جميع الأجهزة.

### ✨ الميزات الجديدة

- **🌐 متاح من جميع الأجهزة**: هاتف، كمبيوتر، تابلت
- **📱 Telegram Web App**: تجربة مستخدم محسنة داخل تيليجرام
- **🎨 تصميم متجاوب**: يتكيف مع جميع أحجام الشاشات
- **🔒 أمان عالي**: يعمل عبر تيليجرام فقط
- **🌍 دعم متعدد اللغات**: العربية والإنجليزية
- **⚡ سرعة عالية**: تحميل سريع وأداء محسن

### 🚀 الإعداد السريع

```bash
# 1. تحميل المشروع
git clone https://github.com/your-username/bot-telegram.git
cd bot-telegram

# 2. تشغيل الإعداد السريع
python quick_setup.py

# 3. تشغيل البوت
python run_enhanced_bot.py
```

### 📋 المتطلبات

- Python 3.8+
- Telegram Bot Token
- معرف المسؤول في تيليجرام

### 🔧 الإعداد اليدوي

#### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

#### 2. إعداد متغيرات البيئة

إنشاء ملف `.env`:

```bash
# المتطلبات الأساسية
BOT_TOKEN=your_telegram_bot_token
ADMIN_CHAT_ID=your_telegram_user_id

# الإعدادات الاختيارية
WEB_SERVER_URL=http://127.0.0.1:5001
FLASK_PORT=5000
TELEGRAM_WEB_APP_PORT=5001
```

#### 3. تشغيل البوت

```bash
# الطريقة المحسنة (موصى بها)
python run_enhanced_bot.py

# أو الطريقة التقليدية
python main.py
```

### 🌐 النشر للإنتاج

#### استخدام ngrok (للاختبار)

```bash
# تشغيل البوت
python run_enhanced_bot.py

# في terminal جديد
ngrok http 5001

# تحديث متغير البيئة
export WEB_SERVER_URL=https://your-ngrok-url.ngrok.io
```

#### النشر على Heroku

```bash
# إنشاء تطبيق
heroku create your-bot-name

# تعيين المتغيرات
heroku config:set BOT_TOKEN=your_token
heroku config:set ADMIN_CHAT_ID=your_id
heroku config:set WEB_SERVER_URL=https://your-bot-name.herokuapp.com

# النشر
git push heroku main
```

#### النشر على Railway

```bash
railway login
railway init
railway up
```

### 📱 كيفية العمل

1. **المستخدم يتلقى رسالة مود** مع زر "🎮 عرض التفاصيل"
2. **الضغط على الزر** يفتح Telegram Web App
3. **عرض تفاصيل المود** بتصميم محسن ومتجاوب
4. **التحميل المباشر** أو العودة لتيليجرام

### 🎨 واجهة المستخدم

#### الهاتف المحمول
- تصميم متجاوب بالكامل
- أزرار كبيرة سهلة اللمس
- تنقل سلس بين الصور

#### الكمبيوتر
- استغلال أفضل للمساحة
- عرض مفصل للمعلومات
- دعم الماوس والكيبورد

#### الجهاز اللوحي
- تنسيق محسن للشاشات المتوسطة
- عرض أكثر للمعلومات
- تجربة مستخدم محسنة

### 🔒 الأمان والحماية

- **التحقق من صحة البيانات**: فحص جميع المدخلات
- **حماية من الوصول غير المصرح**: التحقق من الصلاحيات
- **تشفير الروابط**: حماية البيانات الحساسة
- **تسجيل شامل**: مراقبة جميع العمليات

### 📊 الميزات المتقدمة

#### نظام الإعلانات
- عرض إعلانات مخصصة
- إحصائيات مفصلة
- تحكم كامل في العرض

#### نظام المهام
- مهام تفاعلية للمستخدمين
- تتبع الإنجاز
- مكافآت مخصصة

#### اختصار الروابط
- دعم خدمات متعددة
- إحصائيات النقرات
- روابط مخصصة

### 🛠️ التخصيص

#### تغيير التصميم

```css
/* في telegram_web_app.py */
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
}
```

#### إضافة ميزات جديدة

```python
# في telegram_web_app.py
@app.route('/api/new-feature')
def new_feature():
    return jsonify({"success": True})
```

### 📁 هيكل المشروع

```
bot-telegram/
├── main.py                    # البوت الرئيسي
├── telegram_web_app.py        # نظام Telegram Web App
├── web_server.py             # الخادم الأساسي
├── run_enhanced_bot.py       # تشغيل محسن
├── quick_setup.py            # إعداد سريع
├── supabase_client.py        # قاعدة البيانات
├── requirements.txt          # المتطلبات
├── .env                      # متغيرات البيئة
└── docs/
    ├── TELEGRAM_WEB_APP_SETUP.md
    └── README_ENHANCED.md
```

### 🧪 الاختبار

#### اختبار محلي

```bash
# تشغيل البوت
python run_enhanced_bot.py

# اختبار الصفحة
curl http://localhost:5001/telegram-mod-details?id=1&lang=ar
```

#### اختبار التكامل

1. إرسال `/start` للبوت
2. انتظار رسالة مود
3. الضغط على "عرض التفاصيل"
4. التحقق من عمل الصفحة

### 🔍 استكشاف الأخطاء

#### مشاكل شائعة

**الصفحة لا تفتح**
```bash
# التحقق من الخادم
curl http://localhost:5001/
```

**البيانات لا تظهر**
```bash
# التحقق من قاعدة البيانات
python -c "from supabase_client import get_mod_by_id; print(get_mod_by_id(1))"
```

**خطأ في المتطلبات**
```bash
pip install -r requirements.txt
```

### 📈 الإحصائيات والمراقبة

- **سجلات مفصلة**: في `bot_enhanced.log`
- **مراقبة الأداء**: استخدام الذاكرة والمعالج
- **إحصائيات المستخدمين**: عدد المشاهدات والتحميلات
- **تتبع الأخطاء**: تسجيل شامل للمشاكل

### 🔄 التحديثات المستقبلية

- [ ] دعم PWA (Progressive Web App)
- [ ] حفظ المودات المفضلة
- [ ] نظام تقييم المودات
- [ ] إشعارات push
- [ ] وضع العمل بدون إنترنت

### 🆘 الدعم والمساعدة

- **المطور**: @Kim880198
- **التوثيق**: ملفات المشروع
- **المشاكل**: إنشاء issue في GitHub
- **المجتمع**: قناة الدعم في تيليجرام

### 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

### 🙏 شكر وتقدير

- فريق Telegram لـ Web App API
- مجتمع Python و Flask
- مطوري مودات ماين كرافت
- جميع المساهمين في المشروع

---

**تم تطوير هذا النظام بواسطة**: @Kim880198  
**آخر تحديث**: ديسمبر 2024  
**الإصدار**: 2.0.0 Enhanced
