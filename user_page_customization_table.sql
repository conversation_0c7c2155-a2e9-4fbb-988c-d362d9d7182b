-- جدول إعدادات تخصيص صفحة المود
-- User Page Customization Settings Table

CREATE TABLE IF NOT EXISTS user_page_customization (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL UNIQUE,
    site_name TEXT DEFAULT 'Modetaris',
    show_all_images BOOLEAN DEFAULT true,
    download_button_text_ar TEXT DEFAULT 'تحميل المود',
    download_button_text_en TEXT DEFAULT 'Download Mod',
    open_button_text_ar TEXT DEFAULT 'فتح المود',
    open_button_text_en TEXT DEFAULT 'Open Mod',
    version_label_ar TEXT DEFAULT 'الإصدار',
    version_label_en TEXT DEFAULT 'Version',
    category_label_ar TEXT DEFAULT 'تصنيف المود',
    category_label_en TEXT DEFAULT 'Mod Category',
    description_label_ar TEXT DEFAULT 'الوصف',
    description_label_en TEXT DEFAULT 'Description',
    enable_mod_opening BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهرس على user_id لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_page_customization_user_id ON user_page_customization(user_id);

-- إضافة تعليقات للجدول والأعمدة
COMMENT ON TABLE user_page_customization IS 'جدول إعدادات تخصيص صفحة عرض المود للمستخدمين';
COMMENT ON COLUMN user_page_customization.user_id IS 'معرف المستخدم الفريد';
COMMENT ON COLUMN user_page_customization.site_name IS 'اسم الموقع المخصص في الشريط العلوي';
COMMENT ON COLUMN user_page_customization.show_all_images IS 'عرض جميع صور المود أم الصورة الرئيسية فقط';
COMMENT ON COLUMN user_page_customization.download_button_text_ar IS 'نص زر التحميل باللغة العربية';
COMMENT ON COLUMN user_page_customization.download_button_text_en IS 'نص زر التحميل باللغة الإنجليزية';
COMMENT ON COLUMN user_page_customization.open_button_text_ar IS 'نص زر الفتح باللغة العربية';
COMMENT ON COLUMN user_page_customization.open_button_text_en IS 'نص زر الفتح باللغة الإنجليزية';
COMMENT ON COLUMN user_page_customization.version_label_ar IS 'تسمية حقل الإصدار باللغة العربية';
COMMENT ON COLUMN user_page_customization.version_label_en IS 'تسمية حقل الإصدار باللغة الإنجليزية';
COMMENT ON COLUMN user_page_customization.category_label_ar IS 'تسمية حقل التصنيف باللغة العربية';
COMMENT ON COLUMN user_page_customization.category_label_en IS 'تسمية حقل التصنيف باللغة الإنجليزية';
COMMENT ON COLUMN user_page_customization.description_label_ar IS 'تسمية حقل الوصف باللغة العربية';
COMMENT ON COLUMN user_page_customization.description_label_en IS 'تسمية حقل الوصف باللغة الإنجليزية';
COMMENT ON COLUMN user_page_customization.enable_mod_opening IS 'تفعيل أو إلغاء ميزة فتح المود';

-- إضافة قيود للتحقق من صحة البيانات
ALTER TABLE user_page_customization 
ADD CONSTRAINT check_site_name_length CHECK (LENGTH(site_name) <= 50);

ALTER TABLE user_page_customization 
ADD CONSTRAINT check_button_text_length CHECK (
    LENGTH(download_button_text_ar) <= 30 AND
    LENGTH(download_button_text_en) <= 30 AND
    LENGTH(open_button_text_ar) <= 30 AND
    LENGTH(open_button_text_en) <= 30
);

ALTER TABLE user_page_customization 
ADD CONSTRAINT check_label_text_length CHECK (
    LENGTH(version_label_ar) <= 20 AND
    LENGTH(version_label_en) <= 20 AND
    LENGTH(category_label_ar) <= 20 AND
    LENGTH(category_label_en) <= 20 AND
    LENGTH(description_label_ar) <= 20 AND
    LENGTH(description_label_en) <= 20
);

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_user_page_customization_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث updated_at عند التعديل
CREATE TRIGGER trigger_update_user_page_customization_updated_at
    BEFORE UPDATE ON user_page_customization
    FOR EACH ROW
    EXECUTE FUNCTION update_user_page_customization_updated_at();

-- إدراج بعض البيانات التجريبية (اختياري)
-- INSERT INTO user_page_customization (user_id, site_name, download_button_text_ar, download_button_text_en) 
-- VALUES ('test_user_1', 'My Minecraft Mods', 'تنزيل', 'Download');
