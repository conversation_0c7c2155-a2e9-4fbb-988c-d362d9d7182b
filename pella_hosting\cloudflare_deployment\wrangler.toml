name = "modetaris-bot"
compatibility_date = "2023-12-01"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "modetaris-bot-production"

[env.production.vars]
ENVIRONMENT = "production"

# متغيرات البيئة (يجب إضافتها في لوحة تحكم Cloudflare)
# BOT_TOKEN = "your_bot_token"
# ADMIN_CHAT_ID = "your_admin_id"
# SUPABASE_URL = "your_supabase_url"
# SUPABASE_KEY = "your_supabase_key"

[[pages]]
project_name = "modetaris-bot"
production_branch = "main"
preview_branch = "*"

[pages.build]
command = "npm run build"
destination_dir = "dist"

[pages.functions]
directory = "functions"
compatibility_date = "2023-12-01"
compatibility_flags = ["nodejs_compat"]
