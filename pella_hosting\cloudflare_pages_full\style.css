/*
 * ملف التصميم لصفحة عرض المودات - محسن للهواتف
 * CSS Styles for Mod Details Page - Mobile Optimized
 * Cloudflare Pages Version
 */

body {
    font-family: 'Press Start 2P', cursive;
    background-color: #1a1a1a;
    color: white;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* تصميم الهيدر المحسن */
header {
    background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    position: relative;
}

header h1 {
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 1px;
}

/* تصميم صورة القناة في الهيدر */
header img {
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

header img:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.pixel-border {
    box-shadow: inset 0 0 0 1px #AAAAAA;
    border: 1px solid #AAAAAA;
}

.mod-header {
    background-color: #2D2D2D;
    color: white;
    padding: 15px;
    text-align: center;
}

.mod-container {
    background-color: #2D2D2D;
}

.nav-button {
    background-color: #FFA500;
    color: white;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    flex-shrink: 0;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;
}

.nav-button:hover {
    background-color: #FF8C00;
    transform: scale(1.1);
}

.nav-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    cursor: pointer;
    border: 1px solid #666;
    border-radius: 4px;
    transition: all 0.3s ease;
    opacity: 0.7;
    display: block;
    margin: 0 4px;
}

.thumbnail:hover {
    opacity: 1;
    border-color: #FFA500;
    transform: scale(1.05);
}

.thumbnail.active {
    border-color: #FFA500;
    opacity: 1;
    box-shadow: 0 0 8px rgba(255, 165, 0, 0.6);
}

.pixel-button {
    image-rendering: pixelated;
    background-color: #FFA500;
    color: white;
    border: 2px solid #FFD700;
    border-radius: 8px;
    padding: 12px 24px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 200px;
    min-height: 50px;
    font-weight: bold;
}

.pixel-button.downloading {
    background-color: #4CAF50;
    border-color: #45a049;
    animation: none;
    cursor: not-allowed;
}

.pixel-button.downloaded {
    background-color: #2196F3;
    border-color: #1976D2;
    animation: none;
}

.progress-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background-color: #4CAF50;
    width: 0%;
    transition: width 0.3s ease;
    z-index: 1;
}

.download-icon {
    display: inline-block;
    margin-right: 8px;
    font-size: 18px;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.download-success-animation {
    animation: downloadSuccess 0.6s ease-out;
}

@keyframes downloadSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); box-shadow: 0 0 20px #4CAF50; }
    100% { transform: scale(1); }
}

.pixel-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    background-color: #FF8C00;
}

.pixel-button.downloading:hover {
    transform: none;
}

.pixel-button.downloaded:hover {
    box-shadow: 0 6px 12px rgba(33, 150, 243, 0.4);
}

.notification-enter {
    animation: slideInRight 0.3s ease-out;
}

.notification-exit {
    animation: slideOutRight 0.3s ease-in;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

/* تحسين الإشعارات للهواتف */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    max-width: 300px;
    word-wrap: break-word;
}

.notification.success {
    background: #4CAF50;
}

.notification.error {
    background: #f44336;
}

.notification.warning {
    background: #ff9800;
}

.notification.info {
    background: #2196F3;
}

/* تأثيرات الصور */
.image-glow-effect {
    position: relative;
}

.image-glow-effect::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #FFA500, #FFD700, #FFA500);
    border-radius: 8px;
    z-index: -1;
    opacity: 0.7;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { opacity: 0.7; }
    to { opacity: 1; }
}

/* تأثيرات الجسيمات */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #FFD700;
    border-radius: 50%;
    animation: float 3s ease-in-out infinite;
}

.p-bottom1 {
    bottom: 10px;
    left: 20%;
    animation-delay: 0s;
}

.p-bottom2 {
    bottom: 15px;
    left: 50%;
    animation-delay: 1s;
}

.p-bottom3 {
    bottom: 8px;
    left: 80%;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0) scale(1); opacity: 1; }
    50% { transform: translate(var(--tx, 0), var(--ty, -20px)) scale(1.2); opacity: 0.8; }
}

/* تحسينات للهواتف */
@media (max-width: 768px) {
    .container {
        padding: 0.5rem;
    }
    
    .pixel-button {
        min-width: 180px;
        font-size: 14px;
        padding: 10px 20px;
    }
    
    .thumbnail {
        width: 60px;
        height: 45px;
        margin: 0 2px;
    }
    
    .nav-button {
        width: 35px;
        height: 35px;
        font-size: 18px;
    }
    
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    header h1 {
        font-size: 1.5rem;
    }
    
    .mod-title {
        font-size: 1.25rem;
    }
}

/* تحسينات إضافية للأداء */
.mod-container {
    will-change: transform;
}

.thumbnail {
    will-change: transform, opacity;
}

.pixel-button {
    will-change: transform, background-color;
}

/* تحسين التمرير */
#thumbnail-container {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

#thumbnail-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* تحسين اللمس للهواتف */
.thumbnail,
.nav-button,
.pixel-button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

/* تحسين النصوص */
.info-label {
    font-size: 12px;
    color: #FFA500;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.mod-info {
    font-size: 14px;
    line-height: 1.4;
}

/* تحسين التباعد */
.version-label,
.loader-label,
.description-label {
    font-weight: bold;
}

/* تأثيرات التحميل */
.loading-shimmer {
    background: linear-gradient(90deg, #2D2D2D 25%, #3D3D3D 50%, #2D2D2D 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}
