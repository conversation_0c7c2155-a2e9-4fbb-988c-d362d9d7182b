#!/usr/bin/env python3
"""
اختبار شامل ونهائي للبوت
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def load_env_file():
    """تحميل متغيرات البيئة من ملف .env"""
    try:
        if os.path.exists('.env'):
            with open('.env', 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key] = value
            print("✅ تم تحميل متغيرات البيئة من .env")
        else:
            print("⚠️ ملف .env غير موجود")
    except Exception as e:
        print(f"❌ خطأ في تحميل ملف .env: {e}")

def test_environment_setup():
    """اختبار إعداد البيئة"""
    print("🔧 اختبار إعداد البيئة...")
    
    required_vars = ["BOT_TOKEN", "ADMIN_CHAT_ID", "SUPABASE_URL", "SUPABASE_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        return False
    
    print("✅ جميع متغيرات البيئة المطلوبة موجودة")
    return True

def test_database_connection():
    """اختبار الاتصال مع قاعدة البيانات"""
    print("🗄️ اختبار قاعدة البيانات...")
    
    try:
        from supabase_client import get_all_mods
        
        mods = get_all_mods()
        print(f"✅ تم جلب {len(mods)} مود من قاعدة البيانات")
        
        if len(mods) > 0:
            first_mod = mods[0]
            print(f"📋 مثال: {first_mod.get('title', 'غير محدد')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

async def test_bot_connection():
    """اختبار الاتصال مع البوت"""
    print("🤖 اختبار الاتصال مع البوت...")
    
    try:
        import telegram
        from telegram.ext import Application
        
        bot_token = os.environ.get("BOT_TOKEN")
        if not bot_token:
            print("❌ BOT_TOKEN غير موجود")
            return False
        
        # إنشاء تطبيق البوت
        application = Application.builder().token(bot_token).build()
        print("✅ تم إنشاء تطبيق البوت بنجاح")
        
        # اختبار الاتصال مع Telegram
        bot_info = await application.bot.get_me()
        print(f"✅ معلومات البوت: @{bot_info.username}")
        print(f"📋 اسم البوت: {bot_info.first_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال مع البوت: {e}")
        return False

def test_user_management():
    """اختبار إدارة المستخدمين"""
    print("👥 اختبار إدارة المستخدمين...")
    
    try:
        from main import (
            load_user_channels,
            save_user_channel,
            get_user_channels,
            get_user_default_channel
        )
        
        # اختبار تحميل المستخدمين
        user_channels = load_user_channels()
        print(f"✅ تم تحميل {len(user_channels)} مستخدم")
        
        # اختبار إضافة مستخدم جديد
        test_user_id = 123456789
        test_channel_id = "@test_final_channel"
        test_interval = 60
        test_lang = "ar"
        
        success = save_user_channel(test_user_id, test_channel_id, test_interval, test_lang)
        
        if success:
            print("✅ تم حفظ مستخدم تجريبي بنجاح")
            
            # التحقق من القنوات
            user_channels_dict = get_user_channels(test_user_id)
            if test_channel_id in user_channels_dict:
                print("✅ تم العثور على القناة في قائمة المستخدم")
                
                # التحقق من القناة الافتراضية
                default_channel = get_user_default_channel(test_user_id)
                if default_channel:
                    print("✅ القناة الافتراضية موجودة")
                else:
                    print("⚠️ لا توجد قناة افتراضية")
                
                return True
            else:
                print("❌ لم يتم العثور على القناة")
                return False
        else:
            print("❌ فشل في حفظ المستخدم التجريبي")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة المستخدمين: {e}")
        return False

def test_start_command_flows():
    """اختبار تدفقات أمر /start"""
    print("🚀 اختبار تدفقات أمر /start...")
    
    try:
        from main import load_user_channels
        
        # تحميل المستخدمين
        user_channels = load_user_channels()
        
        # تحليل التدفقات
        new_users = 0
        users_with_channels = 0
        users_without_channels = 0
        
        # محاكاة مستخدم جديد
        new_user_id = "999999999"
        if new_user_id not in user_channels:
            new_users += 1
            print("✅ تدفق المستخدم الجديد: رسالة ترحيب + اختيار لغة")
        
        # تحليل المستخدمين الحاليين
        for user_id, user_data in user_channels.items():
            if isinstance(user_data, dict):
                if 'channels' in user_data:
                    channels = user_data.get('channels', {})
                    if len(channels) > 0:
                        users_with_channels += 1
                    else:
                        users_without_channels += 1
                elif user_data.get("channel_id"):
                    users_with_channels += 1
                else:
                    users_without_channels += 1
        
        print(f"✅ تدفق المستخدمين مع قنوات ({users_with_channels}): القائمة الرئيسية")
        print(f"✅ تدفق المستخدمين بدون قنوات ({users_without_channels}): طلب إضافة قناة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تدفقات /start: {e}")
        return False

def test_message_formatting():
    """اختبار تنسيق الرسائل"""
    print("💬 اختبار تنسيق الرسائل...")
    
    try:
        from main import format_mod_message
        
        # بيانات مود تجريبية
        sample_mod = {
            "name": "Test Mod Final",
            "description": "This is a final test mod for complete testing.",
            "mc_version": "1.21+",
            "download_link": "https://example.com/download",
            "category": "addons"
        }
        
        # اختبار التنسيقات المختلفة
        formats = ["classic", "modern", "elegant", "minimal", "gaming"]
        languages = ["ar", "en"]
        
        for format_type in formats:
            for lang in languages:
                formatted_message = format_mod_message(sample_mod, format_type, lang)
                if formatted_message and len(formatted_message) > 0:
                    print(f"✅ تنسيق {format_type} ({lang}): {len(formatted_message)} حرف")
                else:
                    print(f"❌ فشل في تنسيق {format_type} ({lang})")
                    return False
        
        print("✅ جميع تنسيقات الرسائل تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تنسيق الرسائل: {e}")
        return False

def test_handlers_setup():
    """اختبار إعداد معالجات البوت"""
    print("🔧 اختبار إعداد معالجات البوت...")
    
    try:
        from main import setup_handlers
        
        # محاكاة application
        class MockApplication:
            def __init__(self):
                self.handlers = []
            
            def add_handler(self, handler, group=None):
                self.handlers.append(handler)
        
        mock_app = MockApplication()
        
        # اختبار setup_handlers
        setup_handlers(mock_app)
        
        print(f"✅ تم إعداد {len(mock_app.handlers)} معالج")
        
        if len(mock_app.handlers) > 0:
            print("✅ معالجات البوت تم إعدادها بنجاح")
            return True
        else:
            print("❌ لم يتم إعداد أي معالجات")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معالجات البوت: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    print("🧪 بدء الاختبار الشامل والنهائي للبوت")
    print("=" * 70)
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # تحميل متغيرات البيئة
    load_env_file()
    
    # قائمة الاختبارات
    tests = [
        ("اختبار إعداد البيئة", test_environment_setup, False),
        ("اختبار قاعدة البيانات", test_database_connection, False),
        ("اختبار الاتصال مع البوت", test_bot_connection, True),
        ("اختبار إدارة المستخدمين", test_user_management, False),
        ("اختبار تدفقات أمر /start", test_start_command_flows, False),
        ("اختبار تنسيق الرسائل", test_message_formatting, False),
        ("اختبار معالجات البوت", test_handlers_setup, False)
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for test_name, test_function, is_async in tests:
        print(f"🧪 {test_name}...")
        try:
            if is_async:
                result = await test_function()
            else:
                result = test_function()
            
            if result:
                print(f"✅ {test_name} - نجح")
                success_count += 1
            else:
                print(f"❌ {test_name} - فشل")
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
        print()
    
    print("=" * 70)
    print(f"📊 النتائج النهائية: {success_count}/{total_count} اختبارات نجحت")
    
    if success_count == total_count:
        print("🎉 جميع الاختبارات نجحت! البوت جاهز للعمل بشكل كامل.")
        print()
        print("✅ المشاكل التي تم حلها:")
        print("   • ✅ مشكلة قاعدة البيانات (401 - Invalid API key)")
        print("   • ✅ مشكلة عدم استجابة أمر /start")
        print("   • ✅ مشكلة عدم استجابة الأزرار")
        print("   • ✅ مشكلة حفظ القنوات الجديدة")
        print("   • ✅ مشكلة عرض الواجهة الرئيسية")
        print("   • ✅ مشكلة تدفق المستخدم الجديد")
        print()
        print("🚀 التدفقات الصحيحة:")
        print("   • مستخدم جديد → رسالة ترحيب + اختيار لغة")
        print("   • مستخدم مع قنوات → القائمة الرئيسية مباشرة")
        print("   • مستخدم بدون قنوات → طلب إضافة قناة")
        print("   • بعد إكمال التسجيل → القائمة الرئيسية")
        print()
        print("🎯 البوت جاهز للاستخدام!")
        print("   يمكنك تشغيله باستخدام: python main.py")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    return success_count == total_count

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
