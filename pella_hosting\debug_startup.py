#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص مشاكل التشغيل
Startup troubleshooting tool
"""

import os
import sys
import time
import json
from pathlib import Path

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def check_python_version():
    """فحص إصدار Python"""
    print_header("فحص إصدار Python")
    
    version = sys.version_info
    print(f"📍 إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ إصدار Python قديم! يتطلب Python 3.8+")
        return False
    else:
        print("✅ إصدار Python مناسب")
        return True

def check_environment_variables():
    """فحص متغيرات البيئة"""
    print_header("فحص متغيرات البيئة")
    
    required_vars = [
        "BOT_TOKEN",
        "ADMIN_CHAT_ID", 
        "SUPABASE_URL",
        "SUPABASE_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            # إخفاء جزء من القيم الحساسة
            if "TOKEN" in var or "KEY" in var:
                display_value = value[:10] + "..." + value[-5:] if len(value) > 15 else value
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
        else:
            print(f"❌ {var}: غير موجود")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️ متغيرات مفقودة: {', '.join(missing_vars)}")
        return False
    else:
        print("\n✅ جميع متغيرات البيئة موجودة")
        return True

def check_files():
    """فحص الملفات المطلوبة"""
    print_header("فحص الملفات المطلوبة")
    
    required_files = [
        "main.py",
        "supabase_client.py",
        "web_server.py",
        "telegram_web_app.py",
        "notifications.py",
        "hosting_config.py"
    ]
    
    missing_files = []
    for file_name in required_files:
        file_path = Path(file_name)
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"✅ {file_name}: {size:,} bytes")
        else:
            print(f"❌ {file_name}: مفقود")
            missing_files.append(file_name)
    
    if missing_files:
        print(f"\n⚠️ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ جميع الملفات موجودة")
        return True

def check_imports():
    """فحص استيراد المكتبات"""
    print_header("فحص استيراد المكتبات")
    
    libraries = [
        ("telegram", "python-telegram-bot"),
        ("requests", "requests"),
        ("asyncio", "built-in"),
        ("json", "built-in"),
        ("logging", "built-in")
    ]
    
    failed_imports = []
    for lib_name, package_name in libraries:
        try:
            __import__(lib_name)
            print(f"✅ {lib_name}: متوفر")
        except ImportError as e:
            print(f"❌ {lib_name}: غير متوفر ({e})")
            failed_imports.append((lib_name, package_name))
    
    if failed_imports:
        print(f"\n⚠️ مكتبات مفقودة:")
        for lib, package in failed_imports:
            if package != "built-in":
                print(f"   pip install {package}")
        return False
    else:
        print("\n✅ جميع المكتبات متوفرة")
        return True

def test_telegram_connection():
    """اختبار الاتصال مع Telegram"""
    print_header("اختبار الاتصال مع Telegram")
    
    try:
        import requests
        
        bot_token = os.environ.get("BOT_TOKEN")
        if not bot_token:
            print("❌ BOT_TOKEN غير موجود")
            return False
        
        print("🔄 اختبار الاتصال مع Telegram API...")
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        
        response = requests.get(url, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('ok'):
                bot_info = data.get('result', {})
                print(f"✅ البوت متصل بنجاح")
                print(f"   📛 اسم البوت: {bot_info.get('first_name', 'Unknown')}")
                print(f"   🔗 معرف البوت: @{bot_info.get('username', 'Unknown')}")
                print(f"   🆔 ID: {bot_info.get('id', 'Unknown')}")
                return True
            else:
                print(f"❌ خطأ من Telegram: {data}")
                return False
        else:
            print(f"❌ فشل الاتصال: HTTP {response.status_code}")
            print(f"   📄 الرد: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_supabase_connection():
    """اختبار الاتصال مع Supabase"""
    print_header("اختبار الاتصال مع Supabase")
    
    try:
        import requests
        
        supabase_url = os.environ.get("SUPABASE_URL")
        supabase_key = os.environ.get("SUPABASE_KEY")
        
        if not supabase_url or not supabase_key:
            print("❌ إعدادات Supabase مفقودة")
            return False
        
        print("🔄 اختبار الاتصال مع Supabase...")
        
        headers = {
            'apikey': supabase_key,
            'Authorization': f'Bearer {supabase_key}',
            'Content-Type': 'application/json'
        }
        
        # اختبار الاتصال الأساسي
        url = f"{supabase_url}/rest/v1/"
        response = requests.get(url, headers=headers, timeout=15)
        
        if response.status_code == 200:
            print("✅ الاتصال مع Supabase ناجح")
            
            # اختبار جدول المودات
            mods_url = f"{supabase_url}/rest/v1/mods?limit=1"
            mods_response = requests.get(mods_url, headers=headers, timeout=10)
            
            if mods_response.status_code == 200:
                print("✅ جدول المودات متاح")
                return True
            else:
                print(f"⚠️ مشكلة في جدول المودات: {mods_response.status_code}")
                return False
        else:
            print(f"❌ فشل الاتصال مع Supabase: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال مع Supabase: {e}")
        return False

def run_full_diagnosis():
    """تشغيل التشخيص الكامل"""
    print("🏥 بدء التشخيص الشامل لمشاكل التشغيل")
    print("⏰ " + time.strftime("%Y-%m-%d %H:%M:%S"))
    
    results = {
        "python_version": check_python_version(),
        "environment_vars": check_environment_variables(), 
        "files": check_files(),
        "imports": check_imports(),
        "telegram": test_telegram_connection(),
        "supabase": test_supabase_connection()
    }
    
    # ملخص النتائج
    print_header("ملخص التشخيص")
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"📊 النتائج: {passed}/{total} اختبار نجح")
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        test_display = {
            "python_version": "إصدار Python",
            "environment_vars": "متغيرات البيئة", 
            "files": "الملفات المطلوبة",
            "imports": "استيراد المكتبات",
            "telegram": "الاتصال مع Telegram",
            "supabase": "الاتصال مع Supabase"
        }.get(test_name, test_name)
        
        print(f"   {test_display}: {status}")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت! البوت يجب أن يعمل بشكل طبيعي")
        print("💡 إذا كان البوت لا يزال معلقاً، جرب:")
        print("   - إعادة تشغيل الخادم")
        print("   - فحص لوجز الاستضافة")
        print("   - استخدام simple_start.py بدلاً من start_hosting.py")
    else:
        print(f"\n⚠️ {total - passed} اختبار فشل")
        print("🔧 يرجى حل المشاكل المذكورة أعلاه قبل تشغيل البوت")
    
    return passed == total

if __name__ == "__main__":
    run_full_diagnosis()
