# السماح بالوصول للصور
Options +Indexes
DirectoryIndex disabled

# السماح بجميع أنواع الصور
<FilesMatch "\.(jpg|jpeg|png|gif|webp|bmp)$">
    Order allow,deny
    Allow from all
</FilesMatch>

# إعدادات MIME للصور
AddType image/jpeg .jpg .jpeg
AddType image/png .png
AddType image/gif .gif
AddType image/webp .webp
AddType image/bmp .bmp

# تفعيل ضغط الصور
<IfModule mod_deflate.c>
    <FilesMatch "\.(jpg|jpeg|png|gif|webp|bmp)$">
        SetOutputFilter DEFLATE
    </FilesMatch>
</IfModule>

# إعدادات التخزين المؤقت للصور
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/bmp "access plus 1 month"
</IfModule>

# منع الوصول للملفات الحساسة
<FilesMatch "\.(php|html|htm|js|css)$">
    Order deny,allow
    Deny from all
</FilesMatch>
