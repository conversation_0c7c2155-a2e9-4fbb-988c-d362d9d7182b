#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل جميع الخوادم المطلوبة للبوت
"""

import subprocess
import time
import sys
import os
import threading
import requests
from datetime import datetime

def check_port(port):
    """فحص إذا كان المنفذ مفتوح"""
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except:
        return False

def start_ngrok():
    """تشغيل ngrok"""
    print("🚀 تشغيل ngrok...")
    
    if check_port(4040):
        print("   ✅ ngrok يعمل بالفعل")
        return True
    
    try:
        # محاولة تشغيل ngrok
        if os.path.exists("start_ngrok.bat"):
            subprocess.Popen(["start_ngrok.bat"], shell=True)
        else:
            subprocess.Popen(["ngrok", "http", "5001"])
        
        # انتظار تشغيل ngrok
        print("   ⏳ انتظار تشغيل ngrok...")
        for i in range(30):  # انتظار 30 ثانية كحد أقصى
            if check_port(4040):
                print("   ✅ تم تشغيل ngrok بنجاح")
                return True
            time.sleep(1)
            print(".", end="", flush=True)
        
        print("\n   ❌ فشل في تشغيل ngrok")
        return False
        
    except Exception as e:
        print(f"   ❌ خطأ في تشغيل ngrok: {e}")
        return False

def update_ngrok_url():
    """تحديث رابط ngrok"""
    print("\n🔄 تحديث رابط ngrok...")
    
    try:
        if os.path.exists("update_ngrok_url.py"):
            result = subprocess.run([sys.executable, "update_ngrok_url.py"], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print("   ✅ تم تحديث رابط ngrok")
                return True
            else:
                print(f"   ❌ فشل تحديث رابط ngrok: {result.stderr}")
                return False
        else:
            print("   ⚠️ ملف update_ngrok_url.py غير موجود")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في تحديث رابط ngrok: {e}")
        return False

def start_web_server():
    """تشغيل خادم الويب منفصل"""
    print("\n🌐 تشغيل خادم الويب...")
    
    if check_port(5001):
        print("   ✅ خادم الويب يعمل بالفعل على المنفذ 5001")
        return True
    
    try:
        # تشغيل خادم الويب في thread منفصل
        def run_server():
            subprocess.run([sys.executable, "web_server.py"])
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # انتظار تشغيل الخادم
        print("   ⏳ انتظار تشغيل خادم الويب...")
        for i in range(15):  # انتظار 15 ثانية
            if check_port(5001):
                print("   ✅ تم تشغيل خادم الويب بنجاح")
                return True
            time.sleep(1)
            print(".", end="", flush=True)
        
        print("\n   ❌ فشل في تشغيل خادم الويب")
        return False
        
    except Exception as e:
        print(f"   ❌ خطأ في تشغيل خادم الويب: {e}")
        return False

def start_main_bot():
    """تشغيل البوت الرئيسي"""
    print("\n🤖 تشغيل البوت الرئيسي...")
    
    try:
        # تشغيل البوت
        subprocess.run([sys.executable, "main.py"])
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البوت: {e}")

def test_all_services():
    """اختبار جميع الخدمات"""
    print("\n🧪 اختبار جميع الخدمات...")
    
    services = [
        (4040, "ngrok API"),
        (5001, "Telegram Web App Server"),
        (5000, "Flask Web Server (اختياري)")
    ]
    
    all_working = True
    
    for port, service_name in services:
        if check_port(port):
            print(f"   ✅ {service_name}: يعمل")
            
            # اختبار HTTP للخوادم
            if port in [5000, 5001]:
                try:
                    response = requests.get(f'http://127.0.0.1:{port}/', timeout=3)
                    if response.status_code == 200:
                        print(f"      🌐 HTTP: يستجيب بشكل طبيعي")
                    else:
                        print(f"      ⚠️ HTTP: يستجيب بخطأ {response.status_code}")
                except:
                    print(f"      ❌ HTTP: لا يستجيب")
                    all_working = False
        else:
            print(f"   ❌ {service_name}: لا يعمل")
            if port != 5000:  # Flask server اختياري
                all_working = False
    
    # اختبار ngrok tunnels
    try:
        response = requests.get('http://localhost:4040/api/tunnels', timeout=3)
        if response.status_code == 200:
            data = response.json()
            tunnels = data.get('tunnels', [])
            if tunnels:
                print(f"   ✅ ngrok tunnels: {len(tunnels)} نفق نشط")
                for tunnel in tunnels:
                    public_url = tunnel.get('public_url', 'N/A')
                    print(f"      🔗 {public_url}")
            else:
                print(f"   ⚠️ ngrok: يعمل ولكن لا توجد أنفاق")
                all_working = False
    except:
        print(f"   ❌ ngrok: لا يمكن الوصول للـ API")
        all_working = False
    
    return all_working

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل جميع خوادم البوت...")
    print("=" * 60)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # الخطوة 1: تشغيل ngrok
    ngrok_success = start_ngrok()
    
    if ngrok_success:
        # الخطوة 2: تحديث رابط ngrok
        update_ngrok_url()
        
        # الخطوة 3: تشغيل خادم الويب
        web_server_success = start_web_server()
        
        if web_server_success:
            # الخطوة 4: اختبار الخدمات
            time.sleep(2)  # انتظار قصير
            services_working = test_all_services()
            
            if services_working:
                print("\n🎉 جميع الخدمات تعمل بشكل طبيعي!")
                print("\n📋 يمكنك الآن:")
                print("   1. استخدام البوت في Telegram")
                print("   2. الضغط على أزرار صفحات المودات")
                print("   3. تصفح تفاصيل المودات")
                
                # الخطوة 5: تشغيل البوت الرئيسي
                print("\n" + "=" * 60)
                start_main_bot()
            else:
                print("\n❌ بعض الخدمات لا تعمل بشكل صحيح")
                print("راجع الأخطاء أعلاه وحاول مرة أخرى")
        else:
            print("\n❌ فشل في تشغيل خادم الويب")
            print("تأكد من عدم استخدام المنفذ 5001 من برنامج آخر")
    else:
        print("\n❌ فشل في تشغيل ngrok")
        print("تأكد من تثبيت ngrok وإضافته لـ PATH")
    
    print("\n📞 للدعم: @Kim880198")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التشغيل بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
    
    input("\n📱 اضغط Enter للخروج...")
