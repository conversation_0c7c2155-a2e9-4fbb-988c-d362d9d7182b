-- إصلاح مشكلة أساليب التصميم - إضافة الحقول المفقودة
-- Fix Style Templates Issue - Add Missing Fields
-- تشغيل هذا الكود في SQL Editor في Supabase
-- Run this code in SQL Editor in Supabase

-- التحقق من وجود الجدول
SELECT 'Checking table existence...' as status;

-- إضافة الحقول المفقودة واحد تلو الآخر
-- Adding missing fields one by one

-- 1. style_template
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'page_customization_settings' AND column_name = 'style_template') THEN
        ALTER TABLE page_customization_settings ADD COLUMN style_template TEXT DEFAULT 'default';
        RAISE NOTICE 'Added style_template column';
    ELSE
        RAISE NOTICE 'style_template column already exists';
    END IF;
END $$;

-- 2. custom_accent_color
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'page_customization_settings' AND column_name = 'custom_accent_color') THEN
        ALTER TABLE page_customization_settings ADD COLUMN custom_accent_color TEXT;
        RAISE NOTICE 'Added custom_accent_color column';
    ELSE
        RAISE NOTICE 'custom_accent_color column already exists';
    END IF;
END $$;

-- 3. custom_card_color
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'page_customization_settings' AND column_name = 'custom_card_color') THEN
        ALTER TABLE page_customization_settings ADD COLUMN custom_card_color TEXT;
        RAISE NOTICE 'Added custom_card_color column';
    ELSE
        RAISE NOTICE 'custom_card_color column already exists';
    END IF;
END $$;

-- 4. custom_shadow_color
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'page_customization_settings' AND column_name = 'custom_shadow_color') THEN
        ALTER TABLE page_customization_settings ADD COLUMN custom_shadow_color TEXT;
        RAISE NOTICE 'Added custom_shadow_color column';
    ELSE
        RAISE NOTICE 'custom_shadow_color column already exists';
    END IF;
END $$;

-- 5. custom_font_family
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'page_customization_settings' AND column_name = 'custom_font_family') THEN
        ALTER TABLE page_customization_settings ADD COLUMN custom_font_family TEXT DEFAULT 'Press Start 2P';
        RAISE NOTICE 'Added custom_font_family column';
    ELSE
        RAISE NOTICE 'custom_font_family column already exists';
    END IF;
END $$;

-- 6. custom_font_size
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'page_customization_settings' AND column_name = 'custom_font_size') THEN
        ALTER TABLE page_customization_settings ADD COLUMN custom_font_size TEXT DEFAULT 'medium';
        RAISE NOTICE 'Added custom_font_size column';
    ELSE
        RAISE NOTICE 'custom_font_size column already exists';
    END IF;
END $$;

-- 7. enable_animations
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'page_customization_settings' AND column_name = 'enable_animations') THEN
        ALTER TABLE page_customization_settings ADD COLUMN enable_animations BOOLEAN DEFAULT true;
        RAISE NOTICE 'Added enable_animations column';
    ELSE
        RAISE NOTICE 'enable_animations column already exists';
    END IF;
END $$;

-- 8. enable_gradients
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'page_customization_settings' AND column_name = 'enable_gradients') THEN
        ALTER TABLE page_customization_settings ADD COLUMN enable_gradients BOOLEAN DEFAULT true;
        RAISE NOTICE 'Added enable_gradients column';
    ELSE
        RAISE NOTICE 'enable_gradients column already exists';
    END IF;
END $$;

-- 9. enable_shadows
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'page_customization_settings' AND column_name = 'enable_shadows') THEN
        ALTER TABLE page_customization_settings ADD COLUMN enable_shadows BOOLEAN DEFAULT true;
        RAISE NOTICE 'Added enable_shadows column';
    ELSE
        RAISE NOTICE 'enable_shadows column already exists';
    END IF;
END $$;

-- 10. layout_style
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'page_customization_settings' AND column_name = 'layout_style') THEN
        ALTER TABLE page_customization_settings ADD COLUMN layout_style TEXT DEFAULT 'modern';
        RAISE NOTICE 'Added layout_style column';
    ELSE
        RAISE NOTICE 'layout_style column already exists';
    END IF;
END $$;

-- 11. border_radius
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'page_customization_settings' AND column_name = 'border_radius') THEN
        ALTER TABLE page_customization_settings ADD COLUMN border_radius TEXT DEFAULT 'medium';
        RAISE NOTICE 'Added border_radius column';
    ELSE
        RAISE NOTICE 'border_radius column already exists';
    END IF;
END $$;

-- إضافة القيود (Constraints) بعد إضافة الحقول
-- Adding constraints after adding fields

-- قيود style_template
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'style_template_check') THEN
        ALTER TABLE page_customization_settings 
        ADD CONSTRAINT style_template_check 
        CHECK (style_template IN ('default', 'telegram', 'tiktok', 'classic', 'professional', 'custom'));
        RAISE NOTICE 'Added style_template constraint';
    ELSE
        RAISE NOTICE 'style_template constraint already exists';
    END IF;
END $$;

-- قيود custom_font_size
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'custom_font_size_check') THEN
        ALTER TABLE page_customization_settings 
        ADD CONSTRAINT custom_font_size_check 
        CHECK (custom_font_size IN ('small', 'medium', 'large', 'extra-large'));
        RAISE NOTICE 'Added custom_font_size constraint';
    ELSE
        RAISE NOTICE 'custom_font_size constraint already exists';
    END IF;
END $$;

-- قيود layout_style
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'layout_style_check') THEN
        ALTER TABLE page_customization_settings 
        ADD CONSTRAINT layout_style_check 
        CHECK (layout_style IN ('modern', 'compact', 'spacious', 'minimal'));
        RAISE NOTICE 'Added layout_style constraint';
    ELSE
        RAISE NOTICE 'layout_style constraint already exists';
    END IF;
END $$;

-- قيود border_radius
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'border_radius_check') THEN
        ALTER TABLE page_customization_settings 
        ADD CONSTRAINT border_radius_check 
        CHECK (border_radius IN ('none', 'small', 'medium', 'large', 'round'));
        RAISE NOTICE 'Added border_radius constraint';
    ELSE
        RAISE NOTICE 'border_radius constraint already exists';
    END IF;
END $$;

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_page_customization_style_template ON page_customization_settings(style_template);
CREATE INDEX IF NOT EXISTS idx_page_customization_layout_style ON page_customization_settings(layout_style);

-- التحقق من نجاح العملية
SELECT 'Database update completed successfully!' as status;

-- عرض الحقول الجديدة للتأكد
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'page_customization_settings' 
  AND column_name IN ('style_template', 'custom_accent_color', 'layout_style', 'enable_animations', 'custom_font_family')
ORDER BY column_name;

-- عرض عدد السجلات في الجدول
SELECT COUNT(*) as total_records FROM page_customization_settings;

SELECT 'Please test the style templates now!' as message;
