#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات البوت - التحقق من عمل نظام إدارة القنوات بشكل صحيح
"""

import json
import os
import sys
import logging
from datetime import datetime

# إعداد المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الدوال من البوت
try:
    from main import (
        load_user_channels, save_user_channel, add_user_channel,
        get_user_default_channel, verify_channel_data_integrity,
        get_user_channels, USER_CHANNELS_FILE
    )
    print("✅ تم استيراد الدوال بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد الدوال: {e}")
    sys.exit(1)

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_bot_fixes.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BotTester:
    """فئة اختبار البوت"""
    
    def __init__(self):
        self.test_user_id = 123456789
        self.test_channel_id = "-1001234567890"
        self.backup_file = None
        
    def backup_data(self):
        """نسخ احتياطي من البيانات الحالية"""
        try:
            if os.path.exists(USER_CHANNELS_FILE):
                self.backup_file = f"{USER_CHANNELS_FILE}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                with open(USER_CHANNELS_FILE, 'r', encoding='utf-8') as src:
                    with open(self.backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
                logger.info(f"✅ تم إنشاء نسخة احتياطية: {self.backup_file}")
            else:
                logger.info("ℹ️ لا يوجد ملف بيانات للنسخ الاحتياطي")
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
    
    def restore_data(self):
        """استعادة البيانات من النسخة الاحتياطية"""
        try:
            if self.backup_file and os.path.exists(self.backup_file):
                with open(self.backup_file, 'r', encoding='utf-8') as src:
                    with open(USER_CHANNELS_FILE, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
                logger.info("✅ تم استعادة البيانات من النسخة الاحتياطية")
                os.remove(self.backup_file)
                logger.info("✅ تم حذف النسخة الاحتياطية")
            else:
                logger.info("ℹ️ لا توجد نسخة احتياطية للاستعادة")
        except Exception as e:
            logger.error(f"❌ خطأ في استعادة البيانات: {e}")
    
    def test_add_channel(self):
        """اختبار إضافة قناة جديدة"""
        logger.info("🧪 اختبار إضافة قناة جديدة...")
        
        try:
            # إضافة قناة جديدة
            result = save_user_channel(
                user_id=self.test_user_id,
                channel_id=self.test_channel_id,
                publish_interval_minutes=60,
                lang='ar'
            )
            
            if result:
                logger.info("✅ تم حفظ القناة بنجاح")
                
                # التحقق من وجود البيانات
                user_channels = load_user_channels()
                user_data = user_channels.get(str(self.test_user_id), {})
                channels = user_data.get('channels', {})
                
                if self.test_channel_id in channels:
                    logger.info("✅ تم العثور على القناة في البيانات المحفوظة")
                    
                    # التحقق من سلامة البيانات
                    if verify_channel_data_integrity(self.test_user_id, self.test_channel_id):
                        logger.info("✅ تم التحقق من سلامة البيانات")
                        return True
                    else:
                        logger.error("❌ فشل في التحقق من سلامة البيانات")
                        return False
                else:
                    logger.error("❌ لم يتم العثور على القناة في البيانات المحفوظة")
                    return False
            else:
                logger.error("❌ فشل في حفظ القناة")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار إضافة القناة: {e}")
            return False
    
    def test_duplicate_channel(self):
        """اختبار إضافة قناة مكررة"""
        logger.info("🧪 اختبار إضافة قناة مكررة...")
        
        try:
            # محاولة إضافة نفس القناة مرة أخرى
            result = save_user_channel(
                user_id=self.test_user_id,
                channel_id=self.test_channel_id,
                publish_interval_minutes=120,  # فاصل زمني مختلف
                lang='en'  # لغة مختلفة
            )
            
            if result:
                logger.info("✅ تم تحديث إعدادات القناة الموجودة")
                
                # التحقق من التحديث
                user_channels = load_user_channels()
                user_data = user_channels.get(str(self.test_user_id), {})
                channel_data = user_data.get('channels', {}).get(self.test_channel_id, {})
                
                if channel_data.get('publish_interval') == 120:
                    logger.info("✅ تم تحديث الفاصل الزمني بنجاح")
                    return True
                else:
                    logger.error(f"❌ لم يتم تحديث الفاصل الزمني: {channel_data.get('publish_interval')}")
                    return False
            else:
                logger.error("❌ فشل في تحديث القناة الموجودة")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار القناة المكررة: {e}")
            return False
    
    def test_get_default_channel(self):
        """اختبار الحصول على القناة الافتراضية"""
        logger.info("🧪 اختبار الحصول على القناة الافتراضية...")
        
        try:
            default_channel = get_user_default_channel(self.test_user_id)
            
            if default_channel:
                logger.info(f"✅ تم العثور على القناة الافتراضية: {default_channel.get('channel_id')}")
                
                if default_channel.get('channel_id') == self.test_channel_id:
                    logger.info("✅ القناة الافتراضية صحيحة")
                    return True
                else:
                    logger.error(f"❌ القناة الافتراضية غير صحيحة: {default_channel.get('channel_id')}")
                    return False
            else:
                logger.error("❌ لم يتم العثور على قناة افتراضية")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار القناة الافتراضية: {e}")
            return False
    
    def test_data_persistence(self):
        """اختبار استمرارية البيانات"""
        logger.info("🧪 اختبار استمرارية البيانات...")
        
        try:
            # إعادة تحميل البيانات
            user_channels_before = load_user_channels()
            
            # محاكاة إعادة تشغيل البوت
            user_channels_after = load_user_channels()
            
            # مقارنة البيانات
            if user_channels_before == user_channels_after:
                logger.info("✅ البيانات محفوظة بشكل صحيح")
                return True
            else:
                logger.error("❌ البيانات غير متطابقة بعد إعادة التحميل")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار استمرارية البيانات: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        logger.info("🚀 بدء اختبارات البوت...")
        
        # إنشاء نسخة احتياطية
        self.backup_data()
        
        tests = [
            ("إضافة قناة جديدة", self.test_add_channel),
            ("إضافة قناة مكررة", self.test_duplicate_channel),
            ("الحصول على القناة الافتراضية", self.test_get_default_channel),
            ("استمرارية البيانات", self.test_data_persistence)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"🧪 اختبار: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                if test_func():
                    logger.info(f"✅ نجح اختبار: {test_name}")
                    passed += 1
                else:
                    logger.error(f"❌ فشل اختبار: {test_name}")
                    failed += 1
            except Exception as e:
                logger.error(f"❌ خطأ في اختبار {test_name}: {e}")
                failed += 1
        
        # النتائج النهائية
        logger.info(f"\n{'='*50}")
        logger.info("📊 نتائج الاختبارات:")
        logger.info(f"✅ نجح: {passed}")
        logger.info(f"❌ فشل: {failed}")
        logger.info(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        logger.info(f"{'='*50}")
        
        # استعادة البيانات
        self.restore_data()
        
        return passed, failed

def main():
    """الدالة الرئيسية"""
    print("🤖 اختبار إصلاحات بوت Minecraft Mods")
    print("="*50)
    
    tester = BotTester()
    passed, failed = tester.run_all_tests()
    
    if failed == 0:
        print("\n🎉 جميع الاختبارات نجحت! البوت جاهز للاستخدام.")
        return 0
    else:
        print(f"\n⚠️ فشل {failed} اختبار. يرجى مراجعة السجلات.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
