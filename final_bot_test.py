#!/usr/bin/env python3
"""
اختبار نهائي شامل للبوت
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def load_env_file():
    """تحميل متغيرات البيئة من ملف .env"""
    try:
        if os.path.exists('.env'):
            with open('.env', 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key] = value
            print("✅ تم تحميل متغيرات البيئة من .env")
        else:
            print("⚠️ ملف .env غير موجود")
    except Exception as e:
        print(f"❌ خطأ في تحميل ملف .env: {e}")

def test_database_connection():
    """اختبار الاتصال مع قاعدة البيانات"""
    print("🗄️ اختبار قاعدة البيانات...")
    
    try:
        from supabase_client import get_all_mods
        
        mods = get_all_mods()
        print(f"✅ تم جلب {len(mods)} مود من قاعدة البيانات")
        
        if len(mods) > 0:
            first_mod = mods[0]
            print(f"📋 مثال: {first_mod.get('title', 'غير محدد')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

async def test_bot_initialization():
    """اختبار تهيئة البوت"""
    print("🤖 اختبار تهيئة البوت...")
    
    try:
        import telegram
        from telegram.ext import Application
        
        bot_token = os.environ.get("BOT_TOKEN")
        if not bot_token:
            print("❌ BOT_TOKEN غير موجود")
            return False
        
        # إنشاء تطبيق البوت
        application = Application.builder().token(bot_token).build()
        print("✅ تم إنشاء تطبيق البوت بنجاح")
        
        # اختبار الاتصال مع Telegram
        bot_info = await application.bot.get_me()
        print(f"✅ معلومات البوت: @{bot_info.username}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تهيئة البوت: {e}")
        return False

def test_start_command():
    """اختبار دالة /start"""
    print("🚀 اختبار دالة /start...")
    
    try:
        from main import start, get_user_lang, load_user_channels
        
        print("✅ تم استيراد دوال البوت بنجاح")
        
        # اختبار دوال مساعدة
        test_user_id = "123456789"
        lang = get_user_lang(test_user_id)
        print(f"✅ get_user_lang: {lang}")
        
        user_channels = load_user_channels()
        print(f"✅ load_user_channels: {len(user_channels)} مستخدم")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دالة /start: {e}")
        return False

def test_channel_management():
    """اختبار إدارة القنوات"""
    print("📺 اختبار إدارة القنوات...")
    
    try:
        from main import (
            save_user_channel,
            get_user_channels,
            get_user_default_channel
        )
        
        # اختبار إضافة قناة
        test_user_id = 999888777
        test_channel_id = "@final_test_channel"
        test_interval = 90
        test_lang = "ar"
        
        success = save_user_channel(test_user_id, test_channel_id, test_interval, test_lang)
        
        if success:
            print("✅ تم حفظ القناة بنجاح")
            
            # التحقق من القنوات
            user_channels = get_user_channels(test_user_id)
            if test_channel_id in user_channels:
                print("✅ تم العثور على القناة")
                
                # التحقق من القناة الافتراضية
                default_channel = get_user_default_channel(test_user_id)
                if default_channel:
                    print("✅ القناة الافتراضية موجودة")
                else:
                    print("⚠️ لا توجد قناة افتراضية")
                
                return True
            else:
                print("❌ لم يتم العثور على القناة")
                return False
        else:
            print("❌ فشل في حفظ القناة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة القنوات: {e}")
        return False

def test_handlers_setup():
    """اختبار إعداد handlers"""
    print("🔧 اختبار إعداد handlers...")
    
    try:
        from main import setup_handlers
        
        # محاكاة application
        class MockApplication:
            def __init__(self):
                self.handlers = []
            
            def add_handler(self, handler, group=None):
                self.handlers.append(handler)
        
        mock_app = MockApplication()
        
        # اختبار setup_handlers
        setup_handlers(mock_app)
        
        print(f"✅ تم إعداد {len(mock_app.handlers)} handler")
        
        if len(mock_app.handlers) > 0:
            print("✅ handlers تم إعدادها بنجاح")
            return True
        else:
            print("❌ لم يتم إعداد أي handlers")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار handlers: {e}")
        return False

def test_message_formatting():
    """اختبار تنسيق الرسائل"""
    print("💬 اختبار تنسيق الرسائل...")
    
    try:
        from main import format_mod_message
        
        # بيانات مود تجريبية
        sample_mod = {
            "name": "Test Mod",
            "description": "This is a test mod for testing purposes.",
            "mc_version": "1.21+",
            "download_link": "https://example.com/download",
            "category": "addons"
        }
        
        # اختبار التنسيقات المختلفة
        formats = ["classic", "modern", "elegant", "minimal", "gaming"]
        
        for format_type in formats:
            formatted_message = format_mod_message(sample_mod, format_type, "ar")
            if formatted_message and len(formatted_message) > 0:
                print(f"✅ تنسيق {format_type}: {len(formatted_message)} حرف")
            else:
                print(f"❌ فشل في تنسيق {format_type}")
                return False
        
        print("✅ جميع تنسيقات الرسائل تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تنسيق الرسائل: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    print("🧪 بدء الاختبار النهائي الشامل للبوت")
    print("=" * 60)
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # تحميل متغيرات البيئة
    load_env_file()
    
    # قائمة الاختبارات
    tests = [
        ("اختبار قاعدة البيانات", test_database_connection, False),
        ("اختبار تهيئة البوت", test_bot_initialization, True),
        ("اختبار دالة /start", test_start_command, False),
        ("اختبار إدارة القنوات", test_channel_management, False),
        ("اختبار إعداد handlers", test_handlers_setup, False),
        ("اختبار تنسيق الرسائل", test_message_formatting, False)
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for test_name, test_function, is_async in tests:
        print(f"🧪 {test_name}...")
        try:
            if is_async:
                result = await test_function()
            else:
                result = test_function()
            
            if result:
                print(f"✅ {test_name} - نجح")
                success_count += 1
            else:
                print(f"❌ {test_name} - فشل")
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
        print()
    
    print("=" * 60)
    print(f"📊 النتائج النهائية: {success_count}/{total_count} اختبارات نجحت")
    
    if success_count == total_count:
        print("🎉 جميع الاختبارات نجحت! البوت جاهز للعمل بشكل كامل.")
        print()
        print("✅ المشاكل التي تم حلها:")
        print("   • مشكلة قاعدة البيانات (401 - Invalid API key)")
        print("   • مشكلة عدم استجابة أمر /start")
        print("   • مشكلة عدم استجابة الأزرار")
        print("   • مشكلة حفظ القنوات الجديدة")
        print("   • مشكلة عرض الواجهة الرئيسية")
        print()
        print("🚀 يمكنك الآن تشغيل البوت باستخدام:")
        print("   python main.py")
        print("   أو")
        print("   start_bot_fixed.bat")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    return success_count == total_count

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
