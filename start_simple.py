#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل البوت البسيط
Simple Bot Startup
"""

import os
import sys
import asyncio
import logging
import subprocess
import platform

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)

logger = logging.getLogger(__name__)

def run_simple_fix():
    """تشغيل الإصلاح البسيط"""
    print("تشغيل الإصلاح البسيط...")
    
    try:
        result = subprocess.run([sys.executable, "simple_fix.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("تم تطبيق الإصلاحات بنجاح")
            return True
        else:
            print(f"بعض الإصلاحات فشلت: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("انتهت مهلة الإصلاحات، المتابعة...")
        return False
    except FileNotFoundError:
        print("ملف الإصلاح غير موجود، المتابعة بدون إصلاحات...")
        return False
    except Exception as e:
        print(f"خطأ في الإصلاحات: {e}")
        return False

def check_basic_requirements():
    """فحص المتطلبات الأساسية"""
    print("فحص المتطلبات الأساسية...")
    
    # فحص الملفات المطلوبة
    required_files = ["main.py", ".env"]
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    print("جميع الملفات المطلوبة موجودة")
    
    # فحص متغيرات البيئة
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        required_vars = ["BOT_TOKEN"]
        missing_vars = []
        
        for var in required_vars:
            if not os.environ.get(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"متغيرات بيئة مفقودة: {', '.join(missing_vars)}")
            return False
        
        print("متغيرات البيئة الأساسية موجودة")
        
    except ImportError:
        print("مكتبة python-dotenv غير مثبتة")
    
    return True

def test_internet():
    """اختبار الاتصال بالإنترنت"""
    print("اختبار الاتصال بالإنترنت...")
    
    try:
        import socket
        
        try:
            socket.create_connection(("8.8.8.8", 53), timeout=10)
            print("الاتصال بالإنترنت يعمل")
            return True
        except:
            print("لا يوجد اتصال بالإنترنت")
            return False
        
    except Exception as e:
        print(f"خطأ في اختبار الاتصال: {e}")
        return False

async def start_bot():
    """تشغيل البوت"""
    print("بدء تشغيل البوت...")
    
    try:
        # محاولة استيراد وتشغيل البوت
        if os.path.exists("run_bot_fixed.py"):
            print("استخدام run_bot_fixed.py...")
            from run_bot_fixed import run_bot_with_retry
            await run_bot_with_retry()
        else:
            print("استخدام main.py...")
            from main import main
            await main()
            
    except KeyboardInterrupt:
        print("\nتم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"\nخطأ في تشغيل البوت: {e}")
        raise

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("بوت نشر مودات ماين كرافت - التشغيل البسيط")
    print("Minecraft Mods Bot - Simple Startup")
    print("=" * 50)
    
    # الخطوات الأساسية
    steps = [
        ("تطبيق الإصلاحات البسيطة", run_simple_fix),
        ("فحص المتطلبات الأساسية", check_basic_requirements),
        ("اختبار الاتصال بالإنترنت", test_internet),
    ]
    
    # تنفيذ الخطوات
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        try:
            if not step_func():
                print(f"فشل في {step_name}")
                print("يرجى مراجعة الأخطاء أعلاه وإصلاحها")
                
                # السماح بالمتابعة في بعض الحالات
                if step_name == "تطبيق الإصلاحات البسيطة":
                    print("المتابعة بدون إصلاحات...")
                    continue
                else:
                    return
        except Exception as e:
            print(f"خطأ في {step_name}: {e}")
            if step_name != "تطبيق الإصلاحات البسيطة":
                return
    
    print("\n" + "=" * 50)
    print("الفحوصات الأساسية اكتملت!")
    print("بدء تشغيل البوت...")
    print("اضغط Ctrl+C لإيقاف البوت")
    print("=" * 50)
    
    # تشغيل البوت
    try:
        asyncio.run(start_bot())
    except KeyboardInterrupt:
        print("\n\nتم إيقاف البوت بأمان")
    except Exception as e:
        print(f"\n\nخطأ في تشغيل البوت: {e}")
        print("\nنصائح لحل المشكلة:")
        print("   1. تأكد من صحة إعدادات .env")
        print("   2. تأكد من استقرار الاتصال بالإنترنت")
        print("   3. جرب إعادة تشغيل الكمبيوتر")
        print("   4. جرب تشغيل البوت كمسؤول")

if __name__ == "__main__":
    main()
