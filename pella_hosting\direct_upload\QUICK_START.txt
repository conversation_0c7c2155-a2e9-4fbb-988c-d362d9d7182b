🚀 دليل البدء السريع - Cloudflare Pages
===============================================

📋 الخطوات السريعة:

1️⃣ اذهب إلى: https://dash.cloudflare.com
2️⃣ اختر "Pages" من القائمة
3️⃣ اضغط "Create a project"
4️⃣ اختر "Upload assets"
5️⃣ اسحب جميع ملفات مجلد direct_upload
6️⃣ اضغط "Deploy site"
7️⃣ انتظر 2-3 دقائق
8️⃣ احصل على رابط موقعك!

📁 الملفات المطلوبة:
- index.html (الصفحة الرئيسية)
- _redirects (إعدادات التوجيه)
- api/health.html (فحص API)
- api/mod.html (صفحة المود)
- api/download.html (صفحة التحميل)

🔗 روابط مهمة بعد النشر:
- الرئيسية: https://your-site.pages.dev
- فحص API: https://your-site.pages.dev/api/health
- مود تجريبي: https://your-site.pages.dev/api/mod/1
- تحميل: https://your-site.pages.dev/api/download/1

🤖 ربط البوت:
استبدل YOUR_SITE_URL برابط موقعك في هذا الرابط:
https://api.telegram.org/bot7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4/setWebhook?url=https://YOUR_SITE_URL/api/webhook

✅ مميزات مجانية:
- 500 نشر شهرياً
- 100,000 طلب يومياً
- SSL مجاني
- CDN عالمي
- 100MB مساحة

⚠️ ملاحظات:
- الملفات جاهزة للرفع المباشر
- لا تحتاج GitHub
- يعمل فوراً بعد الرفع
- مناسب لصفحات التحميل البسيطة

📖 للتفاصيل الكاملة: راجع UPLOAD_GUIDE.md
