# ميزة اختصار الروابط - URL Shortener Feature

## نظرة عامة - Overview

تم إضافة ميزة جديدة لاختصار الروابط إلى بوت مودات ماين كرافت. هذه الميزة تتيح للمستخدمين استخدام خدمات اختصار الروابط المختلفة لتحويل روابط التحميل الأصلية إلى روابط مختصرة، مما يوفر إمكانيات ربح إضافية من خلال النقرات على الروابط المختصرة.

A new URL shortener feature has been added to the Minecraft mods bot. This feature allows users to use various URL shortening services to convert original download links to shortened links, providing additional monetization opportunities through clicks on shortened links.

## الميزات الرئيسية - Key Features

### 1. دعم متعدد الخدمات - Multi-Service Support
- دعم مواقع اختصار الروابط المختلفة مثل Linkjust، Short.link، TinyURL، وغيرها
- إمكانية إضافة أي خدمة اختصار تدعم API
- Support for various URL shortening services like Linkjust, Short.link, TinyURL, and others
- Ability to add any shortening service that supports API

### 2. إعدادات مرنة - Flexible Settings
- تفعيل/إيقاف الميزة حسب الحاجة
- إعداد API مخصص لكل مستخدم
- اختبار API للتأكد من عمله
- Enable/disable feature as needed
- Custom API setup for each user
- API testing to ensure functionality

### 3. إحصائيات مفصلة - Detailed Statistics
- تتبع عدد الروابط المختصرة
- إحصائيات النقرات (إذا دعمتها الخدمة)
- تاريخ آخر اختصار رابط
- Track number of shortened URLs
- Click statistics (if supported by service)
- Last shortening date

## كيفية الاستخدام - How to Use

### 1. الوصول للميزة - Accessing the Feature
1. افتح البوت وانتقل إلى القائمة الرئيسية
2. اختر "💰 الربح من البوت"
3. اختر "🔗 اختصار الروابط"

### 2. إعداد API - API Setup
1. اختر "🔧 إعداد API"
2. أرسل بيانات API بالتنسيق التالي:
   ```
   API_URL|API_KEY|SERVICE_NAME
   ```
   مثال:
   ```
   https://linkjust.com/api|98bb78a7fde8f7a5cab6549613690ffa7c39ee24|linkjust
   ```

### 3. اختبار API - Testing API
1. بعد إعداد API، اختر "🧪 اختبار API"
2. سيقوم البوت باختبار الإعدادات والتأكد من عملها

### 4. تفعيل الميزة - Enabling the Feature
1. اختر "🔄 تفعيل اختصار الروابط"
2. ستصبح الميزة نشطة وسيتم اختصار روابط التحميل تلقائياً

## الخدمات المدعومة - Supported Services

### Linkjust
- **API URL**: `https://linkjust.com/api`
- **التنسيق**: `https://linkjust.com/api|YOUR_API_KEY|linkjust`
- **المميزات**: سهولة الاستخدام، إحصائيات مفصلة

### Short.link
- **API URL**: `https://short.link/api`
- **التنسيق**: `https://short.link/api|YOUR_API_KEY|shortlink`
- **المميزات**: معدلات ربح جيدة

### TinyURL
- **API URL**: `https://tinyurl.com/api-create.php`
- **التنسيق**: `https://tinyurl.com/api-create.php|YOUR_TOKEN|tinyurl`
- **المميزات**: خدمة موثوقة ومعروفة

### خدمات أخرى - Other Services
يمكن إضافة أي خدمة اختصار روابط تدعم API بنفس التنسيق المطلوب.

## الملفات المضافة - Added Files

### 1. قاعدة البيانات - Database
- `user_url_shortener_settings_table.sql`: جدول إعدادات اختصار الروابط

### 2. الدوال الجديدة - New Functions
#### في main.py:
- `shorten_url_with_api()`: اختصار رابط باستخدام API
- `get_download_url_for_mod()`: الحصول على رابط التحميل (مختصر أو أصلي)
- `url_shortener_menu()`: قائمة اختصار الروابط
- `shortener_toggle()`: تفعيل/إيقاف الميزة
- `shortener_set_api()`: إعداد API
- `shortener_test_api()`: اختبار API
- `shortener_examples()`: عرض أمثلة الخدمات
- `handle_shortener_api_input()`: معالجة إدخال بيانات API

#### في supabase_client.py:
- `get_user_url_shortener_settings()`: جلب إعدادات المستخدم
- `set_user_url_shortener_settings()`: حفظ إعدادات المستخدم
- `delete_user_url_shortener_settings()`: حذف إعدادات المستخدم
- `update_url_shortener_stats()`: تحديث الإحصائيات
- `get_url_shortener_stats()`: جلب الإحصائيات العامة

## معالجة الأخطاء - Error Handling

### أخطاء API شائعة - Common API Errors
- **انتهاء المهلة الزمنية**: "Request timeout - API server is slow"
- **خطأ في الاتصال**: "Connection error - Cannot reach API server"
- **استجابة غير صالحة**: "Invalid response format"
- **خطأ HTTP**: "HTTP [status_code]: [error_message]"

### التعامل مع الأخطاء - Error Handling
- في حالة فشل اختصار الرابط، يتم استخدام الرابط الأصلي
- تسجيل جميع الأخطاء في ملف السجل
- إشعار المستخدم بالأخطاء المهمة

## الأمان - Security

### حماية بيانات API - API Data Protection
- تشفير مفاتيح API في قاعدة البيانات
- التحقق من صحة روابط API
- حد أدنى لطول مفتاح API (10 أحرف)

### التحقق من الصحة - Validation
- التحقق من تنسيق رابط API
- التحقق من طول مفتاح API
- التحقق من اسم الخدمة المدعومة

## الإحصائيات - Statistics

### إحصائيات المستخدم - User Statistics
- إجمالي الروابط المختصرة
- إجمالي النقرات (إذا دعمتها الخدمة)
- تاريخ آخر اختصار رابط
- حالة الميزة (مفعلة/معطلة)

### الإحصائيات العامة - Global Statistics
- إجمالي المستخدمين
- المستخدمين النشطين
- إجمالي الروابط المختصرة
- إجمالي النقرات
- الخدمة الأكثر استخداماً

## التحديثات المستقبلية - Future Updates

### ميزات مخططة - Planned Features
- دعم المزيد من خدمات الاختصار
- إحصائيات أكثر تفصيلاً
- تقارير دورية للمستخدمين
- إعدادات متقدمة للاختصار

### تحسينات - Improvements
- تحسين أداء API
- واجهة مستخدم محسنة
- دعم اختصار روابط متعددة
- نظام تنبيهات للأخطاء

## الدعم الفني - Technical Support

### متطلبات النظام - System Requirements
- Python 3.8+
- مكتبة requests
- قاعدة بيانات Supabase
- اتصال إنترنت مستقر

### استكشاف الأخطاء - Troubleshooting
1. **الميزة لا تعمل**: تأكد من إعداد API بشكل صحيح
2. **فشل اختصار الروابط**: اختبر API والتأكد من صحة المفتاح
3. **بطء في الاستجابة**: تحقق من سرعة الإنترنت وحالة خادم API

### الحصول على المساعدة - Getting Help
- راجع ملف السجل للأخطاء التفصيلية
- تأكد من صحة إعدادات API
- اتصل بدعم خدمة الاختصار المستخدمة

---

## ملاحظات مهمة - Important Notes

⚠️ **تحذير**: تأكد من الحصول على مفتاح API صالح من خدمة الاختصار قبل الاستخدام.

⚠️ **Warning**: Make sure to obtain a valid API key from the shortening service before use.

📝 **ملاحظة**: بعض خدمات الاختصار قد تتطلب تنسيق مختلف قليلاً للAPI.

📝 **Note**: Some shortening services may require a slightly different API format.

🔒 **أمان**: لا تشارك مفتاح API الخاص بك مع أي شخص آخر.

🔒 **Security**: Do not share your API key with anyone else.
