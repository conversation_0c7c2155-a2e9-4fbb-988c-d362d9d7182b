<?php
/**
 * سكريبت نشر وإعداد الموقع
 * Deployment and Setup Script
 */

define('INCLUDED', true);
require_once 'config.php';

// التحقق من الصلاحيات
if (!isset($_GET['setup']) || $_GET['setup'] !== 'true') {
    die('Access denied. Add ?setup=true to run setup.');
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد موقع عرض المودات</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .status-ok { color: #10b981; }
        .status-error { color: #ef4444; }
        .status-warning { color: #f59e0b; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-6 max-w-4xl">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-center mb-6 text-gray-800">
                🚀 إعداد موقع عرض مودات ماين كرافت
            </h1>
            
            <div class="space-y-6">
                <!-- فحص البيئة -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h2 class="text-xl font-semibold mb-4">🔍 فحص البيئة</h2>
                    <div class="space-y-2">
                        <?php
                        $env_check = checkEnvironment();
                        if ($env_check === true) {
                            echo '<div class="status-ok">✅ جميع متطلبات البيئة متوفرة</div>';
                        } else {
                            echo '<div class="status-error">❌ مشاكل في البيئة:</div>';
                            foreach ($env_check as $error) {
                                echo '<div class="status-error ml-4">• ' . htmlspecialchars($error) . '</div>';
                            }
                        }
                        
                        // فحص إصدار PHP
                        echo '<div class="status-ok">✅ إصدار PHP: ' . PHP_VERSION . '</div>';
                        
                        // فحص الإضافات المطلوبة
                        $extensions = ['curl', 'json', 'mbstring'];
                        foreach ($extensions as $ext) {
                            if (extension_loaded($ext)) {
                                echo '<div class="status-ok">✅ إضافة ' . $ext . ' متوفرة</div>';
                            } else {
                                echo '<div class="status-error">❌ إضافة ' . $ext . ' غير متوفرة</div>';
                            }
                        }
                        ?>
                    </div>
                </div>

                <!-- فحص الملفات -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h2 class="text-xl font-semibold mb-4">📁 فحص الملفات</h2>
                    <div class="space-y-2">
                        <?php
                        $required_files = [
                            'index.php' => 'الصفحة الرئيسية',
                            'api.php' => 'واجهة برمجة التطبيقات',
                            'style.css' => 'ملف التصميم',
                            'script.js' => 'ملف الجافاسكريبت',
                            '.htaccess' => 'إعدادات الخادم',
                            'config.php' => 'ملف الإعدادات'
                        ];
                        
                        foreach ($required_files as $file => $description) {
                            if (file_exists($file)) {
                                $size = filesize($file);
                                echo '<div class="status-ok">✅ ' . $description . ' (' . $file . ') - ' . number_format($size) . ' بايت</div>';
                            } else {
                                echo '<div class="status-error">❌ ' . $description . ' (' . $file . ') غير موجود</div>';
                            }
                        }
                        ?>
                    </div>
                </div>

                <!-- فحص الاتصال بقاعدة البيانات -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h2 class="text-xl font-semibold mb-4">🗄️ فحص قاعدة البيانات</h2>
                    <div class="space-y-2">
                        <?php
                        // اختبار الاتصال بـ Supabase
                        $test_url = SUPABASE_URL . '/rest/v1/minemods?limit=1';
                        $headers = [
                            'apikey: ' . SUPABASE_KEY,
                            'Authorization: Bearer ' . SUPABASE_KEY
                        ];
                        
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $test_url);
                        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                        
                        $response = curl_exec($ch);
                        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        $error = curl_error($ch);
                        curl_close($ch);
                        
                        if ($http_code === 200) {
                            $data = json_decode($response, true);
                            echo '<div class="status-ok">✅ الاتصال بقاعدة البيانات ناجح</div>';
                            echo '<div class="status-ok">✅ عدد المودات المتاحة: ' . count($data) . '</div>';
                        } else {
                            echo '<div class="status-error">❌ فشل الاتصال بقاعدة البيانات</div>';
                            echo '<div class="status-error">كود الخطأ: ' . $http_code . '</div>';
                            if ($error) {
                                echo '<div class="status-error">تفاصيل الخطأ: ' . htmlspecialchars($error) . '</div>';
                            }
                        }
                        ?>
                    </div>
                </div>

                <!-- فحص الصلاحيات -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h2 class="text-xl font-semibold mb-4">🔐 فحص الصلاحيات</h2>
                    <div class="space-y-2">
                        <?php
                        // فحص صلاحيات الكتابة
                        $write_test_dirs = ['.', 'logs'];
                        foreach ($write_test_dirs as $dir) {
                            if (!is_dir($dir)) {
                                mkdir($dir, 0755, true);
                            }
                            
                            if (is_writable($dir)) {
                                echo '<div class="status-ok">✅ صلاحية الكتابة في مجلد ' . $dir . '</div>';
                            } else {
                                echo '<div class="status-error">❌ لا توجد صلاحية كتابة في مجلد ' . $dir . '</div>';
                            }
                        }
                        
                        // اختبار إنشاء ملف
                        $test_file = 'test_write.tmp';
                        if (file_put_contents($test_file, 'test') !== false) {
                            echo '<div class="status-ok">✅ يمكن إنشاء الملفات</div>';
                            unlink($test_file);
                        } else {
                            echo '<div class="status-error">❌ لا يمكن إنشاء الملفات</div>';
                        }
                        ?>
                    </div>
                </div>

                <!-- معلومات الخادم -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h2 class="text-xl font-semibold mb-4">🖥️ معلومات الخادم</h2>
                    <div class="space-y-2">
                        <?php
                        echo '<div>🌐 اسم الخادم: ' . ($_SERVER['SERVER_NAME'] ?? 'غير محدد') . '</div>';
                        echo '<div>📍 عنوان IP: ' . ($_SERVER['SERVER_ADDR'] ?? 'غير محدد') . '</div>';
                        echo '<div>🔧 برنامج الخادم: ' . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد') . '</div>';
                        echo '<div>📂 مجلد الجذر: ' . ($_SERVER['DOCUMENT_ROOT'] ?? 'غير محدد') . '</div>';
                        echo '<div>🕒 المنطقة الزمنية: ' . date_default_timezone_get() . '</div>';
                        echo '<div>💾 حد الذاكرة: ' . ini_get('memory_limit') . '</div>';
                        echo '<div>⏱️ حد وقت التنفيذ: ' . ini_get('max_execution_time') . ' ثانية</div>';
                        ?>
                    </div>
                </div>

                <!-- روابط الاختبار -->
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h2 class="text-xl font-semibold mb-4">🔗 روابط الاختبار</h2>
                    <div class="space-y-2">
                        <?php
                        $base_url = 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
                        $test_mod_id = 1; // يمكن تغييره حسب البيانات المتاحة
                        
                        echo '<div><a href="' . $base_url . '/" class="text-blue-600 hover:underline" target="_blank">🏠 الصفحة الرئيسية</a></div>';
                        echo '<div><a href="' . $base_url . '/?id=' . $test_mod_id . '&lang=ar" class="text-blue-600 hover:underline" target="_blank">🎮 اختبار عرض مود (عربي)</a></div>';
                        echo '<div><a href="' . $base_url . '/?id=' . $test_mod_id . '&lang=en" class="text-blue-600 hover:underline" target="_blank">🎮 اختبار عرض مود (إنجليزي)</a></div>';
                        echo '<div><a href="' . $base_url . '/api.php?path=/mod&id=' . $test_mod_id . '" class="text-blue-600 hover:underline" target="_blank">🔌 اختبار API</a></div>';
                        ?>
                    </div>
                </div>

                <!-- تعليمات التكامل مع البوت -->
                <div class="bg-green-50 p-4 rounded-lg">
                    <h2 class="text-xl font-semibold mb-4">🤖 تكامل مع البوت</h2>
                    <div class="space-y-2">
                        <p class="text-gray-700">لتحديث البوت ليستخدم هذا الموقع:</p>
                        <ol class="list-decimal list-inside space-y-1 text-gray-700">
                            <li>افتح ملف <code class="bg-gray-200 px-1 rounded">main.py</code> في البوت</li>
                            <li>ابحث عن دالة <code class="bg-gray-200 px-1 rounded">_build_mod_post_content</code></li>
                            <li>غيّر رابط الموقع إلى: <code class="bg-gray-200 px-1 rounded"><?php echo $base_url; ?></code></li>
                            <li>أعد تشغيل البوت</li>
                        </ol>
                        <div class="mt-4 p-3 bg-yellow-100 rounded">
                            <strong>مثال على الرابط:</strong><br>
                            <code class="text-sm"><?php echo $base_url; ?>/?id=MOD_ID&lang=ar&user_id=USER_ID&channel=CHANNEL_ID</code>
                        </div>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="flex space-x-4 justify-center">
                    <a href="/" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                        🏠 الذهاب للموقع
                    </a>
                    <button onclick="location.reload()" class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600">
                        🔄 إعادة الفحص
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحديث تلقائي كل 30 ثانية
        setTimeout(() => {
            const reloadBtn = document.querySelector('button[onclick="location.reload()"]');
            if (reloadBtn) {
                reloadBtn.textContent = '🔄 تحديث تلقائي...';
                reloadBtn.style.backgroundColor = '#10b981';
                setTimeout(() => location.reload(), 2000);
            }
        }, 30000);
    </script>
</body>
</html>
