#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل البوت للاستضافة المجانية
Optimized Bot Starter for Free Hosting
"""

import os
import sys
import logging
import asyncio
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent))

# تحميل إعدادات الاستضافة أولاً
try:
    from hosting_config import get_hosting_config, test_hosting_setup
    hosting_config = get_hosting_config()
    print("✅ تم تحميل إعدادات الاستضافة")
except ImportError as e:
    print(f"❌ فشل تحميل إعدادات الاستضافة: {e}")
    sys.exit(1)

# إعداد التسجيل المحسن للاستضافة
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)

# تقليل مستوى التسجيل للمكتبات الخارجية لتوفير الموارد
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("telegram").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)

logger = logging.getLogger(__name__)

def setup_hosting_environment():
    """إعداد بيئة الاستضافة"""
    try:
        # تطبيق إعدادات الاستضافة
        hosting_config.apply_to_environment()
        
        # إعدادات إضافية للاستضافة المجانية
        os.environ["PYTHONUNBUFFERED"] = "1"  # لضمان ظهور اللوجز فوراً
        os.environ["PYTHONDONTWRITEBYTECODE"] = "1"  # لتوفير مساحة التخزين
        
        # تحسينات الذاكرة
        import gc
        gc.set_threshold(700, 10, 10)  # تحسين جمع القمامة
        
        logger.info("✅ تم إعداد بيئة الاستضافة")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل إعداد بيئة الاستضافة: {e}")
        return False

def check_dependencies():
    """فحص المتطلبات المطلوبة"""
    required_modules = [
        'telegram',
        'requests',
        'asyncio'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        logger.error(f"❌ مكتبات مفقودة: {', '.join(missing_modules)}")
        return False
    
    logger.info("✅ جميع المتطلبات متوفرة")
    return True

async def start_bot():
    """تشغيل البوت مع التحسينات"""
    try:
        # استيراد البوت الرئيسي
        from main import main as bot_main
        
        logger.info("🚀 بدء تشغيل البوت...")
        
        # تشغيل البوت
        await bot_main()
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        raise

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل البوت للاستضافة المجانية")
    print("="*50)
    
    # 1. فحص المتطلبات
    if not check_dependencies():
        print("❌ فشل فحص المتطلبات")
        sys.exit(1)
    
    # 2. إعداد البيئة
    if not setup_hosting_environment():
        print("❌ فشل إعداد البيئة")
        sys.exit(1)
    
    # 3. اختبار الاتصال
    print("\n🔍 اختبار الاتصال مع قاعدة البيانات...")
    if not hosting_config.test_connection():
        print("❌ فشل الاتصال مع قاعدة البيانات")
        print("يرجى التحقق من إعدادات Supabase")
        sys.exit(1)
    
    # 4. طباعة ملخص الإعدادات
    hosting_config.print_config_summary()
    
    # 5. تشغيل البوت
    try:
        print("\n🤖 تشغيل البوت...")
        asyncio.run(start_bot())
    except Exception as e:
        logger.error(f"❌ فشل تشغيل البوت: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
