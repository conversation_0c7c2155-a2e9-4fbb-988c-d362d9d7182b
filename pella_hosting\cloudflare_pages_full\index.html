<!DOCTYPE html>
<html id="html-root" lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; font-src 'self' data: https://cdn.jsdelivr.net https://fonts.googleapis.com https://fonts.gstatic.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://telegram.org; img-src 'self' data: blob: https: http:; connect-src 'self' https: http: ws: wss:;">
    <title id="page-title">تفاصيل المود - Modetaris</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='.9em' font-size='90'%3E🎮%3C/text%3E%3C/svg%3E">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Roboto:wght@300;400;500;700&family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="style-templates.css">

    <!-- Custom Style Template CSS -->
    <style id="dynamic-styles">
        :root {
            --bg-color: #1a1a1a;
            --header-color: #FFA500;
            --text-color: #ffffff;
            --button-color: #FFA500;
            --border-color: #333333;
            --accent-color: #FFD700;
            --card-color: #2D2D2D;
            --shadow-color: rgba(0,0,0,0.3);
            --font-family: 'Press Start 2P', monospace;
            --font-size: medium;
            --border-radius: 8px;
        }

        /* Default Style */
        body {
            background: var(--bg-color);
            font-family: var(--font-family);
            color: var(--text-color);
        }
        .mod-container {
            background: var(--card-color);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
        }
        .pixel-button {
            background: var(--button-color);
            border-radius: var(--border-radius);
        }

        /* Loading animation */
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--accent-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #ff4444;
            color: white;
            padding: 20px;
            border-radius: var(--border-radius);
            text-align: center;
            margin: 20px;
        }

        .preview-notice {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 15px;
            border-radius: var(--border-radius);
            margin: 20px;
            text-align: center;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.8; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body class="style-template-default" data-style="default" data-layout="modern">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <p class="text-white mt-4">جاري تحميل بيانات المود...</p>
        </div>
    </div>

    <!-- Error Screen -->
    <div id="error-screen" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center">
        <div class="error-message max-w-md">
            <h2 class="text-xl mb-4">❌ خطأ في التحميل</h2>
            <p id="error-message-text">حدث خطأ أثناء تحميل بيانات المود</p>
            <button onclick="location.reload()" class="pixel-button mt-4 px-6 py-2">
                🔄 إعادة المحاولة
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div id="main-content" class="hidden">
        <!-- Header -->
        <header class="bg-yellow-500 text-white p-4 relative">
            <div class="flex justify-between items-center">
                <!-- صورة القناة - اليسار -->
                <div id="logo-left" class="flex-shrink-0 hidden">
                    <img id="channel-logo-left" src="" alt="Channel Logo" class="w-10 h-10 rounded-full border-2 border-white shadow-lg">
                </div>
                <div class="flex-shrink-0 w-10" id="spacer-left"></div>

                <!-- اسم الموقع - الوسط -->
                <div class="flex-grow text-center">
                    <h1 id="site-name" class="font-bold text-2xl">Modetaris</h1>
                </div>

                <!-- صورة القناة - اليمين -->
                <div id="logo-right" class="flex-shrink-0 hidden">
                    <img id="channel-logo-right" src="" alt="Channel Logo" class="w-10 h-10 rounded-full border-2 border-white shadow-lg">
                </div>
                <div class="flex-shrink-0 w-10" id="spacer-right"></div>
            </div>
        </header>

        <div class="container mx-auto p-4 pb-24">
            <!-- Preview Notice -->
            <div id="preview-notice" class="preview-notice hidden">
                👀 <strong>وضع المعاينة:</strong> هذه معاينة لكيفية ظهور صفحة المود مع التخصيصات التي قمت بها.
            </div>

            <!-- Mod Title -->
            <div class="mod-header pixel-border mb-4">
                <h1 id="mod-title" class="text-2xl mod-title">جاري التحميل...</h1>
            </div>

            <!-- Main Mod Image Display -->
            <div class="mod-container mb-4 p-1 image-glow-effect">
                <div class="relative w-full bg-gray-700" style="padding-top: 56.25%;">
                    <img id="main-mod-image" class="absolute inset-0 w-full h-full object-cover" src="" alt="صورة المود">
                </div>
                <div class="particle p-bottom1" style="--tx: 2px; --ty: -25px;"></div>
                <div class="particle p-bottom2" style="--tx: 0px; --ty: -30px;"></div>
                <div class="particle p-bottom3" style="--tx: -2px; --ty: -28px;"></div>
            </div>

            <!-- Thumbnail Navigation and Controls -->
            <div class="flex items-center justify-center mb-4 px-2">
                <button id="prev-image" class="nav-button pixel-border mr-2" style="flex-shrink: 0;">‹</button>
                <div id="thumbnail-container" class="flex flex-grow justify-center items-center overflow-x-auto" style="scroll-behavior: smooth; -webkit-overflow-scrolling: touch;">
                    <!-- Thumbnails will be inserted here by JavaScript -->
                </div>
                <button id="next-image" class="nav-button pixel-border ml-2" style="flex-shrink: 0;">›</button>
            </div>

            <!-- Mod Info -->
            <div class="grid grid-cols-2 gap-4 mb-6">
                <div class="mod-container pixel-border p-4">
                    <p class="info-label version-label">الإصدار</p>
                    <p id="mod-version" class="mod-info mod-version">جاري التحميل...</p>
                </div>
                <div class="mod-container pixel-border p-4">
                    <p class="info-label loader-label">تصنيف المود</p>
                    <p id="mod-category" class="mod-info mod-category">جاري التحميل...</p>
                </div>
            </div>

            <!-- Mod Description -->
            <div class="mod-container pixel-border p-4 mb-6">
                <p class="info-label text-center description-label">الوصف</p>
                <p id="mod-description" class="mod-info mt-2 text-center mod-description">جاري تحميل الوصف...</p>
            </div>

            <!-- Ad Overlay -->
            <div id="ad-overlay" class="fixed inset-0 bg-black bg-opacity-75 z-60 hidden flex items-center justify-center">
                <div class="bg-white rounded-lg p-6 max-w-md mx-4 text-center relative">
                    <div id="ad-content" class="mb-4">
                        <h3 class="text-lg font-bold text-gray-800 mb-2">إعلان</h3>
                        <p class="text-gray-600 mb-4">سيتم فتح الإعلان في نافذة جديدة...</p>
                    </div>
                    <button id="close-ad-btn" class="bg-red-500 text-white px-4 py-2 rounded hidden" onclick="closeAd()">
                        إغلاق
                    </button>
                    <div id="countdown" class="text-sm text-gray-500 mt-2"></div>
                </div>
            </div>

            <!-- Download Button -->
            <div class="fixed bottom-0 left-0 right-0 p-4 z-50 flex justify-center">
                <button id="download-button" class="pixel-button text-xl py-3 px-10" style="position: relative; overflow: hidden;" onclick="handleDownload()">
                    <div class="progress-bar" id="progress-bar"></div>
                    <span class="download-icon" id="download-icon">📥</span>
                    <span id="download-text">تحميل المود</span>
                </button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });
    </script>
</body>
</html>
