<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - الصفحة غير موجودة</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Press Start 2P', cursive;
            background-color: #1a1a1a;
            color: white;
        }
        .pixel-border {
            box-shadow: inset 0 0 0 1px #AAAAAA;
            border: 1px solid #AAAAAA;
        }
        .error-container {
            background-color: #2D2D2D;
        }
        .pixel-button {
            background-color: #FFA500;
            color: white;
            border: 2px solid #FFD700;
            padding: 10px 20px;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            cursor: pointer;
            box-shadow: inset -4px -4px 0 0 #d27e00,
                       inset 4px 4px 0 0 #ffcb6b;
            transition: all 0.1s;
        }
        .pixel-button:hover {
            transform: translateY(-2px);
            box-shadow: inset -4px -4px 0 0 #d27e00,
                       inset 4px 4px 0 0 #ffcb6b,
                       0 0 10px #FFD700;
        }
        .error-icon {
            font-size: 120px;
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-30px);
            }
            60% {
                transform: translateY(-15px);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="bg-yellow-500 text-white p-4 flex justify-center items-center">
        <div class="font-bold text-2xl">Modetaris</div>
    </header>

    <div class="container mx-auto p-4 min-h-screen flex items-center justify-center">
        <div class="error-container pixel-border p-8 text-center max-w-md">
            <div class="error-icon mb-6">🎮</div>
            <h1 class="text-4xl mb-4 text-red-400">404</h1>
            <h2 class="text-xl mb-4">الصفحة غير موجودة</h2>
            <p class="text-sm mb-6 leading-relaxed">
                عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
            </p>
            <div class="space-y-4">
                <a href="/" class="pixel-button block">
                    🏠 العودة للصفحة الرئيسية
                </a>
                <button onclick="history.back()" class="pixel-button block w-full">
                    ⬅️ العودة للصفحة السابقة
                </button>
            </div>
            <div class="mt-6 text-xs opacity-75">
                <p>إذا كنت تعتقد أن هذا خطأ، يرجى المحاولة مرة أخرى لاحقاً</p>
            </div>
        </div>
    </div>

    <script>
        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            const errorIcon = document.querySelector('.error-icon');
            
            errorIcon.addEventListener('click', function() {
                this.style.animation = 'none';
                setTimeout(() => {
                    this.style.animation = 'bounce 2s infinite';
                }, 10);
            });
        });
    </script>
</body>
</html>
