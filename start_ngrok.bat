@echo off
echo 🚀 Starting ngrok tunnel for Telegram Bot...
echo.

REM تشغيل ngrok على المنفذ 5001
echo Starting ngrok on port 5001...
ngrok http 5001

REM إذا فشل ngrok، عرض رسالة خطأ
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Failed to start ngrok!
    echo.
    echo 💡 Make sure:
    echo    1. ngrok is installed
    echo    2. You have internet connection
    echo    3. Port 5001 is not in use
    echo.
    echo 📥 To install ngrok:
    echo    1. Go to https://ngrok.com/download
    echo    2. Download and extract ngrok.exe
    echo    3. Add ngrok.exe to your PATH
    echo.
    pause
)
