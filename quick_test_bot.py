#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لاستجابة البوت
"""

import asyncio
import logging
import sys
import os

# إعداد المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('quick_test_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

async def test_bot_startup():
    """اختبار بدء تشغيل البوت"""
    try:
        logger.info("🚀 بدء اختبار تشغيل البوت...")
        
        # استيراد الدوال الأساسية
        from main import (
            admin_delete_user_data_menu,
            admin_delete_user_by_id_handler,
            admin_delete_user_from_list_handler,
            admin_confirm_delete_user_data,
            admin_execute_delete_user_data,
            handle_admin_actions
        )
        
        logger.info("✅ تم استيراد دوال حذف بيانات المستخدم بنجاح")
        
        # اختبار استيراد دوال قاعدة البيانات
        import supabase_client
        logger.info("✅ تم استيراد supabase_client بنجاح")
        
        # اختبار استيراد الدوال الرئيسية
        from main import load_user_channels, save_json_file, get_user_lang
        logger.info("✅ تم استيراد الدوال الرئيسية بنجاح")
        
        # اختبار تحميل البيانات
        user_channels = load_user_channels()
        logger.info(f"✅ تم تحميل بيانات القنوات: {len(user_channels)} مستخدم")
        
        logger.info("🎉 جميع الاختبارات نجحت! البوت جاهز للتشغيل")
        return True
        
    except ImportError as e:
        logger.error(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ عام: {e}")
        return False

def check_bot_token():
    """التحقق من وجود Bot Token"""
    try:
        from main import TOKEN
        if TOKEN and TOKEN != "YOUR_BOT_TOKEN_HERE":
            logger.info("✅ Bot Token موجود")
            return True
        else:
            logger.error("❌ Bot Token غير موجود أو غير صحيح")
            return False
    except ImportError:
        logger.error("❌ لا يمكن استيراد TOKEN")
        return False

def check_admin_id():
    """التحقق من معرف الأدمن"""
    try:
        from main import YOUR_CHAT_ID
        if YOUR_CHAT_ID and YOUR_CHAT_ID != "YOUR_CHAT_ID_HERE":
            logger.info(f"✅ معرف الأدمن موجود: {YOUR_CHAT_ID}")
            return True
        else:
            logger.error("❌ معرف الأدمن غير موجود أو غير صحيح")
            return False
    except ImportError:
        logger.error("❌ لا يمكن استيراد YOUR_CHAT_ID")
        return False

def check_callback_handlers():
    """التحقق من معالجات الأزرار"""
    try:
        import re
        
        # قراءة ملف main.py للتحقق من المعالجات
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن pattern CallbackQueryHandler
        pattern_match = re.search(r'CallbackQueryHandler\(handle_admin_actions.*?pattern=r"([^"]+)"', content, re.DOTALL)
        
        if pattern_match:
            pattern = pattern_match.group(1)
            logger.info("✅ تم العثور على pattern CallbackQueryHandler")
            
            # اختبار أزرار حذف بيانات المستخدم
            test_buttons = [
                "admin_delete_user_data",
                "delete_user_by_id",
                "delete_user_from_list",
                "confirm_delete_user_123456789"
            ]
            
            compiled_pattern = re.compile(pattern)
            working_buttons = 0
            
            for button in test_buttons:
                if compiled_pattern.match(button):
                    working_buttons += 1
                    logger.info(f"✅ الزر يعمل: {button}")
                else:
                    logger.error(f"❌ الزر لا يعمل: {button}")
            
            if working_buttons == len(test_buttons):
                logger.info("✅ جميع أزرار حذف بيانات المستخدم تعمل")
                return True
            else:
                logger.error(f"❌ {len(test_buttons) - working_buttons} أزرار لا تعمل")
                return False
        else:
            logger.error("❌ لم يتم العثور على pattern CallbackQueryHandler")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في فحص معالجات الأزرار: {e}")
        return False

def check_files_exist():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        'main.py',
        'supabase_client.py',
        'network_config.py'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            logger.info(f"✅ الملف موجود: {file}")
        else:
            logger.error(f"❌ الملف مفقود: {file}")
            missing_files.append(file)
    
    return len(missing_files) == 0

async def main():
    """الدالة الرئيسية"""
    logger.info("🔧 بدء اختبار سريع للبوت")
    logger.info("="*60)
    
    tests = [
        ("فحص الملفات المطلوبة", check_files_exist),
        ("فحص Bot Token", check_bot_token),
        ("فحص معرف الأدمن", check_admin_id),
        ("فحص معالجات الأزرار", check_callback_handlers),
        ("اختبار بدء التشغيل", test_bot_startup)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 {test_name}:")
        logger.info("-" * 40)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                logger.info(f"✅ نجح: {test_name}")
                passed += 1
            else:
                logger.error(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            logger.error(f"❌ خطأ في {test_name}: {e}")
            failed += 1
    
    logger.info(f"\n{'='*60}")
    logger.info("📊 نتائج الاختبار السريع:")
    logger.info(f"✅ نجح: {passed}")
    logger.info(f"❌ فشل: {failed}")
    logger.info(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    logger.info(f"{'='*60}")
    
    if failed == 0:
        logger.info("\n🎉 جميع الاختبارات نجحت!")
        logger.info("🚀 يمكنك الآن تشغيل البوت بأمان:")
        logger.info("   python main.py")
        return 0
    else:
        logger.error(f"\n⚠️ فشل {failed} اختبار.")
        logger.error("🔧 يرجى إصلاح المشاكل قبل تشغيل البوت.")
        
        if failed == 1 and not check_bot_token():
            logger.info("\n💡 نصيحة: تأكد من إضافة Bot Token الصحيح في main.py")
        
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n💥 خطأ فادح: {e}")
        sys.exit(1)
