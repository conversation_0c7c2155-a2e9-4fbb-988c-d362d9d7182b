#!/usr/bin/env python3
"""
🔒 تفعيل HTTPS خطوة بخطوة
🌐 Enable HTTPS Step by Step

دليل تفاعلي لتفعيل HTTPS وحل مشكلة عدم عمل الصفحات على الأجهزة الأخرى
Interactive guide to enable HTTPS and fix pages not working on other devices
"""

import os
import sys
import time
import socket
import webbrowser
from pathlib import Path

def print_header():
    """طباعة الرأس"""
    print("\n" + "="*70)
    print("🔒 تفعيل HTTPS خطوة بخطوة")
    print("🌐 Enable HTTPS Step by Step")
    print("="*70)
    print("🎯 الهدف: حل مشكلة عدم عمل صفحات المودات على الأجهزة الأخرى")
    print("🎯 Goal: Fix mod pages not working on other devices")
    print("="*70)

def check_ngrok():
    """التحقق من وجود ngrok"""
    ngrok_path = Path("./ngrok.exe")
    if ngrok_path.exists():
        print("✅ ngrok.exe موجود في المجلد")
        return True
    else:
        print("❌ ngrok.exe غير موجود")
        return False

def get_local_ip():
    """الحصول على IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

def step1_current_status():
    """الخطوة 1: فحص الوضع الحالي"""
    print("\n" + "="*50)
    print("📋 الخطوة 1: فحص الوضع الحالي")
    print("="*50)
    
    # فحص ملف .env
    env_file = Path(".env")
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if "WEB_SERVER_URL=" in content and not content.count("# WEB_SERVER_URL="):
                for line in content.split('\n'):
                    if line.startswith('WEB_SERVER_URL=') and not line.startswith('# WEB_SERVER_URL='):
                        current_url = line.split('=', 1)[1]
                        print(f"🔍 الرابط الحالي: {current_url}")
                        
                        if current_url.startswith('https://'):
                            print("✅ الرابط يستخدم HTTPS - ممتاز!")
                            return "https"
                        elif current_url.startswith('http://'):
                            print("⚠️ الرابط يستخدم HTTP - هذا سبب المشكلة!")
                            return "http"
                        else:
                            print("❓ رابط غير صالح")
                            return "invalid"
            
            if "DISABLE_WEB_APP=true" in content:
                print("🔧 Web App معطل حالياً (الحل المؤقت مطبق)")
                return "disabled"
            
            print("❓ لا يوجد رابط محدد")
            return "none"
            
        except Exception as e:
            print(f"❌ خطأ في قراءة ملف .env: {e}")
            return "error"
    else:
        print("❌ ملف .env غير موجود")
        return "no_env"

def step2_ngrok_setup():
    """الخطوة 2: إعداد ngrok"""
    print("\n" + "="*50)
    print("🔧 الخطوة 2: إعداد ngrok للحصول على HTTPS")
    print("="*50)
    
    if not check_ngrok():
        print("❌ ngrok غير موجود. يجب تحميله أولاً.")
        return False
    
    print("\n📋 خطوات إعداد ngrok:")
    print("1. 🌐 اذهب إلى: https://dashboard.ngrok.com/get-started/your-authtoken")
    print("2. 📝 أنشئ حساب مجاني (إذا لم يكن لديك)")
    print("3. 🔑 انسخ الـ authtoken")
    print("4. ⚙️ شغل الأمر: ./ngrok config add-authtoken YOUR_TOKEN")
    print("5. 🚀 شغل الأمر: ./ngrok http 5001")
    
    choice = input("\n❓ هل تريد فتح صفحة ngrok للحصول على authtoken؟ (y/n): ").lower()
    if choice == 'y':
        webbrowser.open("https://dashboard.ngrok.com/get-started/your-authtoken")
        print("✅ تم فتح صفحة ngrok في المتصفح")
    
    print("\n⏳ بعد الحصول على authtoken:")
    authtoken = input("🔑 أدخل الـ authtoken (أو اضغط Enter للتخطي): ").strip()
    
    if authtoken:
        print(f"🔧 تطبيق authtoken...")
        os.system(f"./ngrok config add-authtoken {authtoken}")
        print("✅ تم حفظ authtoken")
        return True
    else:
        print("⏭️ تم تخطي إعداد authtoken")
        return False

def step3_start_ngrok():
    """الخطوة 3: تشغيل ngrok"""
    print("\n" + "="*50)
    print("🚀 الخطوة 3: تشغيل ngrok")
    print("="*50)
    
    print("📋 سيتم تشغيل ngrok الآن...")
    print("⚠️ ملاحظة: اتركه يعمل ولا تغلقه!")
    
    input("⏸️ اضغط Enter عندما تكون جاهز لتشغيل ngrok...")
    
    print("\n🔧 تشغيل ngrok...")
    print("💡 نصيحة: افتح terminal جديد وشغل الأمر التالي:")
    print("   ./ngrok http 5001")
    print("\n📋 بعد تشغيل ngrok:")
    print("1. ابحث عن السطر الذي يحتوي على 'https://'")
    print("2. انسخ الرابط كاملاً")
    print("3. مثال: https://abc123.ngrok.io")
    
    ngrok_url = input("\n🔗 أدخل رابط ngrok HTTPS: ").strip()
    
    if ngrok_url and ngrok_url.startswith('https://'):
        print(f"✅ رابط صحيح: {ngrok_url}")
        return ngrok_url
    else:
        print("❌ رابط غير صحيح أو فارغ")
        return None

def step4_update_env(ngrok_url):
    """الخطوة 4: تحديث ملف .env"""
    print("\n" + "="*50)
    print("📝 الخطوة 4: تحديث ملف .env")
    print("="*50)
    
    env_file = Path(".env")
    env_lines = []
    updated = False
    
    # قراءة الملف الموجود
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('WEB_SERVER_URL=') or line.startswith('# WEB_SERVER_URL='):
                        env_lines.append(f"WEB_SERVER_URL={ngrok_url}")
                        updated = True
                        print(f"✅ تم تحديث WEB_SERVER_URL إلى {ngrok_url}")
                    elif line == "DISABLE_WEB_APP=true":
                        env_lines.append("# DISABLE_WEB_APP=true  # تم تفعيل Web App")
                        print("✅ تم تفعيل Web App")
                    else:
                        env_lines.append(line)
        except Exception as e:
            print(f"❌ خطأ في قراءة ملف .env: {e}")
            return False
    
    # إضافة WEB_SERVER_URL إذا لم يكن موجوداً
    if not updated:
        env_lines.append(f"WEB_SERVER_URL={ngrok_url}")
        print(f"✅ تم إضافة WEB_SERVER_URL={ngrok_url}")
    
    # حفظ الملف
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            for line in env_lines:
                f.write(line + '\n')
        print("✅ تم حفظ ملف .env بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في حفظ ملف .env: {e}")
        return False

def step5_test_solution():
    """الخطوة 5: اختبار الحل"""
    print("\n" + "="*50)
    print("🧪 الخطوة 5: اختبار الحل")
    print("="*50)
    
    print("📋 خطوات الاختبار:")
    print("1. 🤖 شغل البوت: python main.py")
    print("2. 📱 أرسل مود للبوت")
    print("3. 🎮 اضغط على زر 'عرض التفاصيل'")
    print("4. ✨ يجب أن تفتح صفحة ويب كاملة!")
    
    print("\n🌐 للاختبار من أجهزة أخرى:")
    print("• افتح الرابط على هاتفك")
    print("• جرب من شبكة WiFi مختلفة")
    print("• شارك الرابط مع أصدقائك")
    
    print("\n⚠️ ملاحظات مهمة:")
    print("• يجب إبقاء ngrok يعمل")
    print("• إذا أغلقت ngrok، ستحتاج رابط جديد")
    print("• للاستخدام الدائم، فكر في خدمة استضافة")

def alternative_solution():
    """حل بديل بدون ngrok"""
    print("\n" + "="*50)
    print("🔄 حل بديل: استخدام IP المحلي")
    print("="*50)
    
    local_ip = get_local_ip()
    local_url = f"http://{local_ip}:5001"
    
    print(f"🌐 IP المحلي: {local_ip}")
    print(f"🔗 الرابط المحلي: {local_url}")
    
    print("\n⚠️ هذا الحل:")
    print("✅ يعمل للأجهزة في نفس الشبكة")
    print("❌ لا يعمل من خارج الشبكة")
    print("❌ لا يدعم Telegram Web App (يتطلب HTTPS)")
    
    choice = input("\n❓ هل تريد استخدام هذا الحل المؤقت؟ (y/n): ").lower()
    if choice == 'y':
        return step4_update_env(local_url)
    return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # الخطوة 1: فحص الوضع الحالي
    status = step1_current_status()
    
    if status == "https":
        print("\n🎉 رائع! HTTPS مفعل بالفعل!")
        print("✅ يجب أن تعمل صفحات المودات على جميع الأجهزة")
        return
    
    print(f"\n📊 الوضع الحالي: {status}")
    
    # الخطوة 2: إعداد ngrok
    if step2_ngrok_setup():
        # الخطوة 3: تشغيل ngrok
        ngrok_url = step3_start_ngrok()
        
        if ngrok_url:
            # الخطوة 4: تحديث .env
            if step4_update_env(ngrok_url):
                # الخطوة 5: اختبار الحل
                step5_test_solution()
                
                print("\n🎉 تم تطبيق الحل بنجاح!")
                print("🚀 الآن يمكنك تشغيل البوت: python main.py")
                return
    
    # حل بديل
    print("\n🔄 جرب الحل البديل...")
    if alternative_solution():
        print("\n✅ تم تطبيق الحل البديل")
        print("🚀 الآن يمكنك تشغيل البوت: python main.py")
    else:
        print("\n❌ لم يتم تطبيق أي حل")
        print("📞 للمساعدة، تواصل مع @Kim880198")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البرنامج")
    except Exception as e:
        print(f"\n💥 خطأ: {e}")
        print("📞 للمساعدة، تواصل مع @Kim880198")
