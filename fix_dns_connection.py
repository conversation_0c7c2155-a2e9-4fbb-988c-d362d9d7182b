#!/usr/bin/env python3
"""
أداة إصلاح مشاكل DNS والاتصال
DNS and Connection Issues Fixer
"""

import os
import sys
import socket
import subprocess
import platform
import time
import asyncio
import logging
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('dns_fix.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def print_header():
    """طباعة رأس الأداة"""
    print("=" * 60)
    print("🔧 أداة إصلاح مشاكل DNS والاتصال")
    print("   DNS and Connection Issues Fixer")
    print("=" * 60)

def check_internet_connection():
    """فحص الاتصال بالإنترنت"""
    print("\n🌐 فحص الاتصال بالإنترنت...")
    
    # قائمة خوادم للاختبار
    test_servers = [
        ("*******", 53),      # Google DNS
        ("1.1.1.1", 53),      # Cloudflare DNS
        ("208.67.222.222", 53), # OpenDNS
        ("google.com", 80),    # Google HTTP
        ("telegram.org", 443)  # Telegram HTTPS
    ]
    
    successful_connections = 0
    
    for server, port in test_servers:
        try:
            socket.create_connection((server, port), timeout=10)
            print(f"✅ نجح الاتصال مع {server}:{port}")
            successful_connections += 1
        except Exception as e:
            print(f"❌ فشل الاتصال مع {server}:{port} - {e}")
    
    success_rate = (successful_connections / len(test_servers)) * 100
    print(f"\n📊 معدل نجاح الاتصال: {success_rate:.1f}%")
    
    return success_rate > 50

def flush_dns():
    """مسح ذاكرة DNS التخزين المؤقت"""
    print("\n🔄 مسح ذاكرة DNS...")
    
    system = platform.system().lower()
    
    try:
        if system == "windows":
            # Windows DNS flush
            subprocess.run(["ipconfig", "/flushdns"], check=True, capture_output=True)
            print("✅ تم مسح DNS cache في Windows")
            
        elif system == "linux":
            # Linux DNS flush (متعدد الطرق)
            commands = [
                ["sudo", "systemctl", "restart", "systemd-resolved"],
                ["sudo", "service", "networking", "restart"],
                ["sudo", "/etc/init.d/networking", "restart"]
            ]
            
            for cmd in commands:
                try:
                    subprocess.run(cmd, check=True, capture_output=True)
                    print(f"✅ تم تنفيذ: {' '.join(cmd)}")
                    break
                except:
                    continue
                    
        elif system == "darwin":  # macOS
            subprocess.run(["sudo", "dscacheutil", "-flushcache"], check=True, capture_output=True)
            print("✅ تم مسح DNS cache في macOS")
            
        return True
        
    except Exception as e:
        print(f"⚠️ فشل في مسح DNS cache: {e}")
        return False

def configure_dns_servers():
    """تكوين خوادم DNS"""
    print("\n🔧 تكوين خوادم DNS...")
    
    # خوادم DNS موثوقة
    dns_servers = [
        "*******",        # Google Primary
        "8.8.4.4",        # Google Secondary
        "1.1.1.1",        # Cloudflare Primary
        "1.0.0.1",        # Cloudflare Secondary
        "208.67.222.222", # OpenDNS Primary
        "**************"  # OpenDNS Secondary
    ]
    
    system = platform.system().lower()
    
    try:
        if system == "windows":
            # Windows DNS configuration
            print("💡 لتكوين DNS في Windows يدوياً:")
            print("   1. افتح Control Panel > Network and Internet > Network Connections")
            print("   2. انقر بزر الماوس الأيمن على اتصالك > Properties")
            print("   3. اختر Internet Protocol Version 4 (TCP/IPv4) > Properties")
            print("   4. اختر 'Use the following DNS server addresses'")
            print(f"   5. Preferred DNS: {dns_servers[0]}")
            print(f"   6. Alternate DNS: {dns_servers[1]}")
            
            # محاولة تلقائية (قد تحتاج صلاحيات admin)
            try:
                subprocess.run([
                    "netsh", "interface", "ip", "set", "dns", 
                    "name=Wi-Fi", "static", dns_servers[0]
                ], check=True, capture_output=True)
                print("✅ تم تكوين DNS تلقائياً")
            except:
                print("⚠️ فشل التكوين التلقائي - استخدم الطريقة اليدوية")
                
        return True
        
    except Exception as e:
        print(f"❌ فشل في تكوين DNS: {e}")
        return False

def test_dns_resolution():
    """اختبار حل أسماء النطاقات"""
    print("\n🔍 اختبار حل أسماء النطاقات...")
    
    test_domains = [
        "google.com",
        "telegram.org",
        "api.telegram.org",
        "github.com",
        "python.org"
    ]
    
    successful_resolutions = 0
    
    for domain in test_domains:
        try:
            ip = socket.gethostbyname(domain)
            print(f"✅ {domain} -> {ip}")
            successful_resolutions += 1
        except Exception as e:
            print(f"❌ فشل حل {domain}: {e}")
    
    success_rate = (successful_resolutions / len(test_domains)) * 100
    print(f"\n📊 معدل نجاح حل النطاقات: {success_rate:.1f}%")
    
    return success_rate > 80

def check_firewall_and_antivirus():
    """فحص إعدادات الجدار الناري ومضاد الفيروسات"""
    print("\n🛡️ فحص الجدار الناري ومضاد الفيروسات...")
    
    print("💡 تحقق من الإعدادات التالية:")
    print("   1. الجدار الناري (Windows Firewall):")
    print("      - تأكد من السماح لـ Python.exe")
    print("      - تأكد من السماح للمنفذ 443 (HTTPS)")
    print("      - تأكد من السماح للمنفذ 80 (HTTP)")
    
    print("   2. مضاد الفيروسات:")
    print("      - أضف مجلد البوت للاستثناءات")
    print("      - تعطيل فحص الشبكة مؤقتاً للاختبار")
    
    print("   3. برامج VPN/Proxy:")
    print("      - تعطيل VPN مؤقتاً للاختبار")
    print("      - تعطيل Proxy settings")
    
    return True

def fix_python_ssl():
    """إصلاح مشاكل SSL في Python"""
    print("\n🔐 إصلاح مشاكل SSL...")
    
    try:
        import ssl
        import certifi
        
        # تحديث الشهادات
        print("📜 فحص شهادات SSL...")
        
        # إنشاء context SSL آمن
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        print("✅ تم إنشاء SSL context")
        
        # اختبار اتصال SSL
        try:
            sock = socket.create_connection(("api.telegram.org", 443), timeout=10)
            ssock = context.wrap_socket(sock, server_hostname="api.telegram.org")
            print("✅ نجح اختبار SSL مع Telegram API")
            ssock.close()
        except Exception as e:
            print(f"❌ فشل اختبار SSL: {e}")
            
        return True
        
    except ImportError as e:
        print(f"❌ مكتبات SSL مفقودة: {e}")
        print("💡 قم بتثبيت: pip install certifi")
        return False

def create_network_test_script():
    """إنشاء سكريبت اختبار الشبكة"""
    print("\n📝 إنشاء سكريبت اختبار الشبكة...")
    
    script_content = '''#!/usr/bin/env python3
"""
سكريبت اختبار الشبكة البسيط
Simple Network Test Script
"""

import socket
import time
import requests
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

def test_basic_connection():
    """اختبار الاتصال الأساسي"""
    print("🔍 اختبار الاتصال الأساسي...")
    
    try:
        # اختبار DNS
        ip = socket.gethostbyname("google.com")
        print(f"✅ DNS يعمل: google.com -> {ip}")
        
        # اختبار TCP
        sock = socket.create_connection(("*******", 53), timeout=10)
        sock.close()
        print("✅ TCP يعمل")
        
        # اختبار HTTP
        session = requests.Session()
        retry = Retry(total=3, backoff_factor=1)
        adapter = HTTPAdapter(max_retries=retry)
        session.mount('http://', adapter)
        session.mount('https://', adapter)
        
        response = session.get("https://httpbin.org/ip", timeout=10)
        print(f"✅ HTTP يعمل: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        return False

if __name__ == "__main__":
    test_basic_connection()
'''
    
    try:
        with open("network_test.py", "w", encoding="utf-8") as f:
            f.write(script_content)
        print("✅ تم إنشاء network_test.py")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء السكريبت: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    steps = [
        ("فحص الاتصال بالإنترنت", check_internet_connection),
        ("مسح ذاكرة DNS", flush_dns),
        ("تكوين خوادم DNS", configure_dns_servers),
        ("اختبار حل النطاقات", test_dns_resolution),
        ("فحص الجدار الناري", check_firewall_and_antivirus),
        ("إصلاح SSL", fix_python_ssl),
        ("إنشاء سكريبت الاختبار", create_network_test_script)
    ]
    
    print(f"\n🚀 بدء تشخيص وإصلاح {len(steps)} مشاكل محتملة...\n")
    
    success_count = 0
    
    for i, (description, func) in enumerate(steps, 1):
        print(f"\n[{i}/{len(steps)}] {description}")
        print("-" * 40)
        
        try:
            if func():
                success_count += 1
                print(f"✅ نجح: {description}")
            else:
                print(f"⚠️ فشل جزئياً: {description}")
        except Exception as e:
            print(f"❌ خطأ في {description}: {e}")
        
        time.sleep(1)
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {success_count}/{len(steps)} خطوات نجحت")
    
    if success_count >= len(steps) - 2:
        print("🎉 تم إصلاح معظم المشاكل!")
        print("\n✅ خطوات إضافية:")
        print("   1. أعد تشغيل الكمبيوتر")
        print("   2. جرب تشغيل البوت: python main.py")
        print("   3. إذا استمرت المشكلة، جرب: python network_test.py")
    else:
        print("⚠️ لا تزال هناك مشاكل")
        print("\n🔧 حلول إضافية:")
        print("   1. تحقق من إعدادات الراوتر")
        print("   2. اتصل بمزود الإنترنت")
        print("   3. جرب شبكة أخرى (هاتف محمول)")
        print("   4. تحقق من إعدادات البروكسي في المتصفح")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الإصلاح: {e}")
        sys.exit(1)
