# ملف متغيرات البيئة النموذجي الآمن
# Safe Environment Variables Template

# ⚠️ تحذير أمني: لا تضع بيانات حقيقية في هذا الملف
# Security Warning: Do not put real data in this file

# ===== إعدادات البوت الأساسية =====
# Basic Bot Settings

# رمز البوت من BotFather
BOT_TOKEN=your_bot_token_here

# معرف المشرف (Chat ID للمشرف الرئيسي)
ADMIN_CHAT_ID=your_admin_chat_id_here

# اسم المستخدم للمشرف (اختياري)
ADMIN_USERNAME=your_username_here

# ===== إعدادات قاعدة البيانات =====
# Database Settings

# رابط Supabase
SUPABASE_URL=https://your-project.supabase.co

# مفتاح Supabase
SUPABASE_KEY=your_supabase_anon_key_here

# ===== إعدادات التحسين الجديدة =====
# New Optimization Settings

# تفعيل وضع التحسين (true/false)
OPTIMIZATION_ENABLED=true

# حد الذاكرة بالميجابايت
MEMORY_LIMIT_MB=100

# مستوى التسجيل (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# timeout للعمليات (بالثواني)
API_TIMEOUT=30

# عدد المحاولات عند الفشل
MAX_RETRIES=3

# تأخير بين المحاولات (بالثواني)
RETRY_DELAY=2

# ===== إعدادات الخادم =====
# Server Settings

# منفذ خادم الويب
WEB_SERVER_PORT=5001

# منفذ Telegram Web App
TELEGRAM_WEB_APP_PORT=5002

# ===== إعدادات الأمان =====
# Security Settings

# مفتاح التشفير (32 بايت)
ENCRYPTION_SECRET=your_32_byte_encryption_secret_here

# مفتاح JWT
JWT_SECRET=your_jwt_secret_here

# ===== إعدادات اختيارية =====
# Optional Settings

# البيئة (development/production)
ENVIRONMENT=production

# وضع التطوير (true/false)
DEBUG=false

# الحد الأقصى لحجم الملف (بالميجابايت)
MAX_FILE_SIZE_MB=50

# ===== إعدادات Webhook (للاستضافة) =====
# Webhook Settings (for hosting)

# رابط Webhook (اتركه فارغاً للـ polling)
WEBHOOK_URL=

# رمز Webhook السري
WEBHOOK_SECRET=

# ===== تعليمات الاستخدام =====
# Usage Instructions

# 1. انسخ هذا الملف إلى .env
#    cp .env.template .env

# 2. املأ القيم المطلوبة بالبيانات الحقيقية
#    nano .env

# 3. احفظ الملف وشغل البوت
#    python run_optimized_bot.py

# ===== كيفية الحصول على البيانات =====
# How to get the data

# BOT_TOKEN: من @BotFather في Telegram
# ADMIN_CHAT_ID: أرسل /start لـ @userinfobot
# SUPABASE_URL & KEY: من لوحة تحكم Supabase

# ===== إنشاء مفاتيح آمنة =====
# Generate secure keys

# استخدم Python لإنشاء مفاتيح عشوائية:
# import secrets
# print(secrets.token_hex(32))  # للمفاتيح العامة
# print(secrets.token_urlsafe(32))  # للمفاتيح الآمنة للURL
