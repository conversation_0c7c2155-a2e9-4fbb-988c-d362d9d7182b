#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط للبوت - للاستضافة
Simple bot starter for hosting - troubleshooting version
"""

import os
import sys
import asyncio
import logging

# إعداد التسجيل البسيط
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%H:%M:%S"
)
logger = logging.getLogger(__name__)

def setup_environment():
    """إعداد البيئة الأساسية"""
    print("🔧 إعداد البيئة...")
    
    # إعداد متغيرات البيئة الأساسية
    env_vars = {
        "BOT_TOKEN": "7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4",
        "ADMIN_CHAT_ID": "7513880877",
        "ADMIN_USERNAME": "Kim880198",
        "SUPABASE_URL": "https://ytqxxodyecdeosnqoure.supabase.co",
        "SUPABASE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4",
        "OPTIMIZATION_ENABLED": "true",
        "LOW_RESOURCE_MODE": "true"
    }
    
    for key, value in env_vars.items():
        if not os.environ.get(key):
            os.environ[key] = value
    
    print("✅ تم إعداد متغيرات البيئة")

def check_imports():
    """فحص المكتبات المطلوبة"""
    print("📦 فحص المكتبات...")
    
    required_modules = [
        'telegram',
        'requests',
        'asyncio'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module} مفقود")
            missing.append(module)
    
    if missing:
        print(f"❌ مكتبات مفقودة: {', '.join(missing)}")
        print("💡 قم بتثبيتها باستخدام: pip install python-telegram-bot requests")
        return False
    
    print("✅ جميع المكتبات متوفرة")
    return True

def test_basic_connection():
    """اختبار الاتصال الأساسي"""
    print("🌐 اختبار الاتصال...")
    
    try:
        import requests
        
        # اختبار الاتصال بالإنترنت
        response = requests.get("https://httpbin.org/get", timeout=10)
        if response.status_code == 200:
            print("✅ الاتصال بالإنترنت يعمل")
        else:
            print("⚠️ مشكلة في الاتصال بالإنترنت")
            
        # اختبار Telegram API
        bot_token = os.environ.get("BOT_TOKEN")
        if bot_token:
            telegram_url = f"https://api.telegram.org/bot{bot_token}/getMe"
            response = requests.get(telegram_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('ok'):
                    bot_info = data.get('result', {})
                    print(f"✅ البوت متصل: @{bot_info.get('username', 'Unknown')}")
                else:
                    print("❌ خطأ في بيانات البوت")
            else:
                print("❌ فشل الاتصال مع Telegram API")
        else:
            print("❌ BOT_TOKEN غير موجود")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {e}")

async def start_simple_bot():
    """تشغيل البوت بطريقة مبسطة"""
    try:
        print("🤖 بدء تشغيل البوت...")
        
        # استيراد البوت
        print("📥 استيراد ملفات البوت...")
        
        # محاولة استيراد البوت الرئيسي
        try:
            print("   📄 استيراد main.py...")
            import main
            print("   ✅ تم استيراد main.py")
        except Exception as e:
            print(f"   ❌ فشل استيراد main.py: {e}")
            raise
        
        # تشغيل البوت
        print("🚀 تشغيل البوت الرئيسي...")
        await main.main()
        
    except KeyboardInterrupt:
        print("⏹️ تم إيقاف البوت")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        import traceback
        print("📋 تفاصيل الخطأ:")
        traceback.print_exc()
        raise

def main():
    """الدالة الرئيسية المبسطة"""
    print("🚀 بدء تشغيل البوت - الإصدار المبسط")
    print("="*50)
    
    try:
        # 1. إعداد البيئة
        setup_environment()
        
        # 2. فحص المكتبات
        if not check_imports():
            print("❌ فشل فحص المكتبات")
            sys.exit(1)
        
        # 3. اختبار الاتصال
        test_basic_connection()
        
        # 4. تشغيل البوت
        print("\n" + "="*50)
        print("🎯 بدء تشغيل البوت...")
        
        # تشغيل البوت
        asyncio.run(start_simple_bot())
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
