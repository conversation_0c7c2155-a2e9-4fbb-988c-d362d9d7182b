<?php
/**
 * ملف الإعدادات المحسن مع معالجة الأخطاء
 * Enhanced Configuration File with Error Handling
 */

// منع الوصول المباشر
if (!defined('INCLUDED')) {
    define('INCLUDED', true);
}

// تفعيل عرض الأخطاء للتطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// إعدادات قاعدة البيانات Supabase - محدثة
define('SUPABASE_URL', 'https://ytqxxodyecdeosnqoure.supabase.co');
define('SUPABASE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4');

// إعدادات بديلة للاختبار
define('SUPABASE_URL_BACKUP', 'https://ytqxxodyecdeosnqoure.supabase.co');
define('SUPABASE_SERVICE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTI2MTEwNSwiZXhwIjoyMDYwODM3MTA1fQ.Ej6lYJOJzOJJQOJJQOJJQOJJQOJJQOJJQOJJQOJJQOJ');

// إعدادات الموقع
define('SITE_NAME', 'Modetaris');
define('SITE_DESCRIPTION', 'موقع مودات ماين كرافت');
define('DEFAULT_LANGUAGE', 'ar');
define('SITE_URL', 'http://sendaddons.fwh.is');

// إعدادات الأمان والسجلات
define('ENABLE_LOGGING', true);
define('LOG_FILE', 'logs/app.log');
define('DEBUG_MODE', true);

// إعدادات cURL محسنة
define('CURL_TIMEOUT', 30);
define('CURL_CONNECT_TIMEOUT', 10);
define('CURL_RETRIES', 3);

/**
 * دالة إنشاء مجلدات السجلات
 */
function createLogDirectories() {
    $log_dir = dirname(LOG_FILE);
    if (!is_dir($log_dir)) {
        if (!mkdir($log_dir, 0755, true)) {
            error_log("Failed to create log directory: $log_dir");
            return false;
        }
    }
    return true;
}

/**
 * دالة تسجيل الأخطاء المحسنة
 */
function logError($message, $level = 'ERROR', $context = []) {
    if (!ENABLE_LOGGING) return;
    
    createLogDirectories();
    
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    $log_entry = "[$timestamp] [$level] [$ip] $message";
    
    if (!empty($context)) {
        $log_entry .= " | Context: " . json_encode($context);
    }
    
    $log_entry .= " | User-Agent: $user_agent" . PHP_EOL;
    
    if (file_put_contents(LOG_FILE, $log_entry, FILE_APPEND | LOCK_EX) === false) {
        error_log("Failed to write to log file: " . LOG_FILE);
    }
}

/**
 * دالة اختبار الاتصال بـ Supabase محسنة
 */
function testSupabaseConnection($url = null, $key = null) {
    $test_url = ($url ?: SUPABASE_URL) . '/rest/v1/';
    $test_key = $key ?: SUPABASE_KEY;
    
    logError("Testing Supabase connection to: $test_url", 'INFO');
    
    $headers = [
        'apikey: ' . $test_key,
        'Authorization: Bearer ' . $test_key,
        'Content-Type: application/json',
        'Accept: application/json',
        'User-Agent: ModetarisBot/1.0'
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $test_url,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_TIMEOUT => CURL_TIMEOUT,
        CURLOPT_CONNECTTIMEOUT => CURL_CONNECT_TIMEOUT,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3,
        CURLOPT_USERAGENT => 'ModetarisBot/1.0',
        CURLOPT_VERBOSE => DEBUG_MODE
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    $curl_info = curl_getinfo($ch);
    curl_close($ch);
    
    $result = [
        'success' => false,
        'http_code' => $http_code,
        'response' => $response,
        'error' => $curl_error,
        'info' => $curl_info
    ];
    
    if ($curl_error) {
        logError("cURL Error: $curl_error", 'ERROR', $curl_info);
        $result['error'] = $curl_error;
    } elseif ($http_code >= 200 && $http_code < 300) {
        logError("Supabase connection successful", 'INFO');
        $result['success'] = true;
    } else {
        logError("Supabase HTTP Error: $http_code", 'ERROR', [
            'response' => substr($response, 0, 500),
            'url' => $test_url
        ]);
        $result['error'] = "HTTP $http_code";
    }
    
    return $result;
}

/**
 * دالة إرسال طلب محسنة إلى Supabase
 */
function makeSupabaseRequest($endpoint, $method = 'GET', $data = null, $retry_count = 0) {
    $url = SUPABASE_URL . $endpoint;
    
    logError("Making Supabase request: $method $endpoint", 'INFO');
    
    $headers = [
        'apikey: ' . SUPABASE_KEY,
        'Authorization: Bearer ' . SUPABASE_KEY,
        'Content-Type: application/json',
        'Accept: application/json',
        'User-Agent: ModetarisBot/1.0'
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_TIMEOUT => CURL_TIMEOUT,
        CURLOPT_CONNECTTIMEOUT => CURL_CONNECT_TIMEOUT,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3,
        CURLOPT_USERAGENT => 'ModetarisBot/1.0'
    ]);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    } elseif ($method === 'PUT') {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    } elseif ($method === 'DELETE') {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    $curl_info = curl_getinfo($ch);
    curl_close($ch);
    
    $result = [
        'success' => false,
        'data' => null,
        'http_code' => $http_code,
        'error' => null,
        'raw_response' => $response
    ];
    
    if ($curl_error) {
        logError("cURL Error in makeSupabaseRequest: $curl_error", 'ERROR', [
            'endpoint' => $endpoint,
            'method' => $method,
            'retry_count' => $retry_count
        ]);
        
        // إعادة المحاولة في حالة خطأ الشبكة
        if ($retry_count < CURL_RETRIES) {
            sleep(1); // انتظار ثانية واحدة
            return makeSupabaseRequest($endpoint, $method, $data, $retry_count + 1);
        }
        
        $result['error'] = $curl_error;
    } elseif ($http_code >= 200 && $http_code < 300) {
        $decoded_response = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $result['success'] = true;
            $result['data'] = $decoded_response;
            logError("Supabase request successful", 'INFO', [
                'endpoint' => $endpoint,
                'method' => $method,
                'response_size' => strlen($response)
            ]);
        } else {
            logError("JSON decode error: " . json_last_error_msg(), 'ERROR', [
                'response' => substr($response, 0, 500)
            ]);
            $result['error'] = 'Invalid JSON response';
        }
    } else {
        logError("Supabase HTTP Error: $http_code", 'ERROR', [
            'endpoint' => $endpoint,
            'method' => $method,
            'response' => substr($response, 0, 500)
        ]);
        $result['error'] = "HTTP $http_code: " . substr($response, 0, 200);
    }
    
    return $result;
}

/**
 * دالة فحص البيئة المحسنة
 */
function checkEnvironment() {
    $requirements = [
        'php_version' => '7.4.0',
        'extensions' => ['curl', 'json', 'mbstring'],
        'functions' => ['file_get_contents', 'json_encode', 'json_decode', 'curl_init']
    ];
    
    $errors = [];
    $warnings = [];
    
    // فحص إصدار PHP
    if (version_compare(PHP_VERSION, $requirements['php_version'], '<')) {
        $errors[] = "PHP version {$requirements['php_version']} or higher required, current: " . PHP_VERSION;
    }
    
    // فحص الإضافات
    foreach ($requirements['extensions'] as $extension) {
        if (!extension_loaded($extension)) {
            $errors[] = "PHP extension '$extension' is required";
        }
    }
    
    // فحص الدوال
    foreach ($requirements['functions'] as $function) {
        if (!function_exists($function)) {
            $errors[] = "PHP function '$function' is required";
        }
    }
    
    // فحص إعدادات PHP
    if (ini_get('allow_url_fopen') == false) {
        $warnings[] = "allow_url_fopen is disabled";
    }
    
    // فحص الكتابة في مجلد السجلات
    if (!createLogDirectories()) {
        $warnings[] = "Cannot create log directory";
    }
    
    $result = [
        'success' => empty($errors),
        'errors' => $errors,
        'warnings' => $warnings,
        'php_version' => PHP_VERSION,
        'extensions' => array_filter($requirements['extensions'], 'extension_loaded'),
        'functions' => array_filter($requirements['functions'], 'function_exists')
    ];
    
    logError("Environment check completed", 'INFO', $result);
    
    return $result;
}

/**
 * دالة اختبار شاملة للنظام
 */
function runSystemTests() {
    $tests = [
        'environment' => checkEnvironment(),
        'supabase_connection' => testSupabaseConnection(),
        'log_directory' => createLogDirectories()
    ];
    
    logError("System tests completed", 'INFO', $tests);
    
    return $tests;
}

// تشغيل الاختبارات عند تحميل الملف
if (DEBUG_MODE && !defined('SKIP_TESTS')) {
    $system_tests = runSystemTests();
}

// دوال مساعدة إضافية
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}

function isValidUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

// تسجيل بداية تحميل الملف
logError("Config file loaded successfully", 'INFO', [
    'php_version' => PHP_VERSION,
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time')
]);
?>
