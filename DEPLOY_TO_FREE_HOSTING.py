#!/usr/bin/env python3
"""
🚀 نشر البوت على استضافة مجانية
🌐 Deploy Bot to Free Hosting

نشر البوت على خدمات الاستضافة المجانية مع HTTPS دائم
Deploy bot to free hosting services with permanent HTTPS
"""

import os
import json
import shutil
from pathlib import Path

def print_header():
    """طباعة الرأس"""
    print("\n" + "="*70)
    print("🚀 نشر البوت على استضافة مجانية")
    print("🌐 Deploy Bot to Free Hosting")
    print("="*70)
    print("🎯 الهدف: نشر البوت ليعمل 24/7 بدون الحاجة لتشغيل الكمبيوتر")
    print("🎯 Goal: Deploy bot to work 24/7 without keeping computer on")
    print("="*70)

def create_deployment_files():
    """إنشاء ملفات النشر"""
    print("\n📁 إنشاء ملفات النشر...")
    
    # إنشاء Procfile لـ Heroku
    procfile_content = """web: python web_server.py
worker: python main.py"""
    
    with open("Procfile", "w", encoding="utf-8") as f:
        f.write(procfile_content)
    print("✅ تم إنشاء Procfile")
    
    # إنشاء runtime.txt
    runtime_content = "python-3.11.0"
    with open("runtime.txt", "w", encoding="utf-8") as f:
        f.write(runtime_content)
    print("✅ تم إنشاء runtime.txt")
    
    # إنشاء app.json لـ Heroku
    app_json = {
        "name": "minecraft-mods-bot",
        "description": "Telegram bot for Minecraft mods with web interface",
        "keywords": ["telegram", "bot", "minecraft", "mods"],
        "website": "https://github.com/your-username/minecraft-mods-bot",
        "repository": "https://github.com/your-username/minecraft-mods-bot",
        "env": {
            "BOT_TOKEN": {
                "description": "Telegram Bot Token from @BotFather",
                "required": True
            },
            "ADMIN_CHAT_ID": {
                "description": "Admin Telegram User ID",
                "required": True
            },
            "SUPABASE_URL": {
                "description": "Supabase Database URL",
                "required": True
            },
            "SUPABASE_KEY": {
                "description": "Supabase API Key",
                "required": True
            },
            "GEMINI_API_KEY": {
                "description": "Google Gemini API Key",
                "required": False
            }
        },
        "formation": {
            "web": {
                "quantity": 1,
                "size": "free"
            },
            "worker": {
                "quantity": 1,
                "size": "free"
            }
        },
        "buildpacks": [
            {
                "url": "heroku/python"
            }
        ]
    }
    
    with open("app.json", "w", encoding="utf-8") as f:
        json.dump(app_json, f, indent=2, ensure_ascii=False)
    print("✅ تم إنشاء app.json")
    
    # إنشاء railway.json
    railway_json = {
        "build": {
            "builder": "NIXPACKS"
        },
        "deploy": {
            "startCommand": "python main.py",
            "healthcheckPath": "/health",
            "healthcheckTimeout": 100,
            "restartPolicyType": "ON_FAILURE",
            "restartPolicyMaxRetries": 10
        }
    }
    
    with open("railway.json", "w", encoding="utf-8") as f:
        json.dump(railway_json, f, indent=2)
    print("✅ تم إنشاء railway.json")
    
    # إنشاء render.yaml
    render_yaml = """services:
  - type: web
    name: minecraft-mods-bot-web
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: python web_server.py
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: PORT
        value: 5000
    
  - type: worker
    name: minecraft-mods-bot-worker
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: python main.py
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0"""
    
    with open("render.yaml", "w", encoding="utf-8") as f:
        f.write(render_yaml)
    print("✅ تم إنشاء render.yaml")

def update_requirements():
    """تحديث requirements.txt"""
    print("\n📦 تحديث requirements.txt...")
    
    requirements = [
        "python-telegram-bot>=20.0",
        "flask>=2.3.0",
        "requests>=2.31.0",
        "supabase>=1.0.0",
        "google-generativeai>=0.3.0",
        "python-dotenv>=1.0.0",
        "APScheduler>=3.10.0",
        "Pillow>=10.0.0",
        "aiofiles>=23.0.0",
        "aiohttp>=3.8.0",
        "cryptography>=41.0.0",
        "gunicorn>=21.0.0",
        "waitress>=2.1.0"
    ]
    
    with open("requirements.txt", "w", encoding="utf-8") as f:
        for req in requirements:
            f.write(req + "\n")
    print("✅ تم تحديث requirements.txt")

def create_web_server_standalone():
    """إنشاء خادم ويب منفصل للاستضافة"""
    print("\n🌐 إنشاء خادم ويب منفصل...")
    
    web_server_code = '''#!/usr/bin/env python3
"""
خادم ويب منفصل للاستضافة
Standalone web server for hosting
"""

import os
import sys
from pathlib import Path

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, str(Path(__file__).parent))

try:
    from web_server import app, run_web_server
    from telegram_web_app import run_telegram_web_app
    import threading
    import time
except ImportError as e:
    print(f"Error importing modules: {e}")
    sys.exit(1)

def start_all_servers():
    """تشغيل جميع الخوادم"""
    port = int(os.environ.get("PORT", 5000))
    
    print(f"🚀 Starting web servers on port {port}")
    
    # تشغيل خادم Telegram Web App في thread منفصل
    telegram_thread = threading.Thread(
        target=run_telegram_web_app,
        args=(port + 1,),
        daemon=True
    )
    telegram_thread.start()
    
    # تشغيل الخادم الرئيسي
    run_web_server(port)

if __name__ == "__main__":
    start_all_servers()
'''
    
    with open("web_server_standalone.py", "w", encoding="utf-8") as f:
        f.write(web_server_code)
    print("✅ تم إنشاء web_server_standalone.py")

def create_main_standalone():
    """إنشاء main منفصل للاستضافة"""
    print("\n🤖 إنشاء main منفصل...")
    
    main_code = '''#!/usr/bin/env python3
"""
البوت الرئيسي للاستضافة
Main bot for hosting
"""

import os
import sys
from pathlib import Path

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, str(Path(__file__).parent))

# تعيين متغيرات البيئة للاستضافة
os.environ.setdefault("ENVIRONMENT", "production")
os.environ.setdefault("DEBUG", "false")

# تحديد رابط الخادم تلقائياً
if not os.environ.get("WEB_SERVER_URL"):
    # للاستضافة على Heroku/Railway/Render
    app_name = os.environ.get("RAILWAY_PROJECT_NAME") or os.environ.get("RENDER_SERVICE_NAME") or "your-app"
    if "railway" in os.environ.get("RAILWAY_ENVIRONMENT_NAME", ""):
        os.environ["WEB_SERVER_URL"] = f"https://{app_name}.up.railway.app"
    elif "render" in os.environ.get("RENDER", ""):
        os.environ["WEB_SERVER_URL"] = f"https://{app_name}.onrender.com"
    else:
        # Heroku أو خدمة أخرى
        os.environ["WEB_SERVER_URL"] = f"https://{app_name}.herokuapp.com"

try:
    # استيراد وتشغيل البوت الرئيسي
    import main
    print("🚀 Bot started successfully on hosting platform")
except Exception as e:
    print(f"❌ Error starting bot: {e}")
    sys.exit(1)
'''
    
    with open("main_standalone.py", "w", encoding="utf-8") as f:
        f.write(main_code)
    print("✅ تم إنشاء main_standalone.py")

def create_deployment_guide():
    """إنشاء دليل النشر"""
    print("\n📖 إنشاء دليل النشر...")
    
    guide = """# 🚀 دليل نشر البوت على الاستضافة المجانية

## 🌐 خدمات الاستضافة المجانية المدعومة:

### 1. 🚂 Railway (الأفضل - مجاني إلى الأبد)
- ✅ 500 ساعة مجانية شهرياً
- ✅ HTTPS تلقائي
- ✅ قاعدة بيانات مجانية
- ✅ سهولة النشر

**خطوات النشر على Railway:**
1. اذهب إلى: https://railway.app
2. أنشئ حساب مجاني
3. اضغط "New Project" → "Deploy from GitHub repo"
4. ارفع الكود إلى GitHub
5. اربط المستودع
6. أضف متغيرات البيئة:
   - `BOT_TOKEN`: توكن البوت
   - `ADMIN_CHAT_ID`: معرف المدير
   - `SUPABASE_URL`: رابط قاعدة البيانات
   - `SUPABASE_KEY`: مفتاح قاعدة البيانات
7. انتظر النشر

### 2. 🎨 Render (مجاني مع قيود)
- ✅ 750 ساعة مجانية شهرياً
- ✅ HTTPS تلقائي
- ⚠️ ينام بعد عدم الاستخدام

**خطوات النشر على Render:**
1. اذهب إلى: https://render.com
2. أنشئ حساب مجاني
3. اضغط "New" → "Web Service"
4. اربط GitHub repository
5. اختر "Python" environment
6. أضف متغيرات البيئة
7. انتظر النشر

### 3. 🟣 Heroku (مجاني محدود)
- ⚠️ 550 ساعة مجانية شهرياً
- ✅ HTTPS تلقائي
- ⚠️ ينام بعد 30 دقيقة

**خطوات النشر على Heroku:**
1. اذهب إلى: https://heroku.com
2. أنشئ حساب مجاني
3. اضغط "Create new app"
4. اربط GitHub repository
5. فعل "Automatic deploys"
6. أضف متغيرات البيئة في "Config Vars"
7. انتظر النشر

## 🔧 إعداد متغيرات البيئة:

```
BOT_TOKEN=7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4
ADMIN_CHAT_ID=7513880877
ADMIN_USERNAME=Kim880198
SUPABASE_URL=https://ytqxxodyecdeosnqoure.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
GEMINI_API_KEY=AIzaSyDHeDWidjS9PSIwxKXb_BtXCzI7HIzjOiM
ENVIRONMENT=production
DEBUG=false
```

## 🌍 مميزات الاستضافة:

✅ **يعمل 24/7** بدون الحاجة لتشغيل الكمبيوتر
✅ **HTTPS دائم** بدون ngrok
✅ **رابط ثابت** لا يتغير
✅ **وصول عالمي** من أي مكان في العالم
✅ **أداء عالي** وسرعة ممتازة
✅ **نسخ احتياطي تلقائي**

## 🔄 بعد النشر:

1. احصل على رابط التطبيق (مثل: https://your-app.railway.app)
2. اختبر البوت
3. اختبر صفحات المودات
4. شارك الرابط مع المستخدمين

## 🆘 استكشاف الأخطاء:

- تحقق من logs التطبيق
- تأكد من صحة متغيرات البيئة
- تأكد من عمل قاعدة البيانات
- تحقق من requirements.txt

## 📞 للدعم:
تواصل مع المطور: @Kim880198
"""
    
    with open("DEPLOYMENT_GUIDE.md", "w", encoding="utf-8") as f:
        f.write(guide)
    print("✅ تم إنشاء DEPLOYMENT_GUIDE.md")

def show_hosting_options():
    """عرض خيارات الاستضافة"""
    print("\n" + "="*70)
    print("🌐 خيارات الاستضافة المجانية")
    print("="*70)
    
    print("\n🥇 الخيار الأول: Railway (الأفضل)")
    print("✅ مجاني إلى الأبد")
    print("✅ 500 ساعة شهرياً")
    print("✅ HTTPS تلقائي")
    print("✅ لا ينام")
    print("🔗 https://railway.app")
    
    print("\n🥈 الخيار الثاني: Render")
    print("✅ 750 ساعة مجانية")
    print("✅ HTTPS تلقائي")
    print("⚠️ ينام بعد عدم الاستخدام")
    print("🔗 https://render.com")
    
    print("\n🥉 الخيار الثالث: Heroku")
    print("⚠️ 550 ساعة مجانية")
    print("✅ HTTPS تلقائي")
    print("⚠️ ينام بعد 30 دقيقة")
    print("🔗 https://heroku.com")
    
    print("\n💡 التوصية: استخدم Railway للحصول على أفضل تجربة مجانية!")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    print("🔧 إعداد ملفات النشر...")
    create_deployment_files()
    update_requirements()
    create_web_server_standalone()
    create_main_standalone()
    create_deployment_guide()
    
    show_hosting_options()
    
    print("\n" + "="*70)
    print("🎉 تم إعداد ملفات النشر بنجاح!")
    print("="*70)
    
    print("\n📋 الخطوات التالية:")
    print("1. 📁 ارفع الكود إلى GitHub")
    print("2. 🌐 اختر خدمة استضافة (Railway موصى به)")
    print("3. 🔗 اربط المستودع")
    print("4. ⚙️ أضف متغيرات البيئة")
    print("5. 🚀 انتظر النشر")
    print("6. 🧪 اختبر البوت")
    
    print("\n📖 راجع ملف DEPLOYMENT_GUIDE.md للتفاصيل الكاملة")
    print("📞 للدعم: @Kim880198")

if __name__ == "__main__":
    main()
