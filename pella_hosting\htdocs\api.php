<?php
define('INCLUDED', true);
require_once 'config.php';
require_once 'logs.php';

// Supabase PHP Client (بسيط)
class SupabaseClient {
    private $url;
    private $key;
    private $service_key;
    private $headers;

    public function __construct($url, $key, $service_key = null) {
        $this->url = $url;
        $this->key = $key;
        $this->service_key = $service_key;

        // استخدام المفتاح الصحيح للـ Authorization
        $auth_key = $this->service_key ? $this->service_key : $this->key;

        $this->headers = [
            'apikey: ' . $auth_key,
            'Authorization: Bearer ' . $auth_key,
            'Content-Type: application/json',
            'Accept: application/json',
            'Prefer: return=representation'
        ];
    }

    private function makeRequest($method, $path, $data = []) {
        $ch = curl_init();
        $full_url = $this->url . $path;

        curl_setopt($ch, CURLOPT_URL, $full_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, getConfig('database')['supabase']['timeout']);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // قد تحتاج إلى تعطيل هذا على بعض الاستضافات المجانية

        switch ($method) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                break;
            case 'GET':
            default:
                // GET requests don't need POSTFIELDS
                break;
        }

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($response === false) {
            logError("cURL Error for $full_url: $curl_error");
            return ['error' => 'cURL Error: ' . $curl_error, 'http_code' => 0];
        }

        $decoded_response = json_decode($response, true);

        if ($http_code >= 400) {
            logError("Supabase API Error ($http_code) for $full_url: " . ($decoded_response['message'] ?? $response));
            return ['error' => $decoded_response['message'] ?? 'Unknown API Error', 'http_code' => $http_code];
        }

        return $decoded_response;
    }

    public function from($tableName) {
        return new SupabaseTableQuery($this, $tableName);
    }
}

class SupabaseTableQuery {
    private $client;
    private $tableName;
    private $query = [];
    private $selectColumns = '*';

    public function __construct($client, $tableName) {
        $this->client = $client;
        $this->tableName = $tableName;
    }

    public function select($columns = '*') {
        $this->selectColumns = $columns;
        return $this;
    }

    public function eq($column, $value) {
        $this->query['eq'][] = ['column' => $column, 'value' => $value];
        return $this;
    }

    public function filter($column, $operator, $value) {
        $this->query['filter'][] = ['column' => $column, 'operator' => $operator, 'value' => $value];
        return $this;
    }

    public function order($column, $ascending = true) {
        $this->query['order'][] = ['column' => $column, 'ascending' => $ascending];
        return $this;
    }

    public function limit($count) {
        $this->query['limit'] = $count;
        return $this;
    }

    public function single() {
        $this->query['single'] = true;
        return $this->execute('GET');
    }

    public function execute($method) {
        $path = '/rest/v1/' . $this->tableName . '?select=' . urlencode($this->selectColumns);

        foreach ($this->query as $key => $value) {
            if ($key === 'eq') {
                foreach ($value as $eq_clause) {
                    $path .= '&' . urlencode($eq_clause['column']) . '=eq.' . urlencode($eq_clause['value']);
                }
            } elseif ($key === 'filter') {
                foreach ($value as $filter_clause) {
                    $path .= '&' . urlencode($filter_clause['column']) . '=' . urlencode($filter_clause['operator']) . '.' . urlencode($filter_clause['value']);
                }
            } elseif ($key === 'order') {
                foreach ($value as $order_clause) {
                    $path .= '&order=' . urlencode($order_clause['column']) . '.' . ($order_clause['ascending'] ? 'asc' : 'desc');
                }
            } elseif ($key === 'limit') {
                $path .= '&limit=' . urlencode($value);
            }
        }
        
        if (isset($this->query['single']) && $this->query['single']) {
            $path .= '&limit=1'; // Ensure only one record is returned
        }

        return $this->client->makeRequest($method, $path);
    }

    public function insert($data) {
        return $this->client->makeRequest('POST', '/rest/v1/' . $this->tableName, $data);
    }

    public function update($data) {
        // For update, you typically need a WHERE clause (e.g., eq)
        // This simplified client assumes 'eq' is used for updates
        $path = '/rest/v1/' . $this->tableName;
        if (isset($this->query['eq'])) {
            foreach ($this->query['eq'] as $eq_clause) {
                $path .= '?' . urlencode($eq_clause['column']) . '=eq.' . urlencode($eq_clause['value']);
            }
        }
        return $this->client->makeRequest('PATCH', $path, $data);
    }

    public function delete() {
        $path = '/rest/v1/' . $this->tableName;
        if (isset($this->query['eq'])) {
            foreach ($this->query['eq'] as $eq_clause) {
                $path .= '?' . urlencode($eq_clause['column']) . '=eq.' . urlencode($eq_clause['value']);
            }
        }
        return $this->client->makeRequest('DELETE', $path);
    }
}

// تهيئة عميل Supabase
$supabase_config = getConfig('database')['supabase'];
$supabase = new SupabaseClient(
    $supabase_config['url'],
    $supabase_config['key'],
    $supabase_config['service_key']
);

// معالجة طلبات API
header('Content-Type: application/json');

$path = $_GET['path'] ?? '';
$method = $_SERVER['REQUEST_METHOD'];

switch ($path) {
    case '/test':
        // اختبار الاتصال مع قاعدة البيانات
        if ($method === 'GET') {
            $test_result = $supabase->from(getConfig('tables')['mods'])
                                   ->select('id,name')
                                   ->limit(1)
                                   ->execute('GET');

            if (isset($test_result['error'])) {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Database connection failed',
                    'details' => $test_result,
                    'config' => [
                        'url' => SUPABASE_URL,
                        'table' => getConfig('tables')['mods']
                    ]
                ]);
            } else {
                echo json_encode([
                    'status' => 'success',
                    'message' => 'Database connection successful',
                    'data' => $test_result,
                    'config' => [
                        'url' => SUPABASE_URL,
                        'table' => getConfig('tables')['mods']
                    ]
                ]);
            }
        }
        break;

    case '/mods':
        if ($method === 'GET') {
            $limit = $_GET['limit'] ?? 10;
            $search = $_GET['search'] ?? null;

            $query = $supabase->from(getConfig('tables')['mods'])
                             ->select('*')
                             ->limit($limit);

            if ($search) {
                // البحث في الاسم والوصف
                $query->filter('name', 'ilike', "*$search*");
            }

            $mods_data = $query->execute('GET');
            echo json_encode($mods_data);
        } else {
            echo json_encode(['error' => 'Method not allowed', 'http_code' => 405]);
        }
        break;

    case '/mod':
        if ($method === 'GET') {
            $mod_id = $_GET['id'] ?? null;
            if ($mod_id) {
                $mod_data = $supabase->from(getConfig('tables')['mods'])
                                     ->select('*')
                                     ->eq('id', $mod_id)
                                     ->single();
                echo json_encode($mod_data);
            } else {
                echo json_encode(['error' => 'Mod ID is required', 'http_code' => 400]);
            }
        } else {
            echo json_encode(['error' => 'Method not allowed', 'http_code' => 405]);
        }
        break;
    case '/ads_settings':
        if ($method === 'GET') {
            $user_id = $_GET['user_id'] ?? null;
            if ($user_id) {
                $settings = $supabase->from(getConfig('tables')['ads_settings'])
                                     ->select('*')
                                     ->eq('user_id', $user_id)
                                     ->single();
                echo json_encode($settings);
            } else {
                echo json_encode(['error' => 'User ID is required', 'http_code' => 400]);
            }
        } else {
            echo json_encode(['error' => 'Method not allowed', 'http_code' => 405]);
        }
        break;
    case '/tasks':
        if ($method === 'GET') {
            $user_id = $_GET['user_id'] ?? null;
            if ($user_id) {
                $tasks = $supabase->from(getConfig('tables')['tasks'])
                                  ->select('*')
                                  ->eq('user_id', $user_id)
                                  ->execute('GET');
                echo json_encode($tasks);
            } else {
                echo json_encode(['error' => 'User ID is required', 'http_code' => 400]);
            }
        } else {
            echo json_encode(['error' => 'Method not allowed', 'http_code' => 405]);
        }
        break;
    case '/completed_tasks':
        if ($method === 'POST') {
            $data = json_decode(file_get_contents('php://input'), true);
            if (isset($data['user_id']) && isset($data['task_id'])) {
                $result = $supabase->from(getConfig('tables')['completed_tasks'])
                                   ->insert($data);
                echo json_encode($result);
            } else {
                echo json_encode(['error' => 'User ID and Task ID are required', 'http_code' => 400]);
            }
        } else {
            echo json_encode(['error' => 'Method not allowed', 'http_code' => 405]);
        }
        break;
    case '/ad_click':
        if ($method === 'POST') {
            $data = json_decode(file_get_contents('php://input'), true);
            if (isset($data['ad_id']) && isset($data['user_id'])) {
                $result = $supabase->from(getConfig('tables')['ad_clicks'])
                                   ->insert($data);
                echo json_encode($result);
            } else {
                echo json_encode(['error' => 'Ad ID and User ID are required', 'http_code' => 400]);
            }
        } else {
            echo json_encode(['error' => 'Method not allowed', 'http_code' => 405]);
        }
        break;

    case '/custom_download_link':
        if ($method === 'GET') {
            $channel_id = $_GET['channel_id'] ?? null;
            if ($channel_id) {
                $link_data = $supabase->from(getConfig('tables')['custom_download_links'])
                                     ->select('*')
                                     ->eq('channel_id', $channel_id)
                                     ->single();
                echo json_encode($link_data);
            } else {
                echo json_encode(['error' => 'Channel ID is required', 'http_code' => 400]);
            }
        } elseif ($method === 'POST') {
            $data = json_decode(file_get_contents('php://input'), true);
            if (isset($data['channel_id']) && isset($data['custom_link'])) {
                $result = $supabase->from(getConfig('tables')['custom_download_links'])
                                   ->insert($data);
                echo json_encode($result);
            } else {
                echo json_encode(['error' => 'Channel ID and Custom Link are required', 'http_code' => 400]);
            }
        } else {
            echo json_encode(['error' => 'Method not allowed', 'http_code' => 405]);
        }
        break;

    case '/url_shortener':
        if ($method === 'GET') {
            $user_id = $_GET['user_id'] ?? null;
            if ($user_id) {
                $settings = $supabase->from(getConfig('tables')['url_shortener_settings'])
                                     ->select('*')
                                     ->eq('user_id', $user_id)
                                     ->single();
                echo json_encode($settings);
            } else {
                echo json_encode(['error' => 'User ID is required', 'http_code' => 400]);
            }
        } elseif ($method === 'POST') {
            $data = json_decode(file_get_contents('php://input'), true);
            if (isset($data['user_id'])) {
                $result = $supabase->from(getConfig('tables')['url_shortener_settings'])
                                   ->insert($data);
                echo json_encode($result);
            } else {
                echo json_encode(['error' => 'User ID is required', 'http_code' => 400]);
            }
        } else {
            echo json_encode(['error' => 'Method not allowed', 'http_code' => 405]);
        }
        break;

    case '/stats':
        if ($method === 'GET') {
            // إحصائيات عامة
            $mods_count = $supabase->from(getConfig('tables')['mods'])
                                  ->select('id')
                                  ->execute('GET');

            $stats = [
                'total_mods' => is_array($mods_count) ? count($mods_count) : 0,
                'database_status' => 'connected',
                'api_version' => '1.0',
                'last_updated' => date('Y-m-d H:i:s')
            ];

            echo json_encode($stats);
        } else {
            echo json_encode(['error' => 'Method not allowed', 'http_code' => 405]);
        }
        break;

    default:
        echo json_encode(['error' => 'Invalid API path', 'http_code' => 404]);
        break;
}
?>
