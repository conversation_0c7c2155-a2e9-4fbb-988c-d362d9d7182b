<?php
/**
 * ملف اختبار شامل للاتصال وقاعدة البيانات
 * Comprehensive Connection and Database Test
 */

define('INCLUDED', true);
require_once 'config_new.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال الشامل - Comprehensive Connection Test</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .status-ok { color: #10b981; }
        .status-error { color: #ef4444; }
        .status-warning { color: #f59e0b; }
        .status-info { color: #3b82f6; }
        .test-section { border: 1px solid #e5e7eb; border-radius: 8px; margin-bottom: 1rem; }
        .code-block { background: #1f2937; color: #f9fafb; padding: 1rem; border-radius: 4px; font-family: monospace; font-size: 0.875rem; overflow-x: auto; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-6 max-w-6xl">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-center mb-6 text-gray-800">
                🔍 اختبار الاتصال الشامل
            </h1>
            
            <?php
            echo '<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">';
            
            // اختبار 1: معلومات البيئة
            echo '<div class="test-section p-4">';
            echo '<h2 class="text-xl font-semibold mb-4">🖥️ معلومات البيئة</h2>';
            echo '<div class="space-y-2">';
            echo '<div class="status-ok">✅ PHP Version: ' . PHP_VERSION . '</div>';
            echo '<div class="status-ok">✅ Server: ' . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . '</div>';
            echo '<div class="status-ok">✅ Host: ' . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . '</div>';
            echo '<div class="status-ok">✅ Document Root: ' . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . '</div>';
            echo '<div class="status-ok">✅ Memory Limit: ' . ini_get('memory_limit') . '</div>';
            echo '<div class="status-ok">✅ Max Execution Time: ' . ini_get('max_execution_time') . 's</div>';
            echo '</div>';
            echo '</div>';
            
            // اختبار 2: الإضافات المطلوبة
            echo '<div class="test-section p-4">';
            echo '<h2 class="text-xl font-semibold mb-4">🔧 الإضافات المطلوبة</h2>';
            echo '<div class="space-y-2">';
            $extensions = ['curl', 'json', 'mbstring', 'openssl'];
            foreach ($extensions as $ext) {
                if (extension_loaded($ext)) {
                    echo '<div class="status-ok">✅ ' . $ext . ' - متوفر</div>';
                } else {
                    echo '<div class="status-error">❌ ' . $ext . ' - غير متوفر</div>';
                }
            }
            echo '</div>';
            echo '</div>';
            
            // اختبار 3: اختبار cURL أساسي
            echo '<div class="test-section p-4">';
            echo '<h2 class="text-xl font-semibold mb-4">🌐 اختبار cURL الأساسي</h2>';
            echo '<div class="space-y-2">';
            
            if (function_exists('curl_init')) {
                echo '<div class="status-ok">✅ cURL متوفر</div>';
                
                // اختبار اتصال بسيط
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'https://httpbin.org/get');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);
                
                if ($error) {
                    echo '<div class="status-error">❌ cURL Error: ' . htmlspecialchars($error) . '</div>';
                } elseif ($http_code === 200) {
                    echo '<div class="status-ok">✅ cURL يعمل بشكل صحيح</div>';
                } else {
                    echo '<div class="status-warning">⚠️ cURL HTTP Code: ' . $http_code . '</div>';
                }
            } else {
                echo '<div class="status-error">❌ cURL غير متوفر</div>';
            }
            echo '</div>';
            echo '</div>';
            
            // اختبار 4: اختبار Supabase مفصل
            echo '<div class="test-section p-4 lg:col-span-2">';
            echo '<h2 class="text-xl font-semibold mb-4">🗄️ اختبار Supabase المفصل</h2>';
            
            // اختبار الاتصال الأساسي
            echo '<h3 class="text-lg font-medium mb-2">1. اختبار الاتصال الأساسي</h3>';
            $basic_test = testSupabaseConnection();
            
            if ($basic_test['success']) {
                echo '<div class="status-ok">✅ الاتصال الأساسي ناجح</div>';
            } else {
                echo '<div class="status-error">❌ فشل الاتصال الأساسي</div>';
                echo '<div class="status-error">Error: ' . htmlspecialchars($basic_test['error']) . '</div>';
                echo '<div class="status-error">HTTP Code: ' . $basic_test['http_code'] . '</div>';
            }
            
            // اختبار جلب البيانات
            echo '<h3 class="text-lg font-medium mb-2 mt-4">2. اختبار جلب البيانات</h3>';
            
            $endpoints_to_test = [
                '/rest/v1/' => 'Root endpoint',
                '/rest/v1/minemods' => 'Minemods table',
                '/rest/v1/minemods?select=id,name&limit=1' => 'Limited select',
                '/rest/v1/minemods?id=eq.1' => 'Specific mod'
            ];
            
            foreach ($endpoints_to_test as $endpoint => $description) {
                echo '<div class="mb-2">';
                echo '<strong>' . htmlspecialchars($description) . ':</strong><br>';
                
                $test_result = makeSupabaseRequest($endpoint);
                
                if ($test_result['success']) {
                    echo '<div class="status-ok">✅ نجح - HTTP ' . $test_result['http_code'] . '</div>';
                    if (!empty($test_result['data'])) {
                        $data_count = is_array($test_result['data']) ? count($test_result['data']) : 1;
                        echo '<div class="status-info">📊 عدد السجلات: ' . $data_count . '</div>';
                        
                        // عرض عينة من البيانات
                        if (is_array($test_result['data']) && !empty($test_result['data'])) {
                            $sample = $test_result['data'][0];
                            if (isset($sample['id'])) {
                                echo '<div class="status-info">🆔 أول معرف: ' . $sample['id'] . '</div>';
                            }
                            if (isset($sample['name'])) {
                                echo '<div class="status-info">📝 أول اسم: ' . htmlspecialchars($sample['name']) . '</div>';
                            }
                        }
                    }
                } else {
                    echo '<div class="status-error">❌ فشل - HTTP ' . $test_result['http_code'] . '</div>';
                    echo '<div class="status-error">Error: ' . htmlspecialchars($test_result['error']) . '</div>';
                }
                echo '</div>';
            }
            
            // اختبار 5: عرض البيانات الخام
            echo '<h3 class="text-lg font-medium mb-2 mt-4">3. عينة من البيانات الخام</h3>';
            $raw_data_test = makeSupabaseRequest('/rest/v1/minemods?limit=3');
            
            if ($raw_data_test['success'] && !empty($raw_data_test['data'])) {
                echo '<div class="code-block">';
                echo htmlspecialchars(json_encode($raw_data_test['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                echo '</div>';
            } else {
                echo '<div class="status-error">❌ لا يمكن جلب البيانات الخام</div>';
                if (isset($raw_data_test['raw_response'])) {
                    echo '<div class="code-block">';
                    echo 'Raw Response: ' . htmlspecialchars(substr($raw_data_test['raw_response'], 0, 500));
                    echo '</div>';
                }
            }
            
            echo '</div>';
            
            // اختبار 6: اختبار API الجديد
            echo '<div class="test-section p-4 lg:col-span-2">';
            echo '<h2 class="text-xl font-semibold mb-4">🔌 اختبار API الجديد</h2>';
            
            $api_tests = [
                'api_new.php?path=/test' => 'اختبار API',
                'api_new.php?path=/mod&id=1' => 'جلب مود محدد',
                'api_new.php?path=/mods&limit=3' => 'قائمة المودات'
            ];
            
            foreach ($api_tests as $url => $description) {
                echo '<div class="mb-4">';
                echo '<strong>' . htmlspecialchars($description) . ':</strong><br>';
                
                $full_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $url;
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $full_url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 15);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);
                
                if ($error) {
                    echo '<div class="status-error">❌ cURL Error: ' . htmlspecialchars($error) . '</div>';
                } elseif ($http_code === 200) {
                    echo '<div class="status-ok">✅ نجح - HTTP ' . $http_code . '</div>';
                    
                    $json_data = json_decode($response, true);
                    if ($json_data) {
                        echo '<div class="code-block">';
                        echo htmlspecialchars(json_encode($json_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                        echo '</div>';
                    } else {
                        echo '<div class="status-warning">⚠️ استجابة غير JSON صحيحة</div>';
                        echo '<div class="code-block">' . htmlspecialchars(substr($response, 0, 500)) . '</div>';
                    }
                } else {
                    echo '<div class="status-error">❌ فشل - HTTP ' . $http_code . '</div>';
                    echo '<div class="code-block">' . htmlspecialchars(substr($response, 0, 500)) . '</div>';
                }
                echo '</div>';
            }
            
            echo '</div>';
            
            // اختبار 7: اختبار الملفات
            echo '<div class="test-section p-4">';
            echo '<h2 class="text-xl font-semibold mb-4">📁 اختبار الملفات</h2>';
            echo '<div class="space-y-2">';
            
            $files_to_check = [
                'config_new.php' => 'ملف الإعدادات الجديد',
                'api_new.php' => 'API الجديد',
                'index_new.php' => 'الصفحة الرئيسية الجديدة',
                'style.css' => 'ملف التصميم',
                'script.js' => 'ملف الجافاسكريبت'
            ];
            
            foreach ($files_to_check as $file => $description) {
                if (file_exists($file)) {
                    $size = filesize($file);
                    echo '<div class="status-ok">✅ ' . $description . ' (' . number_format($size) . ' بايت)</div>';
                } else {
                    echo '<div class="status-error">❌ ' . $description . ' غير موجود</div>';
                }
            }
            echo '</div>';
            echo '</div>';
            
            // اختبار 8: روابط الاختبار
            echo '<div class="test-section p-4">';
            echo '<h2 class="text-xl font-semibold mb-4">🔗 روابط الاختبار</h2>';
            echo '<div class="space-y-2">';
            
            $base_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
            
            echo '<div><a href="' . $base_url . '/index_new.php?id=1&lang=ar" class="text-blue-600 hover:underline" target="_blank">🎮 اختبار الصفحة الجديدة (عربي)</a></div>';
            echo '<div><a href="' . $base_url . '/index_new.php?id=1&lang=en" class="text-blue-600 hover:underline" target="_blank">🎮 اختبار الصفحة الجديدة (إنجليزي)</a></div>';
            echo '<div><a href="' . $base_url . '/api_new.php?path=/test" class="text-blue-600 hover:underline" target="_blank">🔌 اختبار API الجديد</a></div>';
            echo '<div><a href="' . $base_url . '/api_new.php?path=/mod&id=1" class="text-blue-600 hover:underline" target="_blank">📦 اختبار جلب مود</a></div>';
            
            echo '</div>';
            echo '</div>';
            
            echo '</div>'; // إغلاق grid
            
            // ملخص النتائج
            echo '<div class="mt-6 p-4 bg-blue-50 rounded-lg">';
            echo '<h2 class="text-xl font-semibold mb-4">📊 ملخص النتائج</h2>';
            echo '<div class="grid grid-cols-1 md:grid-cols-3 gap-4">';
            
            // فحص عام للحالة
            $overall_status = $basic_test['success'] ? 'جيد' : 'يحتاج إصلاح';
            $status_class = $basic_test['success'] ? 'status-ok' : 'status-error';
            
            echo '<div class="text-center">';
            echo '<div class="text-2xl mb-2">🎯</div>';
            echo '<div class="font-semibold">الحالة العامة</div>';
            echo '<div class="' . $status_class . '">' . $overall_status . '</div>';
            echo '</div>';
            
            echo '<div class="text-center">';
            echo '<div class="text-2xl mb-2">🗄️</div>';
            echo '<div class="font-semibold">قاعدة البيانات</div>';
            echo '<div class="' . ($basic_test['success'] ? 'status-ok' : 'status-error') . '">' . ($basic_test['success'] ? 'متصلة' : 'غير متصلة') . '</div>';
            echo '</div>';
            
            echo '<div class="text-center">';
            echo '<div class="text-2xl mb-2">📁</div>';
            echo '<div class="font-semibold">الملفات</div>';
            echo '<div class="status-ok">مكتملة</div>';
            echo '</div>';
            
            echo '</div>';
            echo '</div>';
            ?>
            
            <!-- أزرار الإجراءات -->
            <div class="flex space-x-4 justify-center mt-6">
                <button onclick="location.reload()" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                    🔄 إعادة الاختبار
                </button>
                <a href="deploy.php?setup=true" class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600">
                    🔧 صفحة الإعداد الأصلية
                </a>
                <a href="index_new.php?id=1&lang=ar" class="bg-purple-500 text-white px-6 py-2 rounded hover:bg-purple-600">
                    🎮 اختبار الصفحة الجديدة
                </a>
            </div>
        </div>
    </div>
</body>
</html>
