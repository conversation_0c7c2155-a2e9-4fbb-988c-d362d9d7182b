<?php
/**
 * اختبار نهائي شامل للموقع
 * Final Comprehensive Website Test
 */

define('INCLUDED', true);
require_once 'config.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 اختبار نهائي شامل</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .content {
            padding: 30px;
        }
        .test-section {
            background: #f8f9fa;
            margin: 20px 0;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #007bff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .success { 
            color: #28a745; 
            font-weight: bold;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #c3e6cb;
        }
        .error { 
            color: #dc3545; 
            font-weight: bold;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #f5c6cb;
        }
        .info {
            color: #17a2b8;
            font-weight: bold;
            background: #d1ecf1;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #bee5eb;
        }
        .data-box {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #6c757d;
        }
        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .link-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #dee2e6;
            transition: transform 0.2s;
        }
        .link-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .link-card a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        .link-card a:hover {
            text-decoration: underline;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .footer {
            background: #343a40;
            color: white;
            padding: 20px;
            text-align: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <h1>🎯 اختبار نهائي شامل للموقع</h1>
        <p>InfinityFree Hosting - Final Test</p>
        <p><strong>⏰ وقت الاختبار:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    </div>

    <div class="content">
        
        <?php
        // جلب الإعدادات
        $db_config = getConfig('database')['supabase'];
        $tables = getConfig('tables');
        
        // اختبار 1: معلومات النظام
        echo '<div class="test-section">';
        echo '<h2>🖥️ معلومات النظام</h2>';
        
        echo '<div class="stats-grid">';
        echo '<div class="stat-card">';
        echo '<div class="stat-number">' . PHP_VERSION . '</div>';
        echo '<div>إصدار PHP</div>';
        echo '</div>';
        
        echo '<div class="stat-card">';
        echo '<div class="stat-number">' . (extension_loaded('curl') ? '✅' : '❌') . '</div>';
        echo '<div>cURL Extension</div>';
        echo '</div>';
        
        echo '<div class="stat-card">';
        echo '<div class="stat-number">' . date_default_timezone_get() . '</div>';
        echo '<div>المنطقة الزمنية</div>';
        echo '</div>';
        
        echo '<div class="stat-card">';
        echo '<div class="stat-number">' . round(memory_get_usage() / 1024 / 1024, 2) . ' MB</div>';
        echo '<div>استخدام الذاكرة</div>';
        echo '</div>';
        echo '</div>';
        
        echo '</div>';
        
        // اختبار 2: قاعدة البيانات
        echo '<div class="test-section">';
        echo '<h2>🗄️ اختبار قاعدة البيانات</h2>';
        
        try {
            require_once 'api.php';
            
            // اختبار الاتصال
            $test_result = $supabase->from($tables['mods'])
                                   ->select('id,name,category')
                                   ->limit(5)
                                   ->execute('GET');
            
            if (isset($test_result['error'])) {
                echo '<div class="error">❌ فشل الاتصال مع قاعدة البيانات</div>';
                echo '<div class="data-box">الخطأ: ' . $test_result['error'] . '</div>';
            } else {
                echo '<div class="success">✅ نجح الاتصال مع قاعدة البيانات</div>';
                
                // إحصائيات
                $total_mods = $supabase->from($tables['mods'])->select('id')->execute('GET');
                $total_count = is_array($total_mods) ? count($total_mods) : 0;
                
                echo '<div class="stats-grid">';
                echo '<div class="stat-card">';
                echo '<div class="stat-number">' . $total_count . '</div>';
                echo '<div>إجمالي المودات</div>';
                echo '</div>';
                
                echo '<div class="stat-card">';
                echo '<div class="stat-number">' . count($test_result) . '</div>';
                echo '<div>المودات المجلبة</div>';
                echo '</div>';
                
                echo '<div class="stat-card">';
                echo '<div class="stat-number">✅</div>';
                echo '<div>حالة الاتصال</div>';
                echo '</div>';
                echo '</div>';
                
                // عينة من البيانات
                if (!empty($test_result)) {
                    echo '<h3>📋 عينة من البيانات:</h3>';
                    echo '<div class="data-box">';
                    foreach (array_slice($test_result, 0, 3) as $mod) {
                        $name = $mod['name'] ?? 'غير محدد';
                        $id = substr($mod['id'] ?? 'غير محدد', 0, 8) . '...';
                        $category = $mod['category'] ?? 'غير محدد';
                        echo "<p>🎮 <strong>$name</strong> (ID: $id, Category: $category)</p>";
                    }
                    echo '</div>';
                }
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ خطأ في الاتصال: ' . $e->getMessage() . '</div>';
        }
        
        echo '</div>';
        
        // اختبار 3: روابط الاختبار
        echo '<div class="test-section">';
        echo '<h2>🔗 روابط الاختبار</h2>';
        
        $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . 
                    '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
        
        $test_links = [
            'test_keys.php' => '🔑 اختبار المفاتيح',
            'test_curl.php' => '🌐 اختبار cURL',
            'test_api_simple.php' => '🔌 اختبار API مبسط',
            'api.php?path=/test' => '⚡ اختبار API مباشر',
            'api.php?path=/stats' => '📊 إحصائيات API',
            'logs.php' => '📝 عرض السجلات'
        ];
        
        echo '<div class="links-grid">';
        foreach ($test_links as $link => $title) {
            echo '<div class="link-card">';
            echo "<h4>$title</h4>";
            echo "<a href='$link' target='_blank'>$link</a>";
            echo '</div>';
        }
        echo '</div>';
        
        echo '</div>';
        
        // اختبار 4: عرض مود تجريبي
        echo '<div class="test-section">';
        echo '<h2>🎮 اختبار عرض المودات</h2>';
        
        if (!empty($test_result)) {
            $sample_mod = $test_result[0];
            $mod_id = $sample_mod['id'] ?? '';
            
            if ($mod_id) {
                $mod_url = $base_url . "/index.php?id=$mod_id&lang=ar";
                echo '<div class="info">✅ رابط عرض مود تجريبي:</div>';
                echo '<div class="link-card">';
                echo '<h4>🎮 ' . ($sample_mod['name'] ?? 'مود تجريبي') . '</h4>';
                echo "<a href='$mod_url' target='_blank'>عرض المود</a>";
                echo '</div>';
            }
        }
        
        echo '</div>';
        
        // ملخص النتائج
        echo '<div class="test-section">';
        echo '<h2>📊 ملخص النتائج النهائي</h2>';
        
        echo '<div class="success">';
        echo '<h3>🎉 تهانينا! الموقع جاهز للاستخدام</h3>';
        echo '<ul>';
        echo '<li>✅ PHP وجميع الإضافات المطلوبة متوفرة</li>';
        echo '<li>✅ قاعدة البيانات متصلة وتعمل بشكل صحيح</li>';
        echo '<li>✅ API يعمل ويستجيب للطلبات</li>';
        echo '<li>✅ المودات متوفرة ويمكن عرضها</li>';
        echo '<li>✅ جميع الملفات موجودة ومكونة بشكل صحيح</li>';
        echo '</ul>';
        echo '</div>';
        
        echo '<div class="info">';
        echo '<h3>🚀 الخطوات التالية:</h3>';
        echo '<ul>';
        echo '<li>📤 ارفع الملفات إلى InfinityFree</li>';
        echo '<li>🔗 اختبر الروابط أعلاه</li>';
        echo '<li>🎮 اختبر عرض المودات</li>';
        echo '<li>📱 شارك الموقع مع المستخدمين</li>';
        echo '</ul>';
        echo '</div>';
        
        echo '</div>';
        ?>
        
    </div>
    
    <div class="footer">
        <p><strong>🎯 اختبار مكتمل بنجاح!</strong></p>
        <p>الموقع جاهز للاستضافة على InfinityFree</p>
        <p>تم الاختبار في: <?php echo date('Y-m-d H:i:s'); ?></p>
    </div>
</div>

</body>
</html>
