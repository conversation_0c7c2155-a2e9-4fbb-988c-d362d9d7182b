2025-06-12 14:52:06,015 - __main__ - INFO - 🔧 بدء اختبار سريع للبوت
2025-06-12 14:52:06,015 - __main__ - INFO - ============================================================
2025-06-12 14:52:06,015 - __main__ - INFO - 
🧪 فحص الملفات المطلوبة:
2025-06-12 14:52:06,016 - __main__ - INFO - ----------------------------------------
2025-06-12 14:52:06,016 - __main__ - INFO - ✅ الملف موجود: main.py
2025-06-12 14:52:06,016 - __main__ - INFO - ✅ الملف موجود: supabase_client.py
2025-06-12 14:52:06,016 - __main__ - INFO - ✅ الملف موجود: network_config.py
2025-06-12 14:52:06,052 - __main__ - INFO - ✅ نجح: فحص الملفات المطلوبة
2025-06-12 14:52:06,053 - __main__ - INFO - 
🧪 فحص Bot Token:
2025-06-12 14:52:06,053 - __main__ - INFO - ----------------------------------------
2025-06-12 14:52:09,056 - secure_config - INFO - تم تحميل ملف .env بنجاح
2025-06-12 14:52:09,261 - network_config - INFO - ✅ تم تطبيق تحسينات Windows
2025-06-12 14:52:09,262 - network_config - INFO - ✅ تم تطبيق تحسينات الشبكة
2025-06-12 14:52:09,284 - __main__ - ERROR - ❌ لا يمكن استيراد BOT_TOKEN
2025-06-12 14:52:09,285 - __main__ - ERROR - ❌ فشل: فحص Bot Token
2025-06-12 14:52:09,285 - __main__ - INFO - 
🧪 فحص معرف الأدمن:
2025-06-12 14:52:09,286 - __main__ - INFO - ----------------------------------------
2025-06-12 14:52:09,287 - __main__ - INFO - ✅ معرف الأدمن موجود: 7513880877
2025-06-12 14:52:09,287 - __main__ - INFO - ✅ نجح: فحص معرف الأدمن
2025-06-12 14:52:09,287 - __main__ - INFO - 
🧪 فحص معالجات الأزرار:
2025-06-12 14:52:09,288 - __main__ - INFO - ----------------------------------------
2025-06-12 14:52:09,301 - __main__ - INFO - ✅ تم العثور على pattern CallbackQueryHandler
2025-06-12 14:52:09,307 - __main__ - INFO - ✅ الزر يعمل: admin_delete_user_data
2025-06-12 14:52:09,308 - __main__ - INFO - ✅ الزر يعمل: delete_user_by_id
2025-06-12 14:52:09,308 - __main__ - INFO - ✅ الزر يعمل: delete_user_from_list
2025-06-12 14:52:09,309 - __main__ - INFO - ✅ الزر يعمل: confirm_delete_user_123456789
2025-06-12 14:52:09,309 - __main__ - INFO - ✅ جميع أزرار حذف بيانات المستخدم تعمل
2025-06-12 14:52:09,310 - __main__ - INFO - ✅ نجح: فحص معالجات الأزرار
2025-06-12 14:52:09,311 - __main__ - INFO - 
🧪 اختبار بدء التشغيل:
2025-06-12 14:52:09,311 - __main__ - INFO - ----------------------------------------
2025-06-12 14:52:09,312 - __main__ - INFO - 🚀 بدء اختبار تشغيل البوت...
2025-06-12 14:52:09,312 - __main__ - INFO - ✅ تم استيراد دوال حذف بيانات المستخدم بنجاح
2025-06-12 14:52:09,313 - __main__ - INFO - ✅ تم استيراد supabase_client بنجاح
2025-06-12 14:52:09,313 - __main__ - INFO - ✅ تم استيراد الدوال الرئيسية بنجاح
2025-06-12 14:52:09,315 - __main__ - INFO - ✅ تم تحميل بيانات القنوات: 7 مستخدم
2025-06-12 14:52:09,315 - __main__ - INFO - 🎉 جميع الاختبارات نجحت! البوت جاهز للتشغيل
2025-06-12 14:52:09,316 - __main__ - INFO - ✅ نجح: اختبار بدء التشغيل
2025-06-12 14:52:09,316 - __main__ - INFO - 
============================================================
2025-06-12 14:52:09,317 - __main__ - INFO - 📊 نتائج الاختبار السريع:
2025-06-12 14:52:09,317 - __main__ - INFO - ✅ نجح: 4
2025-06-12 14:52:09,318 - __main__ - INFO - ❌ فشل: 1
2025-06-12 14:52:09,318 - __main__ - INFO - 📈 معدل النجاح: 80.0%
2025-06-12 14:52:09,318 - __main__ - INFO - ============================================================
2025-06-12 14:52:09,319 - __main__ - ERROR - 
⚠️ فشل 1 اختبار.
2025-06-12 14:52:09,319 - __main__ - ERROR - 🔧 يرجى إصلاح المشاكل قبل تشغيل البوت.
2025-06-12 14:52:09,320 - __main__ - ERROR - ❌ لا يمكن استيراد BOT_TOKEN
2025-06-12 14:52:09,320 - __main__ - INFO - 
💡 نصيحة: تأكد من إضافة Bot Token الصحيح في main.py
2025-06-12 14:53:17,803 - __main__ - INFO - 🔧 بدء اختبار سريع للبوت
2025-06-12 14:53:17,803 - __main__ - INFO - ============================================================
2025-06-12 14:53:17,803 - __main__ - INFO - 
🧪 فحص الملفات المطلوبة:
2025-06-12 14:53:17,803 - __main__ - INFO - ----------------------------------------
2025-06-12 14:53:17,804 - __main__ - INFO - ✅ الملف موجود: main.py
2025-06-12 14:53:17,804 - __main__ - INFO - ✅ الملف موجود: supabase_client.py
2025-06-12 14:53:17,804 - __main__ - INFO - ✅ الملف موجود: network_config.py
2025-06-12 14:53:17,804 - __main__ - INFO - ✅ نجح: فحص الملفات المطلوبة
2025-06-12 14:53:17,804 - __main__ - INFO - 
🧪 فحص Bot Token:
2025-06-12 14:53:17,805 - __main__ - INFO - ----------------------------------------
2025-06-12 14:53:19,149 - secure_config - INFO - تم تحميل ملف .env بنجاح
2025-06-12 14:53:19,318 - network_config - INFO - ✅ تم تطبيق تحسينات Windows
2025-06-12 14:53:19,318 - network_config - INFO - ✅ تم تطبيق تحسينات الشبكة
2025-06-12 14:53:19,320 - __main__ - INFO - ✅ Bot Token موجود
2025-06-12 14:53:19,320 - __main__ - INFO - ✅ نجح: فحص Bot Token
2025-06-12 14:53:19,321 - __main__ - INFO - 
🧪 فحص معرف الأدمن:
2025-06-12 14:53:19,321 - __main__ - INFO - ----------------------------------------
2025-06-12 14:53:19,321 - __main__ - INFO - ✅ معرف الأدمن موجود: 7513880877
2025-06-12 14:53:19,321 - __main__ - INFO - ✅ نجح: فحص معرف الأدمن
2025-06-12 14:53:19,321 - __main__ - INFO - 
🧪 فحص معالجات الأزرار:
2025-06-12 14:53:19,322 - __main__ - INFO - ----------------------------------------
2025-06-12 14:53:19,327 - __main__ - INFO - ✅ تم العثور على pattern CallbackQueryHandler
2025-06-12 14:53:19,334 - __main__ - INFO - ✅ الزر يعمل: admin_delete_user_data
2025-06-12 14:53:19,334 - __main__ - INFO - ✅ الزر يعمل: delete_user_by_id
2025-06-12 14:53:19,334 - __main__ - INFO - ✅ الزر يعمل: delete_user_from_list
2025-06-12 14:53:19,334 - __main__ - INFO - ✅ الزر يعمل: confirm_delete_user_123456789
2025-06-12 14:53:19,334 - __main__ - INFO - ✅ جميع أزرار حذف بيانات المستخدم تعمل
2025-06-12 14:53:19,335 - __main__ - INFO - ✅ نجح: فحص معالجات الأزرار
2025-06-12 14:53:19,335 - __main__ - INFO - 
🧪 اختبار بدء التشغيل:
2025-06-12 14:53:19,336 - __main__ - INFO - ----------------------------------------
2025-06-12 14:53:19,336 - __main__ - INFO - 🚀 بدء اختبار تشغيل البوت...
2025-06-12 14:53:19,336 - __main__ - INFO - ✅ تم استيراد دوال حذف بيانات المستخدم بنجاح
2025-06-12 14:53:19,336 - __main__ - INFO - ✅ تم استيراد supabase_client بنجاح
2025-06-12 14:53:19,336 - __main__ - INFO - ✅ تم استيراد الدوال الرئيسية بنجاح
2025-06-12 14:53:19,337 - __main__ - INFO - ✅ تم تحميل بيانات القنوات: 7 مستخدم
2025-06-12 14:53:19,337 - __main__ - INFO - 🎉 جميع الاختبارات نجحت! البوت جاهز للتشغيل
2025-06-12 14:53:19,337 - __main__ - INFO - ✅ نجح: اختبار بدء التشغيل
2025-06-12 14:53:19,338 - __main__ - INFO - 
============================================================
2025-06-12 14:53:19,338 - __main__ - INFO - 📊 نتائج الاختبار السريع:
2025-06-12 14:53:19,338 - __main__ - INFO - ✅ نجح: 5
2025-06-12 14:53:19,338 - __main__ - INFO - ❌ فشل: 0
2025-06-12 14:53:19,338 - __main__ - INFO - 📈 معدل النجاح: 100.0%
2025-06-12 14:53:19,339 - __main__ - INFO - ============================================================
2025-06-12 14:53:19,339 - __main__ - INFO - 
🎉 جميع الاختبارات نجحت!
2025-06-12 14:53:19,339 - __main__ - INFO - 🚀 يمكنك الآن تشغيل البوت بأمان:
2025-06-12 14:53:19,339 - __main__ - INFO -    python main.py
