#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

clear

echo "==============================================================="
echo "🚀 أداة إعداد صفحة عرض مودات ماين كرافت"
echo "🌐 Minecraft Mods Display Page Setup Tool"
echo "==============================================================="
echo ""
echo "🎯 هذه الأداة ستساعدك في:"
echo "   • إنشاء جميع الملفات المطلوبة لصفحة عرض المودات"
echo "   • تحضير ملف مضغوط جاهز للرفع على الاستضافة"
echo "   • إنشاء تعليمات مفصلة للتثبيت"
echo "   • تحديث البوت ليستخدم الموقع الجديد"
echo ""
echo "🌟 المميزات:"
echo "   ✅ يعمل على جميع الاستضافات المجانية"
echo "   ✅ لا يحتاج خادم محلي أو ngrok"
echo "   ✅ دعم كامل للغتين العربية والإنجليزية"
echo "   ✅ نظام إعلانات ومهام متقدم"
echo "   ✅ تصميم متجاوب لجميع الأجهزة"
echo ""
echo "==============================================================="
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ Python غير مثبت على النظام"
    echo "📥 يرجى تثبيت Python أولاً"
    echo ""
    echo "Ubuntu/Debian: sudo apt install python3"
    echo "CentOS/RHEL: sudo yum install python3"
    echo "macOS: brew install python3"
    echo ""
    exit 1
fi

# التحقق من وجود ملف الأداة
if [ ! -f "hosting_setup_tool.py" ]; then
    echo "❌ ملف hosting_setup_tool.py غير موجود"
    echo "📁 تأكد من وجود الملف في نفس المجلد"
    echo ""
    exit 1
fi

echo "✅ Python متوفر"
echo "✅ ملف الأداة موجود"
echo ""
echo "🚀 بدء تشغيل الأداة..."
echo ""

# تشغيل الأداة
if command -v python3 &> /dev/null; then
    python3 hosting_setup_tool.py
else
    python hosting_setup_tool.py
fi

echo ""
echo "==============================================================="
echo "🎉 انتهت الأداة من العمل"
echo "💡 نصائح:"
echo "   • ارفع الملف المضغوط على استضافتك"
echo "   • فك الضغط في مجلد htdocs"
echo "   • اختبر الموقع باستخدام deploy.php?setup=true"
echo "   • شغّل سكريبت تحديث البوت"
echo "==============================================================="
echo ""
echo "اضغط Enter للخروج..."
read
