#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح بسيط لمشاكل الاتصال
Simple Connection Issues Fix
"""

import os
import sys
import logging
import platform

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)

logger = logging.getLogger(__name__)

def fix_proxy_settings():
    """إصلاح إعدادات البروكسي"""
    logger.info("إصلاح إعدادات البروكسي...")
    
    # إزالة متغيرات البروكسي
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    for var in proxy_vars:
        if os.environ.get(var):
            del os.environ[var]
            logger.info(f"تم حذف متغير البروكسي: {var}")
    
    logger.info("تم تنظيف إعدادات البروكسي")
    return True

def fix_network_config():
    """إصلاح إعدادات الشبكة"""
    logger.info("إصلاح إعدادات الشبكة...")
    
    try:
        # تحديث ملف network_config.py
        if os.path.exists("network_config.py"):
            with open("network_config.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تعطيل البروكسي
            if "'enabled': True" in content:
                content = content.replace("'enabled': True", "'enabled': False")
                with open("network_config.py", 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info("تم تعطيل البروكسي في network_config.py")
            
            logger.info("تم تحديث إعدادات الشبكة")
        else:
            logger.info("ملف network_config.py غير موجود")
        
        return True
        
    except Exception as e:
        logger.error(f"خطأ في إصلاح إعدادات الشبكة: {e}")
        return False

def clean_temp_files():
    """تنظيف الملفات المؤقتة"""
    logger.info("تنظيف الملفات المؤقتة...")
    
    try:
        import glob
        import shutil
        
        temp_patterns = ["*.pyc", "__pycache__", "*.log"]
        for pattern in temp_patterns:
            files = glob.glob(pattern, recursive=True)
            for file in files:
                try:
                    if os.path.isdir(file):
                        shutil.rmtree(file)
                    else:
                        os.remove(file)
                except:
                    pass
        
        logger.info("تم تنظيف الملفات المؤقتة")
        return True
        
    except Exception as e:
        logger.warning(f"خطأ في تنظيف الملفات المؤقتة: {e}")
        return False

def test_connections():
    """اختبار الاتصالات"""
    logger.info("اختبار الاتصالات...")
    
    try:
        import socket
        
        # اختبار الاتصال بالإنترنت
        try:
            socket.create_connection(("8.8.8.8", 53), timeout=10)
            logger.info("الاتصال بالإنترنت يعمل")
        except:
            logger.error("لا يوجد اتصال بالإنترنت")
            return False
        
        # اختبار الاتصال مع Telegram
        try:
            socket.create_connection(("api.telegram.org", 443), timeout=10)
            logger.info("الاتصال مع Telegram يعمل")
        except:
            logger.warning("مشكلة في الاتصال مع Telegram")
        
        return True
        
    except Exception as e:
        logger.error(f"خطأ في اختبار الاتصالات: {e}")
        return False

def apply_windows_fixes():
    """تطبيق إصلاحات Windows"""
    if platform.system() != "Windows":
        logger.info("النظام ليس Windows، تخطي إصلاحات Windows")
        return True
    
    logger.info("تطبيق إصلاحات Windows...")
    
    try:
        import socket
        
        # تحسين إعدادات TCP
        original_socket = socket.socket
        
        def enhanced_socket(*args, **kwargs):
            sock = original_socket(*args, **kwargs)
            try:
                # تفعيل TCP keepalive
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                # تحسين buffer sizes
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)
                # تعطيل Nagle's algorithm
                if sock.family == socket.AF_INET and sock.type == socket.SOCK_STREAM:
                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            except:
                pass  # تجاهل الأخطاء في التحسينات
            return sock
        
        socket.socket = enhanced_socket
        logger.info("تم تطبيق تحسينات Windows Socket")
        return True
        
    except Exception as e:
        logger.error(f"خطأ في تطبيق إصلاحات Windows: {e}")
        return False

def main():
    """الدالة الرئيسية للإصلاح"""
    print("=" * 60)
    print("إصلاح بسيط لمشاكل الاتصال")
    print("Simple Connection Issues Fix")
    print("=" * 60)
    
    fixes = [
        ("إصلاح إعدادات البروكسي", fix_proxy_settings),
        ("إصلاح إعدادات الشبكة", fix_network_config),
        ("تنظيف الملفات المؤقتة", clean_temp_files),
        ("تطبيق إصلاحات Windows", apply_windows_fixes),
        ("اختبار الاتصالات", test_connections),
    ]
    
    success_count = 0
    
    for fix_name, fix_func in fixes:
        print(f"\n{fix_name}...")
        try:
            if fix_func():
                success_count += 1
                print(f"نجح: {fix_name}")
            else:
                print(f"فشل: {fix_name}")
        except Exception as e:
            print(f"خطأ في {fix_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"النتائج: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count >= len(fixes) - 1:
        print("تم إصلاح معظم المشاكل!")
        print("يمكنك الآن تشغيل البوت:")
        print("   python run_bot_fixed.py")
        print("   أو")
        print("   python main.py")
    else:
        print("لا تزال هناك بعض المشاكل")
        print("راجع الأخطاء أعلاه")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nتم إيقاف الإصلاح بواسطة المستخدم")
    except Exception as e:
        print(f"\nخطأ في الإصلاح: {e}")
        sys.exit(1)
