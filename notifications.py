"""
نظام إدارة الإشعارات للبوت
يوفر وظائف إنشاء وإدارة وإرسال الإشعارات للمستخدمين
"""

import sqlite3
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import asyncio
from telegram import Bot
from telegram.error import TelegramError, Forbidden, BadRequest

# إعداد التسجيل
logger = logging.getLogger(__name__)

# مسارات قواعد البيانات
NOTIFICATIONS_DB = "notifications.db"

def create_notifications_tables() -> bool:
    """إنشاء جداول نظام الإشعارات"""
    try:
        conn = sqlite3.connect(NOTIFICATIONS_DB)
        cursor = conn.cursor()
        
        # جدول قوالب الإشعارات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notification_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                message_type TEXT DEFAULT 'text',
                button_text TEXT,
                button_url TEXT,
                created_by TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول تاريخ عمليات البث
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS broadcast_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                notification_id INTEGER,
                title TEXT NOT NULL,
                message_type TEXT DEFAULT 'text',
                target_audience TEXT DEFAULT 'all',
                total_users INTEGER DEFAULT 0,
                successful_sends INTEGER DEFAULT 0,
                failed_sends INTEGER DEFAULT 0,
                status TEXT DEFAULT 'pending',
                error_message TEXT,
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP,
                created_by TEXT NOT NULL,
                FOREIGN KEY (notification_id) REFERENCES notification_templates (id)
            )
        ''')
        
        # جدول تفاصيل الإرسال
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS broadcast_details (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                broadcast_id INTEGER NOT NULL,
                user_id TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                error_message TEXT,
                sent_at TIMESTAMP,
                FOREIGN KEY (broadcast_id) REFERENCES broadcast_history (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("✅ تم إنشاء جداول نظام الإشعارات بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء جداول نظام الإشعارات: {e}")
        return False

def save_notification_template(title: str, message: str, message_type: str = 'text', 
                             button_text: str = None, button_url: str = None, 
                             created_by: str = None) -> Optional[int]:
    """حفظ قالب إشعار جديد"""
    try:
        conn = sqlite3.connect(NOTIFICATIONS_DB)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO notification_templates 
            (title, message, message_type, button_text, button_url, created_by)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (title, message, message_type, button_text, button_url, created_by))
        
        notification_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        logger.info(f"✅ تم حفظ قالب الإشعار بنجاح - ID: {notification_id}")
        return notification_id
        
    except Exception as e:
        logger.error(f"❌ خطأ في حفظ قالب الإشعار: {e}")
        return None

def get_saved_notifications(created_by: str = None, limit: int = 50, offset: int = 0) -> List[Dict]:
    """جلب قوالب الإشعارات المحفوظة"""
    try:
        conn = sqlite3.connect(NOTIFICATIONS_DB)
        cursor = conn.cursor()
        
        if created_by:
            cursor.execute('''
                SELECT id, title, message, message_type, button_text, button_url, 
                       created_by, created_at, updated_at
                FROM notification_templates 
                WHERE created_by = ?
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            ''', (created_by, limit, offset))
        else:
            cursor.execute('''
                SELECT id, title, message, message_type, button_text, button_url, 
                       created_by, created_at, updated_at
                FROM notification_templates 
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            ''', (limit, offset))
        
        rows = cursor.fetchall()
        conn.close()
        
        notifications = []
        for row in rows:
            notifications.append({
                'id': row[0],
                'title': row[1],
                'message': row[2],
                'message_type': row[3],
                'button_text': row[4],
                'button_url': row[5],
                'created_by': row[6],
                'created_at': row[7],
                'updated_at': row[8]
            })
        
        return notifications
        
    except Exception as e:
        logger.error(f"❌ خطأ في جلب قوالب الإشعارات: {e}")
        return []

def get_notification_by_id(notification_id: int) -> Optional[Dict]:
    """جلب قالب إشعار بواسطة المعرف"""
    try:
        conn = sqlite3.connect(NOTIFICATIONS_DB)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, title, message, message_type, button_text, button_url, 
                   created_by, created_at, updated_at
            FROM notification_templates 
            WHERE id = ?
        ''', (notification_id,))
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return {
                'id': row[0],
                'title': row[1],
                'message': row[2],
                'message_type': row[3],
                'button_text': row[4],
                'button_url': row[5],
                'created_by': row[6],
                'created_at': row[7],
                'updated_at': row[8]
            }
        
        return None
        
    except Exception as e:
        logger.error(f"❌ خطأ في جلب قالب الإشعار: {e}")
        return None

def delete_notification_template(notification_id: int, created_by: str = None) -> bool:
    """حذف قالب إشعار"""
    try:
        conn = sqlite3.connect(NOTIFICATIONS_DB)
        cursor = conn.cursor()
        
        if created_by:
            cursor.execute('''
                DELETE FROM notification_templates 
                WHERE id = ? AND created_by = ?
            ''', (notification_id, created_by))
        else:
            cursor.execute('''
                DELETE FROM notification_templates 
                WHERE id = ?
            ''', (notification_id,))
        
        deleted_rows = cursor.rowcount
        conn.commit()
        conn.close()
        
        if deleted_rows > 0:
            logger.info(f"✅ تم حذف قالب الإشعار - ID: {notification_id}")
            return True
        else:
            logger.warning(f"⚠️ لم يتم العثور على قالب الإشعار للحذف - ID: {notification_id}")
            return False
        
    except Exception as e:
        logger.error(f"❌ خطأ في حذف قالب الإشعار: {e}")
        return False

def create_broadcast_record(notification_id: int, title: str, message_type: str,
                          target_audience: str, total_users: int, created_by: str) -> Optional[int]:
    """إنشاء سجل عملية بث جديدة"""
    try:
        conn = sqlite3.connect(NOTIFICATIONS_DB)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO broadcast_history 
            (notification_id, title, message_type, target_audience, total_users, created_by)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (notification_id, title, message_type, target_audience, total_users, created_by))
        
        broadcast_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        logger.info(f"✅ تم إنشاء سجل عملية البث - ID: {broadcast_id}")
        return broadcast_id
        
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء سجل عملية البث: {e}")
        return None

def update_broadcast_status(broadcast_id: int, status: str, successful_sends: int = 0,
                          failed_sends: int = 0, error_message: str = None) -> bool:
    """تحديث حالة عملية البث"""
    try:
        conn = sqlite3.connect(NOTIFICATIONS_DB)
        cursor = conn.cursor()
        
        if status == 'completed':
            cursor.execute('''
                UPDATE broadcast_history 
                SET status = ?, successful_sends = ?, failed_sends = ?, 
                    error_message = ?, completed_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (status, successful_sends, failed_sends, error_message, broadcast_id))
        else:
            cursor.execute('''
                UPDATE broadcast_history 
                SET status = ?, successful_sends = ?, failed_sends = ?, error_message = ?
                WHERE id = ?
            ''', (status, successful_sends, failed_sends, error_message, broadcast_id))
        
        conn.commit()
        conn.close()
        
        logger.info(f"✅ تم تحديث حالة عملية البث - ID: {broadcast_id}, Status: {status}")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في تحديث حالة عملية البث: {e}")
        return False

def get_broadcast_history(created_by: str = None, limit: int = 50, offset: int = 0) -> List[Dict]:
    """جلب تاريخ عمليات البث"""
    try:
        conn = sqlite3.connect(NOTIFICATIONS_DB)
        cursor = conn.cursor()
        
        if created_by:
            cursor.execute('''
                SELECT id, notification_id, title, message_type, target_audience,
                       total_users, successful_sends, failed_sends, status,
                       error_message, started_at, completed_at, created_by
                FROM broadcast_history 
                WHERE created_by = ?
                ORDER BY started_at DESC
                LIMIT ? OFFSET ?
            ''', (created_by, limit, offset))
        else:
            cursor.execute('''
                SELECT id, notification_id, title, message_type, target_audience,
                       total_users, successful_sends, failed_sends, status,
                       error_message, started_at, completed_at, created_by
                FROM broadcast_history 
                ORDER BY started_at DESC
                LIMIT ? OFFSET ?
            ''', (limit, offset))
        
        rows = cursor.fetchall()
        conn.close()
        
        broadcasts = []
        for row in rows:
            broadcasts.append({
                'id': row[0],
                'notification_id': row[1],
                'title': row[2],
                'message_type': row[3],
                'target_audience': row[4],
                'total_users': row[5],
                'successful_sends': row[6],
                'failed_sends': row[7],
                'status': row[8],
                'error_message': row[9],
                'started_at': row[10],
                'completed_at': row[11],
                'created_by': row[12]
            })
        
        return broadcasts

    except Exception as e:
        logger.error(f"❌ خطأ في جلب تاريخ عمليات البث: {e}")
        return []

def get_broadcast_details(broadcast_id: int) -> Optional[Dict]:
    """جلب تفاصيل عملية بث محددة"""
    try:
        conn = sqlite3.connect(NOTIFICATIONS_DB)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, notification_id, title, message_type, target_audience,
                   total_users, successful_sends, failed_sends, status,
                   error_message, started_at, completed_at, created_by
            FROM broadcast_history
            WHERE id = ?
        ''', (broadcast_id,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return {
                'id': row[0],
                'notification_id': row[1],
                'title': row[2],
                'message_type': row[3],
                'target_audience': row[4],
                'total_users': row[5],
                'successful_sends': row[6],
                'failed_sends': row[7],
                'status': row[8],
                'error_message': row[9],
                'started_at': row[10],
                'completed_at': row[11],
                'created_by': row[12]
            }

        return None

    except Exception as e:
        logger.error(f"❌ خطأ في جلب تفاصيل عملية البث: {e}")
        return None

def get_notification_stats() -> Dict[str, int]:
    """جلب إحصائيات نظام الإشعارات"""
    try:
        conn = sqlite3.connect(NOTIFICATIONS_DB)
        cursor = conn.cursor()

        # عدد قوالب الإشعارات المحفوظة
        cursor.execute('SELECT COUNT(*) FROM notification_templates')
        total_saved_notifications = cursor.fetchone()[0]

        # عدد عمليات البث
        cursor.execute('SELECT COUNT(*) FROM broadcast_history')
        total_broadcasts = cursor.fetchone()[0]

        # عدد عمليات البث المكتملة
        cursor.execute('SELECT COUNT(*) FROM broadcast_history WHERE status = "completed"')
        completed_broadcasts = cursor.fetchone()[0]

        # عدد عمليات البث المعلقة
        cursor.execute('SELECT COUNT(*) FROM broadcast_history WHERE status = "pending"')
        pending_broadcasts = cursor.fetchone()[0]

        # عدد عمليات البث الفاشلة
        cursor.execute('SELECT COUNT(*) FROM broadcast_history WHERE status = "failed"')
        failed_broadcasts = cursor.fetchone()[0]

        # إجمالي الإرسالات
        cursor.execute('SELECT SUM(successful_sends + failed_sends) FROM broadcast_history')
        total_sent_result = cursor.fetchone()[0]
        total_sent_notifications = total_sent_result if total_sent_result else 0

        # الإرسالات الناجحة
        cursor.execute('SELECT SUM(successful_sends) FROM broadcast_history')
        successful_sends_result = cursor.fetchone()[0]
        successful_sends = successful_sends_result if successful_sends_result else 0

        # الإرسالات الفاشلة
        cursor.execute('SELECT SUM(failed_sends) FROM broadcast_history')
        failed_sends_result = cursor.fetchone()[0]
        failed_sends = failed_sends_result if failed_sends_result else 0

        conn.close()

        return {
            'total_saved_notifications': total_saved_notifications,
            'total_broadcasts': total_broadcasts,
            'completed_broadcasts': completed_broadcasts,
            'pending_broadcasts': pending_broadcasts,
            'failed_broadcasts': failed_broadcasts,
            'total_sent_notifications': total_sent_notifications,
            'successful_sends': successful_sends,
            'failed_sends': failed_sends
        }

    except Exception as e:
        logger.error(f"❌ خطأ في جلب إحصائيات الإشعارات: {e}")
        return {
            'total_saved_notifications': 0,
            'total_broadcasts': 0,
            'completed_broadcasts': 0,
            'pending_broadcasts': 0,
            'failed_broadcasts': 0,
            'total_sent_notifications': 0,
            'successful_sends': 0,
            'failed_sends': 0
        }

async def execute_notification_broadcast(context, notification_id: int, created_by: str,
                                       target_audience: str = "all") -> Tuple[int, int, Optional[str]]:
    """تنفيذ إرسال الإشعار لجميع المستخدمين"""
    try:
        # جلب قالب الإشعار
        notification = get_notification_by_id(notification_id)
        if not notification:
            return 0, 0, "لم يتم العثور على قالب الإشعار"

        # تحميل قوائم المستخدمين
        from main import load_user_channels, load_all_users
        user_channels = load_user_channels()
        all_users = load_all_users()

        # جمع جميع معرفات المستخدمين
        all_user_ids = set(list(all_users.keys()) + list(user_channels.keys()))

        # فلترة المستخدمين حسب الجمهور المستهدف
        if target_audience == "ar":
            # المستخدمون العرب فقط
            from main import get_user_lang
            target_users = [uid for uid in all_user_ids if get_user_lang(int(uid)) == "ar"]
        elif target_audience == "en":
            # المستخدمون الإنجليز فقط
            from main import get_user_lang
            target_users = [uid for uid in all_user_ids if get_user_lang(int(uid)) == "en"]
        else:
            # جميع المستخدمين
            target_users = list(all_user_ids)

        total_users = len(target_users)

        # إنشاء سجل عملية البث
        broadcast_id = create_broadcast_record(
            notification_id, notification['title'], notification['message_type'],
            target_audience, total_users, created_by
        )

        if not broadcast_id:
            return 0, 0, "فشل في إنشاء سجل عملية البث"

        # تحديث الحالة إلى "جاري الإرسال"
        update_broadcast_status(broadcast_id, "sending")

        success_count = 0
        failure_count = 0

        # إرسال الإشعار لكل مستخدم
        for user_id in target_users:
            try:
                # إنشاء الرسالة
                message_text = notification['message']

                # إضافة الزر إذا كان موجوداً
                reply_markup = None
                if notification.get('button_text') and notification.get('button_url'):
                    from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                    keyboard = [[InlineKeyboardButton(notification['button_text'], url=notification['button_url'])]]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                # إرسال الرسالة
                await context.bot.send_message(
                    chat_id=int(user_id),
                    text=message_text,
                    parse_mode="HTML",
                    reply_markup=reply_markup
                )

                success_count += 1
                logger.debug(f"✅ تم إرسال الإشعار للمستخدم {user_id}")

            except (Forbidden, BadRequest) as e:
                failure_count += 1
                logger.warning(f"⚠️ فشل إرسال الإشعار للمستخدم {user_id}: {e}")

            except Exception as e:
                failure_count += 1
                logger.error(f"❌ خطأ في إرسال الإشعار للمستخدم {user_id}: {e}")

            # تأخير قصير لتجنب حدود التيليجرام
            await asyncio.sleep(0.1)

        # تحديث حالة عملية البث
        update_broadcast_status(broadcast_id, "completed", success_count, failure_count)

        logger.info(f"✅ تم إكمال إرسال الإشعار - نجح: {success_count}, فشل: {failure_count}")
        return success_count, failure_count, None

    except Exception as e:
        logger.error(f"❌ خطأ في تنفيذ إرسال الإشعار: {e}")
        if 'broadcast_id' in locals():
            update_broadcast_status(broadcast_id, "failed", 0, 0, str(e))
        return 0, 0, str(e)
