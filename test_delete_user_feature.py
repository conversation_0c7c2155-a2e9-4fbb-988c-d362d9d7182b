#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة حذف بيانات المستخدم بالكامل
"""

import json
import os
import sys
import logging
from datetime import datetime

# إعداد المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_delete_user_feature.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DeleteUserFeatureTester:
    """فئة اختبار ميزة حذف بيانات المستخدم"""
    
    def __init__(self):
        self.test_user_id = "999999999"  # معرف مستخدم وهمي للاختبار
        self.backup_files = []
        
    def create_test_data(self):
        """إنشاء بيانات اختبار وهمية"""
        logger.info("🧪 إنشاء بيانات اختبار وهمية...")
        
        try:
            # إنشاء بيانات قنوات وهمية
            user_channels_file = "user_channels.json"
            if os.path.exists(user_channels_file):
                with open(user_channels_file, 'r', encoding='utf-8') as f:
                    user_channels = json.load(f)
            else:
                user_channels = {}
            
            # إضافة بيانات المستخدم الوهمي
            user_channels[self.test_user_id] = {
                "username": "test_user",
                "full_name": "Test User",
                "default_channel": "-1001234567890",
                "channels": {
                    "-1001234567890": {
                        "channel_id": "-1001234567890",
                        "publish_interval": 60,
                        "active": True,
                        "channel_lang": "ar",
                        "categories": ["mods", "addons"],
                        "mc_versions": ["1.20", "1.19"],
                        "message_format": "detailed",
                        "custom_formats": []
                    }
                }
            }
            
            # حفظ البيانات
            with open(user_channels_file, 'w', encoding='utf-8') as f:
                json.dump(user_channels, f, ensure_ascii=False, indent=2)
            
            # إنشاء بيانات المستخدمين العامة
            all_users_file = "all_users.json"
            if os.path.exists(all_users_file):
                with open(all_users_file, 'r', encoding='utf-8') as f:
                    all_users = json.load(f)
            else:
                all_users = {}
            
            all_users[self.test_user_id] = {
                "user_id": self.test_user_id,
                "username": "test_user",
                "full_name": "Test User",
                "language": "ar",
                "last_activity": datetime.now().isoformat(),
                "registration_date": datetime.now().isoformat(),
                "total_mods_published": 5,
                "total_channels": 1
            }
            
            with open(all_users_file, 'w', encoding='utf-8') as f:
                json.dump(all_users, f, ensure_ascii=False, indent=2)
            
            logger.info("✅ تم إنشاء بيانات الاختبار بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء بيانات الاختبار: {e}")
            return False
    
    def backup_existing_data(self):
        """نسخ احتياطي من البيانات الموجودة"""
        logger.info("💾 إنشاء نسخة احتياطية من البيانات الموجودة...")
        
        try:
            files_to_backup = ["user_channels.json", "all_users.json"]
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            for file_name in files_to_backup:
                if os.path.exists(file_name):
                    backup_name = f"{file_name}.backup_{timestamp}"
                    with open(file_name, 'r', encoding='utf-8') as src:
                        with open(backup_name, 'w', encoding='utf-8') as dst:
                            dst.write(src.read())
                    self.backup_files.append(backup_name)
                    logger.info(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def restore_backup_data(self):
        """استعادة البيانات من النسخة الاحتياطية"""
        logger.info("🔄 استعادة البيانات من النسخة الاحتياطية...")
        
        try:
            for backup_file in self.backup_files:
                original_file = backup_file.split('.backup_')[0]
                if os.path.exists(backup_file):
                    with open(backup_file, 'r', encoding='utf-8') as src:
                        with open(original_file, 'w', encoding='utf-8') as dst:
                            dst.write(src.read())
                    os.remove(backup_file)
                    logger.info(f"✅ تم استعادة: {original_file}")
            
            self.backup_files.clear()
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في استعادة البيانات: {e}")
            return False
    
    def test_user_exists_before_deletion(self):
        """اختبار وجود المستخدم قبل الحذف"""
        logger.info("🔍 اختبار وجود المستخدم قبل الحذف...")
        
        try:
            # فحص بيانات القنوات
            with open("user_channels.json", 'r', encoding='utf-8') as f:
                user_channels = json.load(f)
            
            if self.test_user_id in user_channels:
                logger.info("✅ المستخدم موجود في بيانات القنوات")
            else:
                logger.error("❌ المستخدم غير موجود في بيانات القنوات")
                return False
            
            # فحص بيانات المستخدمين العامة
            with open("all_users.json", 'r', encoding='utf-8') as f:
                all_users = json.load(f)
            
            if self.test_user_id in all_users:
                logger.info("✅ المستخدم موجود في البيانات العامة")
            else:
                logger.error("❌ المستخدم غير موجود في البيانات العامة")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص وجود المستخدم: {e}")
            return False
    
    def simulate_delete_user_data(self):
        """محاكاة حذف بيانات المستخدم"""
        logger.info("🗑️ محاكاة حذف بيانات المستخدم...")
        
        try:
            # حذف من بيانات القنوات
            with open("user_channels.json", 'r', encoding='utf-8') as f:
                user_channels = json.load(f)
            
            if self.test_user_id in user_channels:
                del user_channels[self.test_user_id]
                with open("user_channels.json", 'w', encoding='utf-8') as f:
                    json.dump(user_channels, f, ensure_ascii=False, indent=2)
                logger.info("✅ تم حذف بيانات القنوات")
            
            # حذف من البيانات العامة
            with open("all_users.json", 'r', encoding='utf-8') as f:
                all_users = json.load(f)
            
            if self.test_user_id in all_users:
                del all_users[self.test_user_id]
                with open("all_users.json", 'w', encoding='utf-8') as f:
                    json.dump(all_users, f, ensure_ascii=False, indent=2)
                logger.info("✅ تم حذف البيانات العامة")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في محاكاة حذف البيانات: {e}")
            return False
    
    def test_user_deleted_after_deletion(self):
        """اختبار عدم وجود المستخدم بعد الحذف"""
        logger.info("🔍 اختبار عدم وجود المستخدم بعد الحذف...")
        
        try:
            # فحص بيانات القنوات
            with open("user_channels.json", 'r', encoding='utf-8') as f:
                user_channels = json.load(f)
            
            if self.test_user_id not in user_channels:
                logger.info("✅ المستخدم غير موجود في بيانات القنوات (تم الحذف)")
            else:
                logger.error("❌ المستخدم ما زال موجود في بيانات القنوات")
                return False
            
            # فحص بيانات المستخدمين العامة
            with open("all_users.json", 'r', encoding='utf-8') as f:
                all_users = json.load(f)
            
            if self.test_user_id not in all_users:
                logger.info("✅ المستخدم غير موجود في البيانات العامة (تم الحذف)")
            else:
                logger.error("❌ المستخدم ما زال موجود في البيانات العامة")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص حذف المستخدم: {e}")
            return False
    
    def test_database_functions(self):
        """اختبار دوال قاعدة البيانات"""
        logger.info("🗄️ اختبار دوال قاعدة البيانات...")
        
        try:
            # استيراد دوال قاعدة البيانات
            import supabase_client
            
            # اختبار جلب إعدادات الإعلانات
            ads_settings = supabase_client.get_user_ads_settings(self.test_user_id)
            logger.info(f"📢 إعدادات الإعلانات: {ads_settings}")
            
            # اختبار جلب إعدادات المهام
            tasks_settings = supabase_client.get_user_tasks_settings(self.test_user_id)
            logger.info(f"📋 إعدادات المهام: {tasks_settings}")
            
            # اختبار جلب الروابط المخصصة
            custom_links = supabase_client.get_user_custom_download_links(self.test_user_id)
            logger.info(f"🔗 الروابط المخصصة: {len(custom_links)} رابط")
            
            # اختبار جلب تخصيصات الصفحة
            page_customization = supabase_client.get_user_page_customization_settings(self.test_user_id)
            logger.info(f"🎨 تخصيصات الصفحة: {page_customization}")
            
            # اختبار جلب بيانات الدعوات
            invitation_data = supabase_client.get_user_invitation_data(self.test_user_id)
            logger.info(f"🎁 بيانات الدعوات: {invitation_data}")
            
            logger.info("✅ تم اختبار دوال قاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار دوال قاعدة البيانات: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        logger.info("🚀 بدء اختبارات ميزة حذف بيانات المستخدم...")
        
        tests = [
            ("إنشاء نسخة احتياطية", self.backup_existing_data),
            ("إنشاء بيانات اختبار", self.create_test_data),
            ("فحص وجود المستخدم قبل الحذف", self.test_user_exists_before_deletion),
            ("اختبار دوال قاعدة البيانات", self.test_database_functions),
            ("محاكاة حذف البيانات", self.simulate_delete_user_data),
            ("فحص حذف المستخدم بعد الحذف", self.test_user_deleted_after_deletion),
            ("استعادة النسخة الاحتياطية", self.restore_backup_data)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*60}")
            logger.info(f"🧪 اختبار: {test_name}")
            logger.info(f"{'='*60}")
            
            try:
                if test_func():
                    logger.info(f"✅ نجح اختبار: {test_name}")
                    passed += 1
                else:
                    logger.error(f"❌ فشل اختبار: {test_name}")
                    failed += 1
            except Exception as e:
                logger.error(f"❌ خطأ في اختبار {test_name}: {e}")
                failed += 1
        
        # النتائج النهائية
        logger.info(f"\n{'='*60}")
        logger.info("📊 نتائج اختبارات ميزة حذف بيانات المستخدم:")
        logger.info(f"✅ نجح: {passed}")
        logger.info(f"❌ فشل: {failed}")
        logger.info(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        logger.info(f"{'='*60}")
        
        return passed, failed

def main():
    """الدالة الرئيسية"""
    print("🗑️ اختبار ميزة حذف بيانات المستخدم بالكامل")
    print("="*60)
    
    tester = DeleteUserFeatureTester()
    passed, failed = tester.run_all_tests()
    
    if failed == 0:
        print("\n🎉 جميع الاختبارات نجحت! الميزة جاهزة للاستخدام.")
        return 0
    else:
        print(f"\n⚠️ فشل {failed} اختبار. يرجى مراجعة السجلات.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
