#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام الاشتراك في القنوات ونظام الدعوات
Test Subscription and Invitation System

هذا الملف يختبر:
1. نظام الاشتراك في القنوات للإعلانات
2. نظام الدعوات للمميزات الأخرى
3. رسائل التحفيز الموحدة
4. حفظ حالة التفعيل
"""

import asyncio
import json
import os
import sys
from datetime import datetime

# إضافة مجلد المشروع إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الدوال المطلوبة
try:
    from main import (
        # دوال نظام الاشتراك
        check_all_required_subscriptions,
        update_user_subscription_status,
        REQUIRED_CHANNELS,
        
        # دوال نظام الدعوات
        get_user_invitation_level,
        check_user_premium_feature,
        grant_invitation_rewards,
        
        # دوال التحقق من المميزات
        check_feature_access,
        show_feature_locked_message,
        
        # دوال إدارة حالة التفعيل
        mark_feature_as_activated,
        is_feature_activated,
        reset_feature_activation,
        
        # دوال مساعدة
        load_user_invitations,
        save_user_invitations,
        create_user_invitation,
        process_invitation,
        
        # إعدادات
        YOUR_CHAT_ID,
        logger
    )
    print("✅ تم استيراد جميع الدوال بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد الدوال: {e}")
    sys.exit(1)

class TestSubscriptionInvitationSystem:
    """فئة اختبار نظام الاشتراك والدعوات"""
    
    def __init__(self):
        self.test_user_id = "123456789"  # معرف مستخدم تجريبي
        self.test_admin_id = YOUR_CHAT_ID
        self.test_results = []
        
    def log_test(self, test_name, result, details=""):
        """تسجيل نتيجة الاختبار"""
        status = "✅ نجح" if result else "❌ فشل"
        message = f"{status} {test_name}"
        if details:
            message += f" - {details}"
        print(message)
        self.test_results.append({
            'test': test_name,
            'result': result,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
        
    def test_required_channels_config(self):
        """اختبار إعدادات القنوات المطلوبة"""
        print("\n🔧 اختبار إعدادات القنوات المطلوبة:")
        print("=" * 50)
        
        # التحقق من وجود القنوات المطلوبة
        ads_channels = REQUIRED_CHANNELS.get('ads_system', [])
        expected_channels = [
            '@shadercraft443',
            '@mods_addons_for_minecraft_pe',
            '@mineemods'
        ]
        
        self.log_test(
            "وجود قنوات نظام الإعلانات",
            len(ads_channels) == 3,
            f"عدد القنوات: {len(ads_channels)}"
        )
        
        for expected in expected_channels:
            found = any(expected in channel.get('username', '') for channel in ads_channels)
            self.log_test(
                f"وجود القناة {expected}",
                found,
                "موجودة في الإعدادات" if found else "غير موجودة"
            )
            
    def test_invitation_levels(self):
        """اختبار مستويات نظام الدعوات"""
        print("\n🏆 اختبار مستويات نظام الدعوات:")
        print("=" * 50)
        
        # إنشاء مستخدم تجريبي
        create_user_invitation(self.test_user_id)
        
        # اختبار المستويات المختلفة (النظام الجديد)
        test_cases = [
            (0, 0, 'عادي'),
            (1, 1, 'مبتدئ'),
            (3, 2, 'متقدم'),
            (5, 3, 'محترف'),
            (10, 4, 'VIP')
        ]
        
        invitations = load_user_invitations()
        user_data = invitations.get(self.test_user_id, {})
        
        for invites, expected_level, expected_name in test_cases:
            # تعديل عدد الدعوات
            user_data['total_invitations'] = invites
            invitations[self.test_user_id] = user_data
            save_user_invitations(invitations)
            
            # اختبار المستوى
            level_info = get_user_invitation_level(self.test_user_id)
            
            self.log_test(
                f"مستوى {invites} دعوات",
                level_info['level'] == expected_level and level_info['name'] == expected_name,
                f"المستوى: {level_info['level']}, الاسم: {level_info['name']}"
            )
            
    def test_feature_access_logic(self):
        """اختبار منطق الوصول للمميزات"""
        print("\n🔐 اختبار منطق الوصول للمميزات:")
        print("=" * 50)
        
        # إعداد مستخدم بدون مميزات
        invitations = load_user_invitations()
        user_data = invitations.get(self.test_user_id, {})
        user_data['total_invitations'] = 0
        user_data['premium_features'] = {
            'url_shortener_access': False,
            'tasks_system_access': False,
            'ads_system_access': False
        }
        invitations[self.test_user_id] = user_data
        save_user_invitations(invitations)
        
        # اختبار الوصول للمميزات بدون تفعيل
        features_to_test = [
            ('url_shortener', False),
            ('tasks_system', False),
            ('ads_system', False)  # يتطلب اشتراك في قنوات
        ]
        
        for feature, expected_access in features_to_test:
            # استخدام النسخة المتزامنة للاختبار
            from main import check_feature_access_sync
            has_access = check_feature_access_sync(self.test_user_id, feature)
            
            self.log_test(
                f"الوصول لـ {feature} بدون تفعيل",
                has_access == expected_access,
                f"النتيجة: {has_access}, المتوقع: {expected_access}"
            )
            
    def test_feature_activation_tracking(self):
        """اختبار تتبع حالة تفعيل المميزات"""
        print("\n📊 اختبار تتبع حالة تفعيل المميزات:")
        print("=" * 50)
        
        feature_type = 'url_shortener'
        
        # التحقق من عدم التفعيل في البداية
        is_activated_before = is_feature_activated(self.test_user_id, feature_type)
        self.log_test(
            "حالة التفعيل قبل التفعيل",
            not is_activated_before,
            f"مفعل: {is_activated_before}"
        )
        
        # تفعيل الميزة
        activation_result = mark_feature_as_activated(self.test_user_id, feature_type)
        self.log_test(
            "تفعيل الميزة",
            activation_result,
            "تم التفعيل بنجاح" if activation_result else "فشل التفعيل"
        )
        
        # التحقق من التفعيل بعد التفعيل
        is_activated_after = is_feature_activated(self.test_user_id, feature_type)
        self.log_test(
            "حالة التفعيل بعد التفعيل",
            is_activated_after,
            f"مفعل: {is_activated_after}"
        )
        
        # إعادة تعيين التفعيل
        reset_result = reset_feature_activation(self.test_user_id, feature_type)
        self.log_test(
            "إعادة تعيين التفعيل",
            reset_result,
            "تم إعادة التعيين بنجاح" if reset_result else "فشل إعادة التعيين"
        )
        
        # التحقق من عدم التفعيل بعد إعادة التعيين
        is_activated_final = is_feature_activated(self.test_user_id, feature_type)
        self.log_test(
            "حالة التفعيل بعد إعادة التعيين",
            not is_activated_final,
            f"مفعل: {is_activated_final}"
        )
        
    def test_invitation_rewards(self):
        """اختبار نظام مكافآت الدعوات"""
        print("\n🎁 اختبار نظام مكافآت الدعوات:")
        print("=" * 50)
        
        # إعداد مستخدم جديد
        invitations = load_user_invitations()
        if self.test_user_id in invitations:
            del invitations[self.test_user_id]
        save_user_invitations(invitations)
        
        create_user_invitation(self.test_user_id)
        
        # اختبار منح المكافآت لمستويات مختلفة (النظام الجديد)
        reward_levels = [1, 3, 5, 10]

        for level in reward_levels:
            rewards = grant_invitation_rewards(self.test_user_id, level)

            self.log_test(
                f"منح مكافآت المستوى {level}",
                isinstance(rewards, list),
                f"عدد المكافآت: {len(rewards) if rewards else 0}"
            )

            # التحقق من تفعيل المميزات الجديدة
            if level >= 1:
                has_unlimited_channels = check_user_premium_feature(self.test_user_id, 'unlimited_channels')
                self.log_test(
                    f"تفعيل قنوات غير محدودة عند {level} دعوات",
                    has_unlimited_channels,
                    f"مفعل: {has_unlimited_channels}"
                )

            if level >= 3:
                has_url_shortener = check_user_premium_feature(self.test_user_id, 'url_shortener_access')
                has_custom_links = check_user_premium_feature(self.test_user_id, 'custom_download_links')
                self.log_test(
                    f"تفعيل حزمة الربح الكاملة عند {level} دعوات",
                    has_url_shortener and has_custom_links,
                    f"اختصار الروابط: {has_url_shortener}, روابط مخصصة: {has_custom_links}"
                )

            if level >= 5:
                has_extended_intervals = check_user_premium_feature(self.test_user_id, 'publish_intervals_extended')
                self.log_test(
                    f"تفعيل أوقات النشر الجديدة عند {level} دعوات",
                    has_extended_intervals,
                    f"مفعل: {has_extended_intervals}"
                )

            if level >= 10:
                has_tasks = check_user_premium_feature(self.test_user_id, 'tasks_system_access')
                has_vip_customization = check_user_premium_feature(self.test_user_id, 'page_customization_vip')
                self.log_test(
                    f"تفعيل حزمة VIP عند {level} دعوات",
                    has_tasks and has_vip_customization,
                    f"نظام المهام: {has_tasks}, تخصيص VIP: {has_vip_customization}"
                )
                
    def test_publish_intervals_feature(self):
        """اختبار ميزة أوقات النشر الموسعة"""
        print("\n⏰ اختبار ميزة أوقات النشر الموسعة:")
        print("=" * 50)

        from main import get_available_publish_intervals, reset_feature_activation, create_user_invitation

        # إنشاء مستخدم جديد للاختبار
        test_user_new = 987654321

        # إنشاء المستخدم في نظام الدعوات
        create_user_invitation(test_user_new)

        # اختبار المستخدم العادي (بدون مميزات)
        basic_intervals = get_available_publish_intervals(test_user_new)
        basic_count = len(basic_intervals)

        # التحقق من عدم وجود الأوقات الإضافية في القائمة الأساسية
        basic_interval_minutes = [interval['minutes'] for interval in basic_intervals]
        has_10_minutes_basic = 10 in basic_interval_minutes
        has_15_minutes_basic = 15 in basic_interval_minutes

        self.log_test(
            "أوقات النشر للمستخدم العادي",
            basic_count > 0 and not has_10_minutes_basic and not has_15_minutes_basic,
            f"عدد الأوقات: {basic_count}, 10 دقائق: {has_10_minutes_basic}, 15 دقيقة: {has_15_minutes_basic}"
        )

        # منح ميزة الأوقات الموسعة
        grant_invitation_rewards(test_user_new, 5)

        # اختبار المستخدم المميز
        extended_intervals = get_available_publish_intervals(test_user_new)
        extended_count = len(extended_intervals)

        # التحقق من وجود الأوقات الجديدة
        extended_interval_minutes = [interval['minutes'] for interval in extended_intervals]
        has_10_minutes_extended = 10 in extended_interval_minutes
        has_15_minutes_extended = 15 in extended_interval_minutes

        self.log_test(
            "أوقات النشر للمستخدم المميز",
            extended_count > basic_count,
            f"عدد الأوقات: {extended_count} (زيادة: {extended_count - basic_count})"
        )

        self.log_test(
            "وجود وقت النشر كل 10 دقائق للمستخدم المميز",
            has_10_minutes_extended,
            f"موجود: {has_10_minutes_extended}"
        )

        self.log_test(
            "وجود وقت النشر كل 15 دقيقة للمستخدم المميز",
            has_15_minutes_extended,
            f"موجود: {has_15_minutes_extended}"
        )

    def test_admin_privileges(self):
        """اختبار صلاحيات المسؤول"""
        print("\n👑 اختبار صلاحيات المسؤول:")
        print("=" * 50)

        from main import is_admin_user, check_feature_access_sync, get_available_publish_intervals

        # التحقق من صلاحيات المسؤول
        is_admin = is_admin_user(self.test_admin_id)
        self.log_test(
            "التحقق من صلاحيات المسؤول",
            is_admin,
            f"المسؤول: {is_admin}"
        )

        # التحقق من وصول المسؤول لجميع المميزات
        features = ['url_shortener', 'tasks_system', 'ads_system', 'publish_intervals_extended']

        for feature in features:
            has_access = check_feature_access_sync(self.test_admin_id, feature)
            self.log_test(
                f"وصول المسؤول لـ {feature}",
                has_access,
                f"الوصول: {has_access}"
            )

        # التحقق من وصول المسؤول لجميع أوقات النشر
        admin_intervals = get_available_publish_intervals(self.test_admin_id)
        admin_interval_minutes = [interval['minutes'] for interval in admin_intervals]
        has_all_intervals = 10 in admin_interval_minutes and 15 in admin_interval_minutes

        self.log_test(
            "وصول المسؤول لجميع أوقات النشر",
            has_all_intervals,
            f"عدد الأوقات: {len(admin_intervals)}"
        )
            
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار نظام الاشتراك والدعوات")
        print("=" * 60)
        
        try:
            self.test_required_channels_config()
            self.test_invitation_levels()
            self.test_feature_access_logic()
            self.test_feature_activation_tracking()
            self.test_invitation_rewards()
            self.test_publish_intervals_feature()
            self.test_admin_privileges()
            
        except Exception as e:
            print(f"\n❌ خطأ أثناء تشغيل الاختبارات: {e}")
            import traceback
            traceback.print_exc()
            
        # عرض النتائج النهائية
        self.show_final_results()
        
    def show_final_results(self):
        """عرض النتائج النهائية"""
        print("\n📊 النتائج النهائية:")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['result'])
        failed_tests = total_tests - passed_tests
        
        print(f"📈 إجمالي الاختبارات: {total_tests}")
        print(f"✅ نجح: {passed_tests}")
        print(f"❌ فشل: {failed_tests}")
        print(f"📊 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ الاختبارات الفاشلة:")
            for result in self.test_results:
                if not result['result']:
                    print(f"   • {result['test']}: {result['details']}")
                    
        # حفظ النتائج في ملف
        try:
            with open('test_results.json', 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'summary': {
                        'total': total_tests,
                        'passed': passed_tests,
                        'failed': failed_tests,
                        'success_rate': (passed_tests/total_tests)*100
                    },
                    'details': self.test_results
                }, f, indent=2, ensure_ascii=False)
            print(f"\n💾 تم حفظ النتائج في test_results.json")
        except Exception as e:
            print(f"\n⚠️ فشل حفظ النتائج: {e}")

def main():
    """الدالة الرئيسية"""
    tester = TestSubscriptionInvitationSystem()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
