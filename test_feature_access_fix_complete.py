#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لإصلاح نظام رسائل التحذير للمميزات المقفلة
Complete test for feature access warning messages fix
"""

import sys
import os
import asyncio
from unittest.mock import Mock, AsyncMock

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_feature_access_functions():
    """اختبار وجود جميع الدوال المطلوبة"""
    print("🔍 اختبار وجود الدوال المطلوبة...")
    
    try:
        from main import (
            # دوال التحقق الأساسية
            show_feature_locked_message,
            check_feature_access,
            get_user_invitation_level,
            get_user_invitation_stats,
            
            # دوال المميزات الجديدة
            unlimited_channels_menu,
            custom_download_links_menu,
            publish_intervals_extended_menu,
            page_customization_vip_menu,
            
            # دوال المميزات الموجودة
            url_shortener_menu,
            tasks_system_menu
        )
        print("✅ تم استيراد جميع الدوال بنجاح")
        return True
    except ImportError as e:
        print(f"❌ خطأ في استيراد الدوال: {e}")
        return False

def test_feature_info_structure():
    """اختبار بنية معلومات المميزات في show_feature_locked_message"""
    print("\n🏗️ اختبار بنية معلومات المميزات...")
    
    try:
        from main import show_feature_locked_message
        import inspect
        
        # الحصول على كود الدالة
        source = inspect.getsource(show_feature_locked_message)
        
        # التحقق من وجود جميع المميزات المطلوبة
        required_features = [
            'ads_system',
            'unlimited_channels',
            'url_shortener',
            'custom_download_links',
            'publish_intervals_extended',
            'tasks_system',
            'page_customization_vip'
        ]
        
        missing_features = []
        for feature in required_features:
            if f"'{feature}'" not in source:
                missing_features.append(feature)
        
        if missing_features:
            print(f"❌ مميزات مفقودة: {missing_features}")
            return False
        else:
            print("✅ جميع المميزات المطلوبة موجودة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص بنية المميزات: {e}")
        return False

def test_invitation_requirements():
    """اختبار متطلبات الدعوات للمميزات"""
    print("\n🎁 اختبار متطلبات الدعوات...")
    
    expected_requirements = {
        'unlimited_channels': 1,
        'url_shortener': 3,
        'custom_download_links': 3,
        'publish_intervals_extended': 5,
        'tasks_system': 10,
        'page_customization_vip': 10
    }
    
    try:
        from main import show_feature_locked_message
        import inspect
        
        source = inspect.getsource(show_feature_locked_message)
        
        all_correct = True
        for feature, expected_count in expected_requirements.items():
            # البحث عن النمط: 'requirement_count': X
            pattern = f"'{feature}'"
            if pattern in source:
                # البحث عن requirement_count في نفس القسم
                feature_start = source.find(pattern)
                feature_section = source[feature_start:feature_start + 500]  # 500 حرف بعد اسم الميزة
                
                if f"'requirement_count': {expected_count}" in feature_section:
                    print(f"✅ {feature}: {expected_count} دعوات (صحيح)")
                else:
                    print(f"❌ {feature}: متطلبات الدعوات غير صحيحة")
                    all_correct = False
            else:
                print(f"❌ {feature}: الميزة غير موجودة")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في فحص متطلبات الدعوات: {e}")
        return False

async def test_show_feature_locked_message():
    """اختبار دالة عرض رسالة الميزة المقفلة"""
    print("\n📋 اختبار دالة show_feature_locked_message...")
    
    try:
        from main import show_feature_locked_message
        
        # إنشاء mock objects
        update = Mock()
        update.callback_query = Mock()
        update.callback_query.message = Mock()
        update.callback_query.message.photo = None
        
        context = Mock()
        context.bot = AsyncMock()
        context.bot.send_message = AsyncMock()
        
        # محاكاة مستخدم عادي (ليس أدمن)
        test_user_id = "123456789"
        
        print(f"📋 اختبار للمستخدم: {test_user_id}")
        
        # اختبار جميع المميزات
        features_to_test = [
            'unlimited_channels',
            'url_shortener',
            'custom_download_links',
            'publish_intervals_extended',
            'tasks_system',
            'page_customization_vip'
        ]
        
        for feature in features_to_test:
            print(f"🔗 اختبار ميزة {feature}...")
            await show_feature_locked_message(
                update=update,
                context=context,
                user_id=test_user_id,
                feature_type=feature,
                lang="ar"
            )
            print(f"✅ تم إرسال رسالة {feature} بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار show_feature_locked_message: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_menu_functions_use_show_feature_locked_message():
    """اختبار أن دوال القوائم تستخدم show_feature_locked_message"""
    print("\n🔧 اختبار استخدام show_feature_locked_message في دوال القوائم...")
    
    try:
        from main import (
            url_shortener_menu,
            tasks_system_menu,
            unlimited_channels_menu,
            custom_download_links_menu,
            publish_intervals_extended_menu,
            page_customization_vip_menu
        )
        import inspect
        
        functions_to_test = [
            ('url_shortener_menu', url_shortener_menu),
            ('tasks_system_menu', tasks_system_menu),
            ('unlimited_channels_menu', unlimited_channels_menu),
            ('custom_download_links_menu', custom_download_links_menu),
            ('publish_intervals_extended_menu', publish_intervals_extended_menu),
            ('page_customization_vip_menu', page_customization_vip_menu)
        ]
        
        all_correct = True
        for func_name, func in functions_to_test:
            source = inspect.getsource(func)
            
            # التحقق من استخدام check_feature_access
            if "await check_feature_access" in source:
                print(f"✅ {func_name}: يستخدم await check_feature_access")
            else:
                print(f"❌ {func_name}: لا يستخدم await check_feature_access")
                all_correct = False
            
            # التحقق من استخدام show_feature_locked_message
            if "show_feature_locked_message" in source:
                print(f"✅ {func_name}: يستخدم show_feature_locked_message")
            else:
                print(f"❌ {func_name}: لا يستخدم show_feature_locked_message")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في فحص دوال القوائم: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار شامل لإصلاح نظام رسائل التحذير للمميزات المقفلة")
    print("=" * 80)
    
    tests = [
        ("اختبار وجود الدوال", test_feature_access_functions),
        ("اختبار بنية المميزات", test_feature_info_structure),
        ("اختبار متطلبات الدعوات", test_invitation_requirements),
        ("اختبار دالة الرسائل", test_show_feature_locked_message),
        ("اختبار دوال القوائم", test_menu_functions_use_show_feature_locked_message)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📝 {test_name}:")
        print("-" * 50)
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 نتائج الاختبار النهائية:")
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت! تم إصلاح نظام رسائل التحذير بنجاح.")
        print("\n📋 الإصلاحات المطبقة:")
        print("• تم تحديث دالة tasks_system_menu لتستخدم show_feature_locked_message")
        print("• تم إضافة دوال جديدة للمميزات المفقودة:")
        print("  - unlimited_channels_menu")
        print("  - custom_download_links_menu")
        print("  - publish_intervals_extended_menu")
        print("  - page_customization_vip_menu")
        print("• تم إضافة معالجات الأحداث للدوال الجديدة")
        print("• جميع المميزات تستخدم الآن النظام الموحد لرسائل التحذير")
    else:
        print(f"\n⚠️ هناك {total - passed} اختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    asyncio.run(main())
