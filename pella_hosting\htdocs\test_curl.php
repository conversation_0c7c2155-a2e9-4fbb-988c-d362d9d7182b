<?php
/**
 * اختبار الاتصال مع Supabase باستخدام cURL - محدث
 * Test Supabase connection using cURL - Updated
 */

// تحميل الإعدادات من config.php
define('INCLUDED', true);
require_once 'config.php';

// إعدادات قاعدة البيانات من ملف الإعدادات
$db_config = getConfig('database')['supabase'];
$SUPABASE_URL = $db_config['url'];
$SUPABASE_KEY = $db_config['key'];
$SUPABASE_SERVICE_KEY = $db_config['service_key'];
$TABLE_NAME = getConfig('tables')['mods'];

echo "<h1>🧪 اختبار الاتصال مع Supabase - محدث</h1>";
echo "<p><strong>📊 اسم الجدول الصحيح: $TABLE_NAME</strong></p>";
echo "<hr>";

// اختبار 1: الاتصال الأساسي
echo "<h2>1️⃣ اختبار الاتصال الأساسي</h2>";
echo "<p><strong>🔑 استخدام Service Key للوصول الكامل</strong></p>";
$url = $SUPABASE_URL . "/rest/v1/";

// استخدام service key للوصول الكامل
$auth_key = $SUPABASE_SERVICE_KEY ? $SUPABASE_SERVICE_KEY : $SUPABASE_KEY;
$headers = array(
    'apikey: ' . $auth_key,
    'Authorization: Bearer ' . $auth_key,
    'Content-Type: application/json',
    'Prefer: return=representation'
);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code == 200) {
    echo "✅ <strong>نجح الاتصال الأساسي</strong><br>";
    echo "📊 HTTP Code: $http_code<br>";
} else {
    echo "❌ <strong>فشل الاتصال الأساسي</strong><br>";
    echo "📊 HTTP Code: $http_code<br>";
    echo "📄 Response: " . htmlspecialchars($response) . "<br>";
}

echo "<hr>";

// اختبار 2: جلب المودات من الجدول الصحيح
echo "<h2>2️⃣ اختبار جلب المودات من جدول '$TABLE_NAME'</h2>";
$url = $SUPABASE_URL . "/rest/v1/$TABLE_NAME?limit=5";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code == 200) {
    $data = json_decode($response, true);
    echo "✅ <strong>نجح جلب المودات</strong><br>";
    echo "📊 HTTP Code: $http_code<br>";
    echo "📈 عدد المودات: " . count($data) . "<br>";

    if (!empty($data)) {
        echo "<h3>📋 عينة من البيانات:</h3>";
        echo "<ul>";
        foreach (array_slice($data, 0, 3) as $mod) {
            $name = $mod['name'] ?? 'غير محدد';
            $id = $mod['id'] ?? 'غير محدد';
            $category = $mod['category'] ?? 'غير محدد';
            echo "<li>🎮 <strong>$name</strong> (ID: $id, Category: $category)</li>";
        }
        echo "</ul>";
    }
} else {
    echo "❌ <strong>فشل جلب المودات</strong><br>";
    echo "📊 HTTP Code: $http_code<br>";
    echo "📄 Response: " . htmlspecialchars($response) . "<br>";
}

echo "<hr>";
echo "<p><strong>🎯 انتهى الاختبار</strong></p>";
?>
