#!/bin/bash

# بوت نشر مودات ماين كرافت - Supabase Edition
# ملف تشغيل للينكس/ماك

echo "============================================================"
echo "🤖 بوت نشر مودات ماين كرافت - Supabase Edition"
echo "============================================================"
echo

echo "🔄 فحص Python..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python غير مثبت أو غير موجود في PATH"
        echo "🔧 يرجى تثبيت Python 3.8+ أولاً"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Python موجود ($PYTHON_CMD)"
echo

echo "🔄 فحص المتطلبات..."
$PYTHON_CMD -c "import telegram, requests" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️ المكتبات المطلوبة غير مثبتة"
    echo "📦 تثبيت المتطلبات..."
    $PYTHON_CMD -m pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت المتطلبات"
        exit 1
    fi
fi

echo "✅ المتطلبات متوفرة"
echo

echo "🧪 اختبار الاتصال بـ Supabase..."
$PYTHON_CMD test_supabase.py >/dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "⚠️ مشكلة في الاتصال بـ Supabase"
    echo "🔧 تحقق من إعدادات قاعدة البيانات"
    echo
    echo "🚀 سيتم تشغيل البوت على أي حال..."
else
    echo "✅ الاتصال بـ Supabase يعمل بشكل صحيح"
fi

echo
echo "============================================================"
echo "🚀 بدء تشغيل البوت..."
echo "⏹️ اضغط Ctrl+C لإيقاف البوت"
echo "============================================================"
echo

$PYTHON_CMD main.py

echo
echo "============================================================"
echo "⏹️ تم إيقاف البوت"
echo "============================================================"
