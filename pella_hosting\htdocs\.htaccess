# ملف .htaccess لصفحة عرض المودات
# .htaccess file for Mod Details Page

# تفعيل إعادة الكتابة
RewriteEngine On

# إعادة توجيه طلبات API
RewriteRule ^api/mod$ api.php?path=/mod [QSA,L]
RewriteRule ^api/file-info$ api.php?path=/file-info [QSA,L]
RewriteRule ^api/ad-click$ api.php?path=/ad-click [QSA,L]
RewriteRule ^api/complete-task$ api.php?path=/complete-task [QSA,L]

# إعادة توجيه صفحات تفاصيل المود
RewriteRule ^mod-details$ index.php [QSA,L]
RewriteRule ^telegram-mod-details$ index.php [QSA,L]

# إعادة توجيه الصفحة الرئيسية
RewriteRule ^$ index.php [QSA,L]

# حماية الملفات الحساسة
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files ".env">
    Order allow,deny
    Deny from all
</Files>

# تحسين الأداء
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# إعدادات الأمان
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# منع الوصول المباشر للملفات
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# إعدادات PHP
<IfModule mod_php7.c>
    php_value upload_max_filesize 50M
    php_value post_max_size 50M
    php_value memory_limit 128M
    php_value max_execution_time 300
    php_value max_input_time 300
</IfModule>

# صفحات الخطأ المخصصة
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# منع عرض قائمة الملفات
Options -Indexes

# إعادة توجيه HTTPS (اختياري)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
