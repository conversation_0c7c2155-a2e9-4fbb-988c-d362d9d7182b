#!/usr/bin/env python3
"""
إعداد الوصول العام للصفحات
Setup Public Access for Pages

هذا الملف يساعد في إعداد الوصول العام للصفحات من جميع الأجهزة
This file helps setup public access for pages from all devices
"""

import os
import sys
import json
import time
import requests
import subprocess
import threading
from pathlib import Path

def print_header():
    """طباعة رأس الإعداد"""
    print("\n" + "="*60)
    print("🌐 إعداد الوصول العام للصفحات")
    print("🔗 Setup Public Access for All Devices")
    print("="*60)
    print("📱 سيتمكن جميع المستخدمين من الوصول للصفحات")
    print("🌍 من أي جهاز وأي مكان في العالم")
    print("="*60 + "\n")

def check_ngrok():
    """التحقق من وجود ngrok"""
    print("🔍 Checking for ngrok...")
    
    try:
        result = subprocess.run(['ngrok', 'version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ ngrok found: {result.stdout.strip()}")
            return True
        else:
            print("❌ ngrok not working properly")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ ngrok not found")
        return False

def install_ngrok_instructions():
    """تعليمات تثبيت ngrok"""
    print("\n📥 تعليمات تثبيت ngrok:")
    print("="*40)
    print("1. 🌐 اذهب إلى: https://ngrok.com/download")
    print("2. 📱 قم بإنشاء حساب مجاني")
    print("3. 💾 حمل ngrok لنظام التشغيل الخاص بك")
    print("4. 📁 ضع ملف ngrok في مجلد البوت")
    print("5. 🔑 احصل على authtoken من لوحة التحكم")
    print("6. ⚙️ قم بتشغيل: ngrok config add-authtoken YOUR_TOKEN")
    print("="*40)
    
    # محاولة تحميل ngrok تلقائياً
    print("\n🤖 محاولة تحميل ngrok تلقائياً...")
    
    import platform
    system = platform.system().lower()
    
    if system == "windows":
        download_url = "https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-windows-amd64.zip"
        print(f"💾 رابط التحميل لـ Windows: {download_url}")
    elif system == "darwin":  # macOS
        download_url = "https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-darwin-amd64.zip"
        print(f"💾 رابط التحميل لـ macOS: {download_url}")
    else:  # Linux
        download_url = "https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-linux-amd64.tgz"
        print(f"💾 رابط التحميل لـ Linux: {download_url}")
    
    print("\n⚠️ بعد التحميل:")
    print("1. فك الضغط عن الملف")
    print("2. ضع ngrok في مجلد البوت")
    print("3. قم بتشغيل هذا البرنامج مرة أخرى")

def start_ngrok_tunnel(port=5001):
    """تشغيل نفق ngrok"""
    print(f"\n🚀 Starting ngrok tunnel for port {port}...")
    
    try:
        # تشغيل ngrok في الخلفية
        process = subprocess.Popen([
            'ngrok', 'http', str(port), '--log=stdout'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # انتظار قصير لبدء التشغيل
        time.sleep(3)
        
        # الحصول على الرابط العام
        try:
            response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
            if response.status_code == 200:
                data = response.json()
                tunnels = data.get('tunnels', [])
                
                for tunnel in tunnels:
                    if tunnel.get('proto') == 'https':
                        public_url = tunnel.get('public_url')
                        if public_url:
                            print(f"✅ ngrok tunnel created successfully!")
                            print(f"🌐 Public URL: {public_url}")
                            return public_url, process
                
                print("⚠️ HTTPS tunnel not found, trying HTTP...")
                for tunnel in tunnels:
                    if tunnel.get('proto') == 'http':
                        public_url = tunnel.get('public_url')
                        if public_url:
                            print(f"✅ ngrok tunnel created (HTTP)!")
                            print(f"🌐 Public URL: {public_url}")
                            return public_url, process
            
            print("❌ Could not get tunnel information")
            return None, process
            
        except requests.RequestException:
            print("❌ Could not connect to ngrok API")
            return None, process
            
    except Exception as e:
        print(f"❌ Failed to start ngrok: {e}")
        return None, None

def update_env_file(public_url):
    """تحديث ملف .env بالرابط العام"""
    print(f"\n📝 Updating .env file with public URL...")
    
    env_file = Path(".env")
    env_lines = []
    web_server_url_updated = False
    
    # قراءة الملف الموجود
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('WEB_SERVER_URL='):
                        env_lines.append(f"WEB_SERVER_URL={public_url}")
                        web_server_url_updated = True
                        print(f"✅ Updated WEB_SERVER_URL to {public_url}")
                    else:
                        env_lines.append(line)
        except Exception as e:
            print(f"⚠️ Error reading .env file: {e}")
    
    # إضافة WEB_SERVER_URL إذا لم يكن موجوداً
    if not web_server_url_updated:
        env_lines.append(f"WEB_SERVER_URL={public_url}")
        print(f"✅ Added WEB_SERVER_URL={public_url}")
    
    # حفظ الملف
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            for line in env_lines:
                f.write(line + '\n')
        print("✅ .env file updated successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to update .env file: {e}")
        return False

def start_bot_servers():
    """تشغيل خوادم البوت"""
    print("\n🤖 Starting bot servers...")
    
    try:
        # تشغيل خادم Telegram Web App
        from telegram_web_app import run_telegram_web_app
        
        web_app_thread = threading.Thread(
            target=run_telegram_web_app,
            args=(5001,),
            daemon=True,
            name="PublicTelegramWebApp"
        )
        web_app_thread.start()
        print("✅ Telegram Web App server started on port 5001")
        
        # تشغيل الخادم الأساسي
        from web_server import run_web_server
        
        flask_thread = threading.Thread(
            target=run_web_server,
            args=(5000,),
            daemon=True,
            name="PublicFlaskServer"
        )
        flask_thread.start()
        print("✅ Flask server started on port 5000")
        
        # انتظار قصير للتأكد من تشغيل الخوادم
        time.sleep(2)
        return True
        
    except Exception as e:
        print(f"❌ Failed to start servers: {e}")
        return False

def test_public_access(public_url):
    """اختبار الوصول العام"""
    print(f"\n🧪 Testing public access...")
    
    test_urls = [
        f"{public_url}/telegram-mod-details?id=1&lang=ar",
        f"{public_url}/api/mod/1?lang=ar"
    ]
    
    for url in test_urls:
        try:
            print(f"📡 Testing: {url}")
            response = requests.get(url, timeout=10)
            if response.status_code in [200, 404]:
                print(f"✅ URL accessible (status: {response.status_code})")
            else:
                print(f"⚠️ URL returned status: {response.status_code}")
        except Exception as e:
            print(f"❌ Failed to access {url}: {e}")

def create_qr_code(public_url):
    """إنشاء QR code للرابط العام"""
    print(f"\n📱 Creating QR code for easy access...")
    
    try:
        import qrcode
        
        # إنشاء QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(public_url)
        qr.make(fit=True)
        
        # حفظ كصورة
        img = qr.make_image(fill_color="black", back_color="white")
        img.save("public_url_qr.png")
        print("✅ QR code saved as public_url_qr.png")
        print("📱 يمكن للمستخدمين مسح الكود للوصول للصفحة")
        
    except ImportError:
        print("⚠️ qrcode library not installed")
        print("💡 Install with: pip install qrcode[pil]")
    except Exception as e:
        print(f"❌ Failed to create QR code: {e}")

def show_access_info(public_url):
    """عرض معلومات الوصول"""
    print("\n" + "="*60)
    print("🎉 الإعداد مكتمل! الصفحة متاحة الآن لجميع الأجهزة")
    print("🌍 PUBLIC ACCESS SETUP COMPLETE!")
    print("="*60)
    print(f"\n🌐 الرابط العام: {public_url}")
    print(f"📱 صفحة المود: {public_url}/telegram-mod-details?id=1&lang=ar")
    print(f"🔌 API: {public_url}/api/mod/1")
    print("\n📋 كيفية الاستخدام:")
    print("1. 🤖 قم بتشغيل البوت: python main.py")
    print("2. 📱 أرسل مود من البوت")
    print("3. 🎮 اضغط على زر 'عرض التفاصيل'")
    print("4. ✨ ستفتح الصفحة على أي جهاز!")
    print("\n⚠️ ملاحظات مهمة:")
    print("• 🔄 يجب إبقاء ngrok يعمل")
    print("• 🌐 الرابط صالح طالما ngrok يعمل")
    print("• 🔒 للإنتاج، استخدم خدمة استضافة دائمة")
    print("• 📱 يمكن مشاركة الرابط مع المستخدمين")
    print("="*60)

def main():
    """الدالة الرئيسية"""
    try:
        print_header()
        
        # التحقق من ngrok
        if not check_ngrok():
            install_ngrok_instructions()
            input("\n⏸️ اضغط Enter بعد تثبيت ngrok...")
            if not check_ngrok():
                print("❌ ngrok still not found. Please install it first.")
                return
        
        # تشغيل خوادم البوت
        if not start_bot_servers():
            print("❌ Failed to start bot servers")
            return
        
        # تشغيل نفق ngrok
        public_url, ngrok_process = start_ngrok_tunnel(5001)
        if not public_url:
            print("❌ Failed to create ngrok tunnel")
            return
        
        # تحديث ملف .env
        update_env_file(public_url)
        
        # اختبار الوصول العام
        test_public_access(public_url)
        
        # إنشاء QR code
        create_qr_code(public_url)
        
        # عرض معلومات الوصول
        show_access_info(public_url)
        
        print("\n🔄 Servers are running... Press Ctrl+C to stop")
        
        try:
            # إبقاء البرنامج يعمل
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping servers...")
            if ngrok_process:
                ngrok_process.terminate()
            print("✅ Stopped successfully")
        
    except Exception as e:
        print(f"\n💥 Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
