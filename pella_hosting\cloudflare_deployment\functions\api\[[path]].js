// Cloudflare Pages Function للتعامل مع طلبات API
export async function onRequest(context) {
    const { request, env } = context;
    const url = new URL(request.url);
    const path = url.pathname;

    // إعداد CORS headers
    const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // التعامل مع طلبات OPTIONS (CORS preflight)
    if (request.method === 'OPTIONS') {
        return new Response(null, { headers: corsHeaders });
    }

    try {
        // التحقق من متغيرات البيئة
        if (!env.BOT_TOKEN || !env.SUPABASE_URL || !env.SUPABASE_KEY) {
            return new Response(JSON.stringify({
                error: 'متغيرات البيئة غير مكتملة',
                missing: {
                    BOT_TOKEN: !env.BOT_TOKEN,
                    SUPABASE_URL: !env.SUPABASE_URL,
                    SUPABASE_KEY: !env.SUPABASE_KEY
                }
            }), {
                status: 500,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }

        // معالجة المسارات المختلفة
        if (path.startsWith('/api/webhook')) {
            return await handleWebhook(request, env);
        } else if (path.startsWith('/api/download/')) {
            return await handleDownload(request, env);
        } else if (path.startsWith('/api/mod/')) {
            return await handleModPage(request, env);
        } else if (path.startsWith('/api/health')) {
            return await handleHealth(request, env);
        }

        // مسار غير معروف
        return new Response(JSON.stringify({
            error: 'مسار غير معروف',
            path: path,
            available_endpoints: [
                '/api/webhook',
                '/api/download/{mod_id}',
                '/api/mod/{mod_id}',
                '/api/health'
            ]
        }), {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('خطأ في معالجة الطلب:', error);
        return new Response(JSON.stringify({
            error: 'خطأ داخلي في الخادم',
            message: error.message
        }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
}

// معالج webhook للبوت
async function handleWebhook(request, env) {
    if (request.method !== 'POST') {
        return new Response('Method not allowed', { status: 405 });
    }

    try {
        const update = await request.json();
        
        // هنا يمكنك إضافة منطق معالجة updates البوت
        // مثال بسيط:
        console.log('Received update:', update);

        return new Response(JSON.stringify({ ok: true }), {
            headers: { 'Content-Type': 'application/json' }
        });
    } catch (error) {
        console.error('خطأ في معالجة webhook:', error);
        return new Response(JSON.stringify({ error: error.message }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// معالج تحميل المودات
async function handleDownload(request, env) {
    const url = new URL(request.url);
    const modId = url.pathname.split('/').pop();

    try {
        // جلب بيانات المود من Supabase
        const response = await fetch(`${env.SUPABASE_URL}/rest/v1/mods?id=eq.${modId}`, {
            headers: {
                'apikey': env.SUPABASE_KEY,
                'Authorization': `Bearer ${env.SUPABASE_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('فشل في جلب بيانات المود');
        }

        const mods = await response.json();
        if (mods.length === 0) {
            return new Response('المود غير موجود', { status: 404 });
        }

        const mod = mods[0];
        
        // إعادة توجيه لرابط التحميل
        return Response.redirect(mod.download_link, 302);

    } catch (error) {
        console.error('خطأ في تحميل المود:', error);
        return new Response('خطأ في تحميل المود', { status: 500 });
    }
}

// معالج صفحة المود
async function handleModPage(request, env) {
    const url = new URL(request.url);
    const modId = url.pathname.split('/').pop();

    try {
        // جلب بيانات المود من Supabase
        const response = await fetch(`${env.SUPABASE_URL}/rest/v1/mods?id=eq.${modId}`, {
            headers: {
                'apikey': env.SUPABASE_KEY,
                'Authorization': `Bearer ${env.SUPABASE_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('فشل في جلب بيانات المود');
        }

        const mods = await response.json();
        if (mods.length === 0) {
            return new Response('المود غير موجود', { status: 404 });
        }

        const mod = mods[0];
        
        // إنشاء صفحة HTML للمود
        const html = generateModPage(mod);
        
        return new Response(html, {
            headers: { 'Content-Type': 'text/html; charset=utf-8' }
        });

    } catch (error) {
        console.error('خطأ في عرض صفحة المود:', error);
        return new Response('خطأ في عرض صفحة المود', { status: 500 });
    }
}

// معالج فحص الصحة
async function handleHealth(request, env) {
    return new Response(JSON.stringify({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: env.ENVIRONMENT || 'development'
    }), {
        headers: { 'Content-Type': 'application/json' }
    });
}

// إنشاء صفحة HTML للمود
function generateModPage(mod) {
    return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${mod.name} - Modetaris</title>
    <meta name="description" content="${mod.description}">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            background: #333;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .download-btn {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 20px 0;
        }
        .download-btn:hover {
            background: #45a049;
        }
        .mod-image {
            max-width: 100%;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${mod.name}</h1>
            <p>إصدار Minecraft: ${mod.mc_version}</p>
        </div>
        <div class="content">
            ${mod.image_url ? `<img src="${mod.image_url}" alt="${mod.name}" class="mod-image">` : ''}
            <p>${mod.description}</p>
            <a href="/api/download/${mod.id}" class="download-btn">
                📥 تحميل المود
            </a>
        </div>
    </div>
</body>
</html>
    `;
}
