<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>API Health Check</title>
</head>
<body>
    <script>
        // إرجاع استجابة JSON لفحص الصحة
        const healthData = {
            status: "healthy",
            timestamp: new Date().toISOString(),
            service: "Modetaris Bot API",
            version: "1.0.0",
            hosting: "Cloudflare Pages",
            endpoints: {
                health: "/api/health",
                webhook: "/api/webhook", 
                mod: "/api/mod/{id}",
                download: "/api/download/{id}"
            }
        };
        
        // تعيين Content-Type كـ JSON
        document.addEventListener('DOMContentLoaded', function() {
            document.body.innerHTML = '<pre>' + JSON.stringify(healthData, null, 2) + '</pre>';
        });
        
        // للطلبات عبر fetch
        if (window.parent !== window) {
            window.parent.postMessage(healthData, '*');
        }
    </script>
</body>
</html>
