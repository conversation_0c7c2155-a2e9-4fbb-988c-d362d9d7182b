# دليل حل مشاكل الشبكة - Network Troubleshooting Guide

## 🚨 المشكلة الحالية - Current Issue

```
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
```

هذا الخطأ يعني أن النظام لا يستطيع حل أسماء النطاقات (DNS resolution) للوصول إلى خوادم Telegram.

## 🔧 الحلول السريعة - Quick Solutions

### 1. 🚀 الحل الأسرع - Fastest Solution

```bash
# تشغيل أداة الإصلاح الشاملة
fix_and_run.bat

# أو تشغيل الأدوات منفردة
python diagnose_network.py
python auto_fix_network.py
python run_bot_enhanced.py
```

### 2. 🔍 تشخيص المشكلة

```bash
# تشخيص شامل للشبكة
python diagnose_network.py

# اختبار بسيط للاتصال
ping google.com
ping api.telegram.org
nslookup api.telegram.org
```

### 3. 🛠️ إصلاح تلقائي

```bash
# إصلاح تلقائي للمشاكل
python auto_fix_network.py

# تشغيل البوت مع إعدادات محسنة
python run_bot_enhanced.py
```

## 🔍 أسباب المشكلة المحتملة - Possible Causes

### 1. 🌐 مشاكل DNS
- خوادم DNS لا تعمل
- ذاكرة DNS تالفة
- إعدادات DNS خاطئة

### 2. 🔒 مشاكل الأمان
- الجدار الناري يحجب Python
- مضاد الفيروسات يحجب الاتصالات
- إعدادات البروكسي تتداخل

### 3. 🌍 مشاكل الشبكة
- انقطاع الإنترنت
- مشاكل في الراوتر
- مشاكل مزود الخدمة

### 4. 📱 مشاكل Telegram
- Telegram محجوب في المنطقة
- مشاكل في Telegram API
- Bot Token خاطئ

## 🔧 حلول مفصلة - Detailed Solutions

### 1. إصلاح DNS

#### Windows Command Prompt (كمدير):
```cmd
ipconfig /flushdns
ipconfig /release
ipconfig /renew
netsh winsock reset
```

#### تغيير DNS servers:
1. افتح **Control Panel** → **Network and Internet** → **Network Connections**
2. انقر بزر الماوس الأيمن على اتصالك → **Properties**
3. اختر **Internet Protocol Version 4 (TCP/IPv4)** → **Properties**
4. اختر **Use the following DNS server addresses**
5. ضع:
   - **Preferred DNS**: `*******`
   - **Alternate DNS**: `*******`

### 2. إصلاح الجدار الناري

#### Windows Defender Firewall:
1. افتح **Windows Defender Firewall**
2. اختر **Allow an app through firewall**
3. اضغط **Change Settings**
4. اضغط **Allow another app**
5. أضف `python.exe` و `pythonw.exe`

#### إعدادات متقدمة:
```cmd
# السماح لـ Python في الجدار الناري
netsh advfirewall firewall add rule name="Python" dir=in action=allow program="C:\Python\python.exe"
netsh advfirewall firewall add rule name="Python" dir=out action=allow program="C:\Python\python.exe"
```

### 3. إصلاح Python networking

```bash
# تحديث المكتبات
python -m pip install --upgrade pip
pip install --upgrade requests
pip install --upgrade urllib3
pip install --upgrade certifi
pip install --upgrade python-telegram-bot

# إعادة تثبيت المكتبات
pip uninstall python-telegram-bot
pip install python-telegram-bot
```

### 4. إعدادات البروكسي

#### إيقاف البروكسي:
1. افتح **Settings** → **Network & Internet** → **Proxy**
2. تأكد من إيقاف **Use a proxy server**

#### إعدادات Python للبروكسي:
```python
# في main.py أو في بداية السكريبت
import os
os.environ['HTTP_PROXY'] = ''
os.environ['HTTPS_PROXY'] = ''
os.environ['NO_PROXY'] = '*'
```

## 🧪 اختبار الحلول - Testing Solutions

### 1. اختبار DNS
```cmd
nslookup google.com
nslookup api.telegram.org
nslookup *******
```

### 2. اختبار الاتصال
```cmd
ping *******
ping google.com
ping api.telegram.org
telnet api.telegram.org 443
```

### 3. اختبار Python
```python
import socket
import requests

# اختبار DNS
print(socket.gethostbyname('google.com'))

# اختبار HTTP
response = requests.get('https://httpbin.org/ip', timeout=10)
print(response.status_code)

# اختبار Telegram
response = requests.get('https://api.telegram.org/bot123:test/getMe', timeout=10)
print(response.status_code)
```

## 🔄 خطوات الإصلاح المرتبة - Step-by-Step Fix

### الخطوة 1: تشخيص أولي
```bash
python diagnose_network.py
```

### الخطوة 2: إصلاح تلقائي
```bash
python auto_fix_network.py
```

### الخطوة 3: إعادة تشغيل النظام
```cmd
shutdown /r /t 0
```

### الخطوة 4: اختبار البوت
```bash
python run_bot_enhanced.py
```

## 🆘 حلول الطوارئ - Emergency Solutions

### 1. استخدام VPN
إذا كان Telegram محجوب:
- تثبيت VPN موثوق
- الاتصال بخادم في دولة أخرى
- تشغيل البوت

### 2. استخدام Hotspot
```bash
# استخدام هاتف محمول كـ hotspot
# الاتصال بالشبكة الجديدة
# تشغيل البوت
```

### 3. تغيير إعدادات Python
```python
# إضافة في بداية main.py
import ssl
ssl._create_default_https_context = ssl._create_unverified_context

import os
os.environ['PYTHONHTTPSVERIFY'] = '0'
```

## 📊 مراقبة الأداء - Performance Monitoring

### 1. سجلات مفيدة
```bash
# عرض سجل البوت
type bot.log

# عرض سجل الشبكة
type network.log

# عرض سجل الإصلاح
type auto_fix.log
```

### 2. مراقبة الاتصال
```python
# إضافة في البوت للمراقبة
import time
import logging

def monitor_connection():
    while True:
        try:
            socket.gethostbyname('api.telegram.org')
            logging.info("✅ الاتصال متاح")
        except:
            logging.error("❌ انقطع الاتصال")
        time.sleep(60)
```

## 🔧 أدوات مساعدة - Helper Tools

### 1. ملفات الإصلاح المتوفرة
- `diagnose_network.py` - تشخيص شامل
- `auto_fix_network.py` - إصلاح تلقائي
- `run_bot_enhanced.py` - تشغيل محسن
- `fix_and_run.bat` - أداة شاملة

### 2. أوامر Windows مفيدة
```cmd
# معلومات الشبكة
ipconfig /all
netstat -an
route print

# إعادة تعيين الشبكة
netsh int ip reset
netsh winsock reset
netsh advfirewall reset
```

## 📞 الحصول على المساعدة - Getting Help

### 1. معلومات مفيدة للدعم
- نوع نظام التشغيل
- إصدار Python
- إصدار python-telegram-bot
- نوع الاتصال (WiFi/Ethernet)
- مزود الإنترنت
- رسائل الخطأ الكاملة

### 2. سجلات مهمة
```bash
# تشغيل مع سجلات مفصلة
python -v main.py > debug.log 2>&1

# جمع معلومات النظام
systeminfo > system_info.txt
```

---

## ✅ قائمة التحقق السريع - Quick Checklist

- [ ] تشغيل `python diagnose_network.py`
- [ ] تشغيل `python auto_fix_network.py`
- [ ] إعادة تشغيل الكمبيوتر
- [ ] تحديث Python والمكتبات
- [ ] فحص الجدار الناري
- [ ] تغيير DNS إلى *******
- [ ] تعطيل البروكسي
- [ ] اختبار شبكة أخرى
- [ ] تشغيل `python run_bot_enhanced.py`

---

**آخر تحديث**: 2025-01-11  
**الإصدار**: 2.0
