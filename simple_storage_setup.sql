-- إعداد بسيط لـ Supabase Storage
-- تشغيل هذا الكود في SQL Editor في لوحة تحكم Supabase

-- 1. تعطيل RLS مؤقتاً للاختبار (غير آمن للإنتاج)
ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;

-- 2. إنشاء حاوية للصور
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'image3',
    'image3', 
    true,
    52428800, -- 50 MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- 3. إنشاء حاوية بديلة
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'avatars',
    'avatars', 
    true,
    52428800, -- 50 MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- 4. التحقق من الحاويات
SELECT * FROM storage.buckets;

-- ملاحظة: تعطيل RLS يجعل جميع الملفات قابلة للوصول والتعديل من قبل الجميع
-- هذا مناسب للاختبار فقط، يجب تفعيل RLS مع السياسات المناسبة في الإنتاج
