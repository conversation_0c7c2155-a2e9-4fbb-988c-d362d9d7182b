-- إنشاء جدول إعدادات تخصيص الصفحة
-- يجب تشغيل هذا الكود في SQL Editor في لوحة تحكم Supabase
-- قاعدة البيانات: https://ytqxxodyecdeosnqoure.supabase.co

-- إن<PERSON>اء جدول page_customization_settings (الجدول الأساسي المطلوب)
CREATE TABLE IF NOT EXISTS page_customization_settings (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL UNIQUE,
    site_name TEXT DEFAULT 'Modetaris',
    channel_logo_url TEXT,
    logo_position TEXT DEFAULT 'right',
    page_theme TEXT DEFAULT 'default',
    -- الأساليب الجديدة
    style_template TEXT DEFAULT 'default' CHECK (style_template IN ('default', 'telegram', 'tiktok', 'classic', 'professional', 'custom')),
    -- إعدادات الستايل المخصص المتقدم
    custom_bg_color TEXT,
    custom_header_color TEXT,
    custom_text_color TEXT,
    custom_button_color TEXT,
    custom_border_color TEXT,
    custom_accent_color TEXT,
    custom_card_color TEXT,
    custom_shadow_color TEXT,
    -- إعدادات الخطوط والتأثيرات
    custom_font_family TEXT DEFAULT 'Press Start 2P',
    custom_font_size TEXT DEFAULT 'medium',
    enable_animations BOOLEAN DEFAULT true,
    enable_gradients BOOLEAN DEFAULT true,
    enable_shadows BOOLEAN DEFAULT true,
    -- إعدادات التخطيط
    layout_style TEXT DEFAULT 'modern' CHECK (layout_style IN ('modern', 'compact', 'spacious', 'minimal')),
    border_radius TEXT DEFAULT 'medium' CHECK (border_radius IN ('none', 'small', 'medium', 'large', 'round')),
    -- الإعدادات الموجودة
    show_all_images BOOLEAN DEFAULT true,
    enable_mod_opening BOOLEAN DEFAULT true,
    download_button_text_ar TEXT DEFAULT 'تحميل',
    download_button_text_en TEXT DEFAULT 'Download',
    open_button_text_ar TEXT DEFAULT 'فتح',
    open_button_text_en TEXT DEFAULT 'Open',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهرس على user_id لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_page_customization_settings_user_id ON page_customization_settings(user_id);

-- تعطيل RLS مؤقتاً للاختبار (يمكن تفعيله لاحقاً مع السياسات المناسبة)
ALTER TABLE page_customization_settings DISABLE ROW LEVEL SECURITY;

-- إدراج سجل تجريبي للتأكد من عمل الجدول
INSERT INTO page_customization_settings (user_id, site_name)
VALUES ('test_user', 'Test Site')
ON CONFLICT (user_id) DO NOTHING;

-- حذف السجل التجريبي
DELETE FROM page_customization_settings WHERE user_id = 'test_user';

-- التحقق من إنشاء الجدول
SELECT 'page_customization_settings' as table_name, count(*) as row_count FROM page_customization_settings;

-- ملاحظة: تأكد من تشغيل هذا الكود في قاعدة البيانات الصحيحة
-- https://ytqxxodyecdeosnqoure.supabase.co
