#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إعداد البيئة التلقائية
Automatic Environment Setup Tool
"""

import os
import sys
import secrets
import logging
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnvironmentSetup:
    def __init__(self):
        self.env_file = ".env"
        self.template_file = ".env.template"
        self.example_file = ".env.example"
        
    def check_existing_env(self):
        """فحص وجود ملف .env"""
        if os.path.exists(self.env_file):
            logger.info("✅ ملف .env موجود بالفعل")
            return True
        return False
    
    def create_env_from_template(self):
        """إنشاء ملف .env من النموذج"""
        logger.info("🔧 إنشاء ملف .env من النموذج...")
        
        template_path = None
        if os.path.exists(self.template_file):
            template_path = self.template_file
        elif os.path.exists(self.example_file):
            template_path = self.example_file
        else:
            logger.error("❌ لم يتم العثور على ملف نموذج")
            return False
        
        try:
            # نسخ النموذج إلى .env
            with open(template_path, 'r', encoding='utf-8') as template:
                content = template.read()
            
            # استبدال القيم النموذجية بقيم آمنة
            content = self.replace_template_values(content)
            
            with open(self.env_file, 'w', encoding='utf-8') as env_file:
                env_file.write(content)
            
            logger.info("✅ تم إنشاء ملف .env بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل إنشاء ملف .env: {e}")
            return False
    
    def replace_template_values(self, content):
        """استبدال القيم النموذجية بقيم آمنة"""
        logger.info("🔐 إنشاء مفاتيح آمنة...")
        
        # إنشاء مفاتيح آمنة
        encryption_secret = secrets.token_hex(32)
        jwt_secret = secrets.token_urlsafe(32)
        webhook_secret = secrets.token_urlsafe(16)
        
        # استبدال القيم
        replacements = {
            'your_32_byte_encryption_secret_here': encryption_secret,
            'your_jwt_secret_here': jwt_secret,
            'your_webhook_secret_here': webhook_secret,
        }
        
        for old_value, new_value in replacements.items():
            content = content.replace(old_value, new_value)
        
        return content
    
    def validate_env_file(self):
        """التحقق من صحة ملف .env"""
        logger.info("🔍 التحقق من صحة ملف .env...")
        
        if not os.path.exists(self.env_file):
            logger.error("❌ ملف .env غير موجود")
            return False
        
        required_vars = [
            'BOT_TOKEN',
            'ADMIN_CHAT_ID', 
            'SUPABASE_URL',
            'SUPABASE_KEY'
        ]
        
        missing_vars = []
        placeholder_vars = []
        
        try:
            with open(self.env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for var in required_vars:
                if f"{var}=" not in content:
                    missing_vars.append(var)
                elif f"{var}=your_" in content or f"{var}=https://your-" in content:
                    placeholder_vars.append(var)
            
            if missing_vars:
                logger.error(f"❌ متغيرات مفقودة: {missing_vars}")
                return False
            
            if placeholder_vars:
                logger.warning(f"⚠️ متغيرات تحتاج تحديث: {placeholder_vars}")
                return False
            
            logger.info("✅ ملف .env صحيح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في قراءة ملف .env: {e}")
            return False
    
    def interactive_setup(self):
        """إعداد تفاعلي للمتغيرات"""
        logger.info("🎯 بدء الإعداد التفاعلي...")
        
        print("\n" + "="*60)
        print("🤖 مرحباً بك في أداة إعداد بوت مودات ماين كرافت")
        print("="*60)
        
        # جمع البيانات من المستخدم
        bot_token = input("\n📱 أدخل رمز البوت من @BotFather: ").strip()
        if not bot_token:
            logger.error("❌ رمز البوت مطلوب")
            return False
        
        admin_chat_id = input("👤 أدخل معرف المشرف (Chat ID): ").strip()
        if not admin_chat_id:
            logger.error("❌ معرف المشرف مطلوب")
            return False
        
        supabase_url = input("🗄️ أدخل رابط Supabase: ").strip()
        if not supabase_url:
            logger.error("❌ رابط Supabase مطلوب")
            return False
        
        supabase_key = input("🔑 أدخل مفتاح Supabase: ").strip()
        if not supabase_key:
            logger.error("❌ مفتاح Supabase مطلوب")
            return False
        
        # إنشاء ملف .env
        env_content = f"""# ملف متغيرات البيئة - تم إنشاؤه تلقائياً
# Environment Variables - Auto Generated

# ===== إعدادات البوت الأساسية =====
BOT_TOKEN={bot_token}
ADMIN_CHAT_ID={admin_chat_id}

# ===== إعدادات قاعدة البيانات =====
SUPABASE_URL={supabase_url}
SUPABASE_KEY={supabase_key}

# ===== إعدادات التحسين =====
OPTIMIZATION_ENABLED=true
MEMORY_LIMIT_MB=100
LOG_LEVEL=INFO
API_TIMEOUT=30
MAX_RETRIES=3
RETRY_DELAY=2

# ===== إعدادات الخادم =====
WEB_SERVER_PORT=5001
TELEGRAM_WEB_APP_PORT=5002

# ===== إعدادات الأمان =====
ENCRYPTION_SECRET={secrets.token_hex(32)}
JWT_SECRET={secrets.token_urlsafe(32)}

# ===== إعدادات اختيارية =====
ENVIRONMENT=production
DEBUG=false
MAX_FILE_SIZE_MB=50

# تم إنشاء هذا الملف في: {os.path.basename(__file__)}
"""
        
        try:
            with open(self.env_file, 'w', encoding='utf-8') as f:
                f.write(env_content)
            
            logger.info("✅ تم إنشاء ملف .env بنجاح")
            print("\n🎉 تم إعداد البيئة بنجاح!")
            print("🚀 يمكنك الآن تشغيل البوت باستخدام: python run_optimized_bot.py")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل إنشاء ملف .env: {e}")
            return False
    
    def run_setup(self):
        """تشغيل عملية الإعداد"""
        print("🔧 أداة إعداد البيئة التلقائية")
        print("="*40)
        
        # فحص وجود ملف .env
        if self.check_existing_env():
            choice = input("\nملف .env موجود. هل تريد إعادة إنشاؤه؟ (y/N): ").strip().lower()
            if choice not in ['y', 'yes', 'نعم']:
                logger.info("تم إلغاء العملية")
                return True
        
        # محاولة إنشاء من النموذج أولاً
        if os.path.exists(self.template_file) or os.path.exists(self.example_file):
            choice = input("\nهل تريد الإعداد التفاعلي؟ (Y/n): ").strip().lower()
            if choice in ['', 'y', 'yes', 'نعم']:
                return self.interactive_setup()
            else:
                return self.create_env_from_template()
        else:
            # إعداد تفاعلي إذا لم توجد نماذج
            return self.interactive_setup()

def main():
    """الدالة الرئيسية"""
    try:
        setup = EnvironmentSetup()
        success = setup.run_setup()
        
        if success:
            # التحقق من صحة الإعداد
            if setup.validate_env_file():
                print("\n✅ تم إعداد البيئة بنجاح!")
                print("🚀 البوت جاهز للتشغيل")
            else:
                print("\n⚠️ تم إنشاء ملف .env ولكن يحتاج تحديث بعض القيم")
        else:
            print("\n❌ فشل إعداد البيئة")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء العملية")
    except Exception as e:
        logger.error(f"❌ خطأ فادح: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
