#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات خاصة للاستضافة المجانية - Cloudflare Pages
Hosting Configuration for Cloudflare Pages Free Hosting
"""

import os
import logging

logger = logging.getLogger(__name__)

class HostingConfig:
    """إعدادات محسنة للاستضافة المجانية على Cloudflare Pages"""

    def __init__(self):
        self.load_hosting_config()

    def load_hosting_config(self):
        """تحميل إعدادات الاستضافة"""
        # بيانات الاتصال الصحيحة للاستضافة
        self.BOT_TOKEN = "7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4"
        self.ADMIN_CHAT_ID = "7513880877"
        self.ADMIN_USERNAME = "Kim880198"

        # بيانات قاعدة البيانات الصحيحة
        self.SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co"
        self.SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4"
        self.TABLE_NAME = "mods"  # اسم الجدول الصحيح

        # إعدادات Cloudflare Pages المحسنة
        self.HOSTING_PROVIDER = "cloudflare"
        self.CLOUDFLARE_PAGES_ENABLED = True
        self.OPTIMIZATION_ENABLED = True
        self.LOW_RESOURCE_MODE = False  # Cloudflare Pages يدعم موارد أكثر
        self.MAX_MEMORY_MB = 512  # حد الذاكرة لـ Cloudflare Pages
        self.MAX_CPU_USAGE = 0.5  # حد المعالج المحسن

        # إعدادات الشبكة المحسنة لـ Cloudflare
        self.REQUEST_TIMEOUT = 60  # مهلة أطول للطلبات
        self.CONNECTION_POOL_SIZE = 20  # مجموعة اتصالات أكبر
        self.MAX_RETRIES = 5  # محاولات أكثر

        # إعدادات Cloudflare Pages الخاصة
        self.CLOUDFLARE_PAGES_URL = ""  # سيتم تعيينه لاحقاً
        self.CLOUDFLARE_ZONE_ID = ""  # معرف المنطقة
        self.CLOUDFLARE_API_TOKEN = ""  # رمز API

        # إعدادات الأداء المحسنة
        self.ENABLE_CACHING = True
        self.CACHE_TTL = 3600  # مدة التخزين المؤقت بالثواني
        self.ENABLE_COMPRESSION = True
        self.ENABLE_CDN = True

        # تطبيق الإعدادات على متغيرات البيئة
        self.apply_to_environment()
    
    def apply_to_environment(self):
        """تطبيق الإعدادات على متغيرات البيئة"""
        os.environ["BOT_TOKEN"] = self.BOT_TOKEN
        os.environ["ADMIN_CHAT_ID"] = self.ADMIN_CHAT_ID
        os.environ["ADMIN_USERNAME"] = self.ADMIN_USERNAME
        os.environ["SUPABASE_URL"] = self.SUPABASE_URL
        os.environ["SUPABASE_KEY"] = self.SUPABASE_KEY

        # إعدادات التحسين
        os.environ["HOSTING_PROVIDER"] = self.HOSTING_PROVIDER
        os.environ["CLOUDFLARE_PAGES_ENABLED"] = str(self.CLOUDFLARE_PAGES_ENABLED).lower()
        os.environ["OPTIMIZATION_ENABLED"] = str(self.OPTIMIZATION_ENABLED).lower()
        os.environ["LOW_RESOURCE_MODE"] = str(self.LOW_RESOURCE_MODE).lower()
        os.environ["REQUEST_TIMEOUT"] = str(self.REQUEST_TIMEOUT)
        os.environ["MAX_MEMORY_MB"] = str(self.MAX_MEMORY_MB)
        os.environ["CONNECTION_POOL_SIZE"] = str(self.CONNECTION_POOL_SIZE)

        # إعدادات Cloudflare
        os.environ["ENABLE_CACHING"] = str(self.ENABLE_CACHING).lower()
        os.environ["CACHE_TTL"] = str(self.CACHE_TTL)
        os.environ["ENABLE_COMPRESSION"] = str(self.ENABLE_COMPRESSION).lower()
        os.environ["ENABLE_CDN"] = str(self.ENABLE_CDN).lower()

        logger.info("✅ تم تطبيق إعدادات Cloudflare Pages بنجاح")
    
    def get_supabase_headers(self):
        """الحصول على headers صحيحة لـ Supabase"""
        return {
            'apikey': self.SUPABASE_KEY,
            'Authorization': f'Bearer {self.SUPABASE_KEY}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }
    
    def get_database_url(self, endpoint=""):
        """بناء رابط قاعدة البيانات"""
        base_url = f"{self.SUPABASE_URL}/rest/v1/{self.TABLE_NAME}"
        if endpoint:
            return f"{base_url}?{endpoint}"
        return base_url
    
    def get_cloudflare_headers(self):
        """الحصول على headers خاصة بـ Cloudflare"""
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'ModetarisBot/1.0 (Cloudflare Pages)',
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip, deflate, br'
        }

        if self.CLOUDFLARE_API_TOKEN:
            headers['Authorization'] = f'Bearer {self.CLOUDFLARE_API_TOKEN}'

        return headers

    def setup_cloudflare_pages(self, domain_name):
        """إعداد Cloudflare Pages للمشروع"""
        try:
            self.CLOUDFLARE_PAGES_URL = f"https://{domain_name}.pages.dev"

            # إعداد متغيرات البيئة لـ Cloudflare Pages
            cloudflare_env = {
                'NODE_ENV': 'production',
                'HOSTING_PROVIDER': 'cloudflare',
                'CLOUDFLARE_PAGES': 'true',
                'BOT_TOKEN': self.BOT_TOKEN,
                'SUPABASE_URL': self.SUPABASE_URL,
                'SUPABASE_KEY': self.SUPABASE_KEY
            }

            logger.info(f"✅ تم إعداد Cloudflare Pages: {self.CLOUDFLARE_PAGES_URL}")
            return True, cloudflare_env

        except Exception as e:
            logger.error(f"❌ خطأ في إعداد Cloudflare Pages: {e}")
            return False, {}

    def test_connection(self):
        """اختبار الاتصال مع قاعدة البيانات وخدمات Cloudflare"""
        try:
            import requests

            # اختبار الاتصال الأساسي مع Supabase
            url = f"{self.SUPABASE_URL}/rest/v1/"
            response = requests.get(url, headers=self.get_supabase_headers(), timeout=self.REQUEST_TIMEOUT)

            if response.status_code == 200:
                logger.info("✅ اختبار الاتصال مع Supabase نجح")

                # اختبار جدول المودات
                mods_url = self.get_database_url("limit=1")
                mods_response = requests.get(mods_url, headers=self.get_supabase_headers(), timeout=self.REQUEST_TIMEOUT)

                if mods_response.status_code == 200:
                    logger.info("✅ اختبار جدول المودات نجح")

                    # اختبار Cloudflare Pages إذا كان متاحاً
                    if self.CLOUDFLARE_PAGES_URL:
                        try:
                            cf_response = requests.get(self.CLOUDFLARE_PAGES_URL, timeout=10)
                            if cf_response.status_code == 200:
                                logger.info("✅ اختبار Cloudflare Pages نجح")
                            else:
                                logger.warning(f"⚠️ Cloudflare Pages غير متاح: {cf_response.status_code}")
                        except:
                            logger.warning("⚠️ لم يتم اختبار Cloudflare Pages")

                    return True
                else:
                    logger.error(f"❌ فشل اختبار جدول المودات: {mods_response.status_code}")
                    logger.error(f"Response: {mods_response.text}")
                    return False
            else:
                logger.error(f"❌ فشل الاتصال مع Supabase: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في اختبار الاتصال: {e}")
            return False
    
    def print_config_summary(self):
        """طباعة ملخص الإعدادات"""
        print("\n" + "="*70)
        print("🚀 إعدادات الاستضافة المجانية - Cloudflare Pages")
        print("="*70)
        print(f"🤖 Bot Token: {self.BOT_TOKEN[:20]}...")
        print(f"👤 Admin ID: {self.ADMIN_CHAT_ID}")
        print(f"🗄️ Database: {self.SUPABASE_URL}")
        print(f"📊 Table: {self.TABLE_NAME}")
        print(f"🌐 Hosting Provider: {self.HOSTING_PROVIDER.upper()}")
        print(f"☁️ Cloudflare Pages: {'✅ Enabled' if self.CLOUDFLARE_PAGES_ENABLED else '❌ Disabled'}")
        print(f"⚡ Optimization: {'✅ Enabled' if self.OPTIMIZATION_ENABLED else '❌ Disabled'}")
        print(f"💾 Low Resource Mode: {'✅ Enabled' if self.LOW_RESOURCE_MODE else '❌ Disabled'}")
        print(f"🔗 Request Timeout: {self.REQUEST_TIMEOUT}s")
        print(f"💾 Max Memory: {self.MAX_MEMORY_MB}MB")
        print(f"🔄 Connection Pool: {self.CONNECTION_POOL_SIZE}")
        print(f"📦 Caching: {'✅ Enabled' if self.ENABLE_CACHING else '❌ Disabled'}")
        print(f"🗜️ Compression: {'✅ Enabled' if self.ENABLE_COMPRESSION else '❌ Disabled'}")
        print(f"🌍 CDN: {'✅ Enabled' if self.ENABLE_CDN else '❌ Disabled'}")
        if self.CLOUDFLARE_PAGES_URL:
            print(f"🔗 Pages URL: {self.CLOUDFLARE_PAGES_URL}")
        print("="*70)

# إنشاء مثيل الإعدادات
hosting_config = HostingConfig()

def get_hosting_config():
    """الحصول على إعدادات الاستضافة"""
    return hosting_config

def test_hosting_setup():
    """اختبار إعداد الاستضافة"""
    print("🧪 اختبار إعداد الاستضافة على Cloudflare Pages...")

    config = get_hosting_config()
    config.print_config_summary()

    print("\n🔍 اختبار الاتصال...")
    if config.test_connection():
        print("✅ جميع الاختبارات نجحت! البوت جاهز للاستضافة على Cloudflare Pages.")
        print("\n📋 خطوات النشر على Cloudflare Pages:")
        print("1. قم بإنشاء مستودع Git جديد")
        print("2. ارفع ملفات البوت إلى المستودع")
        print("3. اربط المستودع مع Cloudflare Pages")
        print("4. اضبط متغيرات البيئة في إعدادات Cloudflare Pages")
        print("5. انشر المشروع")
        return True
    else:
        print("❌ فشل في الاختبار. يرجى مراجعة الإعدادات.")
        return False

def generate_cloudflare_deployment_guide():
    """إنشاء دليل النشر على Cloudflare Pages"""
    guide = """
# دليل النشر على Cloudflare Pages

## الخطوات المطلوبة:

### 1. إعداد المستودع
```bash
git init
git add .
git commit -m "Initial commit"
git remote add origin YOUR_REPO_URL
git push -u origin main
```

### 2. متغيرات البيئة المطلوبة في Cloudflare Pages:
- BOT_TOKEN: {bot_token}
- ADMIN_CHAT_ID: {admin_id}
- SUPABASE_URL: {supabase_url}
- SUPABASE_KEY: {supabase_key}
- HOSTING_PROVIDER: cloudflare
- CLOUDFLARE_PAGES: true

### 3. إعدادات البناء:
- Build command: npm install && npm run build
- Build output directory: dist
- Root directory: /

### 4. إعدادات Functions (إذا لزم الأمر):
- Compatibility date: 2023-05-18
- Compatibility flags: nodejs_compat

## ملاحظات مهمة:
- تأكد من أن جميع متغيرات البيئة مضبوطة بشكل صحيح
- استخدم HTTPS فقط للروابط
- فعل Caching للحصول على أداء أفضل
"""

    config = get_hosting_config()
    return guide.format(
        bot_token=config.BOT_TOKEN[:20] + "...",
        admin_id=config.ADMIN_CHAT_ID,
        supabase_url=config.SUPABASE_URL,
        supabase_key=config.SUPABASE_KEY[:20] + "..."
    )

if __name__ == "__main__":
    print("🚀 اختبار إعدادات Cloudflare Pages...")
    if test_hosting_setup():
        print("\n📖 دليل النشر:")
        print(generate_cloudflare_deployment_guide())
    else:
        print("\n❌ يرجى إصلاح الأخطاء قبل النشر.")
