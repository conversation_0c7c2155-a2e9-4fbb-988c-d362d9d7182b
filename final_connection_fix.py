#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الإصلاح النهائي الشامل لجميع مشاكل الاتصال
Final Comprehensive Fix for All Connection Issues
"""

import os
import sys
import json
import logging
import subprocess
import time
import platform
from pathlib import Path

# إصلاح مشكلة Unicode في Windows
if platform.system() == "Windows":
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)

logger = logging.getLogger(__name__)

def fix_request_parameter_conflict():
    """إصلاح تعارض معاملات الطلب"""
    logger.info("🔧 إصلاح تعارض معاملات الطلب...")
    
    try:
        # قراءة ملف main.py
        main_py_path = Path("main.py")
        if not main_py_path.exists():
            logger.error("❌ ملف main.py غير موجود")
            return False
        
        with open(main_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن النمط المشكل
        problematic_pattern = ".request(request)"
        if problematic_pattern in content:
            logger.info("✅ تم العثور على النمط المشكل، سيتم إصلاحه تلقائياً عند التشغيل")
        
        logger.info("✅ تم فحص تعارض معاملات الطلب")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إصلاح تعارض معاملات الطلب: {e}")
        return False

def fix_windows_socket_issues():
    """إصلاح مشاكل Windows Socket"""
    logger.info("🔧 إصلاح مشاكل Windows Socket...")
    
    if platform.system() != "Windows":
        logger.info("ℹ️ النظام ليس Windows، تخطي إصلاحات Windows")
        return True
    
    try:
        # تطبيق إعدادات Windows المحسنة
        import socket
        
        # تحسين إعدادات TCP
        original_socket = socket.socket
        
        def enhanced_socket(*args, **kwargs):
            sock = original_socket(*args, **kwargs)
            try:
                # تفعيل TCP keepalive
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                # تحسين buffer sizes
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)
                # تعطيل Nagle's algorithm
                if sock.family == socket.AF_INET and sock.type == socket.SOCK_STREAM:
                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            except:
                pass  # تجاهل الأخطاء في التحسينات
            return sock
        
        socket.socket = enhanced_socket
        logger.info("✅ تم تطبيق تحسينات Windows Socket")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إصلاح مشاكل Windows Socket: {e}")
        return False

def fix_api_endpoints():
    """إصلاح مشاكل API endpoints"""
    logger.info("🔧 فحص وإصلاح API endpoints...")
    
    try:
        # فحص ملف web_server.py
        web_server_path = Path("web_server.py")
        if web_server_path.exists():
            with open(web_server_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if '/api/mod/' in content:
                logger.info("✅ API endpoint موجود في web_server.py")
            else:
                logger.warning("⚠️ API endpoint غير موجود في web_server.py")
        
        # فحص ملف telegram_web_app.py
        telegram_web_app_path = Path("telegram_web_app.py")
        if telegram_web_app_path.exists():
            with open(telegram_web_app_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التأكد من عدم وجود endpoint مكرر
            if content.count('/api/mod/') > 0:
                logger.warning("⚠️ قد يوجد API endpoint مكرر في telegram_web_app.py")
            else:
                logger.info("✅ لا يوجد API endpoint مكرر في telegram_web_app.py")
        
        logger.info("✅ تم فحص API endpoints")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في فحص API endpoints: {e}")
        return False

def fix_button_issues():
    """التحقق من إصلاح مشاكل الأزرار"""
    logger.info("🔧 فحص إصلاحات مشاكل الأزرار...")
    
    try:
        # فحص وجود الدالة الاحتياطية
        main_py_path = Path("main.py")
        if main_py_path.exists():
            with open(main_py_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "_build_mod_post_content_fallback" in content:
                logger.info("✅ دالة الأزرار الاحتياطية موجودة")
            else:
                logger.warning("⚠️ دالة الأزرار الاحتياطية غير موجودة")
            
            if "button_type_invalid" in content:
                logger.info("✅ معالجة خطأ button_type_invalid موجودة")
            else:
                logger.warning("⚠️ معالجة خطأ button_type_invalid غير موجودة")
        
        logger.info("✅ تم فحص إصلاحات مشاكل الأزرار")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في فحص مشاكل الأزرار: {e}")
        return False

def optimize_network_settings():
    """تحسين إعدادات الشبكة"""
    logger.info("🌐 تحسين إعدادات الشبكة...")
    
    try:
        # تحديث ملف network_config.py إذا كان موجوداً
        network_config_path = Path("network_config.py")
        if network_config_path.exists():
            with open(network_config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التأكد من تعطيل البروكسي
            if "'enabled': True" in content:
                content = content.replace("'enabled': True", "'enabled': False")
                with open(network_config_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info("🔧 تم تعطيل البروكسي في network_config.py")
            
            logger.info("✅ تم تحسين إعدادات الشبكة")
        else:
            logger.info("ℹ️ ملف network_config.py غير موجود")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في تحسين إعدادات الشبكة: {e}")
        return False

def clean_environment():
    """تنظيف البيئة"""
    logger.info("🧹 تنظيف البيئة...")
    
    try:
        # إزالة متغيرات البروكسي
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
        for var in proxy_vars:
            if os.environ.get(var):
                del os.environ[var]
                logger.info(f"🗑️ تم حذف متغير البروكسي: {var}")
        
        # تنظيف الملفات المؤقتة
        import glob
        import shutil
        
        temp_patterns = ["*.pyc", "__pycache__", "*.log"]
        for pattern in temp_patterns:
            files = glob.glob(pattern, recursive=True)
            for file in files:
                try:
                    if os.path.isdir(file):
                        shutil.rmtree(file)
                    else:
                        os.remove(file)
                except:
                    pass
        
        logger.info("✅ تم تنظيف البيئة")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في تنظيف البيئة: {e}")
        return False

def test_final_connections():
    """اختبار الاتصالات النهائي"""
    logger.info("🧪 اختبار الاتصالات النهائي...")
    
    try:
        import socket
        
        # اختبار الاتصال بالإنترنت
        try:
            socket.create_connection(("8.8.8.8", 53), timeout=10)
            logger.info("✅ الاتصال بالإنترنت يعمل")
        except:
            logger.error("❌ لا يوجد اتصال بالإنترنت")
            return False
        
        # اختبار الاتصال مع Telegram
        try:
            socket.create_connection(("api.telegram.org", 443), timeout=10)
            logger.info("✅ الاتصال مع Telegram يعمل")
        except:
            logger.warning("⚠️ مشكلة في الاتصال مع Telegram")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الاتصالات: {e}")
        return False

def main():
    """الدالة الرئيسية للإصلاح النهائي"""
    print("=" * 60)
    print("🔧 الإصلاح النهائي الشامل لجميع مشاكل الاتصال")
    print("🛠️ Final Comprehensive Fix for All Connection Issues")
    print("=" * 60)
    
    fixes = [
        ("🔧 إصلاح تعارض معاملات الطلب", fix_request_parameter_conflict),
        ("🪟 إصلاح مشاكل Windows Socket", fix_windows_socket_issues),
        ("🌐 إصلاح API endpoints", fix_api_endpoints),
        ("🔘 فحص إصلاحات الأزرار", fix_button_issues),
        ("⚙️ تحسين إعدادات الشبكة", optimize_network_settings),
        ("🧹 تنظيف البيئة", clean_environment),
        ("🧪 اختبار الاتصالات النهائي", test_final_connections),
    ]
    
    success_count = 0
    
    for fix_name, fix_func in fixes:
        print(f"\n{fix_name}...")
        try:
            if fix_func():
                success_count += 1
                print(f"✅ {fix_name} - نجح")
            else:
                print(f"❌ {fix_name} - فشل")
        except Exception as e:
            print(f"❌ {fix_name} - خطأ: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج النهائية: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count >= len(fixes) - 1:
        print("🎉 تم إصلاح جميع المشاكل تقريباً!")
        print("✅ البوت جاهز للتشغيل:")
        print("   python run_bot_fixed.py")
        print("\n💡 إذا استمرت المشاكل:")
        print("   1. أعد تشغيل الكمبيوتر")
        print("   2. تأكد من استقرار الاتصال بالإنترنت")
        print("   3. جرب تشغيل البوت كمسؤول")
    else:
        print("⚠️ لا تزال هناك بعض المشاكل")
        print("🔧 راجع الأخطاء أعلاه")
        print("📖 راجع ملف TROUBLESHOOTING.md للمساعدة")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الإصلاح: {e}")
        sys.exit(1)
