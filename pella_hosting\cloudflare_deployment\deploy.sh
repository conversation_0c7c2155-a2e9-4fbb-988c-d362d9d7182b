#!/bin/bash

# سكريبت النشر السريع لـ Cloudflare Pages
# Quick deployment script for Cloudflare Pages

echo "🚀 بدء عملية النشر على Cloudflare Pages..."
echo "🚀 Starting Cloudflare Pages deployment..."

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً"
    echo "❌ Node.js is not installed. Please install Node.js first"
    exit 1
fi

# التحقق من وجود npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm غير مثبت. يرجى تثبيت npm أولاً"
    echo "❌ npm is not installed. Please install npm first"
    exit 1
fi

# تثبيت التبعيات
echo "📦 تثبيت التبعيات..."
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت التبعيات"
    echo "❌ Failed to install dependencies"
    exit 1
fi

# بناء المشروع
echo "🔨 بناء المشروع..."
echo "🔨 Building project..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ فشل في بناء المشروع"
    echo "❌ Failed to build project"
    exit 1
fi

# التحقق من وجود wrangler
if command -v wrangler &> /dev/null; then
    echo "🌐 نشر على Cloudflare Pages..."
    echo "🌐 Deploying to Cloudflare Pages..."
    wrangler pages publish dist
    
    if [ $? -eq 0 ]; then
        echo "✅ تم النشر بنجاح!"
        echo "✅ Deployment successful!"
    else
        echo "❌ فشل في النشر"
        echo "❌ Deployment failed"
        exit 1
    fi
else
    echo "⚠️ wrangler غير مثبت. يرجى تثبيته أولاً:"
    echo "⚠️ wrangler is not installed. Please install it first:"
    echo "npm install -g wrangler"
    echo ""
    echo "📁 الملفات جاهزة في مجلد dist/"
    echo "📁 Files are ready in dist/ folder"
    echo "يمكنك رفعها يدوياً على Cloudflare Pages"
    echo "You can upload them manually to Cloudflare Pages"
fi

echo ""
echo "🎉 انتهت عملية الإعداد!"
echo "🎉 Setup completed!"
echo ""
echo "📋 الخطوات التالية:"
echo "📋 Next steps:"
echo "1. ارفع الملفات على GitHub"
echo "1. Upload files to GitHub"
echo "2. اربط المستودع مع Cloudflare Pages"
echo "2. Connect repository to Cloudflare Pages"
echo "3. أضف متغيرات البيئة"
echo "3. Add environment variables"
echo "4. انشر المشروع"
echo "4. Deploy the project"
echo ""
echo "📖 راجع DEPLOYMENT_GUIDE.md للتفاصيل"
echo "📖 Check DEPLOYMENT_GUIDE.md for details"
