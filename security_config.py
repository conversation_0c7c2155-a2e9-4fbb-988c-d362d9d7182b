#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الحماية والأمان للبوت
Security and Protection System for Bot
"""

import os
import re
import time
import hashlib
import secrets
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from functools import wraps
import json

# إعداد التسجيل
logger = logging.getLogger(__name__)

class SecurityConfig:
    """إعدادات الأمان الأساسية"""
    
    # حدود معدل الطلبات (Rate Limiting)
    MAX_REQUESTS_PER_MINUTE = 30
    MAX_REQUESTS_PER_HOUR = 200
    MAX_REQUESTS_PER_DAY = 1000
    
    # حدود طول المدخلات
    MAX_MESSAGE_LENGTH = 4096
    MAX_URL_LENGTH = 2048
    MAX_CHANNEL_ID_LENGTH = 50
    MAX_USERNAME_LENGTH = 32
    
    # أنماط المدخلات المحظورة
    BLOCKED_PATTERNS = [
        r'<script.*?>.*?</script>',  # JavaScript
        r'javascript:',              # JavaScript URLs
        r'data:text/html',          # Data URLs
        r'vbscript:',               # VBScript
        r'onload=',                 # Event handlers
        r'onerror=',
        r'onclick=',
        r'eval\(',                  # eval function
        r'exec\(',                  # exec function
        r'system\(',                # system calls
        r'__import__',              # Python imports
        r'subprocess',              # subprocess calls
    ]
    
    # قائمة المجالات المحظورة
    BLOCKED_DOMAINS = [
        'malware.com',
        'phishing.net',
        'spam.org',
        # يمكن إضافة المزيد حسب الحاجة
    ]
    
    # إعدادات التشفير
    ENCRYPTION_KEY_LENGTH = 32
    SALT_LENGTH = 16
    
    # مهلة انتهاء الجلسات
    SESSION_TIMEOUT_HOURS = 24
    
    # عدد محاولات تسجيل الدخول المسموحة
    MAX_LOGIN_ATTEMPTS = 5
    LOGIN_LOCKOUT_MINUTES = 30

class RateLimiter:
    """نظام تحديد معدل الطلبات"""
    
    def __init__(self):
        self.requests: Dict[str, List[float]] = {}
        self.blocked_users: Dict[str, float] = {}
    
    def is_rate_limited(self, user_id: str) -> Tuple[bool, str]:
        """التحقق من تجاوز حدود معدل الطلبات"""
        current_time = time.time()
        user_id_str = str(user_id)
        
        # التحقق من المستخدمين المحظورين مؤقتاً
        if user_id_str in self.blocked_users:
            if current_time < self.blocked_users[user_id_str]:
                remaining = int(self.blocked_users[user_id_str] - current_time)
                return True, f"محظور مؤقتاً لمدة {remaining} ثانية"
            else:
                del self.blocked_users[user_id_str]
        
        # إنشاء قائمة طلبات للمستخدم إذا لم تكن موجودة
        if user_id_str not in self.requests:
            self.requests[user_id_str] = []
        
        # تنظيف الطلبات القديمة
        self.requests[user_id_str] = [
            req_time for req_time in self.requests[user_id_str]
            if current_time - req_time < 3600  # آخر ساعة
        ]
        
        # عد الطلبات في فترات مختلفة
        minute_requests = sum(1 for req_time in self.requests[user_id_str] 
                             if current_time - req_time < 60)
        hour_requests = len(self.requests[user_id_str])
        
        # التحقق من الحدود
        if minute_requests >= SecurityConfig.MAX_REQUESTS_PER_MINUTE:
            self.blocked_users[user_id_str] = current_time + 300  # حظر لـ 5 دقائق
            return True, "تجاوزت حد الطلبات في الدقيقة"
        
        if hour_requests >= SecurityConfig.MAX_REQUESTS_PER_HOUR:
            self.blocked_users[user_id_str] = current_time + 1800  # حظر لـ 30 دقيقة
            return True, "تجاوزت حد الطلبات في الساعة"
        
        # إضافة الطلب الحالي
        self.requests[user_id_str].append(current_time)
        return False, ""

class InputValidator:
    """نظام التحقق من صحة المدخلات"""
    
    @staticmethod
    def sanitize_text(text: str) -> str:
        """تنظيف النص من المحتوى الضار"""
        if not isinstance(text, str):
            return ""
        
        # إزالة الأنماط المحظورة
        for pattern in SecurityConfig.BLOCKED_PATTERNS:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        # تنظيف HTML tags خطيرة
        dangerous_tags = ['script', 'iframe', 'object', 'embed', 'form']
        for tag in dangerous_tags:
            text = re.sub(f'<{tag}.*?>', '', text, flags=re.IGNORECASE)
            text = re.sub(f'</{tag}>', '', text, flags=re.IGNORECASE)
        
        return text.strip()
    
    @staticmethod
    def validate_url(url: str) -> Tuple[bool, str]:
        """التحقق من صحة وأمان الرابط"""
        if not url or not isinstance(url, str):
            return False, "رابط غير صالح"
        
        if len(url) > SecurityConfig.MAX_URL_LENGTH:
            return False, "الرابط طويل جداً"
        
        # التحقق من البروتوكول
        if not url.startswith(('http://', 'https://')):
            return False, "البروتوكول غير مدعوم"
        
        # التحقق من المجالات المحظورة
        for domain in SecurityConfig.BLOCKED_DOMAINS:
            if domain in url.lower():
                return False, "مجال محظور"
        
        # التحقق من الأنماط المشبوهة
        suspicious_patterns = [
            r'\.exe$', r'\.bat$', r'\.cmd$', r'\.scr$',
            r'javascript:', r'data:', r'vbscript:'
        ]
        
        for pattern in suspicious_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return False, "رابط مشبوه"
        
        return True, ""
    
    @staticmethod
    def validate_channel_id(channel_id: str) -> Tuple[bool, str]:
        """التحقق من صحة معرف القناة"""
        if not channel_id or not isinstance(channel_id, str):
            return False, "معرف قناة غير صالح"
        
        if len(channel_id) > SecurityConfig.MAX_CHANNEL_ID_LENGTH:
            return False, "معرف القناة طويل جداً"
        
        # أنماط معرفات القنوات الصالحة
        valid_patterns = [
            r'^@[a-zA-Z][a-zA-Z0-9_]{4,31}$',  # @username
            r'^-100\d{10,13}$',                # -100xxxxxxxxxx
            r'^-\d{9,12}$'                     # -xxxxxxxxx
        ]
        
        for pattern in valid_patterns:
            if re.match(pattern, channel_id):
                return True, ""
        
        return False, "تنسيق معرف القناة غير صحيح"
    
    @staticmethod
    def validate_user_input(text: str, max_length: int = None) -> Tuple[bool, str]:
        """التحقق العام من مدخلات المستخدم"""
        if not isinstance(text, str):
            return False, "نوع البيانات غير صحيح"
        
        max_len = max_length or SecurityConfig.MAX_MESSAGE_LENGTH
        if len(text) > max_len:
            return False, f"النص طويل جداً (الحد الأقصى: {max_len})"
        
        # التحقق من الأنماط المحظورة
        for pattern in SecurityConfig.BLOCKED_PATTERNS:
            if re.search(pattern, text, re.IGNORECASE):
                return False, "محتوى محظور"
        
        return True, ""

class SecurityLogger:
    """نظام تسجيل الأحداث الأمنية"""
    
    def __init__(self, log_file: str = "security.log"):
        self.log_file = log_file
        self.setup_logger()
    
    def setup_logger(self):
        """إعداد نظام التسجيل الأمني"""
        self.security_logger = logging.getLogger('security')
        self.security_logger.setLevel(logging.INFO)
        
        # إنشاء معالج الملف
        handler = logging.FileHandler(self.log_file, encoding='utf-8')
        formatter = logging.Formatter(
            '%(asctime)s - SECURITY - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.security_logger.addHandler(handler)
    
    def log_security_event(self, event_type: str, user_id: str, details: str, severity: str = "INFO"):
        """تسجيل حدث أمني"""
        message = f"Event: {event_type} | User: {user_id} | Details: {details}"
        
        if severity == "CRITICAL":
            self.security_logger.critical(message)
        elif severity == "ERROR":
            self.security_logger.error(message)
        elif severity == "WARNING":
            self.security_logger.warning(message)
        else:
            self.security_logger.info(message)
    
    def log_failed_attempt(self, user_id: str, attempt_type: str, details: str = ""):
        """تسجيل محاولة فاشلة"""
        self.log_security_event(
            "FAILED_ATTEMPT", 
            user_id, 
            f"Type: {attempt_type} | {details}", 
            "WARNING"
        )
    
    def log_suspicious_activity(self, user_id: str, activity: str, details: str = ""):
        """تسجيل نشاط مشبوه"""
        self.log_security_event(
            "SUSPICIOUS_ACTIVITY", 
            user_id, 
            f"Activity: {activity} | {details}", 
            "ERROR"
        )

class EncryptionManager:
    """نظام التشفير للبيانات الحساسة"""
    
    def __init__(self):
        self.key = self._get_or_create_key()
    
    def _get_or_create_key(self) -> bytes:
        """الحصول على مفتاح التشفير أو إنشاء واحد جديد"""
        key_file = "encryption.key"
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            # إنشاء مفتاح جديد
            key = secrets.token_bytes(SecurityConfig.ENCRYPTION_KEY_LENGTH)
            with open(key_file, 'wb') as f:
                f.write(key)
            os.chmod(key_file, 0o600)  # قراءة للمالك فقط
            return key
    
    def hash_password(self, password: str) -> Tuple[str, str]:
        """تشفير كلمة المرور مع salt"""
        salt = secrets.token_bytes(SecurityConfig.SALT_LENGTH)
        hashed = hashlib.pbkdf2_hmac('sha256', password.encode(), salt, 100000)
        return hashed.hex(), salt.hex()
    
    def verify_password(self, password: str, hashed: str, salt: str) -> bool:
        """التحقق من كلمة المرور"""
        try:
            salt_bytes = bytes.fromhex(salt)
            expected_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt_bytes, 100000)
            return expected_hash.hex() == hashed
        except Exception:
            return False

# إنشاء مثيلات عامة
rate_limiter = RateLimiter()
security_logger = SecurityLogger()
encryption_manager = EncryptionManager()

def security_check(func):
    """ديكوريتر للفحص الأمني"""
    @wraps(func)
    async def wrapper(update, context, *args, **kwargs):
        user = update.effective_user
        user_id = str(user.id)
        
        # فحص معدل الطلبات
        is_limited, reason = rate_limiter.is_rate_limited(user_id)
        if is_limited:
            security_logger.log_failed_attempt(user_id, "RATE_LIMIT", reason)
            if update.message:
                await update.message.reply_text(f"⚠️ {reason}")
            elif update.callback_query:
                await update.callback_query.answer(f"⚠️ {reason}", show_alert=True)
            return
        
        # تسجيل النشاط
        security_logger.log_security_event(
            "FUNCTION_CALL", 
            user_id, 
            f"Function: {func.__name__}"
        )
        
        return await func(update, context, *args, **kwargs)
    
    return wrapper

def admin_only(func):
    """ديكوريتر للتحقق من صلاحيات المسؤول"""
    @wraps(func)
    async def wrapper(update, context, *args, **kwargs):
        user = update.effective_user
        user_id = str(user.id)
        admin_id = os.environ.get("ADMIN_CHAT_ID", "")
        
        if user_id != admin_id:
            security_logger.log_failed_attempt(
                user_id, 
                "UNAUTHORIZED_ADMIN_ACCESS", 
                f"Function: {func.__name__}"
            )
            
            if update.message:
                await update.message.reply_text("❌ ليس لديك صلاحية الوصول!")
            elif update.callback_query:
                await update.callback_query.answer("❌ ليس لديك صلاحية الوصول!", show_alert=True)
            return
        
        return await func(update, context, *args, **kwargs)
    
    return wrapper
