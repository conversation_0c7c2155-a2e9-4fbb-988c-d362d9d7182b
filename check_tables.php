<?php
/**
 * فحص الجداول المتاحة في Supabase
 * Check Available Tables in Supabase
 */

$SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
$SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص الجداول المتاحة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 14px; overflow-x: auto; white-space: pre-wrap; }
        .table-info { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        h1, h2, h3 { color: #333; }
        .sql-code { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 فحص الجداول المتاحة في Supabase</h1>
        
        <?php
        function makeRequest($url, $headers) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            return [
                'response' => $response,
                'http_code' => $http_code,
                'error' => $error
            ];
        }
        
        $headers = [
            'apikey: ' . $SUPABASE_KEY,
            'Authorization: Bearer ' . $SUPABASE_KEY,
            'Content-Type: application/json'
        ];
        
        echo '<div class="table-info">';
        echo '<h2>1. محاولة الوصول للجداول المختلفة</h2>';
        
        $possible_tables = [
            'minemods',
            'mods', 
            'minecraft_mods',
            'mod_data',
            'addons',
            'minecraft_addons',
            'bot_mods',
            'telegram_mods'
        ];
        
        $found_tables = [];
        
        foreach ($possible_tables as $table) {
            echo '<h3>جدول: ' . $table . '</h3>';
            
            $url = $SUPABASE_URL . '/rest/v1/' . $table . '?limit=1';
            $result = makeRequest($url, $headers);
            
            echo '<p><strong>URL:</strong> ' . htmlspecialchars($url) . '</p>';
            echo '<p><strong>HTTP Code:</strong> ' . $result['http_code'] . '</p>';
            
            if ($result['error']) {
                echo '<p class="error">❌ cURL Error: ' . htmlspecialchars($result['error']) . '</p>';
            } elseif ($result['http_code'] === 200) {
                echo '<p class="success">✅ الجدول موجود!</p>';
                $found_tables[] = $table;
                
                $data = json_decode($result['response'], true);
                if ($data && is_array($data)) {
                    echo '<p class="info">📊 عدد السجلات المسترجعة: ' . count($data) . '</p>';
                    
                    if (!empty($data)) {
                        echo '<p><strong>عينة من البيانات:</strong></p>';
                        echo '<div class="code">' . htmlspecialchars(json_encode($data[0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . '</div>';
                    } else {
                        echo '<p class="warning">⚠️ الجدول فارغ</p>';
                    }
                }
            } elseif ($result['http_code'] === 404) {
                echo '<p class="error">❌ الجدول غير موجود</p>';
            } else {
                echo '<p class="error">❌ خطأ: HTTP ' . $result['http_code'] . '</p>';
                if ($result['response']) {
                    echo '<div class="code">' . htmlspecialchars(substr($result['response'], 0, 200)) . '</div>';
                }
            }
            echo '<hr>';
        }
        
        echo '</div>';
        
        echo '<div class="table-info">';
        echo '<h2>2. الجداول الموجودة</h2>';
        
        if (!empty($found_tables)) {
            echo '<p class="success">✅ تم العثور على الجداول التالية:</p>';
            echo '<ul>';
            foreach ($found_tables as $table) {
                echo '<li><strong>' . $table . '</strong></li>';
            }
            echo '</ul>';
        } else {
            echo '<p class="error">❌ لم يتم العثور على أي جداول مودات</p>';
            echo '<p><strong>تحتاج لإنشاء جدول جديد!</strong></p>';
        }
        
        echo '</div>';
        
        echo '<div class="table-info">';
        echo '<h2>3. كيفية إنشاء جدول minemods</h2>';
        
        echo '<p>إذا لم يتم العثور على أي جداول، يمكنك إنشاء جدول جديد في Supabase:</p>';
        
        echo '<h3>أ. في Supabase Dashboard:</h3>';
        echo '<ol>';
        echo '<li>اذهب إلى <strong>Table Editor</strong></li>';
        echo '<li>اضغط على <strong>Create a new table</strong></li>';
        echo '<li>اسم الجدول: <code>minemods</code></li>';
        echo '<li>أضف الأعمدة التالية:</li>';
        echo '</ol>';
        
        echo '<div class="sql-code">';
        echo 'CREATE TABLE minemods (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    description_ar TEXT,
    version VARCHAR(50),
    category VARCHAR(100),
    download_url TEXT,
    image_urls JSONB,
    telegram_description_ar TEXT,
    telegram_description_en TEXT,
    file_size DECIMAL(10,2),
    downloads_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);';
        echo '</div>';
        
        echo '<h3>ب. إضافة بيانات تجريبية:</h3>';
        echo '<div class="sql-code">';
        echo "INSERT INTO minemods (name, description, description_ar, version, category, download_url, image_urls) VALUES
(
    'Test Mod 1',
    'This is a test mod for Minecraft',
    'هذا مود تجريبي لماين كرافت',
    '1.0.0',
    'addons',
    'https://example.com/download/mod1.mcpack',
    '[\"https://via.placeholder.com/800x450/2D2D2D/FFFFFF?text=Test+Mod+1\"]'
),
(
    'Test Mod 2', 
    'Another test mod for Minecraft',
    'مود تجريبي آخر لماين كرافت',
    '1.1.0',
    'texture_packs',
    'https://example.com/download/mod2.mcpack',
    '[\"https://via.placeholder.com/800x450/4A5568/FFFFFF?text=Test+Mod+2\"]'
);";
        echo '</div>';
        
        echo '<h3>ج. تفعيل Row Level Security (اختياري):</h3>';
        echo '<div class="sql-code">';
        echo 'ALTER TABLE minemods ENABLE ROW LEVEL SECURITY;

-- السماح بالقراءة للجميع
CREATE POLICY "Allow public read access" ON minemods
    FOR SELECT USING (true);

-- السماح بالكتابة للمصادقين فقط (اختياري)
CREATE POLICY "Allow authenticated insert" ON minemods
    FOR INSERT WITH CHECK (auth.role() = \'authenticated\');';
        echo '</div>';
        
        echo '</div>';
        
        // إذا تم العثور على جداول، اقترح تحديث الكود
        if (!empty($found_tables)) {
            echo '<div class="table-info" style="background: #e7f3ff;">';
            echo '<h2>4. تحديث الكود لاستخدام الجدول الموجود</h2>';
            
            $suggested_table = $found_tables[0];
            echo '<p>يمكنك تحديث الكود لاستخدام الجدول الموجود: <strong>' . $suggested_table . '</strong></p>';
            
            echo '<p>في الملفات الجديدة، غيّر:</p>';
            echo '<div class="code">// من
$endpoint = "/rest/v1/minemods?id=eq.$mod_id";

// إلى  
$endpoint = "/rest/v1/' . $suggested_table . '?id=eq.$mod_id";</div>';
            
            echo '<p><strong>أو</strong> يمكنني إنشاء ملفات محدثة لك تستخدم الجدول الموجود.</p>';
            echo '</div>';
        }
        ?>
        
        <div style="text-align: center; margin-top: 20px;">
            <button onclick="location.reload()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">
                🔄 إعادة الفحص
            </button>
            <a href="simple_test.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;">
                🔙 العودة للاختبار السريع
            </a>
        </div>
    </div>
</body>
</html>
