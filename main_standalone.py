#!/usr/bin/env python3
"""
البوت الرئيسي للاستضافة
Main bot for hosting
"""

import os
import sys
from pathlib import Path

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, str(Path(__file__).parent))

# تعيين متغيرات البيئة للاستضافة
os.environ.setdefault("ENVIRONMENT", "production")
os.environ.setdefault("DEBUG", "false")

# تحديد رابط الخادم تلقائياً
if not os.environ.get("WEB_SERVER_URL"):
    # للاستضافة على Heroku/Railway/Render
    app_name = os.environ.get("RAILWAY_PROJECT_NAME") or os.environ.get("RENDER_SERVICE_NAME") or "your-app"
    if "railway" in os.environ.get("RAILWAY_ENVIRONMENT_NAME", ""):
        os.environ["WEB_SERVER_URL"] = f"https://{app_name}.up.railway.app"
    elif "render" in os.environ.get("RENDER", ""):
        os.environ["WEB_SERVER_URL"] = f"https://{app_name}.onrender.com"
    else:
        # Heroku أو خدمة أخرى
        os.environ["WEB_SERVER_URL"] = f"https://{app_name}.herokuapp.com"

try:
    # استيراد وتشغيل البوت الرئيسي
    import main
    print("🚀 Bot started successfully on hosting platform")
except Exception as e:
    print(f"❌ Error starting bot: {e}")
    sys.exit(1)
