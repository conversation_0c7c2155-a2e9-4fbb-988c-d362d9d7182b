# إعدادات الشبكة المحسنة لحل مشاكل الاتصال
# Enhanced Network Configuration for Connection Issues

import os
import logging
import asyncio
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

# إعدادات الشبكة الأساسية
NETWORK_CONFIG = {
    # إعدادات timeout محسنة
    'timeouts': {
        'connect_timeout': 60,
        'read_timeout': 60,
        'write_timeout': 60,
        'pool_timeout': 60,
    },
    
    # إعدادات إعادة المحاولة
    'retry': {
        'max_retries': 5,
        'retry_delay': 3,
        'backoff_factor': 1.5,
        'max_delay': 30,
    },
    
    # إعدادات مجموعة الاتصالات
    'connection_pool': {
        'pool_size': 10,
        'max_overflow': 20,
        'pool_timeout': 30,
        'pool_recycle': 3600,
    },
    
    # إعدادات DNS
    'dns': {
        'timeout': 10,
        'servers': ['8.8.8.8', '1.1.1.1', '208.67.222.222'],
    },
    
    # إعدادات البروكسي
    'proxy': {
        'enabled': False,
        'http_proxy': os.environ.get('HTTP_PROXY', ''),
        'https_proxy': os.environ.get('HTTPS_PROXY', ''),
        'no_proxy': os.environ.get('NO_PROXY', ''),
    }
}

def get_network_config() -> Dict[str, Any]:
    """الحصول على إعدادات الشبكة"""
    return NETWORK_CONFIG.copy()

def get_telegram_request_config() -> Dict[str, Any]:
    """الحصول على إعدادات طلبات Telegram محسنة"""
    config = get_network_config()
    
    return {
        'connection_pool_size': config['connection_pool']['pool_size'],
        'proxy_url': get_proxy_url(),
        'read_timeout': config['timeouts']['read_timeout'],
        'write_timeout': config['timeouts']['write_timeout'],
        'connect_timeout': config['timeouts']['connect_timeout'],
        'pool_timeout': config['timeouts']['pool_timeout'],
    }

def get_proxy_url() -> Optional[str]:
    """الحصول على رابط البروكسي إذا كان متوفراً"""
    proxy_config = NETWORK_CONFIG['proxy']
    
    if not proxy_config['enabled']:
        return None
    
    # أولوية للـ HTTPS proxy
    if proxy_config['https_proxy']:
        return proxy_config['https_proxy']
    
    # ثم HTTP proxy
    if proxy_config['http_proxy']:
        return proxy_config['http_proxy']
    
    return None

def check_network_connectivity() -> bool:
    """فحص الاتصال بالشبكة"""
    import socket
    
    dns_servers = NETWORK_CONFIG['dns']['servers']
    timeout = NETWORK_CONFIG['dns']['timeout']
    
    for server in dns_servers:
        try:
            socket.create_connection((server, 53), timeout=timeout)
            logger.info(f"✅ نجح الاتصال مع DNS server: {server}")
            return True
        except OSError as e:
            logger.warning(f"⚠️ فشل الاتصال مع DNS server {server}: {e}")
            continue
    
    logger.error("❌ فشل في الاتصال مع جميع DNS servers")
    return False

def check_telegram_connectivity() -> bool:
    """فحص الاتصال مع خوادم Telegram"""
    import socket
    
    telegram_servers = [
        ('api.telegram.org', 443),
        ('api.telegram.org', 80),
    ]
    
    timeout = NETWORK_CONFIG['timeouts']['connect_timeout']
    
    for server, port in telegram_servers:
        try:
            socket.create_connection((server, port), timeout=timeout)
            logger.info(f"✅ نجح الاتصال مع Telegram server: {server}:{port}")
            return True
        except OSError as e:
            logger.warning(f"⚠️ فشل الاتصال مع Telegram server {server}:{port}: {e}")
            continue
    
    logger.error("❌ فشل في الاتصال مع خوادم Telegram")
    return False

async def test_telegram_api(token: str) -> bool:
    """اختبار الاتصال مع Telegram API"""
    try:
        import aiohttp
        
        config = get_network_config()
        timeout = aiohttp.ClientTimeout(
            total=config['timeouts']['read_timeout'],
            connect=config['timeouts']['connect_timeout']
        )
        
        proxy_url = get_proxy_url()
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            url = f"https://api.telegram.org/bot{token}/getMe"
            
            async with session.get(url, proxy=proxy_url) as response:
                if response.status == 200:
                    logger.info("✅ نجح اختبار Telegram API")
                    return True
                else:
                    logger.error(f"❌ فشل اختبار Telegram API: {response.status}")
                    return False
                    
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار Telegram API: {e}")
        return False

def apply_network_optimizations():
    """تطبيق تحسينات الشبكة على مستوى النظام"""
    try:
        import socket
        import platform

        # تحسينات خاصة بـ Windows
        if platform.system() == "Windows":
            # تعطيل Nagle's algorithm لتحسين الاستجابة
            socket.TCP_NODELAY = 1

            # تحسين buffer sizes
            socket.SO_RCVBUF = 65536
            socket.SO_SNDBUF = 65536

            logger.info("✅ تم تطبيق تحسينات Windows")

        # تحسينات عامة
        # تفعيل TCP keepalive
        socket.SO_KEEPALIVE = 1

        logger.info("✅ تم تطبيق تحسينات الشبكة")

    except Exception as e:
        logger.warning(f"⚠️ فشل في تطبيق بعض تحسينات الشبكة: {e}")

def get_connection_retry_config() -> Dict[str, Any]:
    """الحصول على إعدادات إعادة المحاولة"""
    return NETWORK_CONFIG['retry'].copy()

async def retry_with_backoff(func, *args, **kwargs):
    """تنفيذ دالة مع إعادة المحاولة والتأخير التدريجي"""
    retry_config = get_connection_retry_config()
    
    max_retries = retry_config['max_retries']
    base_delay = retry_config['retry_delay']
    backoff_factor = retry_config['backoff_factor']
    max_delay = retry_config['max_delay']
    
    for attempt in range(max_retries):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            
            delay = min(base_delay * (backoff_factor ** attempt), max_delay)
            logger.warning(f"⚠️ المحاولة {attempt + 1} فشلت: {e}. إعادة المحاولة خلال {delay} ثانية...")
            await asyncio.sleep(delay)

def log_network_info():
    """عرض معلومات الشبكة"""
    logger.info("🌐 إعدادات الشبكة:")
    logger.info(f"   Connect Timeout: {NETWORK_CONFIG['timeouts']['connect_timeout']}s")
    logger.info(f"   Read Timeout: {NETWORK_CONFIG['timeouts']['read_timeout']}s")
    logger.info(f"   Max Retries: {NETWORK_CONFIG['retry']['max_retries']}")
    logger.info(f"   Connection Pool Size: {NETWORK_CONFIG['connection_pool']['pool_size']}")
    
    proxy_url = get_proxy_url()
    if proxy_url:
        logger.info(f"   Proxy: {proxy_url}")
    else:
        logger.info("   Proxy: غير مفعل")

def handle_windows_socket_error(error):
    """معالجة أخطاء Windows Socket المحددة"""
    import platform

    if platform.system() != "Windows":
        return False

    error_code = getattr(error, 'winerror', None)

    # WinError 10057: A request to send or receive data was disallowed because the socket is not connected
    if error_code == 10057:
        logger.warning("🔧 Windows Socket Error 10057: إعادة تهيئة الاتصال...")
        return True

    # WinError 10054: An existing connection was forcibly closed by the remote host
    elif error_code == 10054:
        logger.warning("🔧 Windows Socket Error 10054: الاتصال مقطوع من الخادم البعيد")
        return True

    # WinError 10060: A connection attempt failed because the connected party did not properly respond
    elif error_code == 10060:
        logger.warning("🔧 Windows Socket Error 10060: انتهت مهلة الاتصال")
        return True

    return False

def create_resilient_socket():
    """إنشاء socket مقاوم للأخطاء"""
    import socket
    import platform

    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

        # تحسينات Windows
        if platform.system() == "Windows":
            # تفعيل TCP keepalive
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)

            # تحسين buffer sizes
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)

            # تعطيل Nagle's algorithm
            sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)

        return sock

    except Exception as e:
        logger.error(f"❌ فشل في إنشاء socket محسن: {e}")
        return socket.socket(socket.AF_INET, socket.SOCK_STREAM)

# تطبيق التحسينات عند استيراد الملف
apply_network_optimizations()
