#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط للتأكد من أن التعديلات تعمل
"""

print("🔍 اختبار استيراد الدوال...")

try:
    from main import (
        show_feature_locked_message,
        check_feature_access,
        url_shortener_menu,
        tasks_system_menu
    )
    print("✅ تم استيراد جميع الدوال بنجاح")
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    exit(1)

print("\n🔍 فحص دالة url_shortener_menu...")

import inspect
source = inspect.getsource(url_shortener_menu)

# التحقق من أن الدالة تستخدم await مع check_feature_access
if "await check_feature_access" in source:
    print("✅ دالة url_shortener_menu تستخدم await مع check_feature_access")
else:
    print("❌ دالة url_shortener_menu لا تستخدم await مع check_feature_access")

# التحقق من أن الدالة تستخدم show_feature_locked_message
if "show_feature_locked_message" in source:
    print("✅ دالة url_shortener_menu تستخدم show_feature_locked_message")
else:
    print("❌ دالة url_shortener_menu لا تستخدم show_feature_locked_message")

print("\n🔍 فحص دالة show_feature_locked_message...")

source = inspect.getsource(show_feature_locked_message)

# التحقق من وجود معلومات url_shortener
if "'url_shortener'" in source and "'requirement_count': 3" in source:
    print("✅ معلومات url_shortener موجودة ومتطلبات الدعوات صحيحة (3 دعوات)")
else:
    print("❌ معلومات url_shortener غير صحيحة")

# التحقق من وجود زر الدعوة
if "invitations_menu" in source:
    print("✅ زر الدعوة موجود في الرسالة")
else:
    print("❌ زر الدعوة غير موجود")

print("\n🎉 انتهى الاختبار!")
