@echo off
chcp 65001 >nul
title أداة إعداد صفحة عرض المودات - Hosting Setup Tool

echo.
echo ===============================================================
echo 🚀 أداة إعداد صفحة عرض مودات ماين كرافت
echo 🌐 Minecraft Mods Display Page Setup Tool
echo ===============================================================
echo.
echo 🎯 هذه الأداة ستساعدك في:
echo    • إنشاء جميع الملفات المطلوبة لصفحة عرض المودات
echo    • تحضير ملف مضغوط جاهز للرفع على الاستضافة
echo    • إنشاء تعليمات مفصلة للتثبيت
echo    • تحديث البوت ليستخدم الموقع الجديد
echo.
echo 🌟 المميزات:
echo    ✅ يعمل على جميع الاستضافات المجانية
echo    ✅ لا يحتاج خادم محلي أو ngrok
echo    ✅ دعم كامل للغتين العربية والإنجليزية
echo    ✅ نظام إعلانات ومهام متقدم
echo    ✅ تصميم متجاوب لجميع الأجهزة
echo.
echo ===============================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo 📥 يرجى تحميل وتثبيت Python من: https://python.org
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود ملف الأداة
if not exist "hosting_setup_tool.py" (
    echo ❌ ملف hosting_setup_tool.py غير موجود
    echo 📁 تأكد من وجود الملف في نفس المجلد
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo ✅ ملف الأداة موجود
echo.
echo 🚀 بدء تشغيل الأداة...
echo.

REM تشغيل الأداة
python hosting_setup_tool.py

echo.
echo ===============================================================
echo 🎉 انتهت الأداة من العمل
echo 💡 نصائح:
echo    • ارفع الملف المضغوط على استضافتك
echo    • فك الضغط في مجلد htdocs
echo    • اختبر الموقع باستخدام deploy.php?setup=true
echo    • شغّل سكريبت تحديث البوت
echo ===============================================================
echo.
pause
