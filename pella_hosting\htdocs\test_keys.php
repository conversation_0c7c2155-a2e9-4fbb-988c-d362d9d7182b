<?php
/**
 * اختبار سريع للمفاتيح
 * Quick Keys Test
 */

define('INCLUDED', true);
require_once 'config.php';

echo "<h1>🔑 اختبار مفاتيح Supabase</h1>";
echo "<hr>";

// جلب الإعدادات
$db_config = getConfig('database')['supabase'];
$SUPABASE_URL = $db_config['url'];
$SUPABASE_KEY = $db_config['key'];
$SUPABASE_SERVICE_KEY = $db_config['service_key'];
$TABLE_NAME = getConfig('tables')['mods'];

echo "<h2>📊 معلومات الإعدادات</h2>";
echo "<p><strong>URL:</strong> $SUPABASE_URL</p>";
echo "<p><strong>Table:</strong> $TABLE_NAME</p>";
echo "<p><strong>Anon Key:</strong> " . substr($SUPABASE_KEY, 0, 20) . "...</p>";
echo "<p><strong>Service Key:</strong> " . substr($SUPABASE_SERVICE_KEY, 0, 20) . "...</p>";

echo "<hr>";

// اختبار 1: مع Anon Key
echo "<h2>1️⃣ اختبار مع Anon Key</h2>";
$url = $SUPABASE_URL . "/rest/v1/$TABLE_NAME?limit=1";
$headers_anon = array(
    'apikey: ' . $SUPABASE_KEY,
    'Authorization: Bearer ' . $SUPABASE_KEY,
    'Content-Type: application/json'
);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers_anon);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code == 200) {
    $data = json_decode($response, true);
    echo "✅ <strong>نجح مع Anon Key</strong><br>";
    echo "📊 HTTP Code: $http_code<br>";
    echo "📈 عدد السجلات: " . count($data) . "<br>";
} else {
    echo "❌ <strong>فشل مع Anon Key</strong><br>";
    echo "📊 HTTP Code: $http_code<br>";
    echo "📄 Response: " . htmlspecialchars($response) . "<br>";
}

echo "<hr>";

// اختبار 2: مع Service Key
echo "<h2>2️⃣ اختبار مع Service Key</h2>";
$headers_service = array(
    'apikey: ' . $SUPABASE_SERVICE_KEY,
    'Authorization: Bearer ' . $SUPABASE_SERVICE_KEY,
    'Content-Type: application/json'
);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers_service);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code == 200) {
    $data = json_decode($response, true);
    echo "✅ <strong>نجح مع Service Key</strong><br>";
    echo "📊 HTTP Code: $http_code<br>";
    echo "📈 عدد السجلات: " . count($data) . "<br>";
    
    if (!empty($data)) {
        echo "<h3>📋 عينة من البيانات:</h3>";
        $mod = $data[0];
        $name = $mod['name'] ?? 'غير محدد';
        $id = substr($mod['id'] ?? 'غير محدد', 0, 8) . '...';
        $category = $mod['category'] ?? 'غير محدد';
        echo "<p>🎮 <strong>$name</strong> (ID: $id, Category: $category)</p>";
    }
} else {
    echo "❌ <strong>فشل مع Service Key</strong><br>";
    echo "📊 HTTP Code: $http_code<br>";
    echo "📄 Response: " . htmlspecialchars($response) . "<br>";
}

echo "<hr>";

// اختبار 3: فحص صحة المفاتيح
echo "<h2>3️⃣ فحص صحة المفاتيح</h2>";

// فحص Anon Key
$anon_payload = json_decode(base64_decode(str_replace('_', '/', str_replace('-', '+', explode('.', $SUPABASE_KEY)[1]))), true);
if ($anon_payload) {
    echo "<p><strong>Anon Key:</strong></p>";
    echo "<ul>";
    echo "<li>Role: " . ($anon_payload['role'] ?? 'غير محدد') . "</li>";
    echo "<li>Issued At: " . date('Y-m-d H:i:s', $anon_payload['iat'] ?? 0) . "</li>";
    echo "<li>Expires At: " . date('Y-m-d H:i:s', $anon_payload['exp'] ?? 0) . "</li>";
    echo "</ul>";
}

// فحص Service Key
$service_payload = json_decode(base64_decode(str_replace('_', '/', str_replace('-', '+', explode('.', $SUPABASE_SERVICE_KEY)[1]))), true);
if ($service_payload) {
    echo "<p><strong>Service Key:</strong></p>";
    echo "<ul>";
    echo "<li>Role: " . ($service_payload['role'] ?? 'غير محدد') . "</li>";
    echo "<li>Issued At: " . date('Y-m-d H:i:s', $service_payload['iat'] ?? 0) . "</li>";
    echo "<li>Expires At: " . date('Y-m-d H:i:s', $service_payload['exp'] ?? 0) . "</li>";
    echo "</ul>";
}

echo "<hr>";

// اختبار 4: API الداخلي
echo "<h2>4️⃣ اختبار API الداخلي</h2>";

// بناء الرابط الصحيح
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$path = dirname($_SERVER['REQUEST_URI']);
$api_url = $protocol . '://' . $host . $path . '/api.php?path=' . urlencode('/test');

echo "<p><strong>🔗 API URL:</strong> $api_url</p>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // متابعة الـ redirects
curl_setopt($ch, CURLOPT_MAXREDIRS, 3); // حد أقصى 3 redirects

$api_response = curl_exec($ch);
$api_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$final_url = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
curl_close($ch);

echo "<p><strong>🔗 Final URL:</strong> $final_url</p>";

if ($api_http_code == 200) {
    $api_data = json_decode($api_response, true);
    if ($api_data && isset($api_data['status'])) {
        echo "✅ <strong>نجح API الداخلي</strong><br>";
        echo "📊 Status: " . $api_data['status'] . "<br>";
        echo "📄 Message: " . $api_data['message'] . "<br>";
    } else {
        echo "⚠️ <strong>API يعمل ولكن الاستجابة غير متوقعة</strong><br>";
        echo "📄 Response: " . htmlspecialchars($api_response) . "<br>";
    }
} else {
    echo "❌ <strong>فشل API الداخلي</strong><br>";
    echo "📊 HTTP Code: $api_http_code<br>";
    echo "📄 Response: " . htmlspecialchars($api_response) . "<br>";
}

echo "<hr>";
echo "<p><strong>🎯 انتهى اختبار المفاتيح</strong></p>";
?>
