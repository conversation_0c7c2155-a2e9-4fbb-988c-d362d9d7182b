<?php
/**
 * اختبار نهائي بالبيانات الصحيحة 100%
 * Final Test with 100% Correct Data
 */

// البيانات الصحيحة النهائية
$SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
$SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";
$SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTI2MTEwNSwiZXhwIjoyMDYwODM3MTA1fQ._BQpMA9YZpXCjvpoNRK2QdoecsE5VQsr3AN2DJhj2rw";
$TABLE_NAME = "minemods";

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 الاختبار النهائي</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .container { max-width: 1000px; margin: 0 auto; background: rgba(255,255,255,0.95); color: #333; padding: 20px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .test-section { border: 2px solid #e9ecef; margin: 15px 0; padding: 20px; border-radius: 10px; background: #f8f9fa; }
        .code { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; font-family: 'Courier New', monospace; font-size: 13px; overflow-x: auto; }
        h1, h2, h3 { color: #2c3e50; }
        .highlight { background: linear-gradient(45deg, #ffeaa7, #fab1a0); padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 5px solid #e17055; }
        .final-result { padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center; font-size: 18px; }
        .final-success { background: linear-gradient(45deg, #00b894, #00cec9); color: white; }
        .final-error { background: linear-gradient(45deg, #e17055, #d63031); color: white; }
        .btn { display: inline-block; padding: 12px 24px; margin: 8px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s; }
        .btn-primary { background: linear-gradient(45deg, #0984e3, #74b9ff); color: white; }
        .btn-success { background: linear-gradient(45deg, #00b894, #00cec9); color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; font-size: 2.5em;">🎯 الاختبار النهائي الشامل</h1>
        
        <div class="highlight">
            <h2>📋 البيانات المستخدمة (المحدثة):</h2>
            <p><strong>🌐 Database URL:</strong> <?php echo htmlspecialchars($SUPABASE_URL); ?></p>
            <p><strong>📊 Table Name:</strong> <?php echo htmlspecialchars($TABLE_NAME); ?></p>
            <p><strong>🔑 Anon Key:</strong> <?php echo htmlspecialchars(substr($SUPABASE_KEY, 0, 50)) . '...'; ?></p>
            <p><strong>🛡️ Service Key:</strong> <?php echo htmlspecialchars(substr($SERVICE_KEY, 0, 50)) . '...'; ?></p>
        </div>
        
        <?php
        function makeRequest($url, $headers) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 20);
            curl_setopt($ch, CURLOPT_USERAGENT, 'ModetarisBot/1.0');
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            return [
                'response' => $response,
                'http_code' => $http_code,
                'error' => $error
            ];
        }
        
        // اختبار مع مفتاح anon
        $anon_headers = [
            'apikey: ' . $SUPABASE_KEY,
            'Authorization: Bearer ' . $SUPABASE_KEY,
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        // اختبار مع مفتاح service
        $service_headers = [
            'apikey: ' . $SERVICE_KEY,
            'Authorization: Bearer ' . $SERVICE_KEY,
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        echo '<div class="test-section">';
        echo '<h2>🔌 1. اختبار الاتصال الأساسي</h2>';
        
        $basic_url = $SUPABASE_URL . '/rest/v1/';
        $result = makeRequest($basic_url, $anon_headers);
        
        echo '<p><strong>🌐 URL:</strong> ' . htmlspecialchars($basic_url) . '</p>';
        echo '<p><strong>📊 HTTP Code:</strong> ' . $result['http_code'] . '</p>';
        
        if ($result['error']) {
            echo '<p class="error">❌ cURL Error: ' . htmlspecialchars($result['error']) . '</p>';
        } elseif ($result['http_code'] === 200) {
            echo '<p class="success">✅ الاتصال الأساسي ناجح!</p>';
        } else {
            echo '<p class="error">❌ فشل الاتصال - HTTP: ' . $result['http_code'] . '</p>';
        }
        echo '</div>';
        
        echo '<div class="test-section">';
        echo '<h2>🗄️ 2. اختبار جدول المودات (مع مفتاح anon)</h2>';
        
        $table_url = $SUPABASE_URL . '/rest/v1/' . $TABLE_NAME . '?limit=5';
        $result = makeRequest($table_url, $anon_headers);
        
        echo '<p><strong>🌐 URL:</strong> ' . htmlspecialchars($table_url) . '</p>';
        echo '<p><strong>📊 HTTP Code:</strong> ' . $result['http_code'] . '</p>';
        
        $mods_data = null;
        
        if ($result['error']) {
            echo '<p class="error">❌ cURL Error: ' . htmlspecialchars($result['error']) . '</p>';
        } elseif ($result['http_code'] === 200) {
            echo '<p class="success">✅ جدول المودات متاح مع مفتاح anon!</p>';
            
            $data = json_decode($result['response'], true);
            if ($data && is_array($data)) {
                $mods_data = $data;
                echo '<p class="info">📊 عدد المودات: ' . count($data) . '</p>';
                
                if (!empty($data)) {
                    echo '<h3>🎮 المودات المتاحة:</h3>';
                    foreach (array_slice($data, 0, 3) as $index => $mod) {
                        echo '<div class="code">';
                        echo '<strong>🎯 مود ' . ($index + 1) . ':</strong><br>';
                        echo '🆔 ID: ' . ($mod['id'] ?? 'N/A') . '<br>';
                        echo '📝 Name: ' . htmlspecialchars($mod['name'] ?? 'N/A') . '<br>';
                        echo '🔢 Version: ' . htmlspecialchars($mod['version'] ?? 'N/A') . '<br>';
                        echo '📂 Category: ' . htmlspecialchars($mod['category'] ?? 'N/A') . '<br>';
                        if (isset($mod['download_url'])) {
                            echo '📥 Download: ' . htmlspecialchars(substr($mod['download_url'], 0, 60)) . '...<br>';
                        }
                        echo '</div>';
                    }
                } else {
                    echo '<p class="warning">⚠️ الجدول فارغ - لا توجد مودات</p>';
                }
            } else {
                echo '<p class="error">❌ استجابة غير صحيحة</p>';
                echo '<div class="code">' . htmlspecialchars(substr($result['response'], 0, 300)) . '</div>';
            }
        } else {
            echo '<p class="error">❌ فشل مع مفتاح anon - HTTP: ' . $result['http_code'] . '</p>';
            echo '<div class="code">' . htmlspecialchars(substr($result['response'], 0, 300)) . '</div>';
            
            // محاولة مع مفتاح service
            echo '<h3>🔄 محاولة مع مفتاح service...</h3>';
            $service_result = makeRequest($table_url, $service_headers);
            
            echo '<p><strong>📊 HTTP Code (Service):</strong> ' . $service_result['http_code'] . '</p>';
            
            if ($service_result['http_code'] === 200) {
                echo '<p class="success">✅ نجح مع مفتاح service!</p>';
                $data = json_decode($service_result['response'], true);
                if ($data && is_array($data)) {
                    $mods_data = $data;
                    echo '<p class="info">📊 عدد المودات: ' . count($data) . '</p>';
                }
            } else {
                echo '<p class="error">❌ فشل أيضاً مع مفتاح service</p>';
                echo '<div class="code">' . htmlspecialchars(substr($service_result['response'], 0, 300)) . '</div>';
            }
        }
        echo '</div>';
        
        // اختبار مود محدد
        if ($mods_data && !empty($mods_data)) {
            echo '<div class="test-section">';
            echo '<h2>🎯 3. اختبار مود محدد</h2>';
            
            $first_mod_id = $mods_data[0]['id'] ?? null;
            
            if ($first_mod_id) {
                echo '<p class="info">🎮 اختبار المود رقم: ' . $first_mod_id . '</p>';
                
                $mod_url = $SUPABASE_URL . '/rest/v1/' . $TABLE_NAME . '?id=eq.' . $first_mod_id;
                $result = makeRequest($mod_url, $anon_headers);
                
                echo '<p><strong>🌐 URL:</strong> ' . htmlspecialchars($mod_url) . '</p>';
                echo '<p><strong>📊 HTTP Code:</strong> ' . $result['http_code'] . '</p>';
                
                if ($result['http_code'] === 200) {
                    $mod_data = json_decode($result['response'], true);
                    if ($mod_data && !empty($mod_data)) {
                        echo '<p class="success">✅ تم جلب بيانات المود بنجاح!</p>';
                        $mod = $mod_data[0];
                        echo '<div class="code">';
                        echo '<strong>🎮 تفاصيل المود الكاملة:</strong><br>';
                        echo json_encode($mod, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                        echo '</div>';
                    }
                } else {
                    echo '<p class="error">❌ فشل في جلب المود</p>';
                }
            }
            echo '</div>';
        }
        
        // اختبار API الجديد
        echo '<div class="test-section">';
        echo '<h2>🔌 4. اختبار API المحدث</h2>';
        
        $api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api_fixed.php?path=/test';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo '<p><strong>🌐 API URL:</strong> ' . htmlspecialchars($api_url) . '</p>';
        echo '<p><strong>📊 HTTP Code:</strong> ' . $http_code . '</p>';
        
        if ($error) {
            echo '<p class="error">❌ cURL Error: ' . htmlspecialchars($error) . '</p>';
        } elseif ($http_code === 200) {
            echo '<p class="success">✅ API المحدث يعمل بشكل مثالي!</p>';
            $api_data = json_decode($response, true);
            if ($api_data) {
                echo '<div class="code">' . htmlspecialchars(json_encode($api_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . '</div>';
            }
        } else {
            echo '<p class="error">❌ فشل API - HTTP: ' . $http_code . '</p>';
            echo '<div class="code">' . htmlspecialchars(substr($response, 0, 300)) . '</div>';
        }
        echo '</div>';
        
        // النتيجة النهائية
        $connection_ok = (isset($result) && $result['http_code'] === 200);
        $table_ok = (isset($mods_data) && !empty($mods_data));
        $api_ok = ($http_code === 200);
        
        $all_good = $connection_ok && $table_ok && $api_ok;
        
        echo '<div class="final-result ' . ($all_good ? 'final-success' : 'final-error') . '">';
        if ($all_good) {
            echo '<h2>🎉 مبروك! كل شيء يعمل بشكل مثالي!</h2>';
            echo '<p>✅ الاتصال ناجح | ✅ الجدول متاح | ✅ API يعمل</p>';
            
            echo '<div style="margin-top: 20px;">';
            echo '<h3>🚀 الخطوة التالية:</h3>';
            echo '<p>استبدل الملفات القديمة بالملفات المحدثة:</p>';
            echo '<ul style="text-align: right;">';
            echo '<li><code>config.php</code> ← <code>config_fixed.php</code></li>';
            echo '<li><code>api.php</code> ← <code>api_fixed.php</code></li>';
            echo '<li><code>index.php</code> ← <code>index_new.php</code></li>';
            echo '</ul>';
            echo '</div>';
            
            // روابط الاختبار
            if (isset($first_mod_id)) {
                echo '<div style="margin-top: 20px;">';
                echo '<h3>🔗 روابط الاختبار:</h3>';
                $base_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
                
                echo '<a href="' . $base_url . '/index_new.php?id=' . $first_mod_id . '&lang=ar" class="btn btn-primary" target="_blank">🎮 اختبار الصفحة (عربي)</a>';
                echo '<a href="' . $base_url . '/index_new.php?id=' . $first_mod_id . '&lang=en" class="btn btn-primary" target="_blank">🎮 اختبار الصفحة (إنجليزي)</a>';
                echo '<a href="' . $base_url . '/api_fixed.php?path=/mod&id=' . $first_mod_id . '" class="btn btn-success" target="_blank">🔌 اختبار API</a>';
                echo '</div>';
            }
        } else {
            echo '<h2>❌ يحتاج إصلاح</h2>';
            echo '<ul style="text-align: right;">';
            if (!$connection_ok) echo '<li>❌ مشكلة في الاتصال الأساسي</li>';
            if (!$table_ok) echo '<li>❌ مشكلة في جدول المودات</li>';
            if (!$api_ok) echo '<li>❌ مشكلة في API</li>';
            echo '</ul>';
        }
        echo '</div>';
        ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="location.reload()" class="btn btn-primary">
                🔄 إعادة الاختبار
            </button>
        </div>
    </div>
</body>
</html>
