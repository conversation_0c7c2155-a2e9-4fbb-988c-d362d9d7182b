/*
 * قوالب التصميم المختلفة لصفحة المودات
 * Style Templates for Mod Page
 * Cloudflare Pages Version
 */

/* ========== Telegram Style Template ========== */
.style-template-telegram {
    font-family: 'Roboto', sans-serif;
}

.style-template-telegram body {
    background: linear-gradient(135deg, #0088cc 0%, #40a7e3 100%);
}

.style-template-telegram .mod-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 136, 204, 0.3);
    color: #333;
}

.style-template-telegram .pixel-button {
    background: linear-gradient(45deg, #40a7e3, #64b5f6);
    border: none;
    border-radius: 25px;
    transition: all 0.3s ease;
    color: white;
}

.style-template-telegram .pixel-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(64, 167, 227, 0.4);
}

.style-template-telegram .nav-button {
    background: #40a7e3;
    border-radius: 50%;
}

.style-template-telegram .thumbnail.active {
    border-color: #40a7e3;
    box-shadow: 0 0 8px rgba(64, 167, 227, 0.6);
}

.style-template-telegram .info-label {
    color: #0088cc;
}

.style-template-telegram header {
    background: linear-gradient(135deg, #0088cc 0%, #40a7e3 100%);
}

/* ========== TikTok Style Template ========== */
.style-template-tiktok {
    font-family: 'Poppins', sans-serif;
}

.style-template-tiktok body {
    background: linear-gradient(45deg, #000000 0%, #1a1a1a 50%, #000000 100%);
    color: #ffffff;
}

.style-template-tiktok .mod-container {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 2px solid #ff0050;
    border-radius: 20px;
    box-shadow: 0 0 20px rgba(255, 0, 80, 0.3);
}

.style-template-tiktok .pixel-button {
    background: linear-gradient(45deg, #ff0050, #25f4ee);
    border: none;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    animation: tiktok-pulse 2s infinite;
}

@keyframes tiktok-pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 0, 80, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 0, 80, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 0, 80, 0); }
}

.style-template-tiktok .nav-button {
    background: #ff0050;
    border-radius: 50%;
}

.style-template-tiktok .thumbnail.active {
    border-color: #ff0050;
    box-shadow: 0 0 8px rgba(255, 0, 80, 0.6);
}

.style-template-tiktok .info-label {
    color: #25f4ee;
}

.style-template-tiktok header {
    background: linear-gradient(45deg, #ff0050, #25f4ee);
}

.style-template-tiktok .image-glow-effect::before {
    background: linear-gradient(45deg, #ff0050, #25f4ee, #ff0050);
}

/* ========== Classic Style Template ========== */
.style-template-classic {
    font-family: 'Georgia', serif;
}

.style-template-classic body {
    background: linear-gradient(to bottom, #f5f5dc 0%, #e6ddd4 100%);
    color: #2f4f4f;
}

.style-template-classic .mod-container {
    background: #ffffff;
    border: 3px solid #8b4513;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(139, 69, 19, 0.2);
}

.style-template-classic .pixel-button {
    background: linear-gradient(to bottom, #cd853f 0%, #daa520 100%);
    border: 2px solid #8b4513;
    border-radius: 4px;
    font-family: 'Georgia', serif;
    font-weight: bold;
    color: white;
}

.style-template-classic .pixel-button:hover {
    background: linear-gradient(to bottom, #daa520 0%, #cd853f 100%);
}

.style-template-classic .nav-button {
    background: #8b4513;
    border: 2px solid #654321;
}

.style-template-classic .thumbnail.active {
    border-color: #8b4513;
    box-shadow: 0 0 8px rgba(139, 69, 19, 0.6);
}

.style-template-classic .info-label {
    color: #8b4513;
}

.style-template-classic header {
    background: linear-gradient(to bottom, #8b4513 0%, #cd853f 100%);
}

.style-template-classic .image-glow-effect::before {
    background: linear-gradient(45deg, #8b4513, #daa520, #8b4513);
}

/* ========== Professional Style Template ========== */
.style-template-professional {
    font-family: 'Inter', sans-serif;
}

.style-template-professional body {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #2c3e50;
}

.style-template-professional .mod-container {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
}

.style-template-professional .pixel-button {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    color: white;
}

.style-template-professional .pixel-button:hover {
    background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
    transform: translateY(-1px);
}

.style-template-professional .nav-button {
    background: #3498db;
    border-radius: 4px;
}

.style-template-professional .thumbnail.active {
    border-color: #3498db;
    box-shadow: 0 0 8px rgba(52, 152, 219, 0.6);
}

.style-template-professional .info-label {
    color: #3498db;
}

.style-template-professional header {
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
}

.style-template-professional .image-glow-effect::before {
    background: linear-gradient(45deg, #3498db, #2980b9, #3498db);
}

/* ========== Gaming Style Template ========== */
.style-template-gaming {
    font-family: 'Press Start 2P', monospace;
}

.style-template-gaming body {
    background: linear-gradient(45deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: #00ff00;
}

.style-template-gaming .mod-container {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #00ff00;
    border-radius: 0;
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
    position: relative;
}

.style-template-gaming .mod-container::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ff00, #00ffff, #00ff00);
    z-index: -1;
    animation: gaming-border 2s linear infinite;
}

@keyframes gaming-border {
    0% { filter: hue-rotate(0deg); }
    100% { filter: hue-rotate(360deg); }
}

.style-template-gaming .pixel-button {
    background: linear-gradient(45deg, #00ff00, #00ffff);
    border: 2px solid #00ff00;
    border-radius: 0;
    color: #000;
    font-weight: bold;
    text-transform: uppercase;
    animation: gaming-glow 1.5s ease-in-out infinite alternate;
}

@keyframes gaming-glow {
    from { box-shadow: 0 0 5px #00ff00; }
    to { box-shadow: 0 0 20px #00ff00, 0 0 30px #00ffff; }
}

.style-template-gaming .nav-button {
    background: #00ff00;
    border: 2px solid #00ffff;
    color: #000;
}

.style-template-gaming .thumbnail.active {
    border-color: #00ff00;
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.8);
}

.style-template-gaming .info-label {
    color: #00ffff;
    text-shadow: 0 0 5px #00ffff;
}

.style-template-gaming header {
    background: linear-gradient(45deg, #0f0f23, #00ff00);
}

/* ========== Dark Mode Template ========== */
.style-template-dark {
    font-family: 'Roboto', sans-serif;
}

.style-template-dark body {
    background: linear-gradient(135deg, #121212 0%, #1e1e1e 100%);
    color: #ffffff;
}

.style-template-dark .mod-container {
    background: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.style-template-dark .pixel-button {
    background: linear-gradient(135deg, #bb86fc 0%, #6200ea 100%);
    border: none;
    border-radius: 8px;
    color: white;
}

.style-template-dark .pixel-button:hover {
    background: linear-gradient(135deg, #6200ea 0%, #bb86fc 100%);
}

.style-template-dark .nav-button {
    background: #bb86fc;
    border-radius: 8px;
}

.style-template-dark .thumbnail.active {
    border-color: #bb86fc;
    box-shadow: 0 0 8px rgba(187, 134, 252, 0.6);
}

.style-template-dark .info-label {
    color: #bb86fc;
}

.style-template-dark header {
    background: linear-gradient(135deg, #1e1e1e 0%, #bb86fc 100%);
}

/* ========== Responsive Adjustments ========== */
@media (max-width: 768px) {
    .style-template-gaming .pixel-button {
        font-size: 12px;
    }
    
    .style-template-classic .pixel-button {
        font-size: 14px;
    }
    
    .style-template-professional .pixel-button {
        font-size: 14px;
    }
}

/* ========== Animation Utilities ========== */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}
