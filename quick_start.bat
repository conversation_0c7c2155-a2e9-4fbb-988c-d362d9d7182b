@echo off
echo 🚀 الحل النهائي الشامل - إصلاح وتشغيل البوت
echo.

REM تشغيل الحل النهائي الشامل
python ultimate_fix_and_start.py

REM في حالة الفشل، عرض خيارات بديلة
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في الحل النهائي!
    echo.
    echo 💡 جرب الطرق البديلة:
    echo    1. الحل التلقائي: python auto_start_bot.py
    echo    2. التشغيل المتقدم: python start_all_servers.py
    echo    3. التشغيل اليدوي: python main.py
    echo    4. إصلاح ngrok: python fix_ngrok_warning.py
    echo.
    echo 🔧 خطوات يدوية:
    echo    1. تشغيل ngrok: start_ngrok.bat
    echo    2. تحديث الرابط: python update_ngrok_url.py
    echo    3. تشغيل البوت: python main.py
    echo.
    pause
)
