#!/usr/bin/env python3
"""
اختبار تدفق أمر /start للمستخدمين الجدد والحاليين
"""

import os
import sys
import json
import logging
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def load_env_file():
    """تحميل متغيرات البيئة من ملف .env"""
    try:
        if os.path.exists('.env'):
            with open('.env', 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key] = value
            print("✅ تم تحميل متغيرات البيئة من .env")
        else:
            print("⚠️ ملف .env غير موجود")
    except Exception as e:
        print(f"❌ خطأ في تحميل ملف .env: {e}")

def test_new_user_flow():
    """اختبار تدفق المستخدم الجديد"""
    print("🆕 اختبار تدفق المستخدم الجديد...")
    
    try:
        from main import load_user_channels
        
        # محاكاة مستخدم جديد
        new_user_id = 999999999
        user_id_str = str(new_user_id)
        
        # تحميل قنوات المستخدمين
        user_channels = load_user_channels()
        
        # التحقق من أن المستخدم غير موجود
        if user_id_str not in user_channels:
            print("✅ المستخدم الجديد غير موجود في قاعدة البيانات")
            print("📋 التدفق المتوقع: عرض رسالة الترحيب مع اختيار اللغة")
            expected_flow = "welcome_with_language_selection"
        else:
            print("❌ المستخدم موجود بالفعل في قاعدة البيانات")
            return False
        
        print(f"✅ التدفق المتوقع للمستخدم الجديد: {expected_flow}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تدفق المستخدم الجديد: {e}")
        return False

def test_existing_user_with_channels():
    """اختبار تدفق المستخدم الحالي الذي لديه قنوات"""
    print("👤 اختبار تدفق المستخدم الحالي مع قنوات...")
    
    try:
        from main import load_user_channels, save_user_channel
        
        # إنشاء مستخدم تجريبي مع قناة
        test_user_id = 888888888
        test_channel_id = "@test_existing_channel"
        test_interval = 60
        test_lang = "ar"
        
        # حفظ القناة للمستخدم
        success = save_user_channel(test_user_id, test_channel_id, test_interval, test_lang)
        
        if not success:
            print("❌ فشل في حفظ القناة للمستخدم التجريبي")
            return False
        
        # تحميل قنوات المستخدمين
        user_channels = load_user_channels()
        user_id_str = str(test_user_id)
        
        if user_id_str in user_channels:
            user_data = user_channels.get(user_id_str, {})
            
            # التحقق من النظام الجديد (قنوات متعددة)
            if 'channels' in user_data:
                user_channels_dict = user_data.get('channels', {})
                if len(user_channels_dict) > 0:
                    print("✅ المستخدم لديه قنوات في النظام الجديد")
                    print("📋 التدفق المتوقع: عرض القائمة الرئيسية")
                    expected_flow = "main_menu"
                else:
                    print("⚠️ المستخدم ليس لديه قنوات في النظام الجديد")
                    expected_flow = "add_channel"
            # التحقق من النظام القديم
            elif user_data.get("channel_lang"):
                if user_data.get("channel_id"):
                    print("✅ المستخدم لديه قناة في النظام القديم")
                    print("📋 التدفق المتوقع: عرض القائمة الرئيسية")
                    expected_flow = "main_menu"
                else:
                    print("⚠️ المستخدم اختار لغة لكن لا يوجد قناة")
                    expected_flow = "add_channel"
            else:
                print("⚠️ المستخدم موجود لكن لم يختر لغة")
                expected_flow = "language_selection"
        else:
            print("❌ المستخدم غير موجود")
            return False
        
        print(f"✅ التدفق المتوقع للمستخدم الحالي: {expected_flow}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تدفق المستخدم الحالي: {e}")
        return False

def test_existing_user_without_channels():
    """اختبار تدفق المستخدم الحالي بدون قنوات"""
    print("👤 اختبار تدفق المستخدم الحالي بدون قنوات...")
    
    try:
        from main import load_user_channels, save_json_file
        
        # إنشاء مستخدم تجريبي بدون قنوات
        test_user_id = 777777777
        user_id_str = str(test_user_id)
        
        # إضافة المستخدم بدون قنوات
        user_channels = load_user_channels()
        user_channels[user_id_str] = {
            "channels": {},  # قنوات فارغة
            "user_settings": {
                "default_channel": None,
                "language": "ar"
            }
        }
        
        # حفظ البيانات
        USER_CHANNELS_FILE = "user_channels.json"
        save_json_file(USER_CHANNELS_FILE, user_channels)
        
        # إعادة تحميل البيانات للتأكد
        user_channels = load_user_channels()
        
        if user_id_str in user_channels:
            user_data = user_channels.get(user_id_str, {})
            
            # التحقق من النظام الجديد (قنوات متعددة)
            if 'channels' in user_data:
                user_channels_dict = user_data.get('channels', {})
                if len(user_channels_dict) > 0:
                    print("⚠️ المستخدم لديه قنوات (غير متوقع)")
                    expected_flow = "main_menu"
                else:
                    print("✅ المستخدم ليس لديه قنوات")
                    print("📋 التدفق المتوقع: طلب إضافة قناة")
                    expected_flow = "add_channel"
            else:
                print("⚠️ المستخدم في النظام القديم")
                expected_flow = "language_selection"
        else:
            print("❌ المستخدم غير موجود")
            return False
        
        print(f"✅ التدفق المتوقع للمستخدم بدون قنوات: {expected_flow}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تدفق المستخدم بدون قنوات: {e}")
        return False

def test_start_command_logic():
    """اختبار منطق أمر /start بشكل عام"""
    print("🚀 اختبار منطق أمر /start...")
    
    try:
        from main import load_user_channels
        
        # تحميل جميع المستخدمين
        user_channels = load_user_channels()
        
        print(f"📊 إجمالي المستخدمين في قاعدة البيانات: {len(user_channels)}")
        
        # تحليل أنواع المستخدمين
        users_with_new_system = 0
        users_with_old_system = 0
        users_without_channels = 0
        
        for user_id, user_data in user_channels.items():
            if isinstance(user_data, dict):
                if 'channels' in user_data:
                    channels = user_data.get('channels', {})
                    if len(channels) > 0:
                        users_with_new_system += 1
                    else:
                        users_without_channels += 1
                elif user_data.get("channel_id"):
                    users_with_old_system += 1
                else:
                    users_without_channels += 1
        
        print(f"📈 إحصائيات المستخدمين:")
        print(f"   - مستخدمون بالنظام الجديد (مع قنوات): {users_with_new_system}")
        print(f"   - مستخدمون بالنظام القديم (مع قنوات): {users_with_old_system}")
        print(f"   - مستخدمون بدون قنوات: {users_without_channels}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار منطق /start: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 بدء اختبار تدفق أمر /start")
    print("=" * 60)
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # تحميل متغيرات البيئة
    load_env_file()
    
    # قائمة الاختبارات
    tests = [
        ("اختبار تدفق المستخدم الجديد", test_new_user_flow),
        ("اختبار تدفق المستخدم الحالي مع قنوات", test_existing_user_with_channels),
        ("اختبار تدفق المستخدم الحالي بدون قنوات", test_existing_user_without_channels),
        ("اختبار منطق أمر /start", test_start_command_logic)
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for test_name, test_function in tests:
        print(f"🧪 {test_name}...")
        try:
            if test_function():
                print(f"✅ {test_name} - نجح")
                success_count += 1
            else:
                print(f"❌ {test_name} - فشل")
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
        print()
    
    print("=" * 60)
    print(f"📊 النتائج: {success_count}/{total_count} اختبارات نجحت")
    
    if success_count == total_count:
        print("🎉 جميع اختبارات تدفق /start نجحت!")
        print()
        print("✅ التدفقات المتوقعة:")
        print("   • مستخدم جديد → رسالة ترحيب + اختيار لغة")
        print("   • مستخدم حالي مع قنوات → القائمة الرئيسية")
        print("   • مستخدم حالي بدون قنوات → طلب إضافة قناة")
        print()
        print("🚀 أمر /start يعمل بشكل صحيح!")
    else:
        print("⚠️ بعض اختبارات تدفق /start فشلت.")
    
    return success_count == total_count

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
