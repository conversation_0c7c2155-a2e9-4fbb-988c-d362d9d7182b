# دليل الإصلاح السريع - Quick Fix Guide

## المشكلة الحالية - Current Issue
```
httpx.ConnectError: [Errno 11001] getaddrinfo failed
```

هذا الخطأ يعني أن النظام لا يستطيع حل أسماء النطاقات (DNS resolution).

## الحلول السريعة - Quick Solutions

### 🚀 الحل الأسرع - Fastest Solution
```bash
# 1. شغل أداة الإصلاح التلقائي
python fix_dns_connection.py

# 2. أو استخدم الأداة الشاملة
fix_and_run.bat
```

### 🔧 حلول يدوية - Manual Solutions

#### 1. مسح ذاكرة DNS
```cmd
# في Windows Command Prompt (كمدير)
ipconfig /flushdns
ipconfig /release
ipconfig /renew
```

#### 2. تغيير خوادم DNS
1. افتح **Control Panel** → **Network and Internet** → **Network Connections**
2. انقر بزر الماوس الأيمن على اتصالك → **Properties**
3. اختر **Internet Protocol Version 4 (TCP/IPv4)** → **Properties**
4. اختر **Use the following DNS server addresses**
5. ضع:
   - **Preferred DNS**: `*******`
   - **Alternate DNS**: `*******`

#### 3. فحص الجدار الناري
1. افتح **Windows Defender Firewall**
2. اختر **Allow an app through firewall**
3. تأكد من السماح لـ **Python** و **python.exe**

#### 4. تعطيل البروكسي مؤقتاً
1. افتح **Settings** → **Network & Internet** → **Proxy**
2. تأكد من إيقاف **Use a proxy server**

### 🌐 اختبار الاتصال - Test Connection

#### اختبار DNS
```cmd
nslookup google.com
nslookup api.telegram.org
```

#### اختبار الاتصال
```cmd
ping *******
ping google.com
ping api.telegram.org
```

#### اختبار Python
```python
# شغل هذا الكود في Python
import socket
print(socket.gethostbyname('google.com'))
```

## أدوات الإصلاح المتوفرة - Available Fix Tools

### 1. أداة الإصلاح الشاملة
```bash
python fix_dns_connection.py
```
**الوظائف:**
- فحص الاتصال بالإنترنت
- مسح ذاكرة DNS
- تكوين خوادم DNS
- اختبار حل النطاقات
- فحص الجدار الناري
- إصلاح SSL

### 2. تشغيل البوت المحسن
```bash
python run_bot_offline_mode.py
```
**المميزات:**
- إعدادات شبكة محسنة
- timeout أطول
- إعادة محاولة تلقائية
- معالجة أخطاء محسنة

### 3. أداة Windows السهلة
```bash
fix_and_run.bat
```
**قائمة تفاعلية تتضمن:**
- إصلاح DNS
- تشغيل البوت
- اختبار الشبكة
- دليل استكشاف الأخطاء

## خطوات الإصلاح المرتبة - Step-by-Step Fix

### الخطوة 1: فحص أولي
```bash
# تحقق من Python
python --version

# تحقق من المكتبات
pip list | findstr telegram

# تحقق من الاتصال
ping google.com
```

### الخطوة 2: إصلاح DNS
```bash
# شغل أداة الإصلاح
python fix_dns_connection.py
```

### الخطوة 3: اختبار الإصلاح
```bash
# اختبار الشبكة
python network_test.py

# أو اختبار بسيط
ping api.telegram.org
```

### الخطوة 4: تشغيل البوت
```bash
# تشغيل محسن
python run_bot_offline_mode.py

# أو تشغيل عادي
python main.py
```

## أسباب المشكلة المحتملة - Possible Causes

### 🌐 مشاكل الشبكة
- انقطاع الإنترنت
- مشاكل في الراوتر
- مشاكل مزود الخدمة

### 🔒 مشاكل الأمان
- الجدار الناري يحجب Python
- مضاد الفيروسات يحجب الاتصالات
- إعدادات البروكسي

### 🔧 مشاكل DNS
- خوادم DNS لا تعمل
- ذاكرة DNS تالفة
- إعدادات DNS خاطئة

### 📱 مشاكل Telegram
- Telegram محجوب في المنطقة
- مشاكل في Telegram API
- Bot Token خاطئ

## حلول متقدمة - Advanced Solutions

### استخدام VPN
إذا كان Telegram محجوب:
```bash
# تثبيت وتشغيل VPN
# ثم جرب تشغيل البوت
```

### تغيير إعدادات Python
```python
# إضافة هذا في بداية main.py
import os
os.environ['PYTHONHTTPSVERIFY'] = '0'
```

### استخدام Proxy
```python
# في main.py
from telegram.request import HTTPXRequest

request = HTTPXRequest(
    proxy_url="http://proxy:port",
    connection_pool_size=8
)
```

## إذا استمرت المشكلة - If Problem Persists

### 1. تحقق من الأساسيات
- إعادة تشغيل الكمبيوتر
- تحقق من كابل الإنترنت
- جرب شبكة أخرى (هاتف محمول)

### 2. تحقق من إعدادات النظام
- تحديث Windows
- تحديث تعريفات الشبكة
- فحص الفيروسات

### 3. تحقق من Python
```bash
# إعادة تثبيت المكتبات
pip uninstall python-telegram-bot
pip install python-telegram-bot

# تحديث pip
python -m pip install --upgrade pip
```

### 4. اتصل بالدعم
- تحقق من حالة Telegram API
- اتصل بمزود الإنترنت
- راجع سجلات الراوتر

## ملفات السجل - Log Files

### فحص السجلات
```bash
# سجل إصلاح DNS
type dns_fix.log

# سجل البوت
type bot.log
```

### معلومات مفيدة للدعم
- نوع نظام التشغيل
- إصدار Python
- إصدار python-telegram-bot
- نوع الاتصال (WiFi/Ethernet)
- مزود الإنترنت

---

## ملاحظات مهمة - Important Notes

⚠️ **تحذير**: بعض الحلول تتطلب صلاحيات المدير

💡 **نصيحة**: جرب الحلول بالترتيب المذكور

🔒 **أمان**: لا تعطل الجدار الناري نهائياً، فقط أضف استثناءات

📞 **دعم**: إذا لم تنجح الحلول، قد تحتاج لمساعدة تقنية متخصصة

---

**آخر تحديث**: 2025-01-11
**الإصدار**: 1.0
