#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار API endpoints للتأكد من عملها بشكل صحيح
Test API endpoints to ensure they work correctly
"""

import requests
import json
import sys
import time

def test_api_endpoint(url, description):
    """اختبار API endpoint"""
    print(f"\n🧪 اختبار: {description}")
    print(f"🔗 URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ استجابة JSON صحيحة")
                print(f"📄 البيانات: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
                return True
            except json.JSONDecodeError:
                print("❌ استجابة ليست JSON صحيحة")
                print(f"📄 المحتوى: {response.text[:200]}...")
                return False
        else:
            print(f"❌ خطأ في الاستجابة: {response.status_code}")
            print(f"📄 المحتوى: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ خطأ في الطلب: {e}")
        return False

def test_mod_api_with_sample_id(base_url):
    """اختبار API المود مع معرف عينة"""
    # معرفات عينة للاختبار
    sample_ids = [
        "9d0601a1-6861-4a27-89da-a86f426285da",  # من السجلات
        "12345678-1234-1234-1234-123456789abc",  # معرف وهمي
        "invalid-id"  # معرف غير صالح
    ]
    
    for mod_id in sample_ids:
        url = f"{base_url}/api/mod/{mod_id}?lang=ar&user_id=7513880877&channel=-1002433545184"
        success = test_api_endpoint(url, f"API المود - {mod_id}")
        
        if not success and mod_id == sample_ids[0]:
            print("⚠️ فشل في اختبار المعرف الأول، قد يكون المود غير موجود في قاعدة البيانات")

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار API endpoints للبوت")
    print("🔍 API Endpoints Test for Bot")
    print("=" * 60)
    
    # عناوين الخوادم للاختبار
    servers = [
        "http://localhost:5000",
        "http://localhost:5001",
        "http://127.0.0.1:5000",
        "http://127.0.0.1:5001"
    ]
    
    # البحث عن خادم يعمل
    working_server = None
    for server in servers:
        print(f"\n🔍 فحص الخادم: {server}")
        try:
            response = requests.get(f"{server}/api/test", timeout=5)
            if response.status_code == 200:
                print(f"✅ الخادم {server} يعمل")
                working_server = server
                break
        except:
            print(f"❌ الخادم {server} لا يعمل")
    
    if not working_server:
        print("\n❌ لم يتم العثور على خادم يعمل!")
        print("🔧 تأكد من تشغيل البوت أولاً:")
        print("   python main.py")
        print("   أو")
        print("   python run_bot_fixed.py")
        sys.exit(1)
    
    print(f"\n🎯 استخدام الخادم: {working_server}")
    
    # اختبار API endpoints
    tests = [
        (f"{working_server}/api/test", "اختبار API الأساسي"),
        (f"{working_server}/api/test/mod/12345678-1234-1234-1234-123456789abc", "اختبار API المود التجريبي"),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for url, description in tests:
        if test_api_endpoint(url, description):
            passed_tests += 1
    
    # اختبار API المود مع معرفات مختلفة
    print(f"\n🧪 اختبار API المود مع معرفات مختلفة...")
    test_mod_api_with_sample_id(working_server)
    
    # النتائج
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed_tests}/{total_tests} اختبارات أساسية نجحت")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات الأساسية نجحت!")
        print("✅ API endpoints تعمل بشكل صحيح")
    else:
        print("⚠️ بعض الاختبارات فشلت")
        print("🔧 راجع الأخطاء أعلاه")
    
    print("\n💡 نصائح:")
    print("   - تأكد من تشغيل البوت قبل تشغيل هذا الاختبار")
    print("   - تأكد من وجود مودات في قاعدة البيانات")
    print("   - راجع ملف TROUBLESHOOTING.md للمساعدة")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        sys.exit(1)
