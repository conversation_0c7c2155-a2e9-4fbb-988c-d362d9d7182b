#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لمشاكل Telegram Web App API
Comprehensive Fix for Telegram Web App API Issues
"""

import os
import sys
import logging
import json
import re

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebAppAPIFixer:
    def __init__(self):
        self.fixes_applied = []
        self.issues_found = []
        
    def check_supabase_client(self):
        """فحص وإصلاح supabase_client.py"""
        logger.info("🔍 فحص ملف supabase_client.py...")
        
        try:
            with open('supabase_client.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص دالة get_mod_by_id
            if 'def get_mod_by_id(mod_id: int)' in content:
                self.issues_found.append("❌ دالة get_mod_by_id تتوقع int بدلاً من UUID string")
                
                # إصلاح نوع المعامل
                content = content.replace(
                    'def get_mod_by_id(mod_id: int)',
                    'def get_mod_by_id(mod_id)'
                )
                
                with open('supabase_client.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixes_applied.append("✅ تم إصلاح نوع معامل get_mod_by_id")
            
            # فحص وجود download_link في الاستجابة
            if "'download_url': row['download_url']" in content and "'download_link':" not in content:
                self.issues_found.append("❌ مفتاح download_link مفقود في استجابة get_mod_by_id")
                
                # إضافة download_link للتوافق
                content = content.replace(
                    "'download_url': row['download_url'],",
                    "'download_url': row['download_url'],\n                    'download_link': row['download_url'],  # إضافة للتوافق مع web_server.py"
                )
                
                with open('supabase_client.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixes_applied.append("✅ تم إضافة مفتاح download_link للتوافق")
            
        except Exception as e:
            self.issues_found.append(f"❌ خطأ في فحص supabase_client.py: {e}")
    
    def check_web_server(self):
        """فحص وإصلاح web_server.py"""
        logger.info("🔍 فحص ملف web_server.py...")
        
        try:
            with open('web_server.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص استيراد json
            if 'import json' not in content:
                self.issues_found.append("❌ استيراد json مفقود في web_server.py")
                
                content = content.replace(
                    'import os\nimport logging',
                    'import os\nimport logging\nimport json'
                )
                
                with open('web_server.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixes_applied.append("✅ تم إضافة استيراد json")
            
            # فحص معالجة أخطاء API
            if 'return {"error":' in content and 'app.response_class' not in content:
                self.issues_found.append("❌ معالجة أخطاء API غير محسنة")
                
                # إصلاح معالجة الأخطاء
                old_error_handling = '''return {"error": "Mod not found", "mod_id": mod_id}, 404'''
                new_error_handling = '''response = app.response_class(
                response=json.dumps({"error": "Mod not found", "mod_id": mod_id}),
                status=404,
                mimetype='application/json'
            )
            return response'''
                
                content = content.replace(old_error_handling, new_error_handling)
                
                with open('web_server.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixes_applied.append("✅ تم تحسين معالجة أخطاء API")
            
        except Exception as e:
            self.issues_found.append(f"❌ خطأ في فحص web_server.py: {e}")
    
    def check_mod_details_template(self):
        """فحص ملف mod_details.html"""
        logger.info("🔍 فحص ملف mod_details.html...")
        
        try:
            if not os.path.exists('mod_details.html'):
                self.issues_found.append("❌ ملف mod_details.html مفقود")
                return
            
            with open('mod_details.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص وجود دالة loadModData
            if 'function loadModData()' not in content:
                self.issues_found.append("❌ دالة loadModData مفقودة في mod_details.html")
            else:
                self.fixes_applied.append("✅ دالة loadModData موجودة")
            
            # فحص معالجة أخطاء JSON
            if 'SyntaxError' not in content and 'JSON.parse' in content:
                self.issues_found.append("❌ معالجة أخطاء JSON غير كافية")
            
        except Exception as e:
            self.issues_found.append(f"❌ خطأ في فحص mod_details.html: {e}")
    
    def create_test_endpoint(self):
        """إنشاء endpoint اختبار"""
        logger.info("🔧 إنشاء endpoint اختبار...")
        
        test_endpoint_code = '''
@app.route('/api/test')
def test_api():
    """API endpoint للاختبار"""
    return {
        "status": "success",
        "message": "API يعمل بشكل صحيح",
        "timestamp": datetime.now().isoformat()
    }

@app.route('/api/test/mod/<mod_id>')
def test_mod_api(mod_id):
    """اختبار API للمود"""
    return {
        "status": "success",
        "mod_id": mod_id,
        "message": f"تم استلام طلب للمود {mod_id}",
        "valid_uuid": bool(re.match(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', mod_id, re.IGNORECASE))
    }
'''
        
        try:
            with open('web_server.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            if '/api/test' not in content:
                # إضافة endpoints الاختبار قبل دالة run_web_server
                insertion_point = content.find('def run_web_server(')
                if insertion_point != -1:
                    content = content[:insertion_point] + test_endpoint_code + '\n' + content[insertion_point:]
                    
                    with open('web_server.py', 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.fixes_applied.append("✅ تم إضافة endpoints الاختبار")
                else:
                    self.issues_found.append("❌ لم يتم العثور على نقطة الإدراج لـ endpoints الاختبار")
            else:
                self.fixes_applied.append("✅ endpoints الاختبار موجودة بالفعل")
                
        except Exception as e:
            self.issues_found.append(f"❌ خطأ في إنشاء endpoints الاختبار: {e}")
    
    def add_cors_headers(self):
        """إضافة CORS headers"""
        logger.info("🔧 إضافة CORS headers...")
        
        cors_code = '''
@app.after_request
def after_request_cors(response):
    """إضافة CORS headers للـ API"""
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, ngrok-skip-browser-warning'
    response.headers['ngrok-skip-browser-warning'] = 'true'
    return response
'''
        
        try:
            with open('web_server.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'after_request_cors' not in content:
                # إضافة CORS headers بعد after_request الموجود
                insertion_point = content.find('@app.after_request\ndef after_request(response):')
                if insertion_point != -1:
                    end_point = content.find('\n\n', insertion_point)
                    if end_point != -1:
                        content = content[:end_point] + cors_code + content[end_point:]
                        
                        with open('web_server.py', 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        self.fixes_applied.append("✅ تم إضافة CORS headers")
                    else:
                        self.issues_found.append("❌ لم يتم العثور على نهاية دالة after_request")
                else:
                    self.issues_found.append("❌ لم يتم العثور على دالة after_request")
            else:
                self.fixes_applied.append("✅ CORS headers موجودة بالفعل")
                
        except Exception as e:
            self.issues_found.append(f"❌ خطأ في إضافة CORS headers: {e}")
    
    def generate_report(self):
        """إنشاء تقرير الإصلاح"""
        report = f"""
# 🔧 تقرير إصلاح Telegram Web App API
## Web App API Fix Report

**تاريخ الإصلاح**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## ✅ الإصلاحات المطبقة ({len(self.fixes_applied)})
"""
        
        for fix in self.fixes_applied:
            report += f"- {fix}\n"
        
        report += f"""
## ❌ المشاكل الموجودة ({len(self.issues_found)})
"""
        
        for issue in self.issues_found:
            report += f"- {issue}\n"
        
        if not self.issues_found:
            report += "🎉 **لا توجد مشاكل! جميع الإصلاحات تم تطبيقها بنجاح**\n"
        
        report += f"""
## 📊 النتيجة الإجمالية
- **الإصلاحات المطبقة**: {len(self.fixes_applied)}
- **المشاكل المتبقية**: {len(self.issues_found)}
- **معدل النجاح**: {(len(self.fixes_applied) / (len(self.fixes_applied) + len(self.issues_found)) * 100) if (len(self.fixes_applied) + len(self.issues_found)) > 0 else 100:.1f}%

## 🧪 اختبار الإصلاحات
لاختبار الإصلاحات المطبقة، شغل:
```bash
python test_api_fix.py
```

## 🚀 إعادة تشغيل البوت
بعد تطبيق الإصلاحات، أعد تشغيل البوت:
```bash
python quick_start.py
```
"""
        
        return report
    
    def run_fix(self):
        """تشغيل جميع الإصلاحات"""
        logger.info("🚀 بدء إصلاح مشاكل Web App API...")
        
        self.check_supabase_client()
        self.check_web_server()
        self.check_mod_details_template()
        self.create_test_endpoint()
        self.add_cors_headers()
        
        report = self.generate_report()
        
        # حفظ التقرير
        with open('web_app_api_fix_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        logger.info("✅ تم حفظ التقرير في web_app_api_fix_report.md")

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح شامل لمشاكل Telegram Web App API")
    print("=" * 60)
    
    fixer = WebAppAPIFixer()
    fixer.run_fix()

if __name__ == "__main__":
    from datetime import datetime
    import re
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء الإصلاح")
    except Exception as e:
        logger.error(f"❌ خطأ في الإصلاح: {e}")
        sys.exit(1)
