<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 - خطأ في الخادم</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Press Start 2P', cursive;
            background-color: #1a1a1a;
            color: white;
        }
        .pixel-border {
            box-shadow: inset 0 0 0 1px #AAAAAA;
            border: 1px solid #AAAAAA;
        }
        .error-container {
            background-color: #2D2D2D;
        }
        .pixel-button {
            background-color: #FFA500;
            color: white;
            border: 2px solid #FFD700;
            padding: 10px 20px;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            cursor: pointer;
            box-shadow: inset -4px -4px 0 0 #d27e00,
                       inset 4px 4px 0 0 #ffcb6b;
            transition: all 0.1s;
        }
        .pixel-button:hover {
            transform: translateY(-2px);
            box-shadow: inset -4px -4px 0 0 #d27e00,
                       inset 4px 4px 0 0 #ffcb6b,
                       0 0 10px #FFD700;
        }
        .error-icon {
            font-size: 120px;
            animation: shake 1s infinite;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
            20%, 40%, 60%, 80% { transform: translateX(10px); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="bg-yellow-500 text-white p-4 flex justify-center items-center">
        <div class="font-bold text-2xl">Modetaris</div>
    </header>

    <div class="container mx-auto p-4 min-h-screen flex items-center justify-center">
        <div class="error-container pixel-border p-8 text-center max-w-md">
            <div class="error-icon mb-6">⚠️</div>
            <h1 class="text-4xl mb-4 text-red-400">500</h1>
            <h2 class="text-xl mb-4">خطأ في الخادم</h2>
            <p class="text-sm mb-6 leading-relaxed">
                عذراً، حدث خطأ في الخادم. نحن نعمل على إصلاح المشكلة.
            </p>
            <div class="space-y-4">
                <button onclick="location.reload()" class="pixel-button block w-full">
                    🔄 إعادة المحاولة
                </button>
                <a href="/" class="pixel-button block">
                    🏠 العودة للصفحة الرئيسية
                </a>
            </div>
            <div class="mt-6 text-xs opacity-75">
                <p>إذا استمرت المشكلة، يرجى المحاولة مرة أخرى خلال بضع دقائق</p>
            </div>
        </div>
    </div>

    <script>
        // إعادة المحاولة التلقائية بعد 30 ثانية
        setTimeout(function() {
            const retryButton = document.querySelector('button');
            if (retryButton) {
                retryButton.textContent = '🔄 إعادة المحاولة التلقائية...';
                retryButton.style.backgroundColor = '#4CAF50';
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        }, 30000);
    </script>
</body>
</html>
