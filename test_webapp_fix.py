#!/usr/bin/env python3
"""
اختبار إصلاح WebApp
Test WebApp Fix
"""

import sys
import traceback

def test_telegram_imports():
    """اختبار استيراد مكتبة تيليجرام"""
    print("🔍 Testing Telegram imports...")
    
    try:
        import telegram
        print("✅ telegram module imported successfully")
        
        # اختبار WebApp
        try:
            webapp = telegram.WebApp(url="https://example.com")
            print("✅ telegram.WebApp works correctly")
            print(f"   WebApp URL: {webapp.url}")
        except Exception as e:
            print(f"❌ telegram.WebApp failed: {e}")
            
            # محاولة طريقة بديلة
            try:
                from telegram import WebApp
                webapp = WebApp(url="https://example.com")
                print("✅ WebApp import works directly")
            except Exception as e2:
                print(f"❌ Direct WebApp import failed: {e2}")
                
                # محاولة الطريقة القديمة
                try:
                    # في الإصدارات القديمة، قد يكون WebApp غير متوفر
                    # نستخدم dict بدلاً منه
                    webapp_dict = {"url": "https://example.com"}
                    print("⚠️ Using dict format for WebApp (fallback)")
                    print(f"   WebApp dict: {webapp_dict}")
                    return "dict"
                except Exception as e3:
                    print(f"❌ All WebApp methods failed: {e3}")
                    return "failed"
        
        return "success"
        
    except ImportError as e:
        print(f"❌ Failed to import telegram: {e}")
        return "import_failed"

def test_inline_keyboard():
    """اختبار InlineKeyboard مع WebApp"""
    print("\n🔍 Testing InlineKeyboard with WebApp...")
    
    try:
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        import telegram
        
        # اختبار إنشاء زر WebApp
        try:
            button = InlineKeyboardButton(
                "🎮 Test Button", 
                web_app=telegram.WebApp(url="https://example.com")
            )
            keyboard = InlineKeyboardMarkup([[button]])
            print("✅ InlineKeyboardButton with telegram.WebApp works")
            return "success"
            
        except Exception as e:
            print(f"❌ telegram.WebApp in button failed: {e}")
            
            # محاولة الطريقة البديلة
            try:
                button = InlineKeyboardButton(
                    "🎮 Test Button", 
                    web_app={"url": "https://example.com"}
                )
                keyboard = InlineKeyboardMarkup([[button]])
                print("✅ InlineKeyboardButton with dict works")
                return "dict"
                
            except Exception as e2:
                print(f"❌ Dict format also failed: {e2}")
                return "failed"
                
    except Exception as e:
        print(f"❌ InlineKeyboard test failed: {e}")
        return "failed"

def main():
    """الدالة الرئيسية"""
    print("🧪 WebApp Fix Test")
    print("=" * 50)
    
    # اختبار الاستيرادات
    import_result = test_telegram_imports()
    
    # اختبار InlineKeyboard
    keyboard_result = test_inline_keyboard()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Import test: {import_result}")
    print(f"   Keyboard test: {keyboard_result}")
    
    # تحديد الطريقة الأفضل
    if import_result == "success" and keyboard_result == "success":
        print("\n✅ Recommendation: Use telegram.WebApp(url=...)")
        print("   Code: web_app=telegram.WebApp(url=detail_url)")
        
    elif keyboard_result == "dict":
        print("\n⚠️ Recommendation: Use dict format")
        print("   Code: web_app={'url': detail_url}")
        
    else:
        print("\n❌ WebApp not supported in this version")
        print("   Consider upgrading python-telegram-bot")
        print("   Or use regular URL buttons instead")
    
    return import_result, keyboard_result

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        traceback.print_exc()
