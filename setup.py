#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف إعداد البوت وتثبيت المتطلبات
"""

import subprocess
import sys
import os

def install_requirements():
    """تثبيت المتطلبات من requirements.txt"""
    print("📦 تثبيت المتطلبات...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ تم تثبيت جميع المتطلبات بنجاح!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def check_python_version():
    """التحقق من إصدار Python"""
    version = sys.version_info
    print(f"🐍 إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ يتطلب Python 3.8 أو أحدث!")
        return False
    
    print("✅ إصدار Python مناسب")
    return True

def check_files():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        "main.py",
        "supabase_client.py", 
        "requirements.txt",
        "test_supabase.py"
    ]
    
    print("📁 فحص الملفات المطلوبة...")
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - مفقود!")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    print("✅ جميع الملفات موجودة")
    return True

def test_supabase():
    """اختبار الاتصال بـ Supabase"""
    print("\n🔄 اختبار الاتصال بـ Supabase...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_supabase.py"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ اختبار Supabase نجح!")
            return True
        else:
            print("❌ فشل اختبار Supabase:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ انتهت مهلة اختبار Supabase")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار Supabase: {e}")
        return False

def main():
    """الدالة الرئيسية للإعداد"""
    print("=" * 60)
    print("⚙️ إعداد بوت نشر مودات ماين كرافت")
    print("=" * 60)
    
    # التحقق من إصدار Python
    if not check_python_version():
        sys.exit(1)
    
    # التحقق من الملفات
    if not check_files():
        sys.exit(1)
    
    # تثبيت المتطلبات
    if not install_requirements():
        sys.exit(1)
    
    # اختبار Supabase
    if not test_supabase():
        print("\n⚠️ فشل اختبار Supabase - يمكنك المتابعة ولكن تأكد من إعدادات قاعدة البيانات")
    
    print("\n" + "=" * 60)
    print("🎉 تم الإعداد بنجاح!")
    print("🚀 يمكنك الآن تشغيل البوت باستخدام:")
    print("   python run_bot.py")
    print("   أو")
    print("   python main.py")
    print("=" * 60)

if __name__ == "__main__":
    main()
