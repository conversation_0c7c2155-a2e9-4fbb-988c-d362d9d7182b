#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لدالة أوقات النشر الموسعة
"""

import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from main import (
        get_available_publish_intervals,
        grant_invitation_rewards,
        load_user_invitations,
        save_user_invitations,
        create_user_invitation,
        check_user_premium_feature
    )
    print("✅ تم استيراد الدوال بنجاح")
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    sys.exit(1)

def test_intervals():
    """اختبار دالة أوقات النشر"""
    print("\n🧪 اختبار دالة أوقات النشر الموسعة")
    print("=" * 50)
    
    # مستخدم اختبار جديد
    test_user = 999888777
    
    # إنشاء مستخدم جديد
    create_user_invitation(test_user)
    
    # اختبار المستخدم العادي
    basic_intervals = get_available_publish_intervals(test_user)
    basic_count = len(basic_intervals)
    basic_minutes = [interval['minutes'] for interval in basic_intervals]
    
    print(f"📊 المستخدم العادي:")
    print(f"   عدد الأوقات: {basic_count}")
    print(f"   10 دقائق موجود: {10 in basic_minutes}")
    print(f"   15 دقيقة موجود: {15 in basic_minutes}")
    
    # منح المكافآت للحصول على الأوقات الموسعة
    print(f"\n🎁 منح مكافآت المستوى 5...")
    rewards = grant_invitation_rewards(test_user, 5)
    print(f"   المكافآت الممنوحة: {len(rewards)}")
    
    # التحقق من تفعيل الميزة
    has_feature = check_user_premium_feature(test_user, 'publish_intervals_extended')
    print(f"   ميزة الأوقات الموسعة مفعلة: {has_feature}")
    
    # اختبار المستخدم المميز
    extended_intervals = get_available_publish_intervals(test_user)
    extended_count = len(extended_intervals)
    extended_minutes = [interval['minutes'] for interval in extended_intervals]
    
    print(f"\n📊 المستخدم المميز:")
    print(f"   عدد الأوقات: {extended_count}")
    print(f"   10 دقائق موجود: {10 in extended_minutes}")
    print(f"   15 دقيقة موجود: {15 in extended_minutes}")
    print(f"   الزيادة: {extended_count - basic_count}")
    
    # النتيجة النهائية
    success = (
        extended_count > basic_count and
        10 in extended_minutes and
        15 in extended_minutes and
        has_feature
    )
    
    print(f"\n🎯 النتيجة النهائية: {'✅ نجح' if success else '❌ فشل'}")
    
    return success

if __name__ == "__main__":
    try:
        result = test_intervals()
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        sys.exit(1)
