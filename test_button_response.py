#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لاستجابة الأزرار
"""

import re

def test_callback_pattern():
    """اختبار pattern الخاص بـ CallbackQueryHandler"""
    
    # Pattern من الكود
    pattern = r"^(admin_panel|admin_stats|admin_list_users|admin_republish_list|admin_delete_mod_list|admin_restart_publishing|admin_clear_pending_queue|admin_confirm_clear_pending|admin_custom_links|admin_add_custom_link|admin_view_custom_links|admin_delete_custom_link|admin_toggle_preview|admin_force_enable_all_publish|admin_reset_published_mods|admin_broadcast_menu|admin_notifications_menu|admin_page_customization_menu|admin_delete_user_data|delete_user_by_id|delete_user_from_list|delete_orphaned_data|execute_delete_orphaned|delete_user_list_[0-9]+|confirm_delete_user_[0-9]+|execute_delete_user_[0-9]+|notif_create_new|notif_saved_templates|notif_templates_page_[0-9]+|notif_view_[0-9]+|notif_edit_[0-9]+|notif_delete_[0-9]+|notif_send_menu|notif_send_[0-9]+|notif_confirm_send_[0-9]+|notif_broadcast_history|notif_history_page_[0-9]+|notif_broadcast_details_[0-9]+|notif_detailed_stats|notif_add_button|notif_skip_button|notif_confirm_delete_[0-9]+|admin_view_customizations|admin_view_customizations_page_[0-9]+|back_to_admin|admin_view_mods_[0-9]+|admin_view_custom_links_page_[0-9]+|admin_confirm_delete_link_[0-9]+|admin_execute_delete_link_[0-9]+|reset_published_select_user|reset_published_select_user_[0-9]+|reset_published_user_[0-9]+|reset_published_all_users|confirm_reset_published_user_[0-9]+|confirm_reset_published_all|broadcast_target_(all|ar|en|specific)|broadcast_cancel|admin_list_users_[0-9]+|admin_view_user_[0-9]+|admin_delete_confirm_[0-9]+|admin_delete_execute_[0-9]+|republish_[0-9]+|delete_mod_[0-9]+|delete_mod_execute_[0-9]+)$"
    
    # أزرار حذف بيانات المستخدم للاختبار
    test_buttons = [
        "admin_delete_user_data",
        "delete_user_by_id", 
        "delete_user_from_list",
        "delete_orphaned_data",
        "execute_delete_orphaned",
        "delete_user_list_1",
        "delete_user_list_10",
        "confirm_delete_user_123456789",
        "execute_delete_user_987654321"
    ]
    
    print("🧪 اختبار استجابة أزرار حذف بيانات المستخدم:")
    print("="*60)
    
    compiled_pattern = re.compile(pattern)
    
    for button in test_buttons:
        match = compiled_pattern.match(button)
        status = "✅ يعمل" if match else "❌ لا يعمل"
        print(f"{status} - {button}")
    
    print("\n" + "="*60)
    
    # اختبار أزرار الإشعارات
    notif_buttons = [
        "notif_create_new",
        "notif_saved_templates",
        "notif_templates_page_1",
        "notif_view_123",
        "notif_edit_456",
        "notif_delete_789",
        "notif_send_menu",
        "notif_send_111",
        "notif_confirm_send_222",
        "notif_broadcast_history",
        "notif_history_page_2",
        "notif_broadcast_details_333",
        "notif_detailed_stats",
        "notif_add_button",
        "notif_skip_button",
        "notif_confirm_delete_444"
    ]
    
    print("🧪 اختبار استجابة أزرار الإشعارات:")
    print("="*60)
    
    for button in notif_buttons:
        match = compiled_pattern.match(button)
        status = "✅ يعمل" if match else "❌ لا يعمل"
        print(f"{status} - {button}")
    
    print("\n" + "="*60)
    
    # اختبار أزرار أخرى مهمة
    other_buttons = [
        "admin_panel",
        "admin_stats", 
        "admin_notifications_menu",
        "admin_page_customization_menu",
        "back_to_admin"
    ]
    
    print("🧪 اختبار استجابة أزرار أخرى مهمة:")
    print("="*60)
    
    for button in other_buttons:
        match = compiled_pattern.match(button)
        status = "✅ يعمل" if match else "❌ لا يعمل"
        print(f"{status} - {button}")
    
    print("\n" + "="*60)
    print("📊 ملخص الاختبار:")
    
    all_buttons = test_buttons + notif_buttons + other_buttons
    working_buttons = [btn for btn in all_buttons if compiled_pattern.match(btn)]
    not_working_buttons = [btn for btn in all_buttons if not compiled_pattern.match(btn)]
    
    print(f"✅ أزرار تعمل: {len(working_buttons)}")
    print(f"❌ أزرار لا تعمل: {len(not_working_buttons)}")
    
    if not_working_buttons:
        print(f"\n❌ الأزرار التي لا تعمل:")
        for btn in not_working_buttons:
            print(f"   - {btn}")
    
    print(f"\n📈 معدل النجاح: {(len(working_buttons)/len(all_buttons)*100):.1f}%")

def check_function_exists():
    """التحقق من وجود الدوال المطلوبة"""
    print("\n🔍 التحقق من وجود الدوال:")
    print("="*60)
    
    required_functions = [
        "admin_delete_user_data_menu",
        "admin_delete_user_by_id_handler", 
        "admin_delete_user_from_list_handler",
        "admin_confirm_delete_user_data",
        "admin_execute_delete_user_data",
        "admin_delete_orphaned_data_handler",
        "admin_execute_delete_orphaned_data",
        "delete_all_user_data",
        "cleanup_orphaned_data",
        "handle_admin_delete_user_input"
    ]
    
    try:
        import main
        
        for func_name in required_functions:
            if hasattr(main, func_name):
                print(f"✅ {func_name} - موجودة")
            else:
                print(f"❌ {func_name} - مفقودة")
                
    except ImportError as e:
        print(f"❌ خطأ في استيراد main.py: {e}")

def check_supabase_functions():
    """التحقق من دوال قاعدة البيانات"""
    print("\n🗄️ التحقق من دوال قاعدة البيانات:")
    print("="*60)
    
    required_db_functions = [
        "delete_user_ads_settings",
        "delete_user_tasks_settings",
        "delete_user_custom_download_links", 
        "delete_user_page_customization_settings",
        "delete_user_invitation_data",
        "get_user_ads_settings",
        "get_user_tasks_settings",
        "get_user_custom_download_links",
        "get_user_page_customization_settings",
        "get_user_invitation_data"
    ]
    
    try:
        import supabase_client
        
        for func_name in required_db_functions:
            if hasattr(supabase_client, func_name):
                print(f"✅ {func_name} - موجودة")
            else:
                print(f"❌ {func_name} - مفقودة")
                
    except ImportError as e:
        print(f"❌ خطأ في استيراد supabase_client.py: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار استجابة أزرار البوت")
    print("="*60)
    
    # اختبار patterns
    test_callback_pattern()
    
    # اختبار وجود الدوال
    check_function_exists()
    
    # اختبار دوال قاعدة البيانات
    check_supabase_functions()
    
    print("\n🎯 التوصيات:")
    print("="*60)
    print("1. إذا كانت جميع الأزرار تعمل في الاختبار، المشكلة قد تكون:")
    print("   - خطأ في تشغيل البوت")
    print("   - مشكلة في الاتصال بـ Telegram")
    print("   - خطأ في معالجة الاستثناءات")
    print("\n2. إذا كانت بعض الأزرار لا تعمل:")
    print("   - تحقق من إضافتها في pattern")
    print("   - تأكد من وجود المعالجات في handle_admin_actions")
    print("\n3. للتشخيص المتقدم:")
    print("   - شغل البوت مع logging.DEBUG")
    print("   - راقب السجلات عند الضغط على الأزرار")
    print("   - تحقق من رسائل الخطأ")

if __name__ == "__main__":
    main()
