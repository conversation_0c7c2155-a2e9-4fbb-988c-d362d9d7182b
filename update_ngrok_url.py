#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تحديث رابط ngrok تلقائياً في ملف .env
"""

import requests
import os
import re
import time
from datetime import datetime

def get_ngrok_url():
    """الحصول على رابط ngrok الحالي"""
    try:
        response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
        if response.status_code == 200:
            data = response.json()
            tunnels = data.get('tunnels', [])
            
            # البحث عن نفق HTTPS على المنفذ 5001
            for tunnel in tunnels:
                public_url = tunnel.get('public_url', '')
                config = tunnel.get('config', {})
                addr = config.get('addr', '')
                
                if public_url.startswith('https://') and ':5001' in addr:
                    return public_url
            
            # إذا لم يوجد HTTPS، البحث عن HTTP
            for tunnel in tunnels:
                public_url = tunnel.get('public_url', '')
                config = tunnel.get('config', {})
                addr = config.get('addr', '')
                
                if public_url.startswith('http://') and ':5001' in addr:
                    return public_url
                    
    except Exception as e:
        print(f"❌ خطأ في الحصول على رابط ngrok: {e}")
    
    return None

def update_env_file(new_url):
    """تحديث ملف .env برابط ngrok الجديد"""
    try:
        env_file = '.env'
        
        # قراءة محتوى الملف
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن السطر الذي يحتوي على WEB_SERVER_URL
        pattern = r'WEB_SERVER_URL=.*'
        new_line = f'WEB_SERVER_URL={new_url}'
        
        if re.search(pattern, content):
            # تحديث السطر الموجود
            updated_content = re.sub(pattern, new_line, content)
        else:
            # إضافة السطر في النهاية
            updated_content = content + f'\n{new_line}\n'
        
        # كتابة المحتوى المحدث
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print(f"✅ تم تحديث ملف .env برابط: {new_url}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث ملف .env: {e}")
        return False

def check_ngrok_status():
    """فحص حالة ngrok"""
    try:
        response = requests.get('http://localhost:4040/api/tunnels', timeout=3)
        return response.status_code == 200
    except:
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص حالة ngrok...")
    
    if not check_ngrok_status():
        print("❌ ngrok غير متصل!")
        print("\n💡 لتشغيل ngrok:")
        print("   1. افتح terminal جديد")
        print("   2. شغل الأمر: ngrok http 5001")
        print("   3. أو شغل ملف: start_ngrok.bat")
        print("\n⏳ انتظار تشغيل ngrok...")
        
        # انتظار حتى يتم تشغيل ngrok
        while not check_ngrok_status():
            time.sleep(2)
            print(".", end="", flush=True)
        
        print("\n✅ تم اكتشاف ngrok!")
    
    # الحصول على الرابط الجديد
    new_url = get_ngrok_url()
    
    if new_url:
        print(f"🔗 رابط ngrok الحالي: {new_url}")
        
        # تحديث ملف .env
        if update_env_file(new_url):
            print("✅ تم تحديث الإعدادات بنجاح!")
            print("\n🔄 أعد تشغيل البوت لتطبيق التغييرات.")
        else:
            print("❌ فشل في تحديث الإعدادات!")
    else:
        print("❌ لم يتم العثور على رابط ngrok صالح!")
        print("\n💡 تأكد من:")
        print("   1. تشغيل ngrok على المنفذ 5001")
        print("   2. الأمر الصحيح: ngrok http 5001")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البرنامج.")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    input("\n📱 اضغط Enter للخروج...")
