#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تحديث البوت لاستخدام الموقع الجديد
Bot Update Script for New Website Integration
"""

import os
import re
import shutil
from datetime import datetime

def backup_file(file_path):
    """إنشاء نسخة احتياطية من الملف"""
    if os.path.exists(file_path):
        backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(file_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        return backup_path
    return None

def update_main_py(website_url):
    """تحديث ملف main.py لاستخدام الموقع الجديد"""
    main_file = "main.py"
    
    if not os.path.exists(main_file):
        print(f"❌ ملف {main_file} غير موجود")
        return False
    
    # إنشاء نسخة احتياطية
    backup_file(main_file)
    
    # قراءة محتوى الملف
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة _build_mod_post_content وتحديثها
    pattern = r'(def _build_mod_post_content.*?)(https?://[^"\']+)(["\'])'
    
    def replace_url(match):
        before = match.group(1)
        old_url = match.group(2)
        quote = match.group(3)
        
        # إنشاء الرابط الجديد
        new_url = f"{website_url}/?id={{mod_id}}&lang={{lang}}&user_id={{user_id}}&channel={{channel_id}}"
        
        print(f"🔄 تحديث الرابط من: {old_url}")
        print(f"🔄 إلى: {new_url}")
        
        return f"{before}{new_url}{quote}"
    
    # تطبيق التحديث
    updated_content = re.sub(pattern, replace_url, content, flags=re.DOTALL)
    
    # البحث عن متغير WEB_SERVER_URL وتحديثه
    web_server_pattern = r'(WEB_SERVER_URL\s*=\s*["\'])([^"\']+)(["\'])'
    
    def replace_web_server_url(match):
        before = match.group(1)
        old_url = match.group(2)
        quote = match.group(3)
        
        print(f"🔄 تحديث WEB_SERVER_URL من: {old_url}")
        print(f"🔄 إلى: {website_url}")
        
        return f"{before}{website_url}{quote}"
    
    updated_content = re.sub(web_server_pattern, replace_web_server_url, updated_content)
    
    # كتابة المحتوى المحدث
    with open(main_file, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print(f"✅ تم تحديث {main_file} بنجاح")
    return True

def update_env_file(website_url):
    """تحديث ملف .env"""
    env_file = ".env"
    
    if not os.path.exists(env_file):
        print(f"❌ ملف {env_file} غير موجود")
        return False
    
    # إنشاء نسخة احتياطية
    backup_file(env_file)
    
    # قراءة محتوى الملف
    with open(env_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # تحديث أو إضافة WEB_SERVER_URL
    updated = False
    for i, line in enumerate(lines):
        if line.startswith('WEB_SERVER_URL='):
            old_url = line.split('=', 1)[1].strip()
            lines[i] = f"WEB_SERVER_URL={website_url}\n"
            print(f"🔄 تحديث WEB_SERVER_URL في .env من: {old_url}")
            print(f"🔄 إلى: {website_url}")
            updated = True
            break
    
    # إضافة WEB_SERVER_URL إذا لم يكن موجوداً
    if not updated:
        lines.append(f"\n# رابط الموقع الجديد\nWEB_SERVER_URL={website_url}\n")
        print(f"✅ تم إضافة WEB_SERVER_URL إلى .env: {website_url}")
    
    # كتابة المحتوى المحدث
    with open(env_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print(f"✅ تم تحديث {env_file} بنجاح")
    return True

def update_web_server_py(website_url):
    """تحديث ملف web_server.py إذا كان موجوداً"""
    web_server_file = "web_server.py"
    
    if not os.path.exists(web_server_file):
        print(f"ℹ️ ملف {web_server_file} غير موجود - تخطي")
        return True
    
    # إنشاء نسخة احتياطية
    backup_file(web_server_file)
    
    # قراءة محتوى الملف
    with open(web_server_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # إضافة تعليق يوضح أن الموقع الآن مستضاف خارجياً
    comment = f"""
# ملاحظة: تم نقل صفحة عرض المودات إلى موقع مستقل
# الموقع الجديد: {website_url}
# هذا الخادم المحلي لم يعد مطلوباً لعرض المودات
"""
    
    # إضافة التعليق في بداية الملف
    updated_content = comment + "\n" + content
    
    # كتابة المحتوى المحدث
    with open(web_server_file, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print(f"✅ تم تحديث {web_server_file} بتعليق توضيحي")
    return True

def create_update_summary(website_url):
    """إنشاء ملف ملخص التحديث"""
    summary_file = "WEBSITE_UPDATE_SUMMARY.md"
    
    summary_content = f"""# ملخص تحديث الموقع - Website Update Summary

## 📅 تاريخ التحديث
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 🌐 الموقع الجديد
**الرابط:** {website_url}

## 🔄 التغييرات المطبقة

### 1. تحديث main.py
- ✅ تم تحديث دالة `_build_mod_post_content`
- ✅ تم تحديث متغير `WEB_SERVER_URL`
- ✅ الروابط الآن تشير للموقع الجديد

### 2. تحديث .env
- ✅ تم تحديث/إضافة `WEB_SERVER_URL`
- ✅ البوت الآن يستخدم الموقع الجديد

### 3. تحديث web_server.py
- ✅ تم إضافة تعليق توضيحي
- ℹ️ الخادم المحلي لم يعد مطلوباً

## 🎯 المميزات الجديدة

### ✨ مميزات الموقع المستقل
- 🌍 **وصول عالمي**: يعمل من أي مكان بدون ngrok
- ⚡ **أداء أفضل**: استضافة مخصصة للويب
- 🔒 **أمان محسن**: حماية متقدمة للبيانات
- 📱 **تجاوب كامل**: يعمل على جميع الأجهزة
- 🎨 **تصميم محسن**: واجهة أكثر جاذبية

### 🚀 مميزات تقنية
- 📊 **مراقبة السجلات**: عرض وإدارة السجلات
- 🔧 **إعدادات متقدمة**: تحكم كامل في الموقع
- 🛡️ **حماية الملفات**: حماية الملفات الحساسة
- 📈 **تحسين SEO**: محركات البحث والفهرسة

## 🔗 روابط مهمة

### للمستخدمين
- **الموقع الرئيسي:** {website_url}
- **مثال على رابط مود:** {website_url}/?id=1&lang=ar&user_id=123&channel=456

### للمطورين
- **صفحة الإعداد:** {website_url}/deploy.php?setup=true
- **عارض السجلات:** {website_url}/logs.php?key=admin123
- **API:** {website_url}/api.php

## 📋 خطوات ما بعد التحديث

### 1. اختبار البوت
```bash
python start_bot_final.py
```

### 2. اختبار الروابط
- تأكد من عمل روابط المودات
- اختبر عرض الصور والتحميل
- تحقق من دعم اللغات

### 3. مراقبة الأداء
- راقب سجلات البوت
- تحقق من سجلات الموقع
- راقب استخدام الموارد

## ⚠️ ملاحظات مهمة

1. **النسخ الاحتياطية:** تم إنشاء نسخ احتياطية من جميع الملفات المحدثة
2. **الخادم المحلي:** لم يعد مطلوباً لعرض المودات
3. **ngrok:** لم يعد مطلوباً للوصول العام
4. **الأمان:** تأكد من تغيير كلمات المرور الافتراضية

## 🆘 استكشاف الأخطاء

### إذا لم تعمل الروابط
1. تحقق من رابط الموقع في .env
2. تأكد من رفع جميع ملفات الموقع
3. اختبر الموقع مباشرة في المتصفح

### إذا لم تظهر بيانات المودات
1. تحقق من إعدادات Supabase في الموقع
2. تأكد من صحة مفاتيح API
3. راجع سجلات الموقع

## 📞 الدعم

للحصول على المساعدة:
1. راجع ملف INSTALLATION.md في مجلد الموقع
2. تحقق من السجلات في {website_url}/logs.php
3. اختبر الموقع باستخدام {website_url}/deploy.php?setup=true

---

**تم التحديث بنجاح! 🎉**

البوت الآن يستخدم الموقع المستقل ولا يحتاج للخادم المحلي أو ngrok.
"""
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(summary_content)
    
    print(f"✅ تم إنشاء ملف الملخص: {summary_file}")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔄 سكريبت تحديث البوت لاستخدام الموقع الجديد")
    print("🌐 Bot Update Script for New Website Integration")
    print("=" * 60)
    
    # طلب رابط الموقع من المستخدم
    website_url = input("\n🌐 أدخل رابط الموقع الجديد (مثال: https://your-domain.com): ").strip()
    
    if not website_url:
        print("❌ يجب إدخال رابط الموقع")
        return
    
    # إزالة الشرطة المائلة في النهاية إن وجدت
    website_url = website_url.rstrip('/')
    
    print(f"\n🎯 سيتم تحديث البوت لاستخدام: {website_url}")
    
    # تأكيد من المستخدم
    confirm = input("\n❓ هل تريد المتابعة؟ (y/n): ").strip().lower()
    if confirm not in ['y', 'yes', 'نعم']:
        print("❌ تم إلغاء التحديث")
        return
    
    print("\n🚀 بدء عملية التحديث...")
    
    # تحديث الملفات
    success = True
    
    try:
        # تحديث main.py
        if not update_main_py(website_url):
            success = False
        
        # تحديث .env
        if not update_env_file(website_url):
            success = False
        
        # تحديث web_server.py
        if not update_web_server_py(website_url):
            success = False
        
        # إنشاء ملخص التحديث
        create_update_summary(website_url)
        
        if success:
            print("\n" + "=" * 60)
            print("✅ تم تحديث البوت بنجاح!")
            print("🎉 البوت الآن يستخدم الموقع الجديد")
            print("=" * 60)
            print("\n📋 الخطوات التالية:")
            print("1. اختبر البوت: python start_bot_final.py")
            print("2. تحقق من عمل الروابط")
            print("3. راجع ملف WEBSITE_UPDATE_SUMMARY.md للتفاصيل")
            print(f"4. اختبر الموقع: {website_url}/deploy.php?setup=true")
        else:
            print("\n❌ حدثت أخطاء أثناء التحديث")
            print("🔧 راجع الرسائل أعلاه وأصلح المشاكل")
            
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        print("🔧 تحقق من الملفات والصلاحيات")

if __name__ == "__main__":
    main()
