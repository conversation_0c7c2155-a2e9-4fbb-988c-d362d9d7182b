<?php
/**
 * اختبار سريع بالبيانات الصحيحة
 * Quick Test with Correct Database
 */

// البيانات الصحيحة
$SUPABASE_URL = "https://wnjbrdubiegjuycumuoh.supabase.co";
$SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InduamJyZHViaWVnanV5Y3VtdW9oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5NzAyNTcsImV4cCI6MjA2MjU0NjI1N30.iR00a54EoU4jwUrIQRxS2uqDjkcu_YovYWityww7zIc";
$TABLE_NAME = "mods";

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البيانات الصحيحة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .test-section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 14px; overflow-x: auto; }
        h1, h2, h3 { color: #333; }
        .highlight { background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 اختبار البيانات الصحيحة</h1>
        
        <div class="highlight">
            <h2>📋 البيانات المستخدمة:</h2>
            <p><strong>Database URL:</strong> <?php echo htmlspecialchars($SUPABASE_URL); ?></p>
            <p><strong>Table Name:</strong> <?php echo htmlspecialchars($TABLE_NAME); ?></p>
            <p><strong>API Key:</strong> <?php echo htmlspecialchars(substr($SUPABASE_KEY, 0, 50)) . '...'; ?></p>
        </div>
        
        <?php
        function makeRequest($url, $headers) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            return [
                'response' => $response,
                'http_code' => $http_code,
                'error' => $error
            ];
        }
        
        $headers = [
            'apikey: ' . $SUPABASE_KEY,
            'Authorization: Bearer ' . $SUPABASE_KEY,
            'Content-Type: application/json'
        ];
        
        echo '<div class="test-section">';
        echo '<h2>1. اختبار الاتصال الأساسي</h2>';
        
        $basic_url = $SUPABASE_URL . '/rest/v1/';
        $result = makeRequest($basic_url, $headers);
        
        echo '<p><strong>URL:</strong> ' . htmlspecialchars($basic_url) . '</p>';
        echo '<p><strong>HTTP Code:</strong> ' . $result['http_code'] . '</p>';
        
        if ($result['error']) {
            echo '<p class="error">❌ cURL Error: ' . htmlspecialchars($result['error']) . '</p>';
        } elseif ($result['http_code'] === 200) {
            echo '<p class="success">✅ الاتصال الأساسي ناجح!</p>';
        } else {
            echo '<p class="error">❌ فشل الاتصال - HTTP: ' . $result['http_code'] . '</p>';
        }
        echo '</div>';
        
        echo '<div class="test-section">';
        echo '<h2>2. اختبار جدول المودات</h2>';
        
        $table_url = $SUPABASE_URL . '/rest/v1/' . $TABLE_NAME . '?limit=5';
        $result = makeRequest($table_url, $headers);
        
        echo '<p><strong>URL:</strong> ' . htmlspecialchars($table_url) . '</p>';
        echo '<p><strong>HTTP Code:</strong> ' . $result['http_code'] . '</p>';
        
        if ($result['error']) {
            echo '<p class="error">❌ cURL Error: ' . htmlspecialchars($result['error']) . '</p>';
        } elseif ($result['http_code'] === 200) {
            echo '<p class="success">✅ جدول المودات متاح!</p>';
            
            $data = json_decode($result['response'], true);
            if ($data && is_array($data)) {
                echo '<p class="info">📊 عدد المودات: ' . count($data) . '</p>';
                
                if (!empty($data)) {
                    echo '<h3>المودات المتاحة:</h3>';
                    foreach ($data as $index => $mod) {
                        echo '<div class="code">';
                        echo '<strong>مود ' . ($index + 1) . ':</strong><br>';
                        echo 'ID: ' . ($mod['id'] ?? 'N/A') . '<br>';
                        echo 'Name: ' . htmlspecialchars($mod['name'] ?? 'N/A') . '<br>';
                        echo 'Version: ' . htmlspecialchars($mod['version'] ?? 'N/A') . '<br>';
                        echo 'Category: ' . htmlspecialchars($mod['category'] ?? 'N/A') . '<br>';
                        if (isset($mod['download_url'])) {
                            echo 'Download URL: ' . htmlspecialchars(substr($mod['download_url'], 0, 50)) . '...<br>';
                        }
                        echo '</div>';
                    }
                } else {
                    echo '<p class="warning">⚠️ الجدول فارغ - لا توجد مودات</p>';
                }
            } else {
                echo '<p class="error">❌ استجابة غير صحيحة</p>';
                echo '<div class="code">' . htmlspecialchars(substr($result['response'], 0, 300)) . '</div>';
            }
        } elseif ($result['http_code'] === 404) {
            echo '<p class="error">❌ جدول "' . $TABLE_NAME . '" غير موجود</p>';
            echo '<div class="code">' . htmlspecialchars($result['response']) . '</div>';
        } else {
            echo '<p class="error">❌ خطأ في الوصول للجدول - HTTP: ' . $result['http_code'] . '</p>';
            echo '<div class="code">' . htmlspecialchars(substr($result['response'], 0, 300)) . '</div>';
        }
        echo '</div>';
        
        // اختبار مود محدد
        echo '<div class="test-section">';
        echo '<h2>3. اختبار مود محدد</h2>';
        
        // جلب أول مود متاح
        $first_mod_id = null;
        if (isset($data) && !empty($data)) {
            $first_mod_id = $data[0]['id'] ?? null;
        }
        
        if ($first_mod_id) {
            echo '<p class="info">🎯 اختبار المود رقم: ' . $first_mod_id . '</p>';
            
            $mod_url = $SUPABASE_URL . '/rest/v1/' . $TABLE_NAME . '?id=eq.' . $first_mod_id;
            $result = makeRequest($mod_url, $headers);
            
            echo '<p><strong>URL:</strong> ' . htmlspecialchars($mod_url) . '</p>';
            echo '<p><strong>HTTP Code:</strong> ' . $result['http_code'] . '</p>';
            
            if ($result['error']) {
                echo '<p class="error">❌ cURL Error: ' . htmlspecialchars($result['error']) . '</p>';
            } elseif ($result['http_code'] === 200) {
                $mod_data = json_decode($result['response'], true);
                if ($mod_data && !empty($mod_data)) {
                    echo '<p class="success">✅ تم جلب بيانات المود بنجاح!</p>';
                    $mod = $mod_data[0];
                    echo '<div class="code">';
                    echo '<strong>تفاصيل المود الكاملة:</strong><br>';
                    echo json_encode($mod, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    echo '</div>';
                } else {
                    echo '<p class="warning">⚠️ لم يتم العثور على المود</p>';
                }
            } else {
                echo '<p class="error">❌ فشل في جلب المود - HTTP: ' . $result['http_code'] . '</p>';
            }
        } else {
            echo '<p class="warning">⚠️ لا توجد مودات للاختبار</p>';
        }
        echo '</div>';
        
        // اختبار API الجديد
        echo '<div class="test-section">';
        echo '<h2>4. اختبار API الجديد</h2>';
        
        $api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api_fixed.php?path=/test';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo '<p><strong>API URL:</strong> ' . htmlspecialchars($api_url) . '</p>';
        echo '<p><strong>HTTP Code:</strong> ' . $http_code . '</p>';
        
        if ($error) {
            echo '<p class="error">❌ cURL Error: ' . htmlspecialchars($error) . '</p>';
        } elseif ($http_code === 200) {
            echo '<p class="success">✅ API الجديد يعمل!</p>';
            $api_data = json_decode($response, true);
            if ($api_data) {
                echo '<div class="code">' . htmlspecialchars(json_encode($api_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . '</div>';
            }
        } else {
            echo '<p class="error">❌ فشل API - HTTP: ' . $http_code . '</p>';
            echo '<div class="code">' . htmlspecialchars(substr($response, 0, 300)) . '</div>';
        }
        echo '</div>';
        
        // ملخص النتائج
        echo '<div class="test-section" style="background: #e7f3ff;">';
        echo '<h2>📊 ملخص النتائج</h2>';
        
        $connection_ok = (isset($result) && $result['http_code'] === 200);
        $table_ok = (isset($data) && !empty($data));
        
        echo '<ul>';
        echo '<li>' . ($connection_ok ? '<span class="success">✅</span>' : '<span class="error">❌</span>') . ' الاتصال بقاعدة البيانات</li>';
        echo '<li>' . ($table_ok ? '<span class="success">✅</span>' : '<span class="error">❌</span>') . ' جدول المودات متاح</li>';
        echo '<li>' . (isset($first_mod_id) ? '<span class="success">✅</span>' : '<span class="warning">⚠️</span>') . ' بيانات المودات</li>';
        echo '</ul>';
        
        if ($connection_ok && $table_ok) {
            echo '<div class="success">';
            echo '<h3>🎉 ممتاز! كل شيء يعمل بشكل صحيح!</h3>';
            echo '<p><strong>الخطوة التالية:</strong></p>';
            echo '<ol>';
            echo '<li>استبدل <code>config.php</code> بـ <code>config_fixed.php</code></li>';
            echo '<li>استبدل <code>api.php</code> بـ <code>api_fixed.php</code></li>';
            echo '<li>استبدل <code>index.php</code> بـ <code>index_new.php</code> (مع تحديث اسم الجدول)</li>';
            echo '</ol>';
            echo '</div>';
            
            // روابط الاختبار
            echo '<h3>🔗 روابط الاختبار:</h3>';
            $base_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
            
            if ($first_mod_id) {
                echo '<ul>';
                echo '<li><a href="' . $base_url . '/index_new.php?id=' . $first_mod_id . '&lang=ar" target="_blank">🎮 اختبار الصفحة (عربي)</a></li>';
                echo '<li><a href="' . $base_url . '/index_new.php?id=' . $first_mod_id . '&lang=en" target="_blank">🎮 اختبار الصفحة (إنجليزي)</a></li>';
                echo '<li><a href="' . $base_url . '/api_fixed.php?path=/mod&id=' . $first_mod_id . '" target="_blank">🔌 اختبار API</a></li>';
                echo '</ul>';
            }
        } else {
            echo '<div class="error">';
            echo '<h3>❌ يحتاج إصلاح</h3>';
            if (!$connection_ok) {
                echo '<p>- مشكلة في الاتصال بقاعدة البيانات</p>';
            }
            if (!$table_ok) {
                echo '<p>- مشكلة في جدول المودات أو الجدول فارغ</p>';
            }
            echo '</div>';
        }
        
        echo '</div>';
        ?>
        
        <div style="text-align: center; margin-top: 20px;">
            <button onclick="location.reload()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">
                🔄 إعادة الاختبار
            </button>
        </div>
    </div>
</body>
</html>
