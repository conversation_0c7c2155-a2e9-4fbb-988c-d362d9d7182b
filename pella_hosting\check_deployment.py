#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص جاهزية ملفات البوت للنشر على Pella
Deployment readiness checker for Pella hosting
"""

import os
import json
import sys
from pathlib import Path

def check_deployment_readiness():
    """فحص جاهزية الملفات للنشر"""
    
    print("🔍 فحص جاهزية ملفات البوت للنشر على Pella")
    print("="*60)
    
    # قائمة الملفات الأساسية المطلوبة
    required_files = [
        "main.py",
        "start_hosting.py", 
        "hosting_config.py",
        "supabase_client.py",
        "requirements.txt",
        "Procfile",
        "config.json"
    ]
    
    # قائمة الملفات المهمة
    important_files = [
        "web_server.py",
        "telegram_web_app.py",
        "notifications.py",
        "runtime.txt"
    ]
    
    # قائمة المجلدات المطلوبة
    required_dirs = [
        "htdocs"
    ]
    
    issues = []
    warnings = []
    
    print("📁 فحص الملفات الأساسية:")
    for file_name in required_files:
        file_path = Path(file_name)
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"   ✅ {file_name} ({size:,} bytes)")
        else:
            print(f"   ❌ {file_name} - مفقود!")
            issues.append(f"ملف مطلوب مفقود: {file_name}")
    
    print(f"\n📁 فحص الملفات المهمة:")
    for file_name in important_files:
        file_path = Path(file_name)
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"   ✅ {file_name} ({size:,} bytes)")
        else:
            print(f"   ⚠️ {file_name} - مفقود")
            warnings.append(f"ملف مهم مفقود: {file_name}")
    
    print(f"\n📂 فحص المجلدات:")
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists() and dir_path.is_dir():
            file_count = len(list(dir_path.rglob('*')))
            print(f"   ✅ {dir_name}/ ({file_count} ملف)")
        else:
            print(f"   ❌ {dir_name}/ - مفقود!")
            issues.append(f"مجلد مطلوب مفقود: {dir_name}")
    
    # فحص ملف requirements.txt
    print(f"\n📦 فحص متطلبات Python:")
    req_file = Path("requirements.txt")
    if req_file.exists():
        with open(req_file, 'r') as f:
            requirements = f.read().strip().split('\n')
            requirements = [r.strip() for r in requirements if r.strip() and not r.startswith('#')]
            print(f"   ✅ {len(requirements)} مكتبة مطلوبة")
            for req in requirements[:5]:  # عرض أول 5 مكتبات
                print(f"      - {req}")
            if len(requirements) > 5:
                print(f"      ... و {len(requirements) - 5} مكتبات أخرى")
    else:
        print(f"   ❌ requirements.txt مفقود!")
        issues.append("ملف requirements.txt مفقود")
    
    # فحص ملف Procfile
    print(f"\n⚙️ فحص إعدادات الاستضافة:")
    procfile = Path("Procfile")
    if procfile.exists():
        with open(procfile, 'r') as f:
            content = f.read().strip()
            print(f"   ✅ Procfile: {content}")
    else:
        print(f"   ❌ Procfile مفقود!")
        issues.append("ملف Procfile مفقود")
    
    # فحص ملف config.json
    config_file = Path("config.json")
    if config_file.exists():
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                print(f"   ✅ config.json صالح ({len(config)} إعداد)")
        except json.JSONDecodeError:
            print(f"   ❌ config.json غير صالح!")
            issues.append("ملف config.json غير صالح")
    else:
        print(f"   ⚠️ config.json مفقود")
        warnings.append("ملف config.json مفقود")
    
    # فحص ملف .env
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r') as f:
            env_lines = [line.strip() for line in f.readlines() if line.strip() and not line.startswith('#')]
            print(f"   ✅ .env ({len(env_lines)} متغير)")
    else:
        print(f"   ⚠️ .env مفقود (سيتم استخدام متغيرات البيئة)")
        warnings.append("ملف .env مفقود")
    
    # حساب الحجم الإجمالي
    print(f"\n📊 إحصائيات الحزمة:")
    total_size = 0
    file_count = 0
    
    for file_path in Path('.').rglob('*'):
        if file_path.is_file():
            total_size += file_path.stat().st_size
            file_count += 1
    
    size_mb = total_size / (1024 * 1024)
    print(f"   📦 الحجم الإجمالي: {size_mb:.2f} MB")
    print(f"   📁 عدد الملفات: {file_count:,} ملف")
    
    # عرض النتائج
    print(f"\n" + "="*60)
    print(f"📋 تقرير الفحص:")
    
    if not issues:
        print(f"   🎉 جميع الملفات الأساسية موجودة!")
    else:
        print(f"   ❌ {len(issues)} مشكلة حرجة:")
        for issue in issues:
            print(f"      - {issue}")
    
    if warnings:
        print(f"   ⚠️ {len(warnings)} تحذير:")
        for warning in warnings:
            print(f"      - {warning}")
    
    # تقييم الجاهزية
    if not issues:
        if not warnings:
            print(f"\n✅ الحزمة جاهزة تماماً للنشر على Pella!")
            print(f"🚀 يمكنك رفع الملفات والبدء في النشر.")
        else:
            print(f"\n⚠️ الحزمة جاهزة للنشر مع بعض التحذيرات.")
            print(f"🚀 يمكنك المتابعة، لكن راجع التحذيرات أولاً.")
        return True
    else:
        print(f"\n❌ الحزمة غير جاهزة للنشر!")
        print(f"🔧 يرجى حل المشاكل الحرجة أولاً.")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        is_ready = check_deployment_readiness()
        
        print(f"\n" + "="*60)
        if is_ready:
            print(f"🎯 الخطوة التالية: ارفع الملفات إلى استضافة Pella")
            print(f"📖 راجع QUICK_DEPLOY_GUIDE.md للتعليمات السريعة")
        else:
            print(f"🔧 الخطوة التالية: حل المشاكل المذكورة أعلاه")
            print(f"📖 راجع README_DEPLOYMENT.md للمساعدة")
        
        return 0 if is_ready else 1
        
    except Exception as e:
        print(f"❌ خطأ في فحص الملفات: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
