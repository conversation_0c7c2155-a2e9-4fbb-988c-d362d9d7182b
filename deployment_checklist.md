# قائمة التحقق للنشر - Deployment Checklist

## ✅ قبل النشر على الاستضافة

### 1. ملفات يجب رفعها
- [ ] `mod_details.html` (المحدث)
- [ ] `ad_tasks.js` (إذا كان موجود)
- [ ] أي ملفات CSS أو JavaScript إضافية

### 2. التحقق من الروابط
- [ ] تأكد من أن الرابط `https://cvkrtjvrg.blogspot.com/p/mod-details.html` يعمل
- [ ] اختبر الرابط مع معاملات وهمية:
  ```
  https://cvkrtjvrg.blogspot.com/p/mod-details.html?id=1&lang=ar&user_id=123&channel=-100123
  ```

### 3. اختبار قاعدة البيانات
- [ ] تأكد من أن Supabase يعمل بشكل صحيح
- [ ] اختبر الاتصال بقاعدة البيانات
- [ ] تحقق من وجود مودات في الجدول `minemods`

### 4. اختبار البوت
- [ ] شغل البوت محلياً أولاً
- [ ] اختبر إرسال مود واحد لمستخدم تجريبي
- [ ] تأكد من ظهور زر "عرض التفاصيل" بدلاً من "تحميل"
- [ ] اضغط على الزر وتأكد من فتح الصفحة بشكل صحيح

## ✅ بعد النشر على الاستضافة

### 1. اختبار شامل
- [ ] اختبر مع مستخدم عربي
- [ ] اختبر مع مستخدم إنجليزي
- [ ] اختبر مع مودات مختلفة
- [ ] اختبر معالجة الأخطاء (مود غير موجود)

### 2. مراقبة الأداء
- [ ] راقب سجلات البوت للأخطاء
- [ ] تحقق من سجلات الخادم
- [ ] راقب استجابة قاعدة البيانات

### 3. تجربة المستخدم
- [ ] اطلب من مستخدمين حقيقيين اختبار النظام
- [ ] اجمع ملاحظات حول سهولة الاستخدام
- [ ] تأكد من سرعة تحميل الصفحة

## 🚨 مشاكل محتملة وحلولها

### المشكلة: الصفحة لا تفتح
**الحل:**
- تحقق من رفع الملف بشكل صحيح
- تأكد من أن الرابط صحيح في الكود
- اختبر الرابط مباشرة في المتصفح

### المشكلة: البيانات لا تظهر
**الحل:**
- تحقق من اتصال Supabase
- تأكد من صحة مفاتيح API
- راجع سجلات المتصفح للأخطاء

### المشكلة: اللغة لا تتغير
**الحل:**
- تحقق من معامل `lang` في الرابط
- تأكد من تطبيق الترجمة في JavaScript
- راجع كود `applyTranslation()`

### المشكلة: الصور لا تظهر
**الحل:**
- تحقق من روابط الصور في قاعدة البيانات
- تأكد من أن الصور متاحة عبر الإنترنت
- اختبر الصورة الافتراضية

## 📞 جهات الاتصال للدعم

- **مطور البوت**: @Kim880198
- **دعم Supabase**: https://supabase.com/support
- **دعم Telegram Bots**: https://core.telegram.org/bots

## 📝 ملاحظات إضافية

1. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية من الملفات القديمة
2. **المراقبة**: راقب النظام لمدة 24 ساعة بعد النشر
3. **التحديثات**: كن مستعداً لإجراء تحديثات سريعة إذا لزم الأمر
4. **الوثائق**: احتفظ بهذه الوثائق للرجوع إليها لاحقاً

## 🎉 علامات النجاح

- [ ] المستخدمون يتلقون رسائل مع زر "عرض التفاصيل"
- [ ] الصفحة تفتح بسرعة وتعرض البيانات بشكل صحيح
- [ ] اللغة تتغير حسب إعدادات المستخدم
- [ ] زر التحميل يعمل بشكل صحيح
- [ ] لا توجد أخطاء في السجلات
- [ ] المستخدمون راضون عن التجربة الجديدة
