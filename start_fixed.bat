@echo off
chcp 65001 >nul
echo ========================================
echo 🤖 بوت نشر مودات ماين كرافت - إصدار محسن
echo 🚀 Minecraft Mods Bot - Enhanced Edition
echo ========================================
echo.

echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)
echo ✅ Python متوفر

echo.
echo 🔍 فحص المتطلبات...
if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)

if not exist ".env" (
    echo ❌ ملف .env غير موجود
    echo يرجى إنشاء ملف .env مع إعدادات البوت
    pause
    exit /b 1
)

echo ✅ الملفات المطلوبة موجودة

echo.
echo 🔧 تثبيت المتطلبات...
pip install -r requirements.txt

echo.
echo 🧪 اختبار الاتصال...
python test_connection.py

echo.
echo 🚀 بدء تشغيل البوت المحسن...
echo ⏹️ اضغط Ctrl+C لإيقاف البوت
echo ========================================
python run_bot_fixed.py

pause
