# 🔑 دليل إصلاح مشكلة المفاتيح - Supabase Keys Fix

## 🚨 المشكلة: "Access denied. Invalid key."

### 🔍 السبب:
المشكلة تحدث عندما يكون هناك خطأ في استخدام مفاتيح Supabase أو عدم وجود الصلاحيات المناسبة.

---

## ✅ الحلول المطبقة:

### 1️⃣ تحديث ملف `api.php`:
```php
// تم تحديث constructor لاستخدام المفتاح الصحيح
public function __construct($url, $key, $service_key = null) {
    $this->url = $url;
    $this->key = $key;
    $this->service_key = $service_key;
    
    // استخدام المفتاح الصحيح للـ Authorization
    $auth_key = $this->service_key ? $this->service_key : $this->key;
    
    $this->headers = [
        'apikey: ' . $auth_key,
        'Authorization: Bearer ' . $auth_key,
        'Content-Type: application/json',
        'Accept: application/json',
        'Prefer: return=representation'
    ];
}
```

### 2️⃣ تحديث ملف `test_curl.php`:
```php
// تحميل الإعدادات من config.php
define('INCLUDED', true);
require_once 'config.php';

// إعدادات قاعدة البيانات من ملف الإعدادات
$db_config = getConfig('database')['supabase'];
$SUPABASE_URL = $db_config['url'];
$SUPABASE_KEY = $db_config['key'];
$SUPABASE_SERVICE_KEY = $db_config['service_key'];

// استخدام service key للوصول الكامل
$auth_key = $SUPABASE_SERVICE_KEY ? $SUPABASE_SERVICE_KEY : $SUPABASE_KEY;
$headers = array(
    'apikey: ' . $auth_key,
    'Authorization: Bearer ' . $auth_key,
    'Content-Type: application/json',
    'Prefer: return=representation'
);
```

### 3️⃣ إضافة ملف `test_keys.php`:
ملف جديد لاختبار كلا المفتاحين (anon و service) بشكل منفصل.

---

## 🧪 اختبار الحلول:

### الخطوة 1: اختبار المفاتيح
```
https://yoursite.com/test_keys.php
```

### الخطوة 2: اختبار cURL المحدث
```
https://yoursite.com/test_curl.php
```

### الخطوة 3: اختبار API
```
https://yoursite.com/api.php?path=/test
```

### الخطوة 4: اختبار شامل
```
https://yoursite.com/test_website.php
```

---

## 🔧 إعدادات Supabase المطلوبة:

### في لوحة تحكم Supabase:

#### 1️⃣ إعدادات API:
- اذهب إلى **Settings** → **API**
- انسخ **URL** و **anon public** و **service_role secret**

#### 2️⃣ إعدادات قاعدة البيانات:
- اذهب إلى **Table Editor**
- تأكد من وجود جدول `mods`
- تحقق من الصلاحيات (Policies)

#### 3️⃣ إعدادات RLS (Row Level Security):
```sql
-- إذا كان RLS مفعل، أضف هذه السياسة
CREATE POLICY "Allow public read access" ON public.mods
FOR SELECT USING (true);
```

---

## 🛠️ خطوات الإصلاح اليدوي:

### إذا استمرت المشكلة:

#### 1️⃣ تحقق من المفاتيح في `config.php`:
```php
// تأكد من أن هذه القيم صحيحة
define('SUPABASE_URL', 'https://ytqxxodyecdeosnqoure.supabase.co');
define('SUPABASE_KEY', 'YOUR_ANON_KEY_HERE');
define('SUPABASE_SERVICE_KEY', 'YOUR_SERVICE_KEY_HERE');
```

#### 2️⃣ تعطيل RLS مؤقتاً:
```sql
-- في SQL Editor في Supabase
ALTER TABLE public.mods DISABLE ROW LEVEL SECURITY;
```

#### 3️⃣ إنشاء سياسة عامة:
```sql
-- إذا كنت تريد الإبقاء على RLS
CREATE POLICY "Public read access" ON public.mods
FOR SELECT TO anon USING (true);
```

---

## 📊 نتائج الاختبار المتوقعة:

### ✅ نجح الاختبار:
```json
{
  "status": "success",
  "message": "Database connection successful",
  "data": [...]
}
```

### ❌ فشل الاختبار:
```json
{
  "code": "42501",
  "message": "permission denied for table mods"
}
```

---

## 🎯 الحلول السريعة:

### الحل 1: استخدام Service Key
```php
// في config.php، تأكد من وجود service key صحيح
define('SUPABASE_SERVICE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...');
```

### الحل 2: تعطيل RLS
```sql
-- في Supabase SQL Editor
ALTER TABLE public.mods DISABLE ROW LEVEL SECURITY;
```

### الحل 3: إضافة سياسة عامة
```sql
-- في Supabase SQL Editor
CREATE POLICY "allow_all" ON public.mods FOR ALL USING (true);
```

---

## 📞 للدعم:

### إذا استمرت المشكلة:
1. **تحقق من**: `test_keys.php` لفهم أي مفتاح يعمل
2. **راجع**: إعدادات RLS في Supabase
3. **اختبر**: الاتصال المباشر مع Supabase REST API
4. **تأكد**: من صحة المفاتيح في لوحة تحكم Supabase

### معلومات مفيدة:
- **Anon Key**: للوصول العام (محدود)
- **Service Key**: للوصول الكامل (خطير - استخدم بحذر)
- **RLS**: نظام أمان على مستوى الصفوف
- **Policies**: قواعد الوصول للجداول

---

## ✅ قائمة التحقق:

- [ ] تحديث `api.php` ✅
- [ ] تحديث `test_curl.php` ✅
- [ ] إضافة `test_keys.php` ✅
- [ ] اختبار المفاتيح
- [ ] تحقق من إعدادات Supabase
- [ ] اختبار API النهائي

**🎯 بعد تطبيق هذه الحلول، يجب أن تعمل جميع الاختبارات بنجاح!**
