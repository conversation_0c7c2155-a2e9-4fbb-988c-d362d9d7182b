#!/usr/bin/env python3
"""
اختبار إصلاح الأزرار
Test Button Fix

اختبار شامل لجميع أنواع الأزرار في البوت
Comprehensive test for all button types in the bot
"""

import sys
import traceback
import os
from pathlib import Path

def test_button_creation():
    """اختبار إنشاء الأزرار"""
    print("🔍 Testing button creation...")
    
    try:
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        
        # اختبار أزرار مختلفة
        buttons = []
        
        # 1. زر URL عادي
        try:
            url_button = InlineKeyboardButton("🌐 URL Button", url="https://example.com")
            buttons.append([url_button])
            print("✅ URL button created successfully")
        except Exception as e:
            print(f"❌ URL button failed: {e}")
            return False
        
        # 2. زر WebApp
        try:
            webapp_button = InlineKeyboardButton("🎮 WebApp Button", web_app={"url": "https://example.com"})
            buttons.append([webapp_button])
            print("✅ WebApp button created successfully")
        except Exception as e:
            print(f"❌ WebApp button failed: {e}")
            return False
        
        # 3. زر Callback
        try:
            callback_button = InlineKeyboardButton("⚙️ Callback Button", callback_data="test_callback")
            buttons.append([callback_button])
            print("✅ Callback button created successfully")
        except Exception as e:
            print(f"❌ Callback button failed: {e}")
            return False
        
        # 4. إنشاء Keyboard
        try:
            keyboard = InlineKeyboardMarkup(buttons)
            print("✅ InlineKeyboardMarkup created successfully")
            print(f"   Total buttons: {len(buttons)}")
            return True
        except Exception as e:
            print(f"❌ InlineKeyboardMarkup failed: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import telegram modules: {e}")
        return False

def test_mod_post_content():
    """اختبار إنشاء محتوى منشور المود"""
    print("\n🔍 Testing mod post content creation...")
    
    try:
        # إضافة مسار المشروع
        sys.path.insert(0, str(Path(__file__).parent))
        
        # استيراد الدوال المطلوبة
        from main import _build_mod_post_content
        
        # بيانات مود تجريبية
        test_mod = {
            "id": "test-mod-123",
            "title": "Test Mod",
            "description": "This is a test mod for button testing",
            "image_url": "https://example.com/image.jpg",
            "download_url": "https://example.com/download.zip"
        }
        
        # اختبار إنشاء المحتوى
        try:
            content = _build_mod_post_content(test_mod, "ar", "123456789")
            
            if content and "caption" in content:
                print("✅ Mod post content created successfully")
                print(f"   Caption length: {len(content['caption'])}")
                
                if content.get("reply_markup"):
                    print("✅ Reply markup created")
                    # فحص الأزرار
                    markup = content["reply_markup"]
                    if hasattr(markup, 'inline_keyboard'):
                        button_count = sum(len(row) for row in markup.inline_keyboard)
                        print(f"   Total buttons: {button_count}")
                        
                        # فحص أنواع الأزرار
                        for row in markup.inline_keyboard:
                            for button in row:
                                if hasattr(button, 'web_app') and button.web_app:
                                    print(f"   ✅ WebApp button: {button.text}")
                                elif hasattr(button, 'url') and button.url:
                                    print(f"   ✅ URL button: {button.text}")
                                elif hasattr(button, 'callback_data') and button.callback_data:
                                    print(f"   ✅ Callback button: {button.text}")
                else:
                    print("⚠️ No reply markup created")
                
                return True
            else:
                print("❌ Invalid content structure")
                return False
                
        except Exception as e:
            print(f"❌ Mod post content creation failed: {e}")
            traceback.print_exc()
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import main module: {e}")
        return False

def test_env_settings():
    """اختبار إعدادات البيئة"""
    print("\n🔍 Testing environment settings...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        # فحص الإعدادات المهمة
        web_server_url = os.environ.get("WEB_SERVER_URL")
        disable_web_app = os.environ.get("DISABLE_WEB_APP", "false").lower() == "true"
        
        print(f"   WEB_SERVER_URL: {web_server_url}")
        print(f"   DISABLE_WEB_APP: {disable_web_app}")
        
        if web_server_url:
            if web_server_url.startswith('https://'):
                print("✅ HTTPS URL detected - WebApp should work")
            elif web_server_url.startswith('http://'):
                print("⚠️ HTTP URL detected - WebApp may not work in Telegram")
            else:
                print("❌ Invalid URL format")
                return False
        else:
            print("⚠️ No WEB_SERVER_URL set")
        
        if disable_web_app:
            print("⚠️ WebApp is disabled - only download buttons will be used")
        else:
            print("✅ WebApp is enabled")
        
        return True
        
    except Exception as e:
        print(f"❌ Environment test failed: {e}")
        return False

def test_url_validation():
    """اختبار صحة الروابط"""
    print("\n🔍 Testing URL validation...")
    
    try:
        import re
        
        # أنماط الروابط الصحيحة
        valid_patterns = [
            r'^https://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',  # HTTPS
            r'^http://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',   # HTTP
        ]
        
        # روابط تجريبية
        test_urls = [
            "https://3cf6-41-188-116-40.ngrok-free.app",
            "http://10.8.232.232:5001",
            "https://example.com",
            "invalid-url",
            "",
            None
        ]
        
        for url in test_urls:
            if url is None:
                print(f"   ❌ URL is None")
                continue
                
            if not url:
                print(f"   ❌ URL is empty")
                continue
            
            is_valid = any(re.match(pattern, url) for pattern in valid_patterns)
            status = "✅" if is_valid else "❌"
            print(f"   {status} {url}")
        
        return True
        
    except Exception as e:
        print(f"❌ URL validation test failed: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 Button Fix Comprehensive Test")
    print("=" * 60)
    
    tests = [
        ("Button Creation", test_button_creation),
        ("Environment Settings", test_env_settings),
        ("URL Validation", test_url_validation),
        ("Mod Post Content", test_mod_post_content),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"💥 {test_name} test crashed: {e}")
            results[test_name] = False
    
    # تقرير النتائج
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All tests PASSED! Button system should work correctly.")
        print("✅ You can now run the bot without Button_type_invalid errors.")
    else:
        print("❌ Some tests FAILED! Please check the issues above.")
        print("🔧 Fix the failing tests before running the bot.")
    
    print("\n💡 Recommendations:")
    if results.get("Environment Settings", False):
        web_server_url = os.environ.get("WEB_SERVER_URL", "")
        if web_server_url.startswith('https://'):
            print("   • WebApp buttons should work fine with HTTPS")
        else:
            print("   • Consider using HTTPS for better WebApp support")
    
    if not results.get("Mod Post Content", False):
        print("   • Check main.py for any syntax errors")
        print("   • Ensure all imports are correct")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        traceback.print_exc()
        sys.exit(1)
