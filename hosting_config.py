#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات خاصة للاستضافة المجانية
Hosting Configuration for Free Hosting Services
"""

import os
import logging

logger = logging.getLogger(__name__)

class HostingConfig:
    """إعدادات محسنة للاستضافة المجانية"""
    
    def __init__(self):
        self.load_hosting_config()
    
    def load_hosting_config(self):
        """تحميل إعدادات الاستضافة"""
        # بيانات الاتصال الصحيحة للاستضافة
        self.BOT_TOKEN = "7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4"
        self.ADMIN_CHAT_ID = "7513880877"
        self.ADMIN_USERNAME = "Kim880198"
        
        # بيانات قاعدة البيانات الصحيحة
        self.SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co"
        self.SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4"
        self.TABLE_NAME = "mods"  # اسم الجدول الصحيح
        
        # إعدادات محسنة للاستضافة المجانية
        self.OPTIMIZATION_ENABLED = True
        self.LOW_RESOURCE_MODE = True
        self.MAX_MEMORY_MB = 100  # حد الذاكرة للاستضافة المجانية
        self.MAX_CPU_USAGE = 0.1  # حد المعالج
        
        # إعدادات الشبكة المحسنة
        self.REQUEST_TIMEOUT = 30
        self.CONNECTION_POOL_SIZE = 5
        self.MAX_RETRIES = 3
        
        # تطبيق الإعدادات على متغيرات البيئة
        self.apply_to_environment()
    
    def apply_to_environment(self):
        """تطبيق الإعدادات على متغيرات البيئة"""
        os.environ["BOT_TOKEN"] = self.BOT_TOKEN
        os.environ["ADMIN_CHAT_ID"] = self.ADMIN_CHAT_ID
        os.environ["ADMIN_USERNAME"] = self.ADMIN_USERNAME
        os.environ["SUPABASE_URL"] = self.SUPABASE_URL
        os.environ["SUPABASE_KEY"] = self.SUPABASE_KEY
        
        # إعدادات التحسين
        os.environ["OPTIMIZATION_ENABLED"] = "true"
        os.environ["LOW_RESOURCE_MODE"] = "true"
        os.environ["REQUEST_TIMEOUT"] = str(self.REQUEST_TIMEOUT)
        
        logger.info("✅ تم تطبيق إعدادات الاستضافة بنجاح")
    
    def get_supabase_headers(self):
        """الحصول على headers صحيحة لـ Supabase"""
        return {
            'apikey': self.SUPABASE_KEY,
            'Authorization': f'Bearer {self.SUPABASE_KEY}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }
    
    def get_database_url(self, endpoint=""):
        """بناء رابط قاعدة البيانات"""
        base_url = f"{self.SUPABASE_URL}/rest/v1/{self.TABLE_NAME}"
        if endpoint:
            return f"{base_url}?{endpoint}"
        return base_url
    
    def test_connection(self):
        """اختبار الاتصال مع قاعدة البيانات"""
        try:
            import requests
            
            # اختبار الاتصال الأساسي
            url = f"{self.SUPABASE_URL}/rest/v1/"
            response = requests.get(url, headers=self.get_supabase_headers(), timeout=10)
            
            if response.status_code == 200:
                logger.info("✅ اختبار الاتصال الأساسي نجح")
                
                # اختبار جدول المودات
                mods_url = self.get_database_url("limit=1")
                mods_response = requests.get(mods_url, headers=self.get_supabase_headers(), timeout=10)
                
                if mods_response.status_code == 200:
                    logger.info("✅ اختبار جدول المودات نجح")
                    return True
                else:
                    logger.error(f"❌ فشل اختبار جدول المودات: {mods_response.status_code}")
                    logger.error(f"Response: {mods_response.text}")
                    return False
            else:
                logger.error(f"❌ فشل الاتصال الأساسي: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار الاتصال: {e}")
            return False
    
    def print_config_summary(self):
        """طباعة ملخص الإعدادات"""
        print("\n" + "="*60)
        print("🚀 إعدادات الاستضافة المجانية")
        print("="*60)
        print(f"🤖 Bot Token: {self.BOT_TOKEN[:20]}...")
        print(f"👤 Admin ID: {self.ADMIN_CHAT_ID}")
        print(f"🗄️ Database: {self.SUPABASE_URL}")
        print(f"📊 Table: {self.TABLE_NAME}")
        print(f"⚡ Optimization: {'✅ Enabled' if self.OPTIMIZATION_ENABLED else '❌ Disabled'}")
        print(f"💾 Low Resource Mode: {'✅ Enabled' if self.LOW_RESOURCE_MODE else '❌ Disabled'}")
        print(f"🔗 Request Timeout: {self.REQUEST_TIMEOUT}s")
        print("="*60)

# إنشاء مثيل الإعدادات
hosting_config = HostingConfig()

def get_hosting_config():
    """الحصول على إعدادات الاستضافة"""
    return hosting_config

def test_hosting_setup():
    """اختبار إعداد الاستضافة"""
    print("🧪 اختبار إعداد الاستضافة...")
    
    config = get_hosting_config()
    config.print_config_summary()
    
    print("\n🔍 اختبار الاتصال...")
    if config.test_connection():
        print("✅ جميع الاختبارات نجحت! البوت جاهز للاستضافة.")
        return True
    else:
        print("❌ فشل في الاختبار. يرجى مراجعة الإعدادات.")
        return False

if __name__ == "__main__":
    test_hosting_setup()
