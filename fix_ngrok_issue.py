#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة ngrok وخوادم الويب
Fix ngrok and web servers issues
"""

import os
import sys
import time
import subprocess
import threading
import requests
import platform

def kill_existing_processes():
    """إيقاف العمليات الموجودة"""
    print("🔄 إيقاف العمليات الموجودة...")
    
    try:
        if platform.system() == "Windows":
            # إيقاف ngrok
            subprocess.run(['taskkill', '/IM', 'ngrok.exe', '/F'], 
                         capture_output=True, timeout=5)
            
            # إيقاف Python processes على المنافذ المطلوبة
            for port in [5000, 5001]:
                try:
                    result = subprocess.run(['netstat', '-ano'], 
                                          capture_output=True, text=True, timeout=10)
                    for line in result.stdout.split('\n'):
                        if f':{port}' in line and 'LISTENING' in line:
                            parts = line.split()
                            if len(parts) >= 5:
                                pid = parts[-1]
                                subprocess.run(['taskkill', '/PID', pid, '/F'], 
                                             capture_output=True, timeout=5)
                except:
                    pass
        else:
            # Linux/Mac
            subprocess.run(['pkill', 'ngrok'], capture_output=True, timeout=5)
            for port in [5000, 5001]:
                try:
                    result = subprocess.run(['lsof', f'-ti:{port}'], 
                                          capture_output=True, text=True, timeout=5)
                    if result.stdout.strip():
                        pids = result.stdout.strip().split('\n')
                        for pid in pids:
                            subprocess.run(['kill', '-9', pid], timeout=5)
                except:
                    pass
        
        print("✅ تم إيقاف العمليات الموجودة")
        time.sleep(2)
        
    except Exception as e:
        print(f"⚠️ خطأ في إيقاف العمليات: {e}")

def start_web_server(port=5000):
    """تشغيل خادم الويب"""
    try:
        from web_server import run_web_server
        print(f"🌐 تشغيل خادم الويب على المنفذ {port}...")
        run_web_server(port)
    except Exception as e:
        print(f"❌ خطأ في تشغيل خادم الويب: {e}")

def start_telegram_web_app(port=5001):
    """تشغيل خادم Telegram Web App"""
    try:
        from telegram_web_app import run_telegram_web_app
        print(f"📱 تشغيل خادم Telegram Web App على المنفذ {port}...")
        run_telegram_web_app(port)
    except Exception as e:
        print(f"❌ خطأ في تشغيل خادم Telegram Web App: {e}")

def test_local_servers():
    """اختبار الخوادم المحلية"""
    print("🧪 اختبار الخوادم المحلية...")
    
    servers = [
        ("http://localhost:5000", "خادم الويب الأساسي"),
        ("http://localhost:5001", "خادم Telegram Web App"),
        ("http://127.0.0.1:5000", "خادم الويب الأساسي (127.0.0.1)"),
        ("http://127.0.0.1:5001", "خادم Telegram Web App (127.0.0.1)")
    ]
    
    working_servers = []
    
    for url, name in servers:
        try:
            response = requests.get(f"{url}/api/test", timeout=5)
            if response.status_code == 200:
                print(f"✅ {name} يعمل: {url}")
                working_servers.append(url)
            else:
                print(f"⚠️ {name} يستجيب لكن بخطأ: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {name} لا يعمل: {url}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {name}: {e}")
    
    return working_servers

def start_ngrok_tunnel():
    """تشغيل نفق ngrok"""
    print("🔗 تشغيل نفق ngrok...")
    
    try:
        # فحص إذا كان ngrok مثبت
        try:
            result = subprocess.run(['ngrok', 'version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                print("❌ ngrok غير مثبت")
                return None
        except FileNotFoundError:
            print("❌ ngrok غير موجود في PATH")
            return None
        
        # تشغيل ngrok
        print("📡 بدء تشغيل ngrok...")
        if os.path.exists("start_ngrok.bat"):
            subprocess.Popen(["start_ngrok.bat"], shell=True)
        else:
            subprocess.Popen(["ngrok", "http", "5000"], shell=True)
        
        # انتظار ngrok للبدء
        print("⏳ انتظار ngrok للبدء...")
        for i in range(15):  # انتظار 15 ثانية
            try:
                response = requests.get("http://localhost:4040/api/tunnels", timeout=2)
                if response.status_code == 200:
                    tunnels = response.json().get('tunnels', [])
                    if tunnels:
                        public_url = tunnels[0]['public_url']
                        print(f"✅ ngrok يعمل: {public_url}")
                        
                        # تحديث ملف .env
                        update_env_with_ngrok_url(public_url)
                        return public_url
            except:
                pass
            
            time.sleep(1)
            print(f"⏳ انتظار... ({i+1}/15)")
        
        print("❌ فشل في تشغيل ngrok")
        return None
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل ngrok: {e}")
        return None

def update_env_with_ngrok_url(ngrok_url):
    """تحديث ملف .env برابط ngrok الجديد"""
    try:
        env_path = ".env"
        if not os.path.exists(env_path):
            print("⚠️ ملف .env غير موجود")
            return
        
        # قراءة ملف .env
        with open(env_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # تحديث أو إضافة WEBHOOK_URL
        updated = False
        for i, line in enumerate(lines):
            if line.startswith('WEBHOOK_URL='):
                lines[i] = f'WEBHOOK_URL={ngrok_url}\n'
                updated = True
                break
        
        if not updated:
            lines.append(f'WEBHOOK_URL={ngrok_url}\n')
        
        # كتابة ملف .env المحدث
        with open(env_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print(f"✅ تم تحديث WEBHOOK_URL في .env: {ngrok_url}")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث .env: {e}")

def test_ngrok_tunnel(ngrok_url):
    """اختبار نفق ngrok"""
    if not ngrok_url:
        return False
    
    print(f"🧪 اختبار نفق ngrok: {ngrok_url}")
    
    try:
        # اختبار API endpoint
        response = requests.get(f"{ngrok_url}/api/test", timeout=10)
        if response.status_code == 200:
            print("✅ نفق ngrok يعمل بشكل صحيح")
            return True
        else:
            print(f"⚠️ نفق ngrok يستجيب لكن بخطأ: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في اختبار نفق ngrok: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔧 إصلاح مشكلة ngrok وخوادم الويب")
    print("🌐 Fix ngrok and web servers issues")
    print("=" * 60)
    
    # الخطوة 1: إيقاف العمليات الموجودة
    kill_existing_processes()
    
    # الخطوة 2: تشغيل خوادم الويب
    print("\n🌐 تشغيل خوادم الويب...")
    
    # تشغيل الخوادم في threads منفصلة
    web_server_thread = threading.Thread(target=start_web_server, args=(5000,), daemon=True)
    telegram_server_thread = threading.Thread(target=start_telegram_web_app, args=(5001,), daemon=True)
    
    web_server_thread.start()
    telegram_server_thread.start()
    
    # انتظار الخوادم للبدء
    print("⏳ انتظار الخوادم للبدء...")
    time.sleep(5)
    
    # الخطوة 3: اختبار الخوادم المحلية
    working_servers = test_local_servers()
    
    if not working_servers:
        print("❌ لا توجد خوادم تعمل!")
        return False
    
    # الخطوة 4: تشغيل ngrok
    ngrok_url = start_ngrok_tunnel()
    
    # الخطوة 5: اختبار ngrok
    if ngrok_url:
        ngrok_working = test_ngrok_tunnel(ngrok_url)
    else:
        ngrok_working = False
    
    # النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج الإصلاح:")
    print(f"   🌐 خوادم محلية تعمل: {len(working_servers)}")
    print(f"   🔗 ngrok يعمل: {'✅' if ngrok_working else '❌'}")
    
    if ngrok_working:
        print(f"   🌍 الرابط العام: {ngrok_url}")
        print("\n🎉 تم إصلاح جميع المشاكل!")
        print("✅ الآن يمكن الوصول للمودات المنشورة سابقاً")
    else:
        print("\n⚠️ ngrok لا يعمل، لكن الخوادم المحلية تعمل")
        print("🔧 يمكنك:")
        print("   1. تشغيل ngrok يدوياً: ngrok http 5000")
        print("   2. استخدام خدمة أخرى مثل localtunnel")
        print("   3. نشر البوت على استضافة مجانية")
    
    print("=" * 60)
    
    # إبقاء الخوادم تعمل
    if working_servers:
        print("\n🔄 الخوادم تعمل الآن...")
        print("⏹️ اضغط Ctrl+C لإيقاف الخوادم")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف الخوادم")
    
    return ngrok_working

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الإصلاح: {e}")
        sys.exit(1)
