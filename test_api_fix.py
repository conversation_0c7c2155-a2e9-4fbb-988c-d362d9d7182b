#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح API للتأكد من عمل endpoint بشكل صحيح
Test API Fix to ensure endpoint works correctly
"""

import requests
import json
import logging
import time

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_api_endpoint():
    """اختبار API endpoint للمود"""
    
    # معرف المود من الخطأ الأصلي
    mod_id = "7549a3bd-2d5a-48ab-990d-93c9ae7e9150"
    
    # معاملات الاختبار
    params = {
        'lang': 'ar',
        'user_id': '7513880877',
        'channel': '-1002433545184'
    }
    
    # عناوين الطلب
    headers = {
        'User-Agent': 'TelegramBot/1.0',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    # قائمة المنافذ المحتملة للاختبار
    ports = [5000, 5001, 5002, 8000]
    
    for port in ports:
        try:
            url = f"http://localhost:{port}/api/mod/{mod_id}"
            logger.info(f"🔍 اختبار API على المنفذ {port}...")
            logger.info(f"URL: {url}")
            logger.info(f"Parameters: {params}")
            
            # إرسال الطلب
            response = requests.get(url, params=params, headers=headers, timeout=10)
            
            logger.info(f"📊 استجابة المنفذ {port}:")
            logger.info(f"Status Code: {response.status_code}")
            logger.info(f"Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    logger.info("✅ استجابة JSON صحيحة:")
                    logger.info(json.dumps(data, indent=2, ensure_ascii=False))
                    return True, port, data
                except json.JSONDecodeError as e:
                    logger.error(f"❌ خطأ في تحليل JSON: {e}")
                    logger.error(f"Response text: {response.text[:500]}...")
            else:
                logger.warning(f"⚠️ رمز الحالة: {response.status_code}")
                logger.warning(f"Response: {response.text[:200]}...")
                
        except requests.exceptions.ConnectionError:
            logger.warning(f"⚠️ لا يمكن الاتصال بالمنفذ {port}")
        except requests.exceptions.Timeout:
            logger.warning(f"⚠️ انتهت مهلة الاتصال للمنفذ {port}")
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار المنفذ {port}: {e}")
    
    return False, None, None

def test_mod_details_page():
    """اختبار صفحة تفاصيل المود"""
    
    mod_id = "7549a3bd-2d5a-48ab-990d-93c9ae7e9150"
    
    params = {
        'id': mod_id,
        'lang': 'ar',
        'user_id': '7513880877',
        'channel': '-1002433545184'
    }
    
    ports = [5000, 5001, 5002, 8000]
    
    for port in ports:
        try:
            url = f"http://localhost:{port}/telegram-mod-details"
            logger.info(f"🔍 اختبار صفحة التفاصيل على المنفذ {port}...")
            
            response = requests.get(url, params=params, timeout=10)
            
            logger.info(f"📊 استجابة صفحة التفاصيل للمنفذ {port}:")
            logger.info(f"Status Code: {response.status_code}")
            logger.info(f"Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
            
            if response.status_code == 200:
                if 'text/html' in response.headers.get('Content-Type', ''):
                    logger.info("✅ صفحة HTML صحيحة")
                    return True, port
                else:
                    logger.warning("⚠️ نوع المحتوى غير متوقع")
            else:
                logger.warning(f"⚠️ رمز الحالة: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            logger.warning(f"⚠️ لا يمكن الاتصال بالمنفذ {port}")
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار المنفذ {port}: {e}")
    
    return False, None

def check_server_status():
    """فحص حالة الخوادم"""
    
    logger.info("🔍 فحص حالة الخوادم...")
    
    ports = [5000, 5001, 5002, 8000]
    active_servers = []
    
    for port in ports:
        try:
            url = f"http://localhost:{port}/"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                active_servers.append(port)
                logger.info(f"✅ خادم نشط على المنفذ {port}")
            else:
                logger.warning(f"⚠️ خادم يستجيب بخطأ على المنفذ {port}: {response.status_code}")
        except requests.exceptions.ConnectionError:
            logger.info(f"❌ لا يوجد خادم على المنفذ {port}")
        except Exception as e:
            logger.warning(f"⚠️ خطأ في فحص المنفذ {port}: {e}")
    
    return active_servers

def main():
    """الدالة الرئيسية للاختبار"""
    
    print("🧪 اختبار إصلاح API لبوت مودات ماين كرافت")
    print("=" * 60)
    
    # فحص الخوادم النشطة
    active_servers = check_server_status()
    
    if not active_servers:
        print("❌ لا توجد خوادم نشطة!")
        print("💡 تأكد من تشغيل البوت أولاً:")
        print("   python quick_start.py")
        print("   أو")
        print("   python run_optimized_bot.py")
        return
    
    print(f"✅ تم العثور على {len(active_servers)} خادم نشط: {active_servers}")
    print()
    
    # اختبار API endpoint
    print("🔍 اختبار API endpoint...")
    api_success, api_port, api_data = test_api_endpoint()
    
    if api_success:
        print(f"✅ API يعمل بشكل صحيح على المنفذ {api_port}")
        print(f"📊 بيانات المود: {api_data.get('title', 'Unknown')}")
    else:
        print("❌ API لا يعمل بشكل صحيح")
    
    print()
    
    # اختبار صفحة التفاصيل
    print("🔍 اختبار صفحة تفاصيل المود...")
    page_success, page_port = test_mod_details_page()
    
    if page_success:
        print(f"✅ صفحة التفاصيل تعمل بشكل صحيح على المنفذ {page_port}")
    else:
        print("❌ صفحة التفاصيل لا تعمل بشكل صحيح")
    
    print()
    
    # النتيجة النهائية
    if api_success and page_success:
        print("🎉 جميع الاختبارات نجحت! المشكلة تم حلها.")
    elif api_success:
        print("⚠️ API يعمل لكن صفحة التفاصيل بها مشكلة")
    elif page_success:
        print("⚠️ صفحة التفاصيل تعمل لكن API به مشكلة")
    else:
        print("❌ جميع الاختبارات فشلت. المشكلة لا تزال موجودة.")
        print("💡 تحقق من:")
        print("   1. تشغيل البوت")
        print("   2. إعدادات قاعدة البيانات")
        print("   3. ملفات السجل للأخطاء")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        logger.error(f"Test error: {e}", exc_info=True)
