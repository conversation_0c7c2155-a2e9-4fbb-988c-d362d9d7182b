#!/usr/bin/env python3
"""
🚨 حل عاجل لمشكلة HTTPS
🔒 URGENT HTTPS FIX

حل فوري لمشكلة رفض تيليجرام لروابط HTTP
Immediate fix for Telegram rejecting HTTP links
"""

import os
import sys
from pathlib import Path

def main():
    print("\n" + "="*60)
    print("🚨 حل عاجل لمشكلة HTTPS")
    print("🔒 URGENT HTTPS FIX")
    print("="*60)
    
    print("\n❌ المشكلة المكتشفة:")
    print("تيليجرام يرفض روابط HTTP في Web App buttons")
    print("الخطأ: 'only https links are allowed'")
    
    print("\n🔧 الحل العاجل:")
    print("سيتم تعطيل Web App buttons مؤقتاً واستخدام روابط عادية")
    
    # تحديث ملف .env لتعطيل Web App
    env_file = Path(".env")
    env_lines = []
    
    # قراءة الملف الموجود
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('WEB_SERVER_URL='):
                        # تعليق السطر لتعطيل Web App
                        env_lines.append(f"# {line}")
                        print(f"✅ Disabled Web App: {line}")
                    else:
                        env_lines.append(line)
        except Exception as e:
            print(f"⚠️ Error reading .env file: {e}")
    
    # إضافة إعداد لتعطيل Web App
    env_lines.append("# Web App disabled due to HTTPS requirement")
    env_lines.append("DISABLE_WEB_APP=true")
    
    # حفظ الملف
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            for line in env_lines:
                f.write(line + '\n')
        print("✅ .env file updated - Web App disabled")
    except Exception as e:
        print(f"❌ Failed to update .env file: {e}")
        return False
    
    print("\n✅ تم تطبيق الحل العاجل!")
    print("• تم تعطيل Web App buttons")
    print("• سيستخدم البوت روابط عادية بدلاً منها")
    print("• لن تظهر رسالة خطأ HTTPS")
    
    print("\n🔄 للحل الدائم:")
    print("1. حمل ngrok من: https://ngrok.com/download")
    print("2. ضع ngrok.exe في هذا المجلد")
    print("3. شغل: ngrok http 5001")
    print("4. انسخ الرابط HTTPS")
    print("5. أضفه لملف .env: WEB_SERVER_URL=https://your-url.ngrok.io")
    print("6. احذف السطر: DISABLE_WEB_APP=true")
    
    print("\n🚀 الآن يمكنك تشغيل البوت بدون أخطاء:")
    print("python main.py")
    
    return True

if __name__ == "__main__":
    if main():
        print("\n🎉 تم الحل بنجاح!")
    else:
        print("\n❌ فشل في تطبيق الحل")
    
    input("\nاضغط Enter للمتابعة...")
