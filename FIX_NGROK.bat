@echo off
chcp 65001 >nul
title إصلاح مشكلة ngrok - Fix ngrok Issue

echo.
echo ========================================
echo 🔧 إصلاح مشكلة ngrok
echo 🌐 Fix ngrok Issue
echo ========================================
echo.

echo 🔍 فحص النظام...

:: فحص Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

echo ✅ Python متوفر

:: فحص ngrok
ngrok version >nul 2>&1
if errorlevel 1 (
    echo ❌ ngrok غير مثبت أو غير موجود في PATH
    echo.
    echo 🔧 لتثبيت ngrok:
    echo    1. اذهب إلى https://ngrok.com/download
    echo    2. حمل ngrok لنظام Windows
    echo    3. استخرج الملف وضعه في مجلد البوت
    echo    4. أو أضف مجلد ngrok إلى PATH
    echo.
    pause
    exit /b 1
)

echo ✅ ngrok متوفر

:: إيقاف العمليات الموجودة
echo.
echo 🔄 إيقاف العمليات الموجودة...

:: إيقاف ngrok
taskkill /IM ngrok.exe /F >nul 2>&1

:: إيقاف Python processes على المنافذ 5000 و 5001
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":5000"') do (
    taskkill /PID %%a /F >nul 2>&1
)

for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":5001"') do (
    taskkill /PID %%a /F >nul 2>&1
)

echo ✅ تم إيقاف العمليات الموجودة

:: انتظار قصير
timeout /t 3 /nobreak >nul

echo.
echo 🌐 تشغيل إصلاح ngrok...
python fix_ngrok_issue.py

:: معالجة النتيجة
if errorlevel 1 (
    echo.
    echo ❌ فشل في إصلاح ngrok
    echo.
    echo 🔧 نصائح لحل المشكلة:
    echo    1. تأكد من تثبيت ngrok بشكل صحيح
    echo    2. تأكد من وجود اتصال بالإنترنت
    echo    3. جرب تشغيل ngrok يدوياً: ngrok http 5000
    echo    4. تأكد من عدم حجب الجدار الناري لـ ngrok
    echo.
) else (
    echo.
    echo ✅ تم إصلاح مشكلة ngrok بنجاح
    echo.
    echo 🎉 الآن يمكن الوصول للمودات المنشورة سابقاً
    echo.
)

pause
