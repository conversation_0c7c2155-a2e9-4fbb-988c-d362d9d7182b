# 🚀 دليل نشر بوت التليجرام على استضافة Pella

## 📋 نظرة عامة
هذا المجلد يحتوي على جميع الملفات المطلوبة لنشر بوت التليجرام الخاص بمودات ماين كرافت على استضافة Pella.

**حجم الحزمة:** 1.52 MB
**عدد الملفات:** 38 ملف
**آخر تحديث:** 2025-06-14

## 📁 الملفات المضمنة

### 1. ملفات Python الأساسية
- ✅ `main.py` - الملف الرئيسي للبوت (21,381 سطر)
- ✅ `start_hosting.py` - ملف بدء التشغيل المحسن للاستضافة
- ✅ `hosting_config.py` - إعدادات الاستضافة المجانية
- ✅ `supabase_client.py` - عميل قاعدة البيانات Supabase
- ✅ `web_server.py` - خادم الويب المدمج
- ✅ `telegram_web_app.py` - تطبيق الويب للتليجرام
- ✅ `notifications.py` - نظام الإشعارات المتقدم

### 2. ملفات الإعداد والتكوين
- ✅ `requirements.txt` - متطلبات Python المحسنة للاستضافة المجانية
- ✅ `Procfile` - ملف تشغيل الاستضافة
- ✅ `config.json` - إعدادات التطبيق
- ✅ `runtime.txt` - إصدار Python المطلوب (3.11.0)
- ✅ `.env` - متغيرات البيئة (تم إنشاؤه تلقائياً)

### 3. ملفات الويب والواجهات
- 📁 `htdocs/` - مجلد كامل يحتوي على:
  - `index.php` - الصفحة الرئيسية
  - `api.php` - واجهة برمجة التطبيقات
  - `config.php` - إعدادات PHP
  - `style.css` - ملفات التنسيق
  - `script.js` - ملفات JavaScript
  - `uploaded_images/` - مجلد الصور المرفوعة

### 4. ملفات البيانات والإعدادات
- ✅ `admin_settings.json` - إعدادات المسؤول
- ✅ `mods.json` - قاعدة بيانات المودات المحلية
- ✅ `all_users.json` - قائمة جميع المستخدمين
- ✅ `user_channels.json` - قنوات المستخدمين
- ✅ `notification_templates.json` - قوالب الإشعارات
- ✅ `custom_download_links.json` - روابط التحميل المخصصة
- ✅ `user_invitations.json` - نظام الدعوات
- ✅ `broadcast_history.json` - تاريخ البث

### 5. ملفات التحسين والأمان
- ✅ `optimization_config.py` - إعدادات التحسين
- ✅ `network_config.py` - إعدادات الشبكة
- ✅ `secure_config.py` - الإعدادات الآمنة
- ✅ `security_config.py` - نظام الحماية

## 🔧 خطوات النشر على Pella

### المرحلة 1: تحضير الملفات
1. **رفع الملفات:**
   - ارفع جميع الملفات من مجلد `pella_hosting` إلى استضافة Pella
   - تأكد من الحفاظ على هيكل المجلدات
   - تأكد من أن ملف `start_hosting.py` في المجلد الجذر

2. **التحقق من الصلاحيات:**
   - تأكد من أن ملفات Python قابلة للتنفيذ
   - تأكد من صلاحيات الكتابة لمجلد `uploaded_images`

### المرحلة 2: إعداد متغيرات البيئة
قم بإعداد المتغيرات التالية في لوحة تحكم Pella:

```bash
# بيانات البوت الأساسية
BOT_TOKEN=7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4
ADMIN_CHAT_ID=7513880877
ADMIN_USERNAME=Kim880198

# بيانات قاعدة البيانات Supabase
SUPABASE_URL=https://ytqxxodyecdeosnqoure.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4

# إعدادات التحسين للاستضافة المجانية
OPTIMIZATION_ENABLED=true
LOW_RESOURCE_MODE=true
REQUEST_TIMEOUT=30
```

### المرحلة 3: تشغيل التطبيق
1. **الملف الرئيسي للتشغيل:** `start_hosting.py`
2. **أمر التشغيل:** `python start_hosting.py`
3. **البوت سيبدأ تلقائياً مع:**
   - نظام إدارة قاعدة البيانات
   - خادم الويب المدمج
   - نظام الإشعارات
   - جميع الميزات المتقدمة

### المرحلة 4: التحقق من التشغيل
1. **فحص اللوجز:**
   - تحقق من لوجز التطبيق للتأكد من عدم وجود أخطاء
   - ابحث عن رسالة "🎉 جميع الخدمات الأساسية تعمل!"

2. **اختبار البوت:**
   - أرسل `/start` للبوت في التليجرام
   - تحقق من استجابة البوت
   - اختبر عرض المودات

3. **اختبار خادم الويب:**
   - تأكد من عمل خادم الويب
   - اختبر الوصول للواجهة الويب
   - تحقق من عمل API

## 🎯 الميزات المضمنة

### ميزات البوت المتقدمة
- 🎮 **نشر مودات ماين كرافت** مع دعم الصور والأوصاف
- 👥 **نظام إدارة المستخدمين** المتقدم
- 📢 **نظام الإشعارات** مع قوالب مخصصة
- 🌐 **دعم متعدد اللغات** (عربي/إنجليزي)
- 🎫 **نظام الدعوات** للمستخدمين الجدد
- 📺 **إدارة القنوات** المتعددة
- 🔗 **تخصيص روابط التحميل** لكل قناة
- 📊 **إحصائيات مفصلة** للاستخدام
- 🔒 **نظام حماية متقدم** ضد الإساءة

### ميزات الويب والAPI
- 🌐 **واجهة ويب متجاوبة** لعرض المودات
- 🔌 **API متكامل** للتفاعل مع البوت
- 📁 **نظام إدارة الملفات** المتقدم
- 🖼️ **دعم الصور والملفات** المرفوعة
- 📱 **تطبيق ويب للتليجرام** مدمج
- 🎨 **قوالب تصميم قابلة للتخصيص**

## ⚡ التحسينات للاستضافة المجانية

### تحسينات الأداء
- 💾 **استهلاك ذاكرة منخفض** (أقل من 100MB)
- ⚡ **معالجة سريعة** للطلبات
- 🔄 **نظام cache ذكي** للبيانات
- 📊 **مراقبة الموارد** التلقائية

### تحسينات الشبكة
- 🌐 **اتصال محسن** مع Telegram API
- 🔄 **إعادة المحاولة التلقائية** عند الأخطاء
- ⏱️ **timeout محسن** للطلبات
- 🛡️ **معالجة أخطاء متقدمة**

## 📋 متطلبات النظام

### متطلبات الاستضافة
- 🐍 **Python 3.8+** (مُحسن لـ 3.11.0)
- 💾 **ذاكرة:** 128MB كحد أدنى
- 💿 **مساحة:** 50MB كحد أدنى
- 🌐 **اتصال إنترنت** مستقر

### المكتبات المطلوبة
```
python-telegram-bot>=20.0
requests>=2.31.0
python-dotenv>=1.0.0
httpx>=0.24.0
```

## ⚠️ ملاحظات مهمة

### قبل النشر
1. ✅ تأكد من أن Python 3.8+ مدعوم على الاستضافة
2. ✅ تحقق من حدود الموارد لاستضافة Pella
3. ✅ احتفظ بنسخة احتياطية من البيانات المهمة
4. ✅ اختبر البوت محلياً قبل النشر

### بعد النشر
1. 📊 راقب استهلاك الموارد بانتظام
2. 📝 تحقق من اللوجز يومياً
3. 🔄 قم بتحديث البيانات حسب الحاجة
4. 🛡️ راقب أمان النظام

### استكشاف الأخطاء
- 🔍 **مشكلة في الاتصال:** تحقق من إعدادات Supabase
- 🐛 **أخطاء Python:** تحقق من إصدار Python ومتطلبات المكتبات
- 📱 **مشكلة في البوت:** تحقق من صحة BOT_TOKEN
- 🌐 **مشكلة في الويب:** تحقق من صلاحيات الملفات

## 📞 الدعم الفني

### معلومات الاتصال
- 👨‍💻 **المطور:** @Kim880198
- 📧 **للدعم الفني:** يرجى التواصل عبر التليجرام
- 📚 **الوثائق:** متوفرة في ملفات README

### الدعم المتاح
- ✅ إعداد الاستضافة
- ✅ حل المشاكل التقنية
- ✅ تخصيص الميزات
- ✅ تحديثات النظام

---

**🎉 مبروك! بوتك جاهز للنشر على استضافة Pella**

> تم تجميع هذه الحزمة تلقائياً في 2025-06-14 باستخدام أدوات التجميع المتقدمة
