#!/usr/bin/env python3
"""
إصلاح مشاكل البوت الرئيسية
- مشكلة قاعدة البيانات
- مشكلة /start command
- مشكلة الأزرار
"""

import os
import sys
import json
import logging
import asyncio
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def fix_env_file():
    """إصلاح ملف .env بالبيانات الصحيحة"""
    print("🔧 إصلاح ملف .env...")
    
    try:
        # قراءة الملف الحالي
        env_content = ""
        if os.path.exists('.env'):
            with open('.env', 'r', encoding='utf-8') as f:
                env_content = f.read()
        
        # البيانات الصحيحة
        correct_supabase_url = "https://ytqxxodyecdeosnqoure.supabase.co"
        correct_supabase_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4"
        
        # تحديث البيانات
        lines = env_content.split('\n')
        updated_lines = []
        
        supabase_url_updated = False
        supabase_key_updated = False
        
        for line in lines:
            if line.startswith('SUPABASE_URL='):
                updated_lines.append(f'SUPABASE_URL={correct_supabase_url}')
                supabase_url_updated = True
            elif line.startswith('SUPABASE_KEY='):
                updated_lines.append(f'SUPABASE_KEY={correct_supabase_key}')
                supabase_key_updated = True
            else:
                updated_lines.append(line)
        
        # إضافة البيانات إذا لم تكن موجودة
        if not supabase_url_updated:
            updated_lines.append(f'SUPABASE_URL={correct_supabase_url}')
        if not supabase_key_updated:
            updated_lines.append(f'SUPABASE_KEY={correct_supabase_key}')
        
        # كتابة الملف المحدث
        with open('.env', 'w', encoding='utf-8') as f:
            f.write('\n'.join(updated_lines))
        
        print("✅ تم إصلاح ملف .env بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح ملف .env: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال مع قاعدة البيانات"""
    print("🧪 اختبار الاتصال مع قاعدة البيانات...")
    
    try:
        import requests
        
        # قراءة الإعدادات
        with open('.env', 'r', encoding='utf-8') as f:
            env_content = f.read()
        
        supabase_url = None
        supabase_key = None
        
        for line in env_content.split('\n'):
            if line.startswith('SUPABASE_URL='):
                supabase_url = line.split('=', 1)[1].strip()
            elif line.startswith('SUPABASE_KEY='):
                supabase_key = line.split('=', 1)[1].strip()
        
        if not supabase_url or not supabase_key:
            print("❌ لم يتم العثور على إعدادات قاعدة البيانات")
            return False
        
        # اختبار الاتصال
        headers = {
            'apikey': supabase_key,
            'Authorization': f'Bearer {supabase_key}',
            'Content-Type': 'application/json'
        }
        
        url = f"{supabase_url}/rest/v1/mods"
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ الاتصال ناجح - تم جلب {len(data)} مود")
            return True
        else:
            print(f"❌ فشل الاتصال: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def fix_handlers_issue():
    """إصلاح مشكلة handlers في main.py"""
    print("🔧 فحص وإصلاح مشكلة handlers...")
    
    try:
        # قراءة main.py
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود setup_handlers
        if 'setup_handlers(application)' in content:
            print("✅ setup_handlers موجود بالفعل")
            return True
        else:
            print("⚠️ setup_handlers غير موجود، سيتم إضافته")
            
            # البحث عن المكان المناسب للإضافة
            if '# ========== Error Handler ==========' in content:
                # إضافة setup_handlers بعد Error Handler
                content = content.replace(
                    '# ========== Error Handler ==========\n        application.add_error_handler(error_handler)',
                    '# ========== Error Handler ==========\n        application.add_error_handler(error_handler)\n\n        # ========== Setup Additional Handlers ==========\n        setup_handlers(application)'
                )
                
                # كتابة الملف المحدث
                with open('main.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ تم إضافة setup_handlers بنجاح")
                return True
            else:
                print("❌ لم يتم العثور على المكان المناسب لإضافة setup_handlers")
                return False
                
    except Exception as e:
        print(f"❌ خطأ في إصلاح handlers: {e}")
        return False

def check_required_files():
    """فحص الملفات المطلوبة"""
    print("📁 فحص الملفات المطلوبة...")
    
    required_files = [
        'main.py',
        'supabase_client.py',
        'web_server.py',
        '.env'
    ]
    
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"✅ {file}")
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    return True

def create_startup_script():
    """إنشاء سكريبت تشغيل محسن"""
    print("📝 إنشاء سكريبت تشغيل محسن...")
    
    try:
        startup_content = '''@echo off
echo ========================================
echo 🤖 تشغيل بوت نشر مودات ماين كرافت
echo ========================================
echo.

echo 🔍 فحص المتطلبات...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 🧪 اختبار قاعدة البيانات...
python test_supabase_connection.py
if errorlevel 1 (
    echo ⚠️ مشكلة في قاعدة البيانات
    echo 🔧 سيتم المحاولة مع الإصلاحات...
    python fix_bot_issues.py
)

echo.
echo 🚀 تشغيل البوت...
echo ⏹️ اضغط Ctrl+C لإيقاف البوت
echo ========================================
echo.

python main.py

echo.
echo ========================================
echo ⏹️ تم إيقاف البوت
echo ========================================
pause
'''
        
        with open('start_bot_fixed.bat', 'w', encoding='utf-8') as f:
            f.write(startup_content)
        
        print("✅ تم إنشاء start_bot_fixed.bat")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء سكريبت التشغيل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 بدء إصلاح مشاكل البوت")
    print("=" * 50)
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # قائمة الإصلاحات
    fixes = [
        ("فحص الملفات المطلوبة", check_required_files),
        ("إصلاح ملف .env", fix_env_file),
        ("اختبار قاعدة البيانات", test_database_connection),
        ("إصلاح مشكلة handlers", fix_handlers_issue),
        ("إنشاء سكريبت تشغيل محسن", create_startup_script)
    ]
    
    success_count = 0
    total_count = len(fixes)
    
    for fix_name, fix_function in fixes:
        print(f"🔧 {fix_name}...")
        try:
            if fix_function():
                print(f"✅ {fix_name} - نجح")
                success_count += 1
            else:
                print(f"❌ {fix_name} - فشل")
        except Exception as e:
            print(f"❌ {fix_name} - خطأ: {e}")
        print()
    
    print("=" * 50)
    print(f"📊 النتائج: {success_count}/{total_count} إصلاحات نجحت")
    
    if success_count == total_count:
        print("🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("🚀 يمكنك الآن تشغيل البوت باستخدام: start_bot_fixed.bat")
    else:
        print("⚠️ بعض الإصلاحات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
