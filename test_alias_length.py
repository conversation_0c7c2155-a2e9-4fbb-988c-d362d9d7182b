#!/usr/bin/env python3
"""
اختبار طول الـ alias للروابط المختصرة
Test Alias Length for Shortened URLs
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import create_short_alias

def print_header():
    print("=" * 60)
    print("🔗 اختبار طول الـ Alias للروابط المختصرة")
    print("   Test Alias Length for Shortened URLs")
    print("=" * 60)

def test_alias_creation():
    """اختبار إنشاء alias قصير"""
    print("\n🧪 اختبار إنشاء alias قصير...")
    
    test_cases = [
        {
            'mod_id': '27da24e7-f04b-4987-9e79-0d4f3c8342f8',
            'user_id': '7513880877',
            'description': 'حالة حقيقية من السجلات'
        },
        {
            'mod_id': 'very-long-mod-id-that-exceeds-normal-length-12345',
            'user_id': '1234567890123',
            'description': 'حالة طويلة جداً'
        },
        {
            'mod_id': 'short',
            'user_id': '123',
            'description': 'حالة قصيرة'
        },
        {
            'mod_id': 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
            'user_id': '9876543210',
            'description': 'حالة UUID عادية'
        }
    ]
    
    print(f"\n🔄 اختبار {len(test_cases)} حالات مختلفة...\n")
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"[{i}/{len(test_cases)}] {case['description']}")
        print(f"   📋 Mod ID: {case['mod_id']}")
        print(f"   👤 User ID: {case['user_id']}")
        
        # إنشاء alias
        alias = create_short_alias(case['mod_id'], case['user_id'])
        
        if alias:
            alias_length = len(alias)
            print(f"   🔗 Alias: {alias}")
            print(f"   📏 الطول: {alias_length} حرف")
            
            # فحص الطول
            if alias_length <= 30:
                print(f"   ✅ الطول مقبول ({alias_length} ≤ 30)")
            else:
                print(f"   ❌ الطول طويل جداً ({alias_length} > 30)")
                all_passed = False
            
            # فحص التفرد (تشغيل مرة أخرى للتأكد من التفرد)
            time.sleep(1)  # انتظار ثانية لتغيير timestamp
            alias2 = create_short_alias(case['mod_id'], case['user_id'])
            
            if alias != alias2:
                print(f"   ✅ Alias فريد (مختلف عن المحاولة الثانية)")
            else:
                print(f"   ⚠️ Alias قد يكون متكرر (نفس النتيجة في المحاولة الثانية)")
        else:
            print(f"   ❌ فشل في إنشاء alias")
            all_passed = False
        
        print()
    
    return all_passed

def test_edge_cases():
    """اختبار الحالات الحدية"""
    print("\n🔍 اختبار الحالات الحدية...")
    
    edge_cases = [
        {
            'mod_id': None,
            'user_id': '123456',
            'expected': None,
            'description': 'mod_id فارغ'
        },
        {
            'mod_id': '12345',
            'user_id': None,
            'expected': None,
            'description': 'user_id فارغ'
        },
        {
            'mod_id': '',
            'user_id': '123456',
            'expected': None,
            'description': 'mod_id نص فارغ'
        },
        {
            'mod_id': '12345',
            'user_id': '',
            'expected': None,
            'description': 'user_id نص فارغ'
        }
    ]
    
    all_passed = True
    
    for i, case in enumerate(edge_cases, 1):
        print(f"[{i}/{len(edge_cases)}] {case['description']}")
        
        alias = create_short_alias(case['mod_id'], case['user_id'])
        
        if alias == case['expected']:
            print(f"   ✅ النتيجة متوقعة: {alias}")
        else:
            print(f"   ❌ النتيجة غير متوقعة: {alias} (متوقع: {case['expected']})")
            all_passed = False
        
        print()
    
    return all_passed

def test_length_consistency():
    """اختبار ثبات الطول"""
    print("\n📏 اختبار ثبات الطول...")
    
    # إنشاء 10 aliases مختلفة
    aliases = []
    
    for i in range(10):
        mod_id = f"test-mod-{i:03d}-abcd-efgh-ijkl-mnopqrstuvwx"
        user_id = f"{1000000000 + i}"
        
        alias = create_short_alias(mod_id, user_id)
        if alias:
            aliases.append(alias)
            print(f"   {i+1:2d}. {alias} (طول: {len(alias)})")
        
        time.sleep(0.1)  # انتظار قصير
    
    # فحص الأطوال
    lengths = [len(alias) for alias in aliases]
    max_length = max(lengths) if lengths else 0
    min_length = min(lengths) if lengths else 0
    
    print(f"\n📊 إحصائيات الأطوال:")
    print(f"   📏 أقصر alias: {min_length} حرف")
    print(f"   📏 أطول alias: {max_length} حرف")
    print(f"   📏 متوسط الطول: {sum(lengths)/len(lengths):.1f} حرف")
    
    if max_length <= 30:
        print(f"   ✅ جميع الـ aliases ضمن الحد المسموح (≤ 30)")
        return True
    else:
        print(f"   ❌ بعض الـ aliases تتجاوز الحد المسموح (> 30)")
        return False

def test_uniqueness():
    """اختبار التفرد"""
    print("\n🔄 اختبار التفرد...")
    
    # إنشاء aliases متعددة لنفس المود والمستخدم
    mod_id = "test-uniqueness-mod-12345"
    user_id = "9876543210"
    
    aliases = set()
    
    for i in range(5):
        alias = create_short_alias(mod_id, user_id)
        if alias:
            aliases.add(alias)
            print(f"   {i+1}. {alias}")
        time.sleep(1)  # انتظار ثانية لتغيير timestamp
    
    unique_count = len(aliases)
    total_count = 5
    
    print(f"\n📊 نتائج التفرد:")
    print(f"   🔢 إجمالي المحاولات: {total_count}")
    print(f"   🔢 Aliases فريدة: {unique_count}")
    print(f"   📊 معدل التفرد: {(unique_count/total_count)*100:.1f}%")
    
    if unique_count == total_count:
        print(f"   ✅ جميع الـ aliases فريدة")
        return True
    else:
        print(f"   ⚠️ بعض الـ aliases متكررة")
        return True  # نعتبرها مقبولة لأن timestamp قد يتكرر

def main():
    """الدالة الرئيسية"""
    print_header()
    
    tests = [
        ("إنشاء alias قصير", test_alias_creation),
        ("الحالات الحدية", test_edge_cases),
        ("ثبات الطول", test_length_consistency),
        ("التفرد", test_uniqueness)
    ]
    
    print(f"\n🚀 بدء تشغيل {len(tests)} اختبارات...\n")
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 اختبار: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ نجح: {test_name}")
            else:
                print(f"❌ فشل: {test_name}")
        except Exception as e:
            print(f"💥 خطأ في {test_name}: {e}")
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 النتائج النهائية")
    print("=" * 60)
    
    success_rate = (passed_tests / len(tests)) * 100
    print(f"✅ الاختبارات الناجحة: {passed_tests}/{len(tests)} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ الـ alias الجديد يعمل بشكل مثالي")
        print("🔗 يمكن الآن استخدام ميزة اختصار الروابط بأمان")
    elif success_rate >= 75:
        print("✅ معظم الاختبارات نجحت")
        print("⚠️ قد تحتاج لمراجعة بعض النقاط")
    else:
        print("❌ هناك مشاكل في الـ alias")
        print("🔧 يحتاج لمراجعة وإصلاح")
    
    print("\n💡 ملاحظات:")
    print("- الحد الأقصى للـ alias: 30 حرف")
    print("- التنسيق: m[8حروف]u[6أرقام]t[4أرقام]")
    print("- مثال: m27da24e7u880877t1234")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n💥 خطأ في الاختبار: {e}")
        sys.exit(1)
