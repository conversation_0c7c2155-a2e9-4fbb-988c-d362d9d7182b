#!/usr/bin/env python3
"""
البوت الرئيسي محسن للاستضافة
Main bot optimized for hosting
"""

import os
import sys
import logging
from pathlib import Path

# إعداد المسارات
sys.path.insert(0, str(Path(__file__).parent))

# إعداد متغيرات البيئة للاستضافة
os.environ.setdefault("ENVIRONMENT", "production")
os.environ.setdefault("DEBUG", "false")

# تحديد رابط الخادم تلقائياً
if not os.environ.get("WEB_SERVER_URL"):
    # للاستضافة على Railway
    railway_url = os.environ.get("RAILWAY_STATIC_URL")
    if railway_url:
        os.environ["WEB_SERVER_URL"] = railway_url
        print(f"🚂 Railway URL: {railway_url}")
    else:
        # للاستضافة على خدمات أخرى
        port = os.environ.get("PORT", "5000")
        app_name = os.environ.get("APP_NAME", "minecraft-mods-bot")
        
        # تحديد الخدمة
        if "railway" in os.environ.get("RAILWAY_ENVIRONMENT", ""):
            os.environ["WEB_SERVER_URL"] = f"https://{app_name}.up.railway.app"
        elif "render" in os.environ.get("RENDER", ""):
            os.environ["WEB_SERVER_URL"] = f"https://{app_name}.onrender.com"
        elif "herokuapp" in os.environ.get("DYNO", ""):
            os.environ["WEB_SERVER_URL"] = f"https://{app_name}.herokuapp.com"
        else:
            os.environ["WEB_SERVER_URL"] = f"http://localhost:{port}"

# إعداد الـ logging للاستضافة
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    """تشغيل البوت"""
    try:
        logger.info("🚀 Starting Minecraft Mods Bot on hosting platform...")
        logger.info(f"🌐 Web server URL: {os.environ.get('WEB_SERVER_URL')}")
        logger.info(f"🔧 Environment: {os.environ.get('ENVIRONMENT')}")
        
        # استيراد وتشغيل البوت الأصلي
        from main import *
        logger.info("✅ Bot started successfully!")
        
    except Exception as e:
        logger.error(f"❌ Error starting bot: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
