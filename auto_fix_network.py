#!/usr/bin/env python3
"""
أداة إصلاح تلقائي لمشاكل الشبكة والـ DNS
Automatic Network and DNS Fix Tool
"""

import os
import sys
import subprocess
import platform
import time
import socket

def print_header():
    print("=" * 60)
    print("🔧 أداة الإصلاح التلقائي للشبكة")
    print("   Automatic Network Fix Tool")
    print("=" * 60)

def run_command(command, description):
    """تشغيل أمر مع معالجة الأخطاء"""
    print(f"\n🔄 {description}...")
    print(f"💻 تشغيل: {' '.join(command) if isinstance(command, list) else command}")
    
    try:
        if isinstance(command, str):
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        else:
            result = subprocess.run(command, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print(f"✅ نجح: {description}")
            if result.stdout.strip():
                print(f"📋 النتيجة: {result.stdout.strip()[:200]}...")
            return True
        else:
            print(f"⚠️ تحذير في {description}: {result.stderr.strip()}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ انتهت مهلة {description}")
        return False
    except Exception as e:
        print(f"❌ خطأ في {description}: {e}")
        return False

def flush_dns():
    """مسح ذاكرة DNS"""
    print("\n🔄 مسح ذاكرة DNS...")
    
    system = platform.system().lower()
    success = False
    
    if system == "windows":
        commands = [
            (["ipconfig", "/flushdns"], "مسح DNS cache"),
            (["ipconfig", "/release"], "تحرير عنوان IP"),
            (["ipconfig", "/renew"], "تجديد عنوان IP"),
            (["netsh", "winsock", "reset"], "إعادة تعيين Winsock")
        ]
        
        for cmd, desc in commands:
            if run_command(cmd, desc):
                success = True
    
    elif system == "linux":
        commands = [
            (["sudo", "systemctl", "restart", "systemd-resolved"], "إعادة تشغيل DNS resolver"),
            (["sudo", "service", "networking", "restart"], "إعادة تشغيل الشبكة")
        ]
        
        for cmd, desc in commands:
            try:
                if run_command(cmd, desc):
                    success = True
                    break
            except:
                continue
    
    return success

def configure_dns():
    """تكوين DNS servers موثوقة"""
    print("\n🔧 تكوين DNS servers...")
    
    system = platform.system().lower()
    
    if system == "windows":
        # محاولة تكوين DNS تلقائياً
        dns_commands = [
            ['netsh', 'interface', 'ip', 'set', 'dns', 'name="Wi-Fi"', 'static', '*******'],
            ['netsh', 'interface', 'ip', 'add', 'dns', 'name="Wi-Fi"', '*******', 'index=2'],
            ['netsh', 'interface', 'ip', 'set', 'dns', 'name="Ethernet"', 'static', '*******'],
            ['netsh', 'interface', 'ip', 'add', 'dns', 'name="Ethernet"', '*******', 'index=2']
        ]
        
        success = False
        for cmd in dns_commands:
            try:
                if run_command(cmd, f"تكوين DNS لـ {cmd[4]}"):
                    success = True
            except:
                continue
        
        if not success:
            print("\n💡 تكوين DNS يدوياً:")
            print("1. افتح Control Panel > Network and Internet > Network Connections")
            print("2. انقر بزر الماوس الأيمن على اتصالك > Properties")
            print("3. اختر Internet Protocol Version 4 (TCP/IPv4) > Properties")
            print("4. اختر 'Use the following DNS server addresses'")
            print("5. Preferred DNS: *******")
            print("6. Alternate DNS: *******")
        
        return success
    
    return True

def test_connectivity():
    """اختبار الاتصال بعد الإصلاح"""
    print("\n🧪 اختبار الاتصال...")
    
    test_hosts = [
        ("*******", 53, "Google DNS"),
        ("*******", 53, "Cloudflare DNS"),
        ("api.telegram.org", 443, "Telegram API")
    ]
    
    success_count = 0
    
    for host, port, name in test_hosts:
        try:
            socket.create_connection((host, port), timeout=10)
            print(f"✅ {name} متاح")
            success_count += 1
        except Exception as e:
            print(f"❌ {name} غير متاح: {e}")
    
    success_rate = (success_count / len(test_hosts)) * 100
    print(f"\n📊 معدل نجاح الاتصال: {success_rate:.1f}%")
    
    return success_rate > 66

def fix_python_networking():
    """إصلاح مشاكل Python networking"""
    print("\n🐍 إصلاح مشاكل Python networking...")
    
    commands = [
        (["python", "-m", "pip", "install", "--upgrade", "pip"], "تحديث pip"),
        (["pip", "install", "--upgrade", "requests"], "تحديث requests"),
        (["pip", "install", "--upgrade", "urllib3"], "تحديث urllib3"),
        (["pip", "install", "--upgrade", "certifi"], "تحديث certifi")
    ]
    
    success_count = 0
    
    for cmd, desc in commands:
        if run_command(cmd, desc):
            success_count += 1
    
    return success_count > 0

def create_network_test_script():
    """إنشاء سكريبت اختبار بسيط"""
    print("\n📝 إنشاء سكريبت اختبار...")
    
    test_script = '''#!/usr/bin/env python3
import socket
import requests

def test_connection():
    try:
        # اختبار DNS
        socket.gethostbyname("google.com")
        print("✅ DNS يعمل")
        
        # اختبار HTTP
        requests.get("https://httpbin.org/ip", timeout=10)
        print("✅ HTTP يعمل")
        
        # اختبار Telegram
        requests.get("https://api.telegram.org/bot123:test/getMe", timeout=10)
        print("✅ Telegram API قابل للوصول")
        
        return True
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        return False

if __name__ == "__main__":
    if test_connection():
        print("🎉 الشبكة تعمل بشكل جيد!")
    else:
        print("❌ لا تزال هناك مشاكل في الشبكة")
'''
    
    try:
        with open("test_connection.py", "w", encoding="utf-8") as f:
            f.write(test_script)
        print("✅ تم إنشاء test_connection.py")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء السكريبت: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # التحقق من صلاحيات المدير
    if platform.system().lower() == "windows":
        try:
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if not is_admin:
                print("⚠️ تحذير: قد تحتاج لتشغيل البرنامج كمدير للحصول على أفضل النتائج")
        except:
            pass
    
    fixes = [
        ("مسح ذاكرة DNS", flush_dns),
        ("تكوين DNS servers", configure_dns),
        ("إصلاح Python networking", fix_python_networking),
        ("إنشاء سكريبت اختبار", create_network_test_script),
        ("اختبار الاتصال", test_connectivity)
    ]
    
    print(f"\n🚀 بدء تطبيق {len(fixes)} إصلاحات...\n")
    
    successful_fixes = 0
    
    for fix_name, fix_func in fixes:
        print(f"\n[{fixes.index((fix_name, fix_func)) + 1}/{len(fixes)}] {fix_name}")
        print("-" * 50)
        
        try:
            if fix_func():
                successful_fixes += 1
                print(f"✅ نجح: {fix_name}")
            else:
                print(f"⚠️ فشل جزئياً: {fix_name}")
        except Exception as e:
            print(f"❌ خطأ في {fix_name}: {e}")
        
        time.sleep(2)
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج الإصلاح")
    print("=" * 60)
    
    success_rate = (successful_fixes / len(fixes)) * 100
    print(f"✅ الإصلاحات الناجحة: {successful_fixes}/{len(fixes)} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 تم إصلاح معظم المشاكل!")
        print("💡 جرب تشغيل البوت الآن")
        print("🧪 أو شغل: python test_connection.py")
    elif success_rate >= 50:
        print("⚠️ تم إصلاح بعض المشاكل")
        print("🔄 قد تحتاج لإعادة تشغيل الكمبيوتر")
    else:
        print("❌ فشل في معظم الإصلاحات")
        print("🆘 تحتاج لمساعدة تقنية متخصصة")
    
    print("\n💡 خطوات إضافية:")
    print("1. أعد تشغيل الكمبيوتر")
    print("2. تحقق من إعدادات الراوتر")
    print("3. جرب شبكة أخرى")
    print("4. اتصل بمزود الإنترنت")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح بواسطة المستخدم")
    except Exception as e:
        print(f"\n💥 خطأ في الإصلاح: {e}")
        sys.exit(1)
