<?php
/**
 * اختبار بسيط وسريع للاتصال
 * Simple and Quick Connection Test
 */

// إعدادات قاعدة البيانات
$SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
$SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - Quick Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .test-section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 14px; overflow-x: auto; }
        h1, h2, h3 { color: #333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار سريع للاتصال</h1>
        
        <?php
        echo '<div class="test-section">';
        echo '<h2>1. معلومات أساسية</h2>';
        echo '<p><strong>PHP Version:</strong> ' . PHP_VERSION . '</p>';
        echo '<p><strong>Server:</strong> ' . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . '</p>';
        echo '<p><strong>Host:</strong> ' . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . '</p>';
        echo '<p><strong>Current Time:</strong> ' . date('Y-m-d H:i:s') . '</p>';
        echo '</div>';
        
        echo '<div class="test-section">';
        echo '<h2>2. فحص cURL</h2>';
        if (function_exists('curl_init')) {
            echo '<p class="success">✅ cURL متوفر</p>';
            
            // اختبار cURL بسيط
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://httpbin.org/get');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                echo '<p class="error">❌ cURL Error: ' . htmlspecialchars($error) . '</p>';
            } elseif ($http_code === 200) {
                echo '<p class="success">✅ cURL يعمل بشكل صحيح (HTTP 200)</p>';
            } else {
                echo '<p class="warning">⚠️ cURL HTTP Code: ' . $http_code . '</p>';
            }
        } else {
            echo '<p class="error">❌ cURL غير متوفر</p>';
        }
        echo '</div>';
        
        echo '<div class="test-section">';
        echo '<h2>3. اختبار Supabase</h2>';
        
        // اختبار الاتصال الأساسي
        echo '<h3>أ. اختبار الاتصال الأساسي</h3>';
        $test_url = $SUPABASE_URL . '/rest/v1/';
        
        $headers = [
            'apikey: ' . $SUPABASE_KEY,
            'Authorization: Bearer ' . $SUPABASE_KEY,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $test_url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo '<p><strong>URL:</strong> ' . htmlspecialchars($test_url) . '</p>';
        
        if ($error) {
            echo '<p class="error">❌ cURL Error: ' . htmlspecialchars($error) . '</p>';
        } else {
            echo '<p><strong>HTTP Code:</strong> ' . $http_code . '</p>';
            
            if ($http_code >= 200 && $http_code < 300) {
                echo '<p class="success">✅ الاتصال ناجح!</p>';
            } elseif ($http_code === 404) {
                echo '<p class="error">❌ خطأ 404 - الرابط غير صحيح أو الجدول غير موجود</p>';
            } elseif ($http_code === 401 || $http_code === 403) {
                echo '<p class="error">❌ خطأ في المصادقة - تحقق من مفتاح API</p>';
            } else {
                echo '<p class="error">❌ خطأ HTTP: ' . $http_code . '</p>';
            }
            
            if ($response) {
                echo '<p><strong>Response:</strong></p>';
                echo '<div class="code">' . htmlspecialchars(substr($response, 0, 500)) . '</div>';
            }
        }
        
        // اختبار جدول المودات
        echo '<h3>ب. اختبار جدول المودات</h3>';
        $mods_url = $SUPABASE_URL . '/rest/v1/minemods?limit=3';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $mods_url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo '<p><strong>URL:</strong> ' . htmlspecialchars($mods_url) . '</p>';
        
        if ($error) {
            echo '<p class="error">❌ cURL Error: ' . htmlspecialchars($error) . '</p>';
        } else {
            echo '<p><strong>HTTP Code:</strong> ' . $http_code . '</p>';
            
            if ($http_code === 200) {
                echo '<p class="success">✅ جدول المودات متاح!</p>';
                
                $data = json_decode($response, true);
                if ($data && is_array($data)) {
                    echo '<p class="info">📊 عدد المودات المسترجعة: ' . count($data) . '</p>';
                    
                    if (!empty($data)) {
                        echo '<p><strong>أول مود:</strong></p>';
                        $first_mod = $data[0];
                        echo '<div class="code">';
                        echo 'ID: ' . ($first_mod['id'] ?? 'N/A') . '<br>';
                        echo 'Name: ' . htmlspecialchars($first_mod['name'] ?? 'N/A') . '<br>';
                        echo 'Version: ' . htmlspecialchars($first_mod['version'] ?? 'N/A') . '<br>';
                        echo 'Category: ' . htmlspecialchars($first_mod['category'] ?? 'N/A') . '<br>';
                        echo '</div>';
                    }
                } else {
                    echo '<p class="warning">⚠️ استجابة غير صحيحة أو فارغة</p>';
                }
            } else {
                echo '<p class="error">❌ فشل في الوصول لجدول المودات</p>';
            }
            
            if ($response) {
                echo '<p><strong>Response Sample:</strong></p>';
                echo '<div class="code">' . htmlspecialchars(substr($response, 0, 300)) . '</div>';
            }
        }
        
        echo '</div>';
        
        // اختبار مود محدد
        echo '<div class="test-section">';
        echo '<h2>4. اختبار مود محدد (ID: 1)</h2>';
        
        $specific_mod_url = $SUPABASE_URL . '/rest/v1/minemods?id=eq.1';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $specific_mod_url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo '<p class="error">❌ cURL Error: ' . htmlspecialchars($error) . '</p>';
        } else {
            if ($http_code === 200) {
                $data = json_decode($response, true);
                if ($data && is_array($data) && !empty($data)) {
                    echo '<p class="success">✅ تم العثور على المود!</p>';
                    $mod = $data[0];
                    echo '<div class="code">';
                    echo '<strong>تفاصيل المود:</strong><br>';
                    echo 'ID: ' . ($mod['id'] ?? 'N/A') . '<br>';
                    echo 'Name: ' . htmlspecialchars($mod['name'] ?? 'N/A') . '<br>';
                    echo 'Description: ' . htmlspecialchars(substr($mod['description'] ?? 'N/A', 0, 100)) . '<br>';
                    echo 'Version: ' . htmlspecialchars($mod['version'] ?? 'N/A') . '<br>';
                    echo 'Download URL: ' . htmlspecialchars($mod['download_url'] ?? 'N/A') . '<br>';
                    echo '</div>';
                } else {
                    echo '<p class="warning">⚠️ لم يتم العثور على مود بالمعرف 1</p>';
                }
            } else {
                echo '<p class="error">❌ فشل في جلب المود - HTTP: ' . $http_code . '</p>';
            }
        }
        
        echo '</div>';
        
        // ملخص النتائج
        echo '<div class="test-section" style="background: #e7f3ff;">';
        echo '<h2>📊 ملخص النتائج</h2>';
        
        $curl_ok = function_exists('curl_init');
        $basic_connection = ($http_code >= 200 && $http_code < 300);
        
        echo '<ul>';
        echo '<li>' . ($curl_ok ? '<span class="success">✅</span>' : '<span class="error">❌</span>') . ' cURL متوفر</li>';
        echo '<li>' . ($basic_connection ? '<span class="success">✅</span>' : '<span class="error">❌</span>') . ' الاتصال بـ Supabase</li>';
        echo '</ul>';
        
        if ($curl_ok && $basic_connection) {
            echo '<p class="success"><strong>🎉 الاختبار ناجح! يمكنك الآن استخدام الملفات الجديدة.</strong></p>';
            echo '<p><strong>الخطوة التالية:</strong></p>';
            echo '<ul>';
            echo '<li>استبدل <code>index.php</code> بـ <code>index_new.php</code></li>';
            echo '<li>استبدل <code>api.php</code> بـ <code>api_new.php</code></li>';
            echo '<li>استبدل <code>config.php</code> بـ <code>config_new.php</code></li>';
            echo '</ul>';
        } else {
            echo '<p class="error"><strong>❌ يحتاج إصلاح قبل المتابعة</strong></p>';
            if (!$curl_ok) {
                echo '<p>- تأكد من تفعيل cURL في الاستضافة</p>';
            }
            if (!$basic_connection) {
                echo '<p>- تحقق من رابط Supabase ومفتاح API</p>';
            }
        }
        
        echo '</div>';
        ?>
        
        <div style="text-align: center; margin-top: 20px;">
            <button onclick="location.reload()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
                🔄 إعادة الاختبار
            </button>
        </div>
    </div>
</body>
</html>
