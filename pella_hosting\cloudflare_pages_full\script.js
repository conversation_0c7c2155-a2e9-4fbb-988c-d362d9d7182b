/**
 * ملف الجافاسكريبت لصفحة عرض المودات - محسن للهواتف
 * JavaScript for Mod Details Page - Mobile Optimized
 * Cloudflare Pages Version
 */

// متغيرات عامة
let currentImageIndex = 0;
let imageUrls = [];
let modData = null;
let adsSettings = null;
let pageCustomization = null;
let modDownloadUrl = '';
let modId = '';
let modTitle = '';
let lang = 'ar';
let userId = '';
let channelId = '';
let isPreview = false;
let adShown = false;
let isDownloading = false;
let isDownloaded = false;
let downloadProgress = 0;
let modFileSize = 0;
let downloadedFileName = '';
let downloadStartTime = 0;

// عناصر DOM
let mainModImage, prevButton, nextButton, thumbnailContainer;
let downloadButton, downloadIcon, downloadText, progressBar;
let loadingScreen, errorScreen, mainContent;

// إعدادات Supabase
const SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";

/**
 * تهيئة الصفحة
 */
function initializePage() {
    // الحصول على عناصر DOM
    initializeElements();
    
    // استخراج المعاملات من الرابط
    extractUrlParameters();
    
    // تحميل البيانات
    loadPageData();
    
    // إعداد مستمعي الأحداث
    setupEventListeners();
}

/**
 * تهيئة عناصر DOM
 */
function initializeElements() {
    mainModImage = document.getElementById('main-mod-image');
    prevButton = document.getElementById('prev-image');
    nextButton = document.getElementById('next-image');
    thumbnailContainer = document.getElementById('thumbnail-container');
    downloadButton = document.getElementById('download-button');
    downloadIcon = document.getElementById('download-icon');
    downloadText = document.getElementById('download-text');
    progressBar = document.getElementById('progress-bar');
    loadingScreen = document.getElementById('loading-screen');
    errorScreen = document.getElementById('error-screen');
    mainContent = document.getElementById('main-content');
}

/**
 * استخراج المعاملات من الرابط
 */
function extractUrlParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    modId = urlParams.get('id') || '1';
    lang = urlParams.get('lang') || 'ar';
    userId = urlParams.get('user_id') || '';
    channelId = urlParams.get('channel') || '';
    isPreview = urlParams.get('preview') === '1';
    
    // تحديث اتجاه الصفحة
    document.documentElement.lang = lang;
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
}

/**
 * تحميل بيانات الصفحة
 */
async function loadPageData() {
    try {
        if (isPreview) {
            await loadPreviewData();
        } else {
            await loadModData();
        }
        
        // تحميل إعدادات إضافية
        if (userId) {
            await Promise.all([
                loadAdsSettings(),
                loadPageCustomization()
            ]);
        }
        
        // عرض البيانات
        displayModData();
        applyCustomization();
        hideLoadingScreen();
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        showErrorScreen(error.message);
    }
}

/**
 * تحميل بيانات المود من Supabase
 */
async function loadModData() {
    const response = await fetch(`${SUPABASE_URL}/rest/v1/mods?id=eq.${modId}`, {
        headers: {
            'apikey': SUPABASE_KEY,
            'Authorization': `Bearer ${SUPABASE_KEY}`,
            'Content-Type': 'application/json'
        }
    });
    
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: فشل في جلب بيانات المود`);
    }
    
    const data = await response.json();
    if (!data || data.length === 0) {
        throw new Error('المود غير موجود');
    }
    
    modData = data[0];
}

/**
 * تحميل بيانات المعاينة
 */
async function loadPreviewData() {
    modData = {
        id: 'preview-mod-id',
        name: lang === 'ar' ? 'مود تجريبي للمعاينة' : 'Preview Demo Mod',
        description: lang === 'ar' ? 
            'هذا مود تجريبي لمعاينة تصميم الصفحة. يمكنك رؤية كيف ستبدو صفحة المود مع التخصيصات التي قمت بها.' : 
            'This is a demo mod for page preview. You can see how your mod page will look with your customizations.',
        description_ar: 'هذا مود تجريبي لمعاينة تصميم الصفحة. يمكنك رؤية كيف ستبدو صفحة المود مع التخصيصات التي قمت بها.',
        version: '1.0.0',
        category: 'addons',
        download_url: '#preview-download',
        image_urls: [
            'https://via.placeholder.com/800x450/4F46E5/FFFFFF?text=' + encodeURIComponent(lang === 'ar' ? 'صورة تجريبية 1' : 'Demo Image 1'),
            'https://via.placeholder.com/800x450/7C3AED/FFFFFF?text=' + encodeURIComponent(lang === 'ar' ? 'صورة تجريبية 2' : 'Demo Image 2'),
            'https://via.placeholder.com/800x450/059669/FFFFFF?text=' + encodeURIComponent(lang === 'ar' ? 'صورة تجريبية 3' : 'Demo Image 3')
        ]
    };
}

/**
 * تحميل إعدادات الإعلانات
 */
async function loadAdsSettings() {
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/user_ads_settings?user_id=eq.${userId}`, {
            headers: {
                'apikey': SUPABASE_KEY,
                'Authorization': `Bearer ${SUPABASE_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            adsSettings = data.length > 0 ? data[0] : null;
        }
    } catch (error) {
        console.warn('فشل في تحميل إعدادات الإعلانات:', error);
    }
}

/**
 * تحميل إعدادات تخصيص الصفحة
 */
async function loadPageCustomization() {
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/page_customization_settings?user_id=eq.${userId}`, {
            headers: {
                'apikey': SUPABASE_KEY,
                'Authorization': `Bearer ${SUPABASE_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            pageCustomization = data.length > 0 ? data[0] : null;
        }
    } catch (error) {
        console.warn('فشل في تحميل إعدادات التخصيص:', error);
    }
}

/**
 * عرض بيانات المود
 */
function displayModData() {
    if (!modData) return;
    
    // تحديث العنوان
    modTitle = modData.name || 'N/A';
    document.getElementById('mod-title').textContent = modTitle;
    document.getElementById('page-title').textContent = `${modTitle} - ${lang === 'ar' ? 'تفاصيل المود' : 'Mod Details'}`;
    
    // تحديث الصور
    imageUrls = modData.image_urls || [];
    if (imageUrls.length > 0) {
        mainModImage.src = imageUrls[0];
        updateThumbnails();
    }
    
    // تحديث المعلومات
    document.getElementById('mod-version').textContent = modData.version || 'N/A';
    
    // تحديث التصنيف
    const categoryNames = {
        'ar': {
            'addons': 'إضافات',
            'shaders': 'شيدرات', 
            'texture_packs': 'حزم النسيج',
            'seeds': 'بذور',
            'maps': 'خرائط',
            'unknown': 'غير محدد'
        },
        'en': {
            'addons': 'Add-ons',
            'shaders': 'Shaders',
            'texture_packs': 'Texture Packs', 
            'seeds': 'Seeds',
            'maps': 'Maps',
            'unknown': 'Not specified'
        }
    };
    
    const categoryKey = (modData.category || 'unknown').toLowerCase();
    const categoryName = categoryNames[lang][categoryKey] || categoryNames[lang]['unknown'];
    document.getElementById('mod-category').textContent = categoryName;
    
    // تحديث الوصف
    const description = lang === 'ar' ? 
        (modData.description_ar || modData.description) : 
        (modData.description || modData.description_ar);
    document.getElementById('mod-description').textContent = description || 
        (lang === 'ar' ? 'لا يوجد وصف متاح' : 'No description available');
    
    // تحديث رابط التحميل
    modDownloadUrl = modData.download_url || '#';
    
    // إظهار إشعار المعاينة
    if (isPreview) {
        document.getElementById('preview-notice').classList.remove('hidden');
    }
}

/**
 * تطبيق التخصيصات
 */
function applyCustomization() {
    if (!pageCustomization) return;
    
    // تطبيق الستايل
    const styleTemplate = pageCustomization.style_template || 'default';
    document.body.className = `style-template-${styleTemplate}`;
    document.body.setAttribute('data-style', styleTemplate);
    
    // تطبيق الألوان المخصصة
    const root = document.documentElement;
    if (pageCustomization.custom_bg_color) {
        root.style.setProperty('--bg-color', pageCustomization.custom_bg_color);
    }
    if (pageCustomization.custom_header_color) {
        root.style.setProperty('--header-color', pageCustomization.custom_header_color);
    }
    if (pageCustomization.custom_button_color) {
        root.style.setProperty('--button-color', pageCustomization.custom_button_color);
    }
    if (pageCustomization.custom_text_color) {
        root.style.setProperty('--text-color', pageCustomization.custom_text_color);
    }
    
    // تطبيق اسم الموقع
    if (pageCustomization.site_name) {
        document.getElementById('site-name').textContent = pageCustomization.site_name;
    }
    
    // تطبيق نص زر التحميل
    const downloadButtonText = lang === 'ar' ? 
        (pageCustomization.download_button_text_ar || 'تحميل المود') :
        (pageCustomization.download_button_text_en || 'Download Mod');
    document.getElementById('download-text').textContent = downloadButtonText;
    
    // تطبيق صورة القناة
    if (pageCustomization.channel_logo_url) {
        const logoPosition = pageCustomization.logo_position || 'right';
        if (logoPosition === 'left') {
            document.getElementById('logo-left').classList.remove('hidden');
            document.getElementById('channel-logo-left').src = pageCustomization.channel_logo_url;
            document.getElementById('spacer-left').style.display = 'none';
        } else {
            document.getElementById('logo-right').classList.remove('hidden');
            document.getElementById('channel-logo-right').src = pageCustomization.channel_logo_url;
            document.getElementById('spacer-right').style.display = 'none';
        }
    }
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // أزرار التنقل بين الصور
    prevButton.addEventListener('click', previousImage);
    nextButton.addEventListener('click', nextImage);
    
    // منع إغلاق الصفحة أثناء التحميل
    window.addEventListener('beforeunload', function(event) {
        if (isDownloading) {
            event.preventDefault();
            event.returnValue = '';
        }
    });
    
    // معالجة زر الرجوع للهواتف
    window.addEventListener('popstate', function(event) {
        if (isDownloading) {
            event.preventDefault();
            const confirmMessage = lang === 'ar' ?
                'جاري التحميل. هل تريد المغادرة؟' :
                'Download in progress. Do you want to leave?';
            
            if (confirm(confirmMessage)) {
                window.history.back();
            } else {
                window.history.pushState(null, null, window.location.href);
            }
        }
    });
    
    // تحسين اللمس للهواتف
    setupTouchEvents();
}

/**
 * إعداد أحداث اللمس للهواتف
 */
function setupTouchEvents() {
    let touchStartX = 0;
    let touchEndX = 0;
    
    mainModImage.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    });
    
    mainModImage.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    });
    
    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                nextImage(); // سحب لليسار = الصورة التالية
            } else {
                previousImage(); // سحب لليمين = الصورة السابقة
            }
        }
    }
}

/**
 * الانتقال للصورة السابقة
 */
function previousImage() {
    if (imageUrls.length === 0) return;

    currentImageIndex = (currentImageIndex - 1 + imageUrls.length) % imageUrls.length;
    updateImage();
}

/**
 * الانتقال للصورة التالية
 */
function nextImage() {
    if (imageUrls.length === 0) return;

    currentImageIndex = (currentImageIndex + 1) % imageUrls.length;
    updateImage();
}

/**
 * تحديث الصورة الرئيسية
 */
function updateImage() {
    if (imageUrls.length > 0) {
        mainModImage.src = imageUrls[currentImageIndex];
        mainModImage.classList.add('fade-in');
        setTimeout(() => mainModImage.classList.remove('fade-in'), 500);
    }
    updateThumbnails();
    updateNavigationButtons();
}

/**
 * تحديث الصور المصغرة
 */
function updateThumbnails() {
    thumbnailContainer.innerHTML = '';
    if (imageUrls.length === 0) return;

    for (let i = 0; i < imageUrls.length; i++) {
        const thumbnail = document.createElement('img');
        thumbnail.src = imageUrls[i];
        thumbnail.classList.add('thumbnail');
        thumbnail.loading = 'lazy';

        if (i === currentImageIndex) {
            thumbnail.classList.add('active');
        }

        thumbnail.dataset.index = i;
        thumbnail.addEventListener('click', (event) => {
            event.preventDefault();
            currentImageIndex = parseInt(event.target.dataset.index);
            updateImage();

            // تمرير الصورة المحددة إلى المنتصف
            thumbnail.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'center'
            });
        });

        // إضافة معالج اللمس للهواتف
        thumbnail.addEventListener('touchstart', (event) => {
            event.preventDefault();
            thumbnail.style.transform = 'scale(0.95)';
        });

        thumbnail.addEventListener('touchend', (event) => {
            event.preventDefault();
            thumbnail.style.transform = 'scale(1)';
            currentImageIndex = parseInt(event.target.dataset.index);
            updateImage();
        });

        thumbnailContainer.appendChild(thumbnail);
    }
}

/**
 * تحديث أزرار التنقل
 */
function updateNavigationButtons() {
    if (imageUrls.length <= 1) {
        prevButton.style.display = 'none';
        nextButton.style.display = 'none';
    } else {
        prevButton.style.display = 'flex';
        nextButton.style.display = 'flex';
    }
}

/**
 * معالجة التحميل
 */
async function handleDownload() {
    if (isDownloading || isDownloaded) return;

    // التحقق من وجود إعلان
    if (adsSettings && adsSettings.ads_enabled && !adShown) {
        showAd();
        return;
    }

    startDownload();
}

/**
 * عرض الإعلان
 */
function showAd() {
    if (!adsSettings || !adsSettings.ads_enabled) {
        startDownload();
        return;
    }

    const adOverlay = document.getElementById('ad-overlay');
    const adContent = document.getElementById('ad-content');
    const closeBtn = document.getElementById('close-ad-btn');
    const countdown = document.getElementById('countdown');

    adOverlay.classList.remove('hidden');

    // فتح الإعلان في نافذة جديدة
    if (adsSettings.ad_link) {
        window.open(adsSettings.ad_link, '_blank');
    }

    // عداد تنازلي لإظهار زر الإغلاق
    let timeLeft = adsSettings.ad_close_delay || 5;
    countdown.textContent = lang === 'ar' ?
        `يمكنك الإغلاق خلال ${timeLeft} ثواني` :
        `You can close in ${timeLeft} seconds`;

    const timer = setInterval(() => {
        timeLeft--;
        if (timeLeft > 0) {
            countdown.textContent = lang === 'ar' ?
                `يمكنك الإغلاق خلال ${timeLeft} ثواني` :
                `You can close in ${timeLeft} seconds`;
        } else {
            clearInterval(timer);
            closeBtn.classList.remove('hidden');
            countdown.textContent = lang === 'ar' ?
                'يمكنك الإغلاق الآن' :
                'You can close now';
        }
    }, 1000);

    adShown = true;
}

/**
 * إغلاق الإعلان
 */
function closeAd() {
    document.getElementById('ad-overlay').classList.add('hidden');
    startDownload();
}

/**
 * بدء التحميل
 */
function startDownload() {
    if (isDownloading || isDownloaded) return;

    isDownloading = true;
    downloadStartTime = Date.now();

    // تحديث واجهة زر التحميل
    downloadButton.classList.add('downloading');
    downloadIcon.innerHTML = '<div class="spinner"></div>';
    downloadText.textContent = lang === 'ar' ? 'جاري التحميل...' : 'Downloading...';

    // محاكاة شريط التقدم
    simulateDownloadProgress();

    // فتح رابط التحميل
    if (modDownloadUrl && modDownloadUrl !== '#' && modDownloadUrl !== '#preview-download') {
        window.open(modDownloadUrl, '_blank');
    }

    // إظهار إشعار
    showNotification(
        lang === 'ar' ? 'بدء التحميل...' : 'Download started...',
        'success'
    );
}

/**
 * محاكاة تقدم التحميل
 */
function simulateDownloadProgress() {
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);
            completeDownload();
        }

        progressBar.style.width = progress + '%';
        downloadProgress = progress;
    }, 200);
}

/**
 * إكمال التحميل
 */
function completeDownload() {
    isDownloading = false;
    isDownloaded = true;

    // تحديث واجهة زر التحميل
    downloadButton.classList.remove('downloading');
    downloadButton.classList.add('downloaded');
    downloadIcon.innerHTML = '✅';
    downloadText.textContent = lang === 'ar' ? 'تم التحميل' : 'Downloaded';

    // تأثير النجاح
    downloadButton.classList.add('download-success-animation');
    setTimeout(() => {
        downloadButton.classList.remove('download-success-animation');
    }, 600);

    // إظهار إشعار النجاح
    showNotification(
        lang === 'ar' ? 'تم التحميل بنجاح!' : 'Download completed successfully!',
        'success'
    );
}

/**
 * إظهار الإشعارات
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type} notification-enter`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 3 ثواني
    setTimeout(() => {
        notification.classList.remove('notification-enter');
        notification.classList.add('notification-exit');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

/**
 * إخفاء شاشة التحميل
 */
function hideLoadingScreen() {
    loadingScreen.classList.add('hidden');
    mainContent.classList.remove('hidden');
    mainContent.classList.add('fade-in');
}

/**
 * إظهار شاشة الخطأ
 */
function showErrorScreen(message) {
    loadingScreen.classList.add('hidden');
    document.getElementById('error-message-text').textContent = message;
    errorScreen.classList.remove('hidden');
}

// تصدير الدوال للاستخدام العام
window.handleDownload = handleDownload;
window.closeAd = closeAd;
window.previousImage = previousImage;
window.nextImage = nextImage;
