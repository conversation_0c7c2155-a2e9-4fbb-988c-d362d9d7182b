#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد سريع للاستضافة المجانية
Quick Setup for Free Hosting
"""

import os
import shutil
import sys
from pathlib import Path

def create_env_file():
    """إنشاء ملف .env للاستضافة"""
    env_content = """# إعدادات البوت للاستضافة المجانية
BOT_TOKEN=7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4
ADMIN_CHAT_ID=7513880877
ADMIN_USERNAME=Kim880198

# إعدادات قاعدة البيانات
SUPABASE_URL=https://ytqxxodyecdeosnqoure.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4

# إعدادات التحسين
OPTIMIZATION_ENABLED=true
LOW_RESOURCE_MODE=true
ENVIRONMENT=production
DEBUG=false
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ تم إنشاء ملف .env")

def copy_requirements():
    """نسخ ملف المتطلبات المحسن"""
    if Path('requirements_hosting.txt').exists():
        shutil.copy('requirements_hosting.txt', 'requirements.txt')
        print("✅ تم نسخ متطلبات الاستضافة المحسنة")
    else:
        print("⚠️ ملف requirements_hosting.txt غير موجود")

def create_runtime_file():
    """إنشاء ملف runtime.txt"""
    runtime_content = "python-3.11.0\n"
    
    with open('runtime.txt', 'w') as f:
        f.write(runtime_content)
    
    print("✅ تم إنشاء ملف runtime.txt")

def create_app_json():
    """إنشاء ملف app.json للاستضافة"""
    app_json_content = """{
  "name": "Telegram Mods Bot",
  "description": "بوت تيليجرام لنشر المودات والإضافات",
  "keywords": ["telegram", "bot", "mods", "minecraft"],
  "website": "https://github.com/yourusername/telegram-mods-bot",
  "repository": "https://github.com/yourusername/telegram-mods-bot",
  "logo": "https://telegram.org/img/t_logo.png",
  "success_url": "/",
  "env": {
    "BOT_TOKEN": {
      "description": "توكن البوت من BotFather",
      "value": "7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4"
    },
    "ADMIN_CHAT_ID": {
      "description": "معرف المسؤول",
      "value": "7513880877"
    },
    "SUPABASE_URL": {
      "description": "رابط قاعدة البيانات",
      "value": "https://ytqxxodyecdeosnqoure.supabase.co"
    },
    "SUPABASE_KEY": {
      "description": "مفتاح قاعدة البيانات",
      "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4"
    }
  },
  "formation": {
    "web": {
      "quantity": 1,
      "size": "free"
    }
  },
  "buildpacks": [
    {
      "url": "heroku/python"
    }
  ]
}"""
    
    with open('app.json', 'w', encoding='utf-8') as f:
        f.write(app_json_content)
    
    print("✅ تم إنشاء ملف app.json")

def check_required_files():
    """فحص الملفات المطلوبة"""
    required_files = [
        'main.py',
        'supabase_client.py',
        'hosting_config.py',
        'start_hosting.py',
        'web_server.py',
        'telegram_web_app.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"⚠️ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    else:
        print("✅ جميع الملفات المطلوبة موجودة")
        return True

def run_tests():
    """تشغيل الاختبارات"""
    print("\n🧪 تشغيل اختبار الاتصال...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, 'test_hosting_connection.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ اختبار الاتصال نجح")
            return True
        else:
            print("❌ اختبار الاتصال فشل")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إعداد البوت للاستضافة المجانية")
    print("="*50)
    
    # 1. فحص الملفات المطلوبة
    print("\n1️⃣ فحص الملفات المطلوبة...")
    if not check_required_files():
        print("❌ يرجى التأكد من وجود جميع الملفات المطلوبة")
        return False
    
    # 2. إنشاء ملفات الإعداد
    print("\n2️⃣ إنشاء ملفات الإعداد...")
    create_env_file()
    copy_requirements()
    create_runtime_file()
    create_app_json()
    
    # 3. تشغيل الاختبارات
    print("\n3️⃣ تشغيل الاختبارات...")
    if not run_tests():
        print("⚠️ فشل في الاختبارات، ولكن يمكن المتابعة")
    
    # 4. ملخص الإعداد
    print("\n" + "="*50)
    print("✅ تم إعداد البوت للاستضافة بنجاح!")
    print("="*50)
    
    print("\n📋 الملفات المُنشأة:")
    print("   ✅ .env - متغيرات البيئة")
    print("   ✅ requirements.txt - المتطلبات")
    print("   ✅ runtime.txt - إصدار Python")
    print("   ✅ app.json - إعدادات التطبيق")
    
    print("\n🚀 خطوات الاستضافة:")
    print("   1. ارفع جميع الملفات للاستضافة")
    print("   2. تأكد من إعداد متغيرات البيئة")
    print("   3. شغل البوت باستخدام start_hosting.py")
    
    print("\n📞 للدعم:")
    print("   - راجع ملف HOSTING_GUIDE.md")
    print("   - شغل test_hosting_connection.py للاختبار")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
