-- تحديث جدول إعدادات تخصيص الصفحة لإضافة الأساليب الجديدة
-- Update page customization settings table to add new styles
-- يجب تشغيل هذا الكود في SQL Editor في لوحة تحكم Supabase
-- قاعدة البيانات: https://ytqxxodyecdeosnqoure.supabase.co

-- التحقق من وجود الجدول أولاً
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'page_customization_settings') THEN
        RAISE EXCEPTION 'Table page_customization_settings does not exist. Please create it first.';
    END IF;
END $$;

-- إضافة الحقول الجديدة للأساليب المتقدمة
ALTER TABLE page_customization_settings
ADD COLUMN IF NOT EXISTS style_template TEXT DEFAULT 'default';

-- إعدادات الألوان المتقدمة
ALTER TABLE page_customization_settings 
ADD COLUMN IF NOT EXISTS custom_accent_color TEXT;

ALTER TABLE page_customization_settings 
ADD COLUMN IF NOT EXISTS custom_card_color TEXT;

ALTER TABLE page_customization_settings 
ADD COLUMN IF NOT EXISTS custom_shadow_color TEXT;

-- إعدادات الخطوط والتأثيرات
ALTER TABLE page_customization_settings 
ADD COLUMN IF NOT EXISTS custom_font_family TEXT DEFAULT 'Press Start 2P';

ALTER TABLE page_customization_settings 
ADD COLUMN IF NOT EXISTS custom_font_size TEXT DEFAULT 'medium' CHECK (custom_font_size IN ('small', 'medium', 'large', 'extra-large'));

ALTER TABLE page_customization_settings 
ADD COLUMN IF NOT EXISTS enable_animations BOOLEAN DEFAULT true;

ALTER TABLE page_customization_settings 
ADD COLUMN IF NOT EXISTS enable_gradients BOOLEAN DEFAULT true;

ALTER TABLE page_customization_settings 
ADD COLUMN IF NOT EXISTS enable_shadows BOOLEAN DEFAULT true;

-- إعدادات التخطيط
ALTER TABLE page_customization_settings 
ADD COLUMN IF NOT EXISTS layout_style TEXT DEFAULT 'modern' CHECK (layout_style IN ('modern', 'compact', 'spacious', 'minimal'));

ALTER TABLE page_customization_settings 
ADD COLUMN IF NOT EXISTS border_radius TEXT DEFAULT 'medium' CHECK (border_radius IN ('none', 'small', 'medium', 'large', 'round'));

-- تحديث القيود للحقل page_theme ليشمل الأساليب الجديدة
ALTER TABLE page_customization_settings 
DROP CONSTRAINT IF EXISTS page_customization_settings_page_theme_check;

ALTER TABLE page_customization_settings 
ADD CONSTRAINT page_customization_settings_page_theme_check 
CHECK (page_theme IN ('default', 'telegram', 'tiktok', 'classic', 'professional', 'dark', 'light', 'custom'));

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_page_customization_style_template ON page_customization_settings(style_template);
CREATE INDEX IF NOT EXISTS idx_page_customization_layout_style ON page_customization_settings(layout_style);

-- إدراج قوالب افتراضية للأساليب الجديدة (اختياري)
-- يمكن استخدام هذه البيانات كمرجع للأساليب المختلفة

-- قالب ستايل تيليجرام
INSERT INTO page_customization_settings (
    user_id, 
    site_name, 
    style_template, 
    page_theme,
    custom_bg_color, 
    custom_header_color, 
    custom_text_color, 
    custom_button_color,
    custom_accent_color,
    layout_style,
    enable_gradients
) VALUES (
    'template_telegram', 
    'Telegram Style', 
    'telegram', 
    'telegram',
    '#0088cc', 
    '#0088cc', 
    '#ffffff', 
    '#40a7e3',
    '#64b5f6',
    'modern',
    true
) ON CONFLICT (user_id) DO NOTHING;

-- قالب ستايل تيك توك
INSERT INTO page_customization_settings (
    user_id, 
    site_name, 
    style_template, 
    page_theme,
    custom_bg_color, 
    custom_header_color, 
    custom_text_color, 
    custom_button_color,
    custom_accent_color,
    layout_style,
    enable_animations,
    enable_gradients
) VALUES (
    'template_tiktok', 
    'TikTok Style', 
    'tiktok', 
    'custom',
    '#000000', 
    '#ff0050', 
    '#ffffff', 
    '#ff0050',
    '#25f4ee',
    'modern',
    true,
    true
) ON CONFLICT (user_id) DO NOTHING;

-- قالب ستايل كلاسيكي
INSERT INTO page_customization_settings (
    user_id, 
    site_name, 
    style_template, 
    page_theme,
    custom_bg_color, 
    custom_header_color, 
    custom_text_color, 
    custom_button_color,
    custom_accent_color,
    layout_style,
    border_radius,
    enable_shadows
) VALUES (
    'template_classic', 
    'Classic Style', 
    'classic', 
    'custom',
    '#f5f5dc', 
    '#8b4513', 
    '#2f4f4f', 
    '#cd853f',
    '#daa520',
    'spacious',
    'small',
    true
) ON CONFLICT (user_id) DO NOTHING;

-- قالب ستايل احترافي
INSERT INTO page_customization_settings (
    user_id, 
    site_name, 
    style_template, 
    page_theme,
    custom_bg_color, 
    custom_header_color, 
    custom_text_color, 
    custom_button_color,
    custom_accent_color,
    layout_style,
    border_radius,
    enable_shadows,
    enable_gradients
) VALUES (
    'template_professional', 
    'Professional Style', 
    'professional', 
    'custom',
    '#f8f9fa', 
    '#2c3e50', 
    '#2c3e50', 
    '#3498db',
    '#e74c3c',
    'minimal',
    'medium',
    true,
    false
) ON CONFLICT (user_id) DO NOTHING;

-- التحقق من نجاح التحديث
SELECT 'page_customization_settings updated successfully' as status, 
       count(*) as total_records 
FROM page_customization_settings;

-- عرض الحقول الجديدة
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'page_customization_settings' 
  AND column_name IN ('style_template', 'custom_accent_color', 'layout_style', 'enable_animations')
ORDER BY column_name;
