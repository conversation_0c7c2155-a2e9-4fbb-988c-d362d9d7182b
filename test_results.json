{"timestamp": "2025-06-12T20:18:20.503647", "summary": {"total": 41, "passed": 41, "failed": 0, "success_rate": 100.0}, "details": [{"test": "وجود قنوات نظام الإعلانات", "result": true, "details": "عد<PERSON> القنوات: 3", "timestamp": "2025-06-12T20:18:20.443010"}, {"test": "وجود القناة @shadercraft443", "result": true, "details": "موجودة في الإعدادات", "timestamp": "2025-06-12T20:18:20.443165"}, {"test": "وجود القناة @mods_addons_for_minecraft_pe", "result": true, "details": "موجودة في الإعدادات", "timestamp": "2025-06-12T20:18:20.443409"}, {"test": "وجود القناة @mineemods", "result": true, "details": "موجودة في الإعدادات", "timestamp": "2025-06-12T20:18:20.443544"}, {"test": "مستوى 0 دعوات", "result": true, "details": "المستوى: 0, الاسم: عادي", "timestamp": "2025-06-12T20:18:20.453242"}, {"test": "مستوى 1 دعوات", "result": true, "details": "المستوى: 1, الاسم: مب<PERSON><PERSON>ئ", "timestamp": "2025-06-12T20:18:20.457438"}, {"test": "مستوى 3 دعوات", "result": true, "details": "المستوى: 2, الاسم: متقدم", "timestamp": "2025-06-12T20:18:20.459190"}, {"test": "مستوى 5 دعوات", "result": true, "details": "المستوى: 3, الاسم: محترف", "timestamp": "2025-06-12T20:18:20.460552"}, {"test": "مستوى 10 دعوات", "result": true, "details": "المستوى: 4, الاسم: VIP", "timestamp": "2025-06-12T20:18:20.461855"}, {"test": "الوصول لـ url_shortener بدون تفعيل", "result": true, "details": "النتيجة: False, المتوقع: False", "timestamp": "2025-06-12T20:18:20.464585"}, {"test": "الوصول لـ tasks_system بدون تفعيل", "result": true, "details": "النتيجة: False, المتوقع: False", "timestamp": "2025-06-12T20:18:20.465162"}, {"test": "الوصول لـ ads_system بدون تفعيل", "result": true, "details": "النتيجة: False, المتوقع: False", "timestamp": "2025-06-12T20:18:20.465358"}, {"test": "حالة التفعيل قبل التفعيل", "result": true, "details": "مفعل: False", "timestamp": "2025-06-12T20:18:20.466178"}, {"test": "تفعيل الميزة", "result": true, "details": "تم التفعيل بنجاح", "timestamp": "2025-06-12T20:18:20.467626"}, {"test": "حالة التفعيل بعد التفعيل", "result": true, "details": "مفعل: True", "timestamp": "2025-06-12T20:18:20.468109"}, {"test": "إعادة تعيين التفعيل", "result": true, "details": "تم إعادة التعيين بنجاح", "timestamp": "2025-06-12T20:18:20.472152"}, {"test": "حالة التفعيل بعد إعادة التعيين", "result": true, "details": "مفعل: False", "timestamp": "2025-06-12T20:18:20.472928"}, {"test": "منح مكافآت المستوى 1", "result": true, "details": "عد<PERSON> المكافآت: 1", "timestamp": "2025-06-12T20:18:20.478173"}, {"test": "تفعيل قنوات غير محدودة عند 1 دعوات", "result": true, "details": "مفعل: True", "timestamp": "2025-06-12T20:18:20.478797"}, {"test": "من<PERSON> مكافآت المستوى 3", "result": true, "details": "عد<PERSON> المكافآت: 1", "timestamp": "2025-06-12T20:18:20.480736"}, {"test": "تفعيل قنوات غير محدودة عند 3 دعوات", "result": true, "details": "مفعل: True", "timestamp": "2025-06-12T20:18:20.481286"}, {"test": "تفعيل حزمة الربح الكاملة عند 3 دعوات", "result": true, "details": "اختصار الروابط: True, روابط مخصصة: True", "timestamp": "2025-06-12T20:18:20.482080"}, {"test": "من<PERSON> مكاف<PERSON><PERSON> المستوى 5", "result": true, "details": "عد<PERSON> المكافآت: 1", "timestamp": "2025-06-12T20:18:20.483766"}, {"test": "تفعيل قنوات غير محدودة عند 5 دعوات", "result": true, "details": "مفعل: True", "timestamp": "2025-06-12T20:18:20.484291"}, {"test": "تفعيل حزمة الربح الكاملة عند 5 دعوات", "result": true, "details": "اختصار الروابط: True, روابط مخصصة: True", "timestamp": "2025-06-12T20:18:20.485065"}, {"test": "تفعيل أوقات النشر الجديدة عند 5 دعوات", "result": true, "details": "مفعل: True", "timestamp": "2025-06-12T20:18:20.485576"}, {"test": "من<PERSON> مكاف<PERSON><PERSON> المستوى 10", "result": true, "details": "عد<PERSON> المكافآت: 1", "timestamp": "2025-06-12T20:18:20.487648"}, {"test": "تفعيل قنوات غير محدودة عند 10 دعوات", "result": true, "details": "مفعل: True", "timestamp": "2025-06-12T20:18:20.488321"}, {"test": "تفعيل حزمة الربح الكاملة عند 10 دعوات", "result": true, "details": "اختصار الروابط: True, روابط مخصصة: True", "timestamp": "2025-06-12T20:18:20.490241"}, {"test": "تفعيل أوقات النشر الجديدة عند 10 دعوات", "result": true, "details": "مفعل: True", "timestamp": "2025-06-12T20:18:20.491701"}, {"test": "تفعيل حزمة VIP عند 10 دعوات", "result": true, "details": "نظام المهام: True, تخصيص VIP: True", "timestamp": "2025-06-12T20:18:20.492735"}, {"test": "أوقات النشر للمستخدم العادي", "result": true, "details": "عدد الأوقات: 15, 10 دقائق: False, 15 دقيقة: False", "timestamp": "2025-06-12T20:18:20.496278"}, {"test": "أوقات النشر للمستخدم المميز", "result": true, "details": "عدد الأوقات: 17 (زيادة: 2)", "timestamp": "2025-06-12T20:18:20.499358"}, {"test": "وجود وقت النشر كل 10 دقائق للمستخدم المميز", "result": true, "details": "موجود: True", "timestamp": "2025-06-12T20:18:20.499535"}, {"test": "وجود وقت النشر كل 15 دقيقة للمستخدم المميز", "result": true, "details": "موجود: True", "timestamp": "2025-06-12T20:18:20.499723"}, {"test": "التحقق من صلاحيات المسؤول", "result": true, "details": "المسؤول: True", "timestamp": "2025-06-12T20:18:20.500278"}, {"test": "وصول المسؤول لـ url_shortener", "result": true, "details": "الوصول: True", "timestamp": "2025-06-12T20:18:20.500419"}, {"test": "وصول المسؤول لـ tasks_system", "result": true, "details": "الوصول: True", "timestamp": "2025-06-12T20:18:20.500553"}, {"test": "وصول المسؤول لـ ads_system", "result": true, "details": "الوصول: True", "timestamp": "2025-06-12T20:18:20.500691"}, {"test": "وصول المسؤول لـ publish_intervals_extended", "result": true, "details": "الوصول: True", "timestamp": "2025-06-12T20:18:20.500824"}, {"test": "وصول المسؤول لجميع أوقات النشر", "result": true, "details": "ع<PERSON><PERSON> الأوقات: 17", "timestamp": "2025-06-12T20:18:20.501426"}]}