@echo off
chcp 65001 >nul
title 🔒 حل مشكلة HTTPS - Minecraft Mods Bot

echo.
echo ================================================================
echo 🔒 حل مشكلة عدم عمل الصفحات على الأجهزة الأخرى
echo 🌐 Fix: Pages Not Working on Other Devices
echo ================================================================
echo.

echo 🚨 المشكلة:
echo صفحات عرض المودات لا تعمل إلا على الكمبيوتر المحلي
echo.

echo ✅ الحل:
echo سيتم إعداد HTTPS تلقائياً للوصول من جميع الأجهزة
echo.

echo 📋 اختر طريقة الحل:
echo 1. 🚀 حل تلقائي مع HTTPS (موصى به)
echo 2. 🏠 حل للشبكة المحلية فقط
echo 3. ⚙️ حل يدوي متقدم
echo 4. 🧪 اختبار النظام
echo 5. ❓ مساعدة ومعلومات
echo.

set /p choice="🔢 أدخل رقم الخيار (1-5): "

if "%choice%"=="1" goto auto_fix
if "%choice%"=="2" goto local_fix
if "%choice%"=="3" goto manual_fix
if "%choice%"=="4" goto test_fix
if "%choice%"=="5" goto help_info

echo ❌ خيار غير صالح
pause
goto start

:auto_fix
echo.
echo ================================================================
echo 🚀 الحل التلقائي مع HTTPS
echo ================================================================
echo.
echo ✨ سيتم:
echo • تحميل وإعداد ngrok تلقائياً
echo • إنشاء رابط HTTPS عام آمن
echo • تحديث إعدادات البوت
echo • تشغيل جميع الخوادم
echo.
echo ⏳ جاري التحضير...
python start_with_https.py
goto end

:local_fix
echo.
echo ================================================================
echo 🏠 الحل للشبكة المحلية
echo ================================================================
echo.
echo 📶 سيتم:
echo • إعداد الوصول عبر الشبكة المحلية
echo • الحصول على IP الشبكة
echo • تشغيل الخوادم على الشبكة
echo • إنشاء دليل الاتصال
echo.
echo ⏳ جاري الإعداد...
python local_network_setup.py
goto end

:manual_fix
echo.
echo ================================================================
echo ⚙️ الحل اليدوي المتقدم
echo ================================================================
echo.
echo 🔧 للمستخدمين المتقدمين:
echo • إعداد ngrok يدوياً
echo • تكوين HTTPS مخصص
echo • خيارات متقدمة
echo.
echo ⏳ جاري فتح الإعداد المتقدم...
python setup_https_access.py
goto end

:test_fix
echo.
echo ================================================================
echo 🧪 اختبار النظام
echo ================================================================
echo.
echo 🔍 سيتم:
echo • اختبار الخوادم
echo • فحص الاتصالات
echo • التحقق من الروابط
echo • إنشاء تقرير الحالة
echo.
echo ⏳ جاري الاختبار...
python test_enhanced_system.py
goto end

:help_info
cls
echo.
echo ================================================================
echo ❓ مساعدة ومعلومات
echo ================================================================
echo.
echo 🚨 المشكلة الشائعة:
echo عندما يضغط المستخدمون على زر "عرض التفاصيل" في البوت،
echo تظهر رسالة "لم يتم العثور على الصفحة" أو "Connection failed"
echo.
echo 🔍 سبب المشكلة:
echo • الرابط يستخدم localhost (127.0.0.1) - يعمل فقط على نفس الجهاز
echo • الرابط يستخدم HTTP بدلاً من HTTPS - تيليجرام يتطلب HTTPS
echo • الصفحة غير متاحة من خارج الشبكة المحلية
echo.
echo ✅ الحل الموصى به:
echo استخدم الخيار رقم 1: "حل تلقائي مع HTTPS"
echo.
echo 📋 ما سيحدث:
echo 1. تحميل أداة ngrok تلقائياً (مجانية)
echo 2. إنشاء رابط HTTPS عام آمن
echo 3. تحديث إعدادات البوت تلقائياً
echo 4. تشغيل جميع الخوادم المطلوبة
echo.
echo 🌍 النتيجة:
echo • يمكن لأي شخص في العالم الوصول للصفحة
echo • يعمل على جميع الأجهزة (هاتف، كمبيوتر، تابلت)
echo • رابط آمن ومشفر (HTTPS)
echo • سرعة عالية وأداء ممتاز
echo.
echo 📱 للاختبار بعد الحل:
echo • افتح الرابط على هاتفك
echo • جرب من شبكة WiFi مختلفة
echo • شارك الرابط مع أصدقائك
echo.
echo 💡 نصائح:
echo • احتفظ بالبرنامج يعمل للحفاظ على الرابط
echo • للاستخدام الدائم، فكر في خدمة استضافة مجانية
echo • راجع ملف FIX_HTTPS_PROBLEM.md للتفاصيل
echo.
echo 📞 للدعم الفني:
echo • تواصل مع المطور: @Kim880198
echo • أرسل screenshot للخطأ إذا واجهت مشاكل
echo.
echo 🔄 متطلبات الحل:
echo • اتصال بالإنترنت (لتحميل ngrok)
echo • Python 3.8+ مثبت
echo • مساحة 50 MB تقريباً
echo.
pause
goto start

:start
cls
echo.
echo ================================================================
echo 🔒 حل مشكلة عدم عمل الصفحات على الأجهزة الأخرى
echo ================================================================
echo.
goto auto_fix

:end
echo.
echo ================================================================
echo 🎉 تم تطبيق الحل!
echo ================================================================
echo.
echo ✅ إذا نجح الحل:
echo • جرب فتح الرابط على هاتفك
echo • شارك الرابط مع المستخدمين
echo • احتفظ بالبرنامج يعمل
echo.
echo ❌ إذا لم ينجح الحل:
echo • جرب الخيار رقم 2 (الشبكة المحلية)
echo • راجع ملف FIX_HTTPS_PROBLEM.md
echo • تواصل مع المطور: @Kim880198
echo.
pause
