# 👨‍💻 دليل المطور - إضافة مميزات جديدة للنظام

## 📋 نظرة عامة

هذا الدليل يوضح كيفية إضافة مميزات جديدة لنظام الاشتراك والدعوات في البوت بطريقة صحيحة ومتسقة.

## 🎯 أنواع المميزات

### 1. **مميزات تتطلب اشتراك في قنوات** (Subscription-based)
- مثل: نظام الإعلانات
- تتطلب اشتراك في قنوات محددة
- التحقق فوري عبر Telegram API

### 2. **مميزات تتطلب دعوات** (Invitation-based)
- مثل: اختصار الروابط، نظام المهام
- تتطلب عدد معين من الدعوات
- التحقق عبر نظام المكافآت

## 🔧 خطوات إضافة ميزة جديدة

### الخطوة 1: تحديد نوع الميزة

```python
# في main.py - تحديث قوائم المميزات

# للمميزات التي تتطلب اشتراك فقط
subscription_only_features = ['ads_system', 'new_subscription_feature']

# للمميزات التي تتطلب دعوات
invitation_restricted_features = {
    'url_shortener': 'url_shortener_access',
    'tasks_system': 'tasks_system_access',
    'new_invitation_feature': 'new_invitation_feature_access',  # إضافة جديدة
    # ... باقي المميزات
}
```

### الخطوة 2: إضافة القنوات المطلوبة (للمميزات التي تتطلب اشتراك)

```python
# في main.py - تحديث REQUIRED_CHANNELS
REQUIRED_CHANNELS = {
    'ads_system': [
        {'username': '@shadercraft443', 'name': 'Shader Craft', 'url': 'https://t.me/shadercraft443'},
        # ... باقي القنوات
    ],
    'new_subscription_feature': [  # إضافة جديدة
        {'username': '@new_channel1', 'name': 'New Channel 1', 'url': 'https://t.me/new_channel1'},
        {'username': '@new_channel2', 'name': 'New Channel 2', 'url': 'https://t.me/new_channel2'},
    ]
}
```

### الخطوة 3: إضافة الميزة لنظام المكافآت (للمميزات التي تتطلب دعوات)

```python
# في دالة grant_invitation_rewards - إضافة الميزة للمكافآت المناسبة
rewards = {
    # ... المكافآت الموجودة
    7: {  # مكافأة جديدة عند 7 دعوات
        'new_invitation_feature_access': True,
        'reward_name': 'الميزة الجديدة',
        'reward_desc': 'وصف الميزة الجديدة'
    }
}
```

### الخطوة 4: إضافة معلومات الميزة للرسائل التحفيزية

```python
# في دالة show_feature_locked_message - إضافة معلومات الميزة
feature_info = {
    # ... المميزات الموجودة
    'new_invitation_feature': {
        'ar': {
            'name': 'الميزة الجديدة',
            'icon': '🆕',
            'requirement_type': 'invitation',
            'requirement_count': 7,
            'requirement_text': 'دعوة 7 أصدقاء أو أكثر',
            'benefits': [
                '🆕 فائدة جديدة 1',
                '⚡ فائدة جديدة 2',
                '🎯 فائدة جديدة 3'
            ]
        },
        'en': {
            'name': 'New Feature',
            'icon': '🆕',
            'requirement_type': 'invitation',
            'requirement_count': 7,
            'requirement_text': 'Invite 7 or more friends',
            'benefits': [
                '🆕 New benefit 1',
                '⚡ New benefit 2',
                '🎯 New benefit 3'
            ]
        }
    }
}
```

### الخطوة 5: إنشاء دالة معالج الميزة

```python
async def new_feature_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض قائمة الميزة الجديدة"""
    query = update.callback_query
    await query.answer()
    
    user_id = query.from_user.id
    lang = get_user_lang(user_id)
    
    # التحقق من الوصول للميزة
    has_access = await check_feature_access(user_id, 'new_invitation_feature', context)
    if not has_access:
        # عرض رسالة تحفيزية موحدة
        await show_feature_locked_message(update, context, user_id, 'new_invitation_feature', lang)
        return
    
    # إذا كان لديه وصول، عرض قائمة الميزة
    texts = {
        "ar": {
            "title": "🆕 <b>الميزة الجديدة</b>",
            "description": "مرحباً بك في الميزة الجديدة!",
            "back": "🔙 العودة"
        },
        "en": {
            "title": "🆕 <b>New Feature</b>",
            "description": "Welcome to the new feature!",
            "back": "🔙 Back"
        }
    }
    
    text = texts.get(lang, texts['ar'])
    
    keyboard = [
        # أزرار الميزة هنا
        [InlineKeyboardButton(text["back"], callback_data="main_menu")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    message = f"{text['title']}\n\n{text['description']}"
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")
```

### الخطوة 6: إضافة معالج الأحداث

```python
# في دالة main() - إضافة معالج الأحداث
application.add_handler(CallbackQueryHandler(new_feature_menu, pattern="^new_feature_menu$"))
```

### الخطوة 7: إضافة زر الوصول للميزة

```python
# في القائمة المناسبة - إضافة زر للميزة الجديدة
keyboard = [
    # ... الأزرار الموجودة
    [InlineKeyboardButton("🆕 الميزة الجديدة", callback_data="new_feature_menu")]
]
```

## 📝 مثال كامل: إضافة ميزة "نظام التقييمات"

### 1. تحديث قوائم المميزات:
```python
invitation_restricted_features = {
    # ... المميزات الموجودة
    'rating_system': 'rating_system_access',
}
```

### 2. إضافة للمكافآت:
```python
rewards = {
    # ... المكافآت الموجودة
    8: {
        'rating_system_access': True,
        'reward_name': 'نظام التقييمات',
        'reward_desc': 'إمكانية تقييم المودات والحصول على تقييمات'
    }
}
```

### 3. إضافة معلومات الميزة:
```python
'rating_system': {
    'ar': {
        'name': 'نظام التقييمات',
        'icon': '⭐',
        'requirement_type': 'invitation',
        'requirement_count': 8,
        'requirement_text': 'دعوة 8 أصدقاء أو أكثر',
        'benefits': [
            '⭐ تقييم المودات',
            '📊 إحصائيات التقييمات',
            '🏆 نظام الترتيب',
            '💬 تعليقات المستخدمين'
        ]
    },
    'en': {
        'name': 'Rating System',
        'icon': '⭐',
        'requirement_type': 'invitation',
        'requirement_count': 8,
        'requirement_text': 'Invite 8 or more friends',
        'benefits': [
            '⭐ Rate mods',
            '📊 Rating statistics',
            '🏆 Ranking system',
            '💬 User comments'
        ]
    }
}
```

### 4. إنشاء دالة المعالج:
```python
async def rating_system_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض قائمة نظام التقييمات"""
    query = update.callback_query
    await query.answer()
    
    user_id = query.from_user.id
    lang = get_user_lang(user_id)
    
    # التحقق من الوصول للميزة
    has_access = await check_feature_access(user_id, 'rating_system', context)
    if not has_access:
        await show_feature_locked_message(update, context, user_id, 'rating_system', lang)
        return
    
    # عرض قائمة نظام التقييمات
    texts = {
        "ar": {
            "title": "⭐ <b>نظام التقييمات</b>",
            "description": "قيّم المودات وشارك رأيك مع المجتمع!",
            "rate_mod": "⭐ تقييم مود",
            "view_ratings": "📊 عرض التقييمات",
            "my_ratings": "📝 تقييماتي",
            "back": "🔙 العودة"
        },
        "en": {
            "title": "⭐ <b>Rating System</b>",
            "description": "Rate mods and share your opinion with the community!",
            "rate_mod": "⭐ Rate Mod",
            "view_ratings": "📊 View Ratings",
            "my_ratings": "📝 My Ratings",
            "back": "🔙 Back"
        }
    }
    
    text = texts.get(lang, texts['ar'])
    
    keyboard = [
        [InlineKeyboardButton(text["rate_mod"], callback_data="rate_mod_menu")],
        [InlineKeyboardButton(text["view_ratings"], callback_data="view_ratings_menu")],
        [InlineKeyboardButton(text["my_ratings"], callback_data="my_ratings_menu")],
        [InlineKeyboardButton(text["back"], callback_data="main_menu")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    message = f"{text['title']}\n\n{text['description']}"
    is_photo = bool(query.message and query.message.photo)
    await update_ui_response(query, message, is_photo=is_photo, reply_markup=reply_markup, parse_mode="HTML")
```

### 5. إضافة المعالجات:
```python
# في دالة main()
application.add_handler(CallbackQueryHandler(rating_system_menu, pattern="^rating_system_menu$"))
application.add_handler(CallbackQueryHandler(rate_mod_menu, pattern="^rate_mod_menu$"))
application.add_handler(CallbackQueryHandler(view_ratings_menu, pattern="^view_ratings_menu$"))
application.add_handler(CallbackQueryHandler(my_ratings_menu, pattern="^my_ratings_menu$"))
```

## 🧪 اختبار الميزة الجديدة

### إضافة اختبار للميزة:
```python
# في test_subscription_invitation_system.py
def test_new_feature_access(self):
    """اختبار الوصول للميزة الجديدة"""
    print("\n🆕 اختبار الميزة الجديدة:")
    print("=" * 50)
    
    # اختبار بدون دعوات كافية
    has_access_before = check_feature_access_sync(self.test_user_id, 'rating_system')
    self.log_test(
        "الوصول لنظام التقييمات بدون دعوات كافية",
        not has_access_before,
        f"الوصول: {has_access_before}"
    )
    
    # منح الدعوات المطلوبة
    grant_invitation_rewards(self.test_user_id, 8)
    
    # اختبار بعد منح الدعوات
    has_access_after = check_user_premium_feature(self.test_user_id, 'rating_system_access')
    self.log_test(
        "الوصول لنظام التقييمات بعد 8 دعوات",
        has_access_after,
        f"الوصول: {has_access_after}"
    )
```

## 📋 قائمة التحقق للمطور

عند إضافة ميزة جديدة، تأكد من:

### ✅ الإعدادات الأساسية:
- [ ] إضافة الميزة للقائمة المناسبة (`subscription_only_features` أو `invitation_restricted_features`)
- [ ] إضافة القنوات المطلوبة (إذا كانت تتطلب اشتراك)
- [ ] إضافة الميزة لنظام المكافآت (إذا كانت تتطلب دعوات)

### ✅ واجهة المستخدم:
- [ ] إضافة معلومات الميزة لدالة `show_feature_locked_message`
- [ ] إنشاء دالة معالج للميزة
- [ ] إضافة معالج الأحداث في `main()`
- [ ] إضافة زر الوصول في القائمة المناسبة

### ✅ الاختبار:
- [ ] إضافة اختبارات للميزة الجديدة
- [ ] تشغيل الاختبارات والتأكد من نجاحها
- [ ] اختبار الميزة يدوياً في البوت

### ✅ التوثيق:
- [ ] تحديث ملف التوثيق
- [ ] إضافة أمثلة للاستخدام
- [ ] توثيق أي متطلبات خاصة

## 🎯 نصائح مهمة

### 🔧 أفضل الممارسات:
1. **استخدم أسماء واضحة** للمميزات والدوال
2. **اتبع نمط التسمية الموجود** في الكود
3. **أضف تسجيل مناسب** للعمليات المهمة
4. **تعامل مع الأخطاء** بشكل مناسب
5. **اختبر الميزة بدقة** قبل النشر

### ⚠️ تجنب الأخطاء الشائعة:
- عدم إضافة الميزة لجميع القوائم المطلوبة
- نسيان إضافة معالج الأحداث
- عدم اختبار الميزة مع مستويات دعوات مختلفة
- عدم إضافة دعم متعدد اللغات

## 🎉 الخلاصة

باتباع هذا الدليل، يمكنك إضافة مميزات جديدة للنظام بطريقة متسقة ومنظمة. النظام مصمم ليكون مرناً وقابلاً للتوسع، مما يسهل إضافة مميزات جديدة دون التأثير على الوظائف الموجودة.

---

*تذكر: اختبر دائماً الميزة الجديدة بدقة قبل نشرها للمستخدمين!*
