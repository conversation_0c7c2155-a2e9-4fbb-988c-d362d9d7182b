<?php
/**
 * مسح السجلات
 * Clear Logs
 */

define('INCLUDED', true);
require_once 'config.php';

header('Content-Type: application/json');

// التحقق من الطريقة
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// التحقق من الصلاحيات
$access_key = $_POST['key'] ?? '';
$required_key = 'admin123'; // يجب تغييره في الإنتاج

if ($access_key !== $required_key) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit;
}

// الحصول على اسم الملف
$file_name = $_POST['file'] ?? '';

if (empty($file_name)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'File name required']);
    exit;
}

// التحقق من صحة اسم الملف
if (!preg_match('/^[a-zA-Z0-9_.-]+\.log$/', $file_name)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid file name']);
    exit;
}

// مسار الملف
$log_dir = dirname(LOG_FILE);
$file_path = $log_dir . '/' . $file_name;

// التحقق من وجود الملف
if (!file_exists($file_path)) {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'File not found']);
    exit;
}

// محاولة مسح محتوى الملف
try {
    $result = file_put_contents($file_path, '');
    
    if ($result !== false) {
        // تسجيل عملية المسح
        $timestamp = date('Y-m-d H:i:s');
        $clear_message = "[$timestamp] [INFO] Log file cleared by admin\n";
        file_put_contents($file_path, $clear_message);
        
        echo json_encode(['success' => true, 'message' => 'Log cleared successfully']);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to clear log file']);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Error: ' . $e->getMessage()]);
}
?>
<?php
/**
 * مسح السجلات
 * Clear Logs
 */

define('INCLUDED', true);
require_once 'config.php';

header('Content-Type: application/json');

// التحقق من الطريقة
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// التحقق من الصلاحيات
$access_key = $_POST['key'] ?? '';
$required_key = 'admin123'; // يجب تغييره في الإنتاج

if ($access_key !== $required_key) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit;
}

// الحصول على اسم الملف
$file_name = $_POST['file'] ?? '';

if (empty($file_name)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'File name required']);
    exit;
}

// التحقق من صحة اسم الملف
if (!preg_match('/^[a-zA-Z0-9_.-]+\.log$/', $file_name)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid file name']);
    exit;
}

// مسار الملف
$log_dir = dirname(LOG_FILE);
$file_path = $log_dir . '/' . $file_name;

// التحقق من وجود الملف
if (!file_exists($file_path)) {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'File not found']);
    exit;
}

// محاولة مسح محتوى الملف
try {
    $result = file_put_contents($file_path, '');
    
    if ($result !== false) {
        // تسجيل عملية المسح
        $timestamp = date('Y-m-d H:i:s');
        $clear_message = "[$timestamp] [INFO] Log file cleared by admin\n";
        file_put_contents($file_path, $clear_message);
        
        echo json_encode(['success' => true, 'message' => 'Log cleared successfully']);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to clear log file']);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Error: ' . $e->getMessage()]);
}
?>
