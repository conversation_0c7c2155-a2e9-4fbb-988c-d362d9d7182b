#!/usr/bin/env python3
"""
اختبار تشغيل البوت بشكل مبسط
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

async def test_bot_components():
    """اختبار مكونات البوت الأساسية"""
    print("🧪 اختبار مكونات البوت...")
    
    # اختبار 1: استيراد المكتبات
    print("📦 اختبار استيراد المكتبات...")
    try:
        import telegram
        from telegram.ext import Application
        print("✅ telegram library")
        
        import requests
        print("✅ requests library")
        
        from supabase_client import get_all_mods
        print("✅ supabase_client")
        
        from web_server import run_web_server
        print("✅ web_server")
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        return False
    
    # اختبار 2: قراءة إعدادات البوت
    print("\n⚙️ اختبار إعدادات البوت...")
    try:
        bot_token = os.environ.get("BOT_TOKEN")
        admin_id = os.environ.get("ADMIN_CHAT_ID")
        
        if not bot_token:
            print("❌ BOT_TOKEN غير موجود في متغيرات البيئة")
            return False
        
        if not admin_id:
            print("❌ ADMIN_CHAT_ID غير موجود في متغيرات البيئة")
            return False
        
        print(f"✅ BOT_TOKEN: {bot_token[:10]}...")
        print(f"✅ ADMIN_CHAT_ID: {admin_id}")
        
    except Exception as e:
        print(f"❌ خطأ في قراءة الإعدادات: {e}")
        return False
    
    # اختبار 3: اختبار قاعدة البيانات
    print("\n🗄️ اختبار قاعدة البيانات...")
    try:
        mods = get_all_mods()
        print(f"✅ تم جلب {len(mods)} مود من قاعدة البيانات")
        
        if len(mods) > 0:
            first_mod = mods[0]
            print(f"📋 مثال: {first_mod.get('title', 'غير محدد')}")
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    
    # اختبار 4: إنشاء تطبيق البوت
    print("\n🤖 اختبار إنشاء تطبيق البوت...")
    try:
        application = Application.builder().token(bot_token).build()
        print("✅ تم إنشاء تطبيق البوت بنجاح")
        
        # اختبار الاتصال مع Telegram
        bot_info = await application.bot.get_me()
        print(f"✅ معلومات البوت: @{bot_info.username}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء تطبيق البوت: {e}")
        return False
    
    print("\n✅ جميع اختبارات المكونات نجحت!")
    return True

async def test_start_command():
    """اختبار دالة /start"""
    print("\n🚀 اختبار دالة /start...")
    
    try:
        # استيراد الدوال من main.py
        from main import start, get_user_lang, load_user_channels
        
        print("✅ تم استيراد دوال البوت بنجاح")
        
        # اختبار دوال مساعدة
        test_user_id = "123456789"
        lang = get_user_lang(test_user_id)
        print(f"✅ get_user_lang: {lang}")
        
        user_channels = load_user_channels()
        print(f"✅ load_user_channels: {len(user_channels)} مستخدم")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دالة /start: {e}")
        return False
    
    print("✅ دالة /start جاهزة للعمل")
    return True

def load_env_file():
    """تحميل متغيرات البيئة من ملف .env"""
    try:
        if os.path.exists('.env'):
            with open('.env', 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key] = value
            print("✅ تم تحميل متغيرات البيئة من .env")
        else:
            print("⚠️ ملف .env غير موجود")
    except Exception as e:
        print(f"❌ خطأ في تحميل ملف .env: {e}")

async def main():
    """الدالة الرئيسية"""
    print("🧪 بدء اختبار تشغيل البوت")
    print("=" * 50)
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # تحميل متغيرات البيئة
    load_env_file()
    
    # تشغيل الاختبارات
    tests = [
        ("اختبار مكونات البوت", test_bot_components),
        ("اختبار دالة /start", test_start_command)
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for test_name, test_function in tests:
        print(f"🧪 {test_name}...")
        try:
            if await test_function():
                print(f"✅ {test_name} - نجح")
                success_count += 1
            else:
                print(f"❌ {test_name} - فشل")
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
        print()
    
    print("=" * 50)
    print(f"📊 النتائج: {success_count}/{total_count} اختبارات نجحت")
    
    if success_count == total_count:
        print("🎉 جميع الاختبارات نجحت! البوت جاهز للتشغيل.")
        print("🚀 يمكنك تشغيل البوت باستخدام: python main.py")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    return success_count == total_count

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
