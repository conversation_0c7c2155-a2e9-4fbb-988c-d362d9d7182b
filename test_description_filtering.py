#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات فلترة المودات بناءً على جودة الأوصاف
Test script for improved mod filtering based on description quality
"""

import sys
import json
import logging
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def is_description_usable(description_text: str, min_length: int = 10) -> bool:
    """
    فحص ما إذا كان الوصف قابل للاستخدام
    Args:
        description_text: نص الوصف
        min_length: الحد الأدنى لطول الوصف
    Returns:
        bool: True إذا كان الوصف قابل للاستخدام
    """
    if not description_text or not isinstance(description_text, str):
        return False
    
    # تنظيف النص
    cleaned_text = description_text.strip()
    
    # فحص الطول
    if len(cleaned_text) < min_length:
        return False
    
    # فحص النصوص غير المفيدة
    useless_phrases = [
        'description unavailable',
        'no description',
        'تفاصيل غير متوفرة',
        'لا يوجد وصف',
        'coming soon',
        'قريباً',
        'n/a',
        'null',
        'undefined',
        'test',
        'اختبار'
    ]
    
    cleaned_lower = cleaned_text.lower()
    for phrase in useless_phrases:
        if phrase in cleaned_lower:
            return False
    
    return True

def test_description_filtering():
    """اختبار فلترة الأوصاف"""
    print("🧪 اختبار فلترة الأوصاف المحسنة")
    print("=" * 60)
    
    # بيانات اختبار متنوعة
    test_descriptions = [
        # أوصاف جيدة
        {
            "text": "هذا مود رائع يضيف ميزات جديدة ومثيرة للعبة Minecraft",
            "expected": True,
            "category": "وصف عربي جيد"
        },
        {
            "text": "This amazing mod adds new features and exciting gameplay to Minecraft",
            "expected": True,
            "category": "وصف إنجليزي جيد"
        },
        {
            "text": "مود يحتوي على أسلحة وأدوات جديدة مع تحسينات في الجرافيك والصوت",
            "expected": True,
            "category": "وصف عربي مفصل"
        },
        
        # أوصاف سيئة
        {
            "text": "",
            "expected": False,
            "category": "وصف فارغ"
        },
        {
            "text": "   ",
            "expected": False,
            "category": "مسافات فقط"
        },
        {
            "text": "test",
            "expected": False,
            "category": "كلمة اختبار"
        },
        {
            "text": "اختبار",
            "expected": False,
            "category": "كلمة اختبار عربية"
        },
        {
            "text": "No description",
            "expected": False,
            "category": "لا يوجد وصف"
        },
        {
            "text": "لا يوجد وصف",
            "expected": False,
            "category": "لا يوجد وصف عربي"
        },
        {
            "text": "Description unavailable",
            "expected": False,
            "category": "وصف غير متوفر"
        },
        {
            "text": "تفاصيل غير متوفرة",
            "expected": False,
            "category": "تفاصيل غير متوفرة عربي"
        },
        {
            "text": "Coming soon",
            "expected": False,
            "category": "قريباً"
        },
        {
            "text": "قريباً",
            "expected": False,
            "category": "قريباً عربي"
        },
        {
            "text": "N/A",
            "expected": False,
            "category": "غير متوفر"
        },
        {
            "text": "null",
            "expected": False,
            "category": "قيمة فارغة"
        },
        {
            "text": "undefined",
            "expected": False,
            "category": "غير معرف"
        },
        {
            "text": "short",
            "expected": False,
            "category": "وصف قصير جداً"
        },
        
        # حالات حدية
        {
            "text": "This is exactly ten chars",
            "expected": True,
            "category": "وصف بالحد الأدنى للطول"
        },
        {
            "text": "Nine char",
            "expected": False,
            "category": "وصف أقل من الحد الأدنى"
        },
        {
            "text": None,
            "expected": False,
            "category": "قيمة None"
        },
        {
            "text": 123,
            "expected": False,
            "category": "رقم بدلاً من نص"
        },
        {
            "text": ["list", "instead", "of", "string"],
            "expected": False,
            "category": "قائمة بدلاً من نص"
        }
    ]
    
    passed_tests = 0
    failed_tests = 0
    
    print(f"📋 تشغيل {len(test_descriptions)} اختبار...")
    print()
    
    for i, test_case in enumerate(test_descriptions, 1):
        text = test_case["text"]
        expected = test_case["expected"]
        category = test_case["category"]
        
        try:
            result = is_description_usable(text)
            
            if result == expected:
                status = "✅ نجح"
                passed_tests += 1
            else:
                status = "❌ فشل"
                failed_tests += 1
                
            print(f"{i:2d}. {status} | {category}")
            print(f"    النص: {repr(text)}")
            print(f"    متوقع: {expected}, النتيجة: {result}")
            
            if result != expected:
                print(f"    ⚠️  النتيجة غير متطابقة!")
            print()
            
        except Exception as e:
            print(f"{i:2d}. ❌ خطأ | {category}")
            print(f"    النص: {repr(text)}")
            print(f"    خطأ: {e}")
            print()
            failed_tests += 1
    
    # النتائج النهائية
    print("=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"✅ اختبارات نجحت: {passed_tests}")
    print(f"❌ اختبارات فشلت: {failed_tests}")
    print(f"📈 معدل النجاح: {(passed_tests / len(test_descriptions)) * 100:.1f}%")
    
    if failed_tests == 0:
        print("🎉 جميع الاختبارات نجحت!")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت، يرجى مراجعة الكود.")
        return False

def test_mod_filtering_simulation():
    """محاكاة فلترة المودات"""
    print("\n" + "=" * 60)
    print("🎮 محاكاة فلترة المودات")
    print("=" * 60)
    
    # بيانات مودات تجريبية
    test_mods = [
        {
            "id": "mod_001",
            "title": "مود الأسلحة الجديدة",
            "telegram_description_ar": "مود رائع يضيف أسلحة جديدة ومثيرة للعبة",
            "telegram_description_en": "Amazing mod that adds new and exciting weapons to the game",
            "description": {"ar": "وصف أساسي عربي", "en": "Basic English description"},
            "description_ar": "وصف عربي إضافي"
        },
        {
            "id": "mod_002", 
            "title": "مود بدون وصف تليجرام",
            "telegram_description_ar": "",
            "telegram_description_en": "",
            "description": {"ar": "هذا وصف أساسي جيد باللغة العربية", "en": "This is a good basic description in English"},
            "description_ar": ""
        },
        {
            "id": "mod_003",
            "title": "مود بوصف سيء",
            "telegram_description_ar": "test",
            "telegram_description_en": "N/A",
            "description": {"ar": "اختبار", "en": "test"},
            "description_ar": "قريباً"
        },
        {
            "id": "mod_004",
            "title": "مود بدون أوصاف",
            "telegram_description_ar": "",
            "telegram_description_en": "",
            "description": {},
            "description_ar": ""
        },
        {
            "id": "mod_005",
            "title": "مود بوصف أساسي فقط",
            "telegram_description_ar": "",
            "telegram_description_en": "",
            "description": "This is a simple string description that should work fine",
            "description_ar": ""
        }
    ]
    
    print(f"📋 فحص {len(test_mods)} مود...")
    print()
    
    usable_mods = []
    skipped_mods = []
    
    for mod in test_mods:
        mod_id = mod.get('id')
        mod_title = mod.get('title')
        
        # محاكاة منطق الفلترة من الكود الأساسي
        telegram_desc_ar = mod.get('telegram_description_ar', '').strip()
        telegram_desc_en = mod.get('telegram_description_en', '').strip()
        
        # إذا لم توجد أوصاف تيليجرام، استخدم الأوصاف الأساسية
        if not telegram_desc_ar and not telegram_desc_en:
            description_data = mod.get('description', {})
            description_ar_data = mod.get('description_ar', '').strip()
            
            if isinstance(description_data, dict):
                telegram_desc_ar = description_data.get('ar', '')
                telegram_desc_en = description_data.get('en', '')
            elif isinstance(description_data, str) and description_data.strip():
                telegram_desc_en = description_data.strip()
            
            if description_ar_data:
                telegram_desc_ar = description_ar_data
        
        # فحص الأوصاف
        has_usable_descriptions = (
            is_description_usable(telegram_desc_en) or 
            is_description_usable(telegram_desc_ar)
        )
        
        print(f"🔍 {mod_id} - {mod_title}")
        print(f"   وصف تليجرام عربي: {bool(telegram_desc_ar)} (طول: {len(telegram_desc_ar)})")
        print(f"   وصف تليجرام إنجليزي: {bool(telegram_desc_en)} (طول: {len(telegram_desc_en)})")
        print(f"   قابل للاستخدام: {has_usable_descriptions}")
        
        if has_usable_descriptions:
            usable_mods.append(mod)
            print("   ✅ سيتم اقتراحه")
        else:
            skipped_mods.append(mod)
            print("   ❌ سيتم تخطيه")
        print()
    
    print("=" * 60)
    print("📊 نتائج الفلترة:")
    print(f"✅ مودات قابلة للاستخدام: {len(usable_mods)}")
    print(f"❌ مودات تم تخطيها: {len(skipped_mods)}")
    print(f"📈 معدل القبول: {(len(usable_mods) / len(test_mods)) * 100:.1f}%")
    
    if usable_mods:
        print("\n🎯 المودات التي ستظهر للمستخدمين:")
        for mod in usable_mods:
            print(f"   • {mod['id']} - {mod['title']}")
    
    if skipped_mods:
        print("\n🚫 المودات التي تم تخطيها:")
        for mod in skipped_mods:
            print(f"   • {mod['id']} - {mod['title']}")

if __name__ == "__main__":
    print("🚀 بدء اختبار تحسينات فلترة المودات")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # تشغيل اختبار فحص الأوصاف
    description_test_passed = test_description_filtering()
    
    # تشغيل محاكاة فلترة المودات
    test_mod_filtering_simulation()
    
    print("\n" + "=" * 60)
    if description_test_passed:
        print("🎉 جميع الاختبارات نجحت! التحسينات جاهزة للاستخدام.")
    else:
        print("⚠️ بعض الاختبارات فشلت، يرجى مراجعة الكود قبل التطبيق.")
    print("=" * 60)
