#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع للبوت مع فحص أولي
Quick Start Bot with Initial Check
"""

import os
import sys
import subprocess
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def quick_check():
    """فحص سريع للمتطلبات الأساسية"""
    print("🔍 فحص سريع للمتطلبات...")
    
    # فحص الملفات الأساسية
    required_files = ["main.py", "web_server.py", "supabase_client.py"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {missing_files}")
        return False
    
    # فحص متغيرات البيئة الأساسية
    env_vars = ["BOT_TOKEN", "ADMIN_CHAT_ID", "SUPABASE_URL", "SUPABASE_KEY"]
    missing_vars = [v for v in env_vars if not os.environ.get(v)]
    
    if missing_vars:
        print(f"⚠️ متغيرات بيئة مفقودة: {missing_vars}")
        print("💡 سيتم تشغيل أداة الإعداد...")
        return "setup_needed"
    
    print("✅ الفحص السريع نجح!")
    return True

def run_setup():
    """تشغيل أداة الإعداد"""
    print("🔧 تشغيل أداة إعداد البيئة...")
    try:
        result = subprocess.run([sys.executable, "setup_environment.py"], 
                              timeout=300)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ فشل الإعداد: {e}")
        return False

def run_diagnostic():
    """تشغيل أداة التشخيص"""
    print("🔍 تشغيل أداة التشخيص...")
    try:
        subprocess.run([sys.executable, "debug_issues.py"], timeout=60)
    except Exception as e:
        print(f"⚠️ فشل التشخيص: {e}")

def run_bot():
    """تشغيل البوت"""
    print("🚀 تشغيل البوت...")
    
    # محاولة تشغيل البوت المحسن أولاً
    if os.path.exists("run_optimized_bot.py"):
        try:
            subprocess.run([sys.executable, "run_optimized_bot.py"])
            return
        except KeyboardInterrupt:
            print("⏹️ تم إيقاف البوت")
            return
        except Exception as e:
            print(f"⚠️ فشل تشغيل البوت المحسن: {e}")
    
    # العودة للتشغيل التقليدي
    print("🔄 محاولة التشغيل التقليدي...")
    try:
        subprocess.run([sys.executable, "main.py"])
    except KeyboardInterrupt:
        print("⏹️ تم إيقاف البوت")
    except Exception as e:
        print(f"❌ فشل تشغيل البوت: {e}")

def main():
    """الدالة الرئيسية"""
    print("🤖 تشغيل سريع لبوت مودات ماين كرافت")
    print("=" * 50)
    
    # فحص سريع
    check_result = quick_check()
    
    if check_result == "setup_needed":
        # تشغيل الإعداد
        if run_setup():
            print("✅ تم الإعداد بنجاح!")
        else:
            print("❌ فشل الإعداد")
            return
    elif not check_result:
        print("❌ فشل الفحص السريع")
        run_diagnostic()
        return
    
    # تشغيل البوت
    try:
        run_bot()
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        print("🔍 تشغيل التشخيص...")
        run_diagnostic()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء العملية")
    except Exception as e:
        print(f"\n❌ خطأ فادح: {e}")
        sys.exit(1)
