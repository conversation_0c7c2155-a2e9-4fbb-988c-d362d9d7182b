@echo off
chcp 65001 >nul
title إصلاح طوارئ للشبكة - Emergency Network Fix

echo ===============================================
echo 🚨 إصلاح طوارئ للشبكة
echo    Emergency Network Fix
echo ===============================================
echo.

echo ⚠️ هذا الملف يحتاج صلاحيات المدير
echo    This file needs administrator privileges
echo.

echo 🔧 سيتم تطبيق الإصلاحات التالية:
echo    1. مسح ذاكرة DNS
echo    2. إعادة تعيين Winsock
echo    3. تجديد عنوان IP
echo    4. إعادة تشغيل خدمات الشبكة
echo.

pause

echo.
echo 🔄 بدء الإصلاحات...
echo ===============================================

echo.
echo 1️⃣ مسح ذاكرة DNS...
ipconfig /flushdns
if %errorlevel% equ 0 (
    echo ✅ تم مسح ذاكرة DNS بنجاح
) else (
    echo ❌ فشل في مسح ذاكرة DNS
)

echo.
echo 2️⃣ تحرير عنوان IP...
ipconfig /release
if %errorlevel% equ 0 (
    echo ✅ تم تحرير عنوان IP بنجاح
) else (
    echo ❌ فشل في تحرير عنوان IP
)

echo.
echo 3️⃣ تجديد عنوان IP...
ipconfig /renew
if %errorlevel% equ 0 (
    echo ✅ تم تجديد عنوان IP بنجاح
) else (
    echo ❌ فشل في تجديد عنوان IP
)

echo.
echo 4️⃣ إعادة تعيين Winsock...
netsh winsock reset
if %errorlevel% equ 0 (
    echo ✅ تم إعادة تعيين Winsock بنجاح
) else (
    echo ❌ فشل في إعادة تعيين Winsock
)

echo.
echo 5️⃣ إعادة تعيين TCP/IP...
netsh int ip reset
if %errorlevel% equ 0 (
    echo ✅ تم إعادة تعيين TCP/IP بنجاح
) else (
    echo ❌ فشل في إعادة تعيين TCP/IP
)

echo.
echo 6️⃣ اختبار الاتصال...
echo 🌐 اختبار الاتصال مع Google DNS...
ping -n 4 *******
if %errorlevel% equ 0 (
    echo ✅ الاتصال مع Google DNS يعمل
) else (
    echo ❌ فشل الاتصال مع Google DNS
)

echo.
echo 🌐 اختبار حل أسماء النطاقات...
nslookup google.com
if %errorlevel% equ 0 (
    echo ✅ حل أسماء النطاقات يعمل
) else (
    echo ❌ فشل في حل أسماء النطاقات
)

echo.
echo ===============================================
echo 📊 ملخص الإصلاحات
echo ===============================================

echo.
echo ✅ تم تطبيق جميع الإصلاحات
echo.
echo 💡 الخطوات التالية:
echo    1. أعد تشغيل الكمبيوتر
echo    2. جرب تشغيل البوت مرة أخرى
echo    3. إذا لم تنجح، جرب شبكة أخرى
echo.

echo 🔄 هل تريد إعادة تشغيل الكمبيوتر الآن؟
echo    (y = نعم، n = لا)
set /p restart="اختر (y/n): "

if /i "%restart%"=="y" (
    echo.
    echo 🔄 إعادة تشغيل الكمبيوتر خلال 10 ثوان...
    echo اضغط Ctrl+C لإلغاء إعادة التشغيل
    shutdown /r /t 10
) else (
    echo.
    echo 💡 يُنصح بإعادة تشغيل الكمبيوتر لتطبيق التغييرات بالكامل
)

echo.
echo 👋 انتهى إصلاح الطوارئ
pause
