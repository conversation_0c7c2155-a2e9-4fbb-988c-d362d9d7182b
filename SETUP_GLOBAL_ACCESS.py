#!/usr/bin/env python3
"""
🌍 إعداد الوصول العالمي للبوت
🔒 Setup Global Access for Bot

إعداد HTTPS مع ngrok للوصول من جميع أنحاء العالم
Setup HTTPS with ngrok for worldwide access
"""

import os
import sys
import time
import requests
import subprocess
from pathlib import Path

def print_header():
    """طباعة الرأس"""
    print("\n" + "="*70)
    print("🌍 إعداد الوصول العالمي للبوت")
    print("🔒 Setup Global Access for Bot")
    print("="*70)
    print("🎯 الهدف: جعل صفحات المودات تعمل من أي شبكة في العالم")
    print("🎯 Goal: Make mod pages work from any network worldwide")
    print("="*70)

def check_ngrok():
    """التحقق من وجود ngrok"""
    ngrok_path = Path("./ngrok.exe")
    if ngrok_path.exists():
        print("✅ ngrok.exe موجود")
        return True
    else:
        print("❌ ngrok.exe غير موجود")
        return False

def setup_authtoken():
    """إعداد authtoken"""
    print("\n" + "="*50)
    print("🔑 إعداد ngrok authtoken")
    print("="*50)
    
    print("📋 خطوات الحصول على authtoken:")
    print("1. 🌐 تم فتح صفحة ngrok في المتصفح")
    print("2. 📝 أنشئ حساب مجاني (إذا لم يكن لديك)")
    print("3. 🔑 انسخ الـ authtoken من الصفحة")
    print("4. 📋 الصقه هنا")
    
    print("\n💡 نصيحة: authtoken يبدأ بـ: 2...")
    print("مثال: 2abc123def456ghi789jkl012mno345pqr678stu901vwx234yz")
    
    while True:
        authtoken = input("\n🔑 أدخل الـ authtoken: ").strip()
        
        if not authtoken:
            print("❌ لم تدخل أي authtoken")
            continue
        
        if not authtoken.startswith('2'):
            print("⚠️ authtoken يجب أن يبدأ بـ '2'")
            continue
        
        if len(authtoken) < 40:
            print("⚠️ authtoken قصير جداً")
            continue
        
        print(f"🔧 تطبيق authtoken...")
        try:
            result = subprocess.run([
                './ngrok', 'config', 'add-authtoken', authtoken
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ تم حفظ authtoken بنجاح!")
                return True
            else:
                print(f"❌ خطأ في حفظ authtoken: {result.stderr}")
                continue
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
            continue

def start_ngrok():
    """تشغيل ngrok"""
    print("\n" + "="*50)
    print("🚀 تشغيل ngrok")
    print("="*50)
    
    print("🔧 بدء تشغيل ngrok...")
    
    try:
        # تشغيل ngrok في الخلفية
        process = subprocess.Popen([
            './ngrok', 'http', '5001', '--log=stdout'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print("⏳ انتظار تشغيل ngrok...")
        time.sleep(8)  # انتظار أطول
        
        # محاولة الحصول على الرابط
        for attempt in range(5):
            try:
                response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    tunnels = data.get('tunnels', [])
                    
                    https_url = None
                    http_url = None
                    
                    for tunnel in tunnels:
                        public_url = tunnel.get('public_url', '')
                        if public_url.startswith('https://'):
                            https_url = public_url
                        elif public_url.startswith('http://'):
                            http_url = public_url
                    
                    if https_url:
                        print(f"🎉 تم إنشاء رابط HTTPS: {https_url}")
                        return https_url, process
                    elif http_url:
                        print(f"⚠️ تم إنشاء رابط HTTP فقط: {http_url}")
                        return http_url, process
                
                print(f"⏳ محاولة {attempt + 1}/5: انتظار ngrok...")
                time.sleep(3)
                
            except requests.RequestException:
                if attempt < 4:
                    print(f"⏳ محاولة {attempt + 1}/5: انتظار ngrok API...")
                    time.sleep(3)
                else:
                    print("❌ لا يمكن الاتصال بـ ngrok API")
        
        return None, process
        
    except Exception as e:
        print(f"❌ فشل في تشغيل ngrok: {e}")
        return None, None

def update_env_file(https_url):
    """تحديث ملف .env"""
    print("\n" + "="*50)
    print("📝 تحديث ملف .env")
    print("="*50)
    
    env_file = Path(".env")
    env_lines = []
    updated = False
    
    # قراءة الملف الموجود
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('WEB_SERVER_URL=') or line.startswith('# WEB_SERVER_URL='):
                        env_lines.append(f"WEB_SERVER_URL={https_url}")
                        updated = True
                        print(f"✅ تم تحديث WEB_SERVER_URL إلى {https_url}")
                    elif line == "DISABLE_WEB_APP=true" or line == "# DISABLE_WEB_APP=true  # تم تفعيل Web App":
                        env_lines.append("# DISABLE_WEB_APP=true  # تم تفعيل Web App مع HTTPS")
                        print("✅ تم تفعيل Web App مع HTTPS")
                    else:
                        env_lines.append(line)
        except Exception as e:
            print(f"❌ خطأ في قراءة ملف .env: {e}")
            return False
    
    # إضافة WEB_SERVER_URL إذا لم يكن موجوداً
    if not updated:
        env_lines.append(f"WEB_SERVER_URL={https_url}")
        print(f"✅ تم إضافة WEB_SERVER_URL={https_url}")
    
    # حفظ الملف
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            for line in env_lines:
                f.write(line + '\n')
        print("✅ تم حفظ ملف .env بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في حفظ ملف .env: {e}")
        return False

def show_success_info(https_url):
    """عرض معلومات النجاح"""
    print("\n" + "="*70)
    print("🎉 تم الإعداد بنجاح! الوصول العالمي مفعل")
    print("🌍 SUCCESS! Global access enabled")
    print("="*70)
    
    print(f"\n🔒 الرابط العالمي الآمن: {https_url}")
    print(f"📱 صفحة المود: {https_url}/telegram-mod-details?id=1&lang=ar")
    print(f"🔌 API: {https_url}/api/mod/1")
    
    print("\n📋 كيفية الاستخدام:")
    print("1. 🤖 شغل البوت: python main.py")
    print("2. 📱 أرسل مود من البوت")
    print("3. 🎮 اضغط على زر 'عرض التفاصيل'")
    print("4. ✨ ستفتح الصفحة من أي مكان في العالم!")
    
    print("\n🧪 للاختبار:")
    print(f"• افتح هذا الرابط على هاتفك: {https_url}")
    print("• شارك الرابط مع أصدقائك")
    print("• جرب من شبكات مختلفة")
    print("• جرب من دول مختلفة")
    
    print("\n🌍 المميزات الجديدة:")
    print("✅ يعمل من أي شبكة في العالم")
    print("✅ يعمل على جميع الأجهزة")
    print("✅ رابط HTTPS آمن")
    print("✅ سرعة عالية")
    print("✅ متوافق مع Telegram Web App")
    
    print("\n⚠️ ملاحظات مهمة:")
    print("• 🔄 يجب إبقاء هذا البرنامج يعمل")
    print("• 🌐 الرابط صالح طالما ngrok يعمل")
    print("• 🔄 إذا أعدت تشغيل ngrok، ستحصل على رابط جديد")
    print("• 💰 للاستخدام الدائم، فكر في خدمة استضافة مدفوعة")

def main():
    """الدالة الرئيسية"""
    try:
        print_header()
        
        # التحقق من ngrok
        if not check_ngrok():
            print("❌ يجب تحميل ngrok أولاً")
            return
        
        # إعداد authtoken
        if not setup_authtoken():
            print("❌ فشل في إعداد authtoken")
            return
        
        # تشغيل ngrok
        https_url, ngrok_process = start_ngrok()
        if not https_url:
            print("❌ فشل في تشغيل ngrok")
            return
        
        # تحديث ملف .env
        if not update_env_file(https_url):
            print("❌ فشل في تحديث ملف .env")
            return
        
        # عرض معلومات النجاح
        show_success_info(https_url)
        
        print("\n🔄 ngrok يعمل في الخلفية...")
        print("🚀 الآن يمكنك تشغيل البوت: python main.py")
        print("⚠️ لا تغلق هذه النافذة!")
        
        # إبقاء البرنامج يعمل
        try:
            while True:
                time.sleep(60)
                print("🔄 ngrok يعمل... الرابط نشط")
        except KeyboardInterrupt:
            print("\n🛑 إيقاف ngrok...")
            if ngrok_process:
                ngrok_process.terminate()
            print("✅ تم إيقاف ngrok")
        
    except Exception as e:
        print(f"\n💥 خطأ: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
