# Modetaris Bot - Cloudflare Pages Edition 🚀

نسخة محسنة ومطورة من صفحات تحميل مودات Minecraft، محولة من InfinityFree إلى Cloudflare Pages مع الحفاظ على جميع المميزات الأصلية وإضافة تحسينات جديدة.

## المميزات الرئيسية ✨

### 🎮 **إدارة المودات:**
- عرض تفاصيل المودات مع صور متعددة
- دعم جميع أنواع المحتوى (مودات، شيدرات، خرائط، بذور)
- تنقل سلس بين الصور باللمس
- معلومات مفصلة (الإصدار، التصنيف، الوصف)

### 🎨 **تخصيص كامل:**
- 6 قوالب تصميم مختلفة (Default, Telegram, TikTok, Classic, Professional, Gaming, Dark)
- تخصيص الألوان والخطوط
- إضافة صورة القناة في الهيدر
- تخصيص نصوص الأزرار
- دعم اللغتين العربية والإنجليزية

### 📱 **محسن للهواتف:**
- تصميم متجاوب 100%
- تنقل باللمس والسحب
- أزرار محسنة للمس
- تحميل سريع ومحسن

### 💰 **نظام الإعلانات:**
- إعلانات قابلة للتخصيص
- عداد تنازلي لإغلاق الإعلان
- دعم شبكات إعلانية متعددة
- إحصائيات مفصلة

### 🚀 **أداء عالي:**
- استضافة على Cloudflare CDN
- تحميل سريع عالمياً
- SSL مجاني
- 99.9% uptime

## التقنيات المستخدمة 🛠️

- **Frontend:** HTML5, CSS3, JavaScript (ES6+)
- **Styling:** Tailwind CSS + Custom CSS
- **Backend:** Cloudflare Pages Functions
- **Database:** Supabase
- **CDN:** Cloudflare Global Network
- **Fonts:** Google Fonts

## البنية التقنية 🏗️

```
cloudflare_pages_full/
├── 📄 index.html              # الصفحة الرئيسية
├── 🎨 style.css               # التصميم الأساسي
├── 🎭 style-templates.css     # قوالب التصميم
├── ⚡ script.js               # الوظائف التفاعلية
├── 🔀 _redirects              # إعدادات التوجيه
├── 🛡️ _headers               # إعدادات الأمان
├── 🤖 robots.txt             # إعدادات SEO
├── 📁 functions/
│   └── 🔌 api/
│       └── [[path]].js        # API Functions
└── 📚 docs/
    ├── DEPLOYMENT_GUIDE.md
    └── README.md
```

## التثبيت السريع ⚡

### الطريقة الأولى: الرفع المباشر (الأسهل)

1. **تحميل الملفات:**
   ```bash
   # نسخ مجلد cloudflare_pages_full
   cp -r cloudflare_pages_full/ my-mod-site/
   ```

2. **رفع على Cloudflare Pages:**
   - اذهب إلى [dash.cloudflare.com](https://dash.cloudflare.com)
   - اختر Pages → Create a project
   - اختر "Upload assets"
   - اسحب جميع الملفات
   - اضغط "Deploy site"

3. **الحصول على الرابط:**
   ```
   https://your-project.pages.dev
   ```

### الطريقة الثانية: ربط GitHub

1. **إنشاء مستودع:**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin https://github.com/username/mod-site.git
   git push -u origin main
   ```

2. **ربط مع Cloudflare Pages:**
   - اختر "Connect to Git"
   - اختر المستودع
   - إعدادات البناء: اتركها فارغة
   - اضغط "Save and Deploy"

## الإعداد والتكوين ⚙️

### 1. إعداد قاعدة البيانات

تأكد من وجود الجداول التالية في Supabase:

```sql
-- جدول المودات
CREATE TABLE mods (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    description_ar TEXT,
    version TEXT,
    category TEXT,
    download_url TEXT,
    image_urls TEXT[]
);

-- جدول إعدادات الإعلانات
CREATE TABLE user_ads_settings (
    user_id TEXT PRIMARY KEY,
    ads_enabled BOOLEAN DEFAULT false,
    ad_link TEXT,
    ad_close_delay INTEGER DEFAULT 5
);

-- جدول إعدادات التخصيص
CREATE TABLE page_customization_settings (
    user_id TEXT PRIMARY KEY,
    site_name TEXT,
    style_template TEXT DEFAULT 'default',
    custom_bg_color TEXT,
    custom_header_color TEXT,
    custom_button_color TEXT,
    custom_text_color TEXT,
    channel_logo_url TEXT,
    logo_position TEXT DEFAULT 'right',
    download_button_text_ar TEXT,
    download_button_text_en TEXT
);
```

### 2. ربط البوت

```bash
# إعداد webhook
curl -X POST "https://api.telegram.org/bot{BOT_TOKEN}/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://your-site.pages.dev/api/webhook"}'
```

## استخدام API 🔗

### الحصول على بيانات مود:
```javascript
fetch('/api/mod/123')
  .then(response => response.json())
  .then(data => console.log(data));
```

### الحصول على إعدادات التخصيص:
```javascript
fetch('/api/customization/user123')
  .then(response => response.json())
  .then(data => console.log(data));
```

### فحص حالة الخدمة:
```javascript
fetch('/api/health')
  .then(response => response.json())
  .then(data => console.log(data));
```

## أمثلة الاستخدام 📝

### 1. عرض مود بتخصيصات:
```
https://your-site.pages.dev/mod/123?user_id=456&lang=ar
```

### 2. معاينة التخصيصات:
```
https://your-site.pages.dev/preview?user_id=456&lang=en
```

### 3. تحميل مباشر:
```
https://your-site.pages.dev/download/123
```

## التخصيص المتقدم 🎨

### إضافة قالب تصميم جديد:

1. **في `style-templates.css`:**
```css
.style-template-custom {
    font-family: 'Your Font', sans-serif;
}

.style-template-custom body {
    background: your-gradient;
}

.style-template-custom .pixel-button {
    background: your-button-style;
}
```

2. **في قاعدة البيانات:**
```sql
UPDATE page_customization_settings 
SET style_template = 'custom' 
WHERE user_id = 'your_user_id';
```

### تخصيص الألوان:
```sql
UPDATE page_customization_settings SET
    custom_bg_color = '#your-color',
    custom_header_color = '#your-color',
    custom_button_color = '#your-color'
WHERE user_id = 'your_user_id';
```

## الأداء والتحسين 🚀

### المميزات المطبقة:
- ✅ تحميل كسول للصور
- ✅ ضغط الملفات
- ✅ تخزين مؤقت محسن
- ✅ CDN عالمي
- ✅ GZIP compression

### نصائح للتحسين:
1. **ضغط الصور:** استخدم WebP للصور
2. **تحسين الخطوط:** استخدم font-display: swap
3. **تقليل الطلبات:** دمج ملفات CSS/JS
4. **مراقبة الأداء:** استخدم Cloudflare Analytics

## استكشاف الأخطاء 🔧

### مشاكل شائعة وحلولها:

#### الصفحة لا تعمل:
```bash
# تحقق من الملفات
ls -la cloudflare_pages_full/
# تأكد من وجود index.html
```

#### API لا يعمل:
```bash
# اختبار الصحة
curl https://your-site.pages.dev/api/health
```

#### البيانات لا تظهر:
```javascript
// تحقق من console
console.log('Checking Supabase connection...');
```

## المساهمة والتطوير 🤝

### إعداد بيئة التطوير:

1. **استنساخ المشروع:**
```bash
git clone https://github.com/your-repo/mod-site.git
cd mod-site
```

2. **تشغيل محلي:**
```bash
# استخدام Live Server أو أي خادم محلي
python -m http.server 8000
```

3. **اختبار التغييرات:**
```bash
# رفع على Cloudflare Pages للاختبار
wrangler pages publish cloudflare_pages_full/
```

## الترخيص 📄

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم والمساعدة 💬

- 📖 [دليل النشر الكامل](DEPLOYMENT_GUIDE.md)
- 🌐 [وثائق Cloudflare Pages](https://developers.cloudflare.com/pages/)
- 💬 [مجتمع Cloudflare](https://community.cloudflare.com/)
- 🐛 [الإبلاغ عن مشاكل](https://github.com/your-repo/issues)

## الشكر والتقدير 🙏

- فريق Cloudflare لتوفير منصة رائعة
- مجتمع Minecraft العربي
- جميع المساهمين في المشروع

---

**صُنع بـ ❤️ للمجتمع العربي لمودات Minecraft**

🚀 **جاهز للنشر على Cloudflare Pages في دقائق!**
