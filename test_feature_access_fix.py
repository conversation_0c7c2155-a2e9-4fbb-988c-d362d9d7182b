#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح نظام رسائل التحذير للمميزات المقفلة
Test for feature access warning messages fix
"""

import sys
import os
import asyncio
from unittest.mock import Mock, AsyncMock

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_feature_access_functions():
    """اختبار وجود الدوال المطلوبة"""
    print("🔍 اختبار وجود الدوال المطلوبة...")
    
    try:
        from main import (
            show_feature_locked_message,
            check_feature_access,
            url_shortener_menu,
            tasks_system_menu,
            get_user_invitation_level,
            get_user_invitation_stats
        )
        print("✅ تم استيراد جميع الدوال بنجاح")
        return True
    except ImportError as e:
        print(f"❌ خطأ في استيراد الدوال: {e}")
        return False

async def test_show_feature_locked_message():
    """اختبار دالة عرض رسالة الميزة المقفلة"""
    print("\n📋 اختبار دالة show_feature_locked_message...")
    
    try:
        from main import show_feature_locked_message
        
        # إنشاء mock objects
        update = Mock()
        update.callback_query = Mock()
        update.callback_query.message = Mock()
        update.callback_query.message.photo = None
        
        context = Mock()
        context.bot = AsyncMock()
        context.bot.send_message = AsyncMock()
        
        # اختبار الدالة
        await show_feature_locked_message(
            update=update,
            context=context,
            user_id="123456789",
            feature_type="url_shortener",
            lang="ar"
        )
        
        print("✅ دالة show_feature_locked_message تعمل بدون أخطاء")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في دالة show_feature_locked_message: {e}")
        return False

def test_feature_info_structure():
    """اختبار بنية معلومات المميزات"""
    print("\n🏗️ اختبار بنية معلومات المميزات...")
    
    try:
        from main import show_feature_locked_message
        import inspect
        
        # الحصول على كود الدالة
        source = inspect.getsource(show_feature_locked_message)
        
        # التحقق من وجود المميزات المطلوبة
        required_features = [
            'url_shortener',
            'tasks_system',
            'unlimited_channels',
            'custom_download_links'
        ]
        
        missing_features = []
        for feature in required_features:
            if f"'{feature}'" not in source:
                missing_features.append(feature)
        
        if missing_features:
            print(f"❌ مميزات مفقودة: {missing_features}")
            return False
        else:
            print("✅ جميع المميزات المطلوبة موجودة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص بنية المميزات: {e}")
        return False

def test_invitation_requirements():
    """اختبار متطلبات الدعوات للمميزات"""
    print("\n🎁 اختبار متطلبات الدعوات...")
    
    expected_requirements = {
        'unlimited_channels': 1,
        'url_shortener': 3,
        'custom_download_links': 3,
        'publish_intervals_extended': 5,
        'tasks_system': 10,
        'page_customization_vip': 10
    }
    
    try:
        from main import show_feature_locked_message
        import inspect
        
        source = inspect.getsource(show_feature_locked_message)
        
        all_correct = True
        for feature, expected_count in expected_requirements.items():
            # البحث عن النمط: 'requirement_count': X
            pattern = f"'{feature}'"
            if pattern in source:
                # البحث عن requirement_count في نفس القسم
                feature_start = source.find(pattern)
                feature_section = source[feature_start:feature_start + 500]  # 500 حرف بعد اسم الميزة
                
                if f"'requirement_count': {expected_count}" in feature_section:
                    print(f"✅ {feature}: {expected_count} دعوات (صحيح)")
                else:
                    print(f"❌ {feature}: متطلبات الدعوات غير صحيحة")
                    all_correct = False
            else:
                print(f"❌ {feature}: الميزة غير موجودة")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في فحص متطلبات الدعوات: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار إصلاح نظام رسائل التحذير للمميزات المقفلة")
    print("=" * 60)
    
    tests = [
        ("اختبار وجود الدوال", test_feature_access_functions),
        ("اختبار دالة الرسائل", test_show_feature_locked_message),
        ("اختبار بنية المميزات", test_feature_info_structure),
        ("اختبار متطلبات الدعوات", test_invitation_requirements)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📝 {test_name}:")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التعديلات تعمل بشكل صحيح.")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    asyncio.run(main())
