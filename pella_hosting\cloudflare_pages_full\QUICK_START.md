# دليل البدء السريع ⚡

## نشر في 5 دقائق! 🚀

### الخطوة 1: تحضير الملفات 📁
```bash
# تأكد من وجود جميع الملفات في مجلد cloudflare_pages_full
ls cloudflare_pages_full/
```

### الخطوة 2: رفع على Cloudflare 🌐
1. اذهب إلى: https://dash.cloudflare.com
2. اختر **Pages** من القائمة
3. اضغط **"Create a project"**
4. اختر **"Upload assets"**
5. اسحب مجلد `cloudflare_pages_full` كاملاً
6. اضغط **"Deploy site"**

### الخطوة 3: انتظار النشر ⏱️
- انتظر 2-3 دقائق
- احصل على رابط موقعك: `https://your-project.pages.dev`

### الخطوة 4: اختبار الموقع ✅
```
✅ الرئيسية: https://your-site.pages.dev
✅ فحص API: https://your-site.pages.dev/api/health  
✅ مود تجريبي: https://your-site.pages.dev/mod/1
✅ تحميل: https://your-site.pages.dev/download/1
```

### الخطوة 5: ربط البوت 🤖
```bash
# استبدل YOUR_SITE_URL برابط موقعك
curl "https://api.telegram.org/bot7605181405:AAEBua03X_QdD6GkCqKWR1P02EtXIbnSkx4/setWebhook?url=https://YOUR_SITE_URL/api/webhook"
```

## 🎉 تم! موقعك جاهز الآن

### روابط مفيدة:
- 📖 [دليل النشر الكامل](DEPLOYMENT_GUIDE.md)
- 📚 [README الشامل](README.md)
- 🔧 [استكشاف الأخطاء](DEPLOYMENT_GUIDE.md#استكشاف-الأخطاء)

### مميزات جاهزة للاستخدام:
- ✅ جميع قوالب التصميم (6 قوالب)
- ✅ دعم اللغتين العربية والإنجليزية
- ✅ نظام الإعلانات
- ✅ تخصيص كامل للألوان والنصوص
- ✅ API كامل للبوت
- ✅ محسن للهواتف 100%

**🚀 استمتع بموقعك الجديد على Cloudflare Pages!**
