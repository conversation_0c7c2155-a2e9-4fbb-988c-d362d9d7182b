# 🌐 دليل استضافة InfinityFree للموقع

## 📋 نظرة عامة

هذا الدليل يوضح كيفية رفع ونشر موقع عرض المودات على استضافة InfinityFree المجانية.

## 🎯 البيانات الصحيحة المستخدمة

### 📊 قاعدة البيانات Supabase:
- **URL**: `https://ytqxxodyecdeosnqoure.supabase.co`
- **Table Name**: `mods` (تم تصحيحه من `minemods`)
- **API Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- **إجمالي المودات**: 127 مود

## 🚀 خطوات الرفع على InfinityFree

### 1️⃣ إنشاء حساب InfinityFree
1. اذهب إلى [InfinityFree.net](https://infinityfree.net/)
2. أنشئ حساب جديد مجاني
3. اختر نطاق فرعي مجاني أو ربط نطاق خاص

### 2️⃣ رفع الملفات
1. ادخل إلى لوحة التحكم
2. افتح File Manager
3. اذهب إلى مجلد `htdocs`
4. ارفع جميع الملفات التالية:

#### الملفات الأساسية المطلوبة:
```
htdocs/
├── index.php          # الصفحة الرئيسية (محدث)
├── api.php            # واجهة برمجة التطبيقات (محدث)
├── config.php         # ملف الإعدادات (محدث)
├── style.css          # ملف التنسيقات
├── script.js          # ملف الجافاسكريبت
├── test_curl.php      # اختبار الاتصال (محدث)
├── test_website.php   # اختبار شامل للموقع (جديد)
├── logs.php           # عرض السجلات
├── clear_log.php      # مسح السجلات
├── 404.html           # صفحة خطأ 404
├── 500.html           # صفحة خطأ 500
├── robots.txt         # إعدادات محركات البحث
└── sitemap.xml        # خريطة الموقع
```

### 3️⃣ اختبار الموقع
بعد رفع الملفات، قم بزيارة الروابط التالية للاختبار:

1. **اختبار شامل**: `yoursite.com/test_website.php`
2. **اختبار cURL**: `yoursite.com/test_curl.php`
3. **اختبار API**: `yoursite.com/api.php?path=/test`

## 🔧 إعدادات مهمة

### إعدادات PHP المطلوبة:
- **PHP Version**: 7.4 أو أحدث ✅
- **cURL Extension**: مطلوب ✅
- **JSON Extension**: مطلوب ✅
- **mbstring Extension**: مطلوب ✅

### إعدادات الأمان:
- **SSL/HTTPS**: متوفر مجاناً ✅
- **File Permissions**: 644 للملفات، 755 للمجلدات
- **Error Reporting**: معطل في الإنتاج

## 🌐 استخدام الموقع

### عرض مود معين:
```
https://yoursite.com/index.php?id=MOD_ID&lang=ar
```

### مثال حقيقي:
```
https://yoursite.com/index.php?id=f0cb0668-14d6-4a1f-9fae-3d1a5b4520d3&lang=ar
```

### API Endpoints:
- **اختبار**: `/api.php?path=/test`
- **جلب مود**: `/api.php?path=/mod&id=MOD_ID`
- **قائمة المودات**: `/api.php?path=/mods&limit=10`
- **البحث**: `/api.php?path=/mods&search=shader`
- **إحصائيات**: `/api.php?path=/stats`

## 🧪 اختبارات التحقق

### 1️⃣ اختبار الاتصال الأساسي:
```bash
curl -I https://yoursite.com/
# يجب أن يعطي: HTTP/1.1 200 OK
```

### 2️⃣ اختبار قاعدة البيانات:
```bash
curl https://yoursite.com/api.php?path=/test
# يجب أن يعطي: {"status":"success","message":"Database connection successful"}
```

### 3️⃣ اختبار جلب المودات:
```bash
curl https://yoursite.com/api.php?path=/mods&limit=1
# يجب أن يعطي: مصفوفة JSON بمود واحد
```

## 🔍 استكشاف الأخطاء

### مشكلة: خطأ 500 Internal Server Error
**الحل:**
1. تحقق من صلاحيات الملفات (644)
2. راجع ملف `logs.php` للأخطاء
3. تأكد من وجود جميع الملفات المطلوبة

### مشكلة: فشل الاتصال مع قاعدة البيانات
**الحل:**
1. تحقق من إعدادات `config.php`
2. تأكد من صحة رابط Supabase والمفتاح
3. تحقق من اسم الجدول (`mods` وليس `minemods`)

### مشكلة: صفحة فارغة أو خطأ PHP
**الحل:**
1. تفعيل عرض الأخطاء مؤقتاً في `config.php`
2. تحقق من إصدار PHP (يجب أن يكون 7.4+)
3. تأكد من تثبيت إضافة cURL

## 📊 مراقبة الأداء

### إحصائيات الاستخدام:
- **عرض النطاق الترددي**: غير محدود
- **مساحة التخزين**: 5 GB
- **قواعد البيانات**: 400 MB (لا نحتاجها - نستخدم Supabase)
- **المجالات الفرعية**: غير محدود

### تحسين الأداء:
- ✅ ضغط الملفات مفعل
- ✅ تخزين مؤقت للصور
- ✅ تحسين استعلامات قاعدة البيانات
- ✅ تقليل حجم الملفات

## 🎯 نصائح مهمة

### للحصول على أفضل أداء:
1. **استخدم CDN**: لتسريع تحميل الصور
2. **ضغط الصور**: قبل رفعها
3. **تحسين الكود**: إزالة التعليقات والمسافات الزائدة
4. **مراقبة الاستخدام**: تجنب تجاوز حدود الاستضافة

### للأمان:
1. **تحديث منتظم**: للملفات والإعدادات
2. **نسخ احتياطية**: للملفات المهمة
3. **مراقبة السجلات**: للأنشطة المشبوهة
4. **حماية الملفات الحساسة**: مثل `config.php`

## 📞 الدعم

### إذا واجهت مشاكل:
1. **اختبر أولاً**: `test_website.php`
2. **راجع السجلات**: `logs.php`
3. **تحقق من الإعدادات**: `config.php`
4. **اتصل بدعم InfinityFree**: إذا كانت مشكلة في الاستضافة

### روابط مفيدة:
- [InfinityFree Knowledge Base](https://infinityfree.net/support)
- [PHP Documentation](https://www.php.net/docs.php)
- [Supabase Documentation](https://supabase.com/docs)

## ✅ قائمة التحقق النهائية

- [ ] ✅ تم رفع جميع الملفات
- [ ] ✅ تم اختبار الموقع عبر `test_website.php`
- [ ] ✅ تم التحقق من عمل API
- [ ] ✅ تم اختبار عرض مود واحد على الأقل
- [ ] ✅ تم التحقق من عمل البحث
- [ ] ✅ تم مراجعة السجلات للتأكد من عدم وجود أخطاء

**🎉 مبروك! موقعك جاهز ويعمل على InfinityFree!**
