-- Add link_type column to custom_download_links table if it does not exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'custom_download_links'
        AND column_name = 'link_type'
    ) THEN
        ALTER TABLE custom_download_links
        ADD COLUMN link_type VARCHAR(20) DEFAULT 'custom';

        -- Add CHECK constraint
        ALTER TABLE custom_download_links
        ADD CONSTRAINT chk_link_type_enum
        CHECK (link_type IN ('direct', 'redirect', 'custom'));
    END IF;

    -- Add index if it does not exist
    IF NOT EXISTS (
        SELECT 1
        FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE c.relname = 'idx_link_type'
        AND n.nspname = 'public' -- Assuming public schema
    ) THEN
        CREATE INDEX idx_link_type ON custom_download_links(link_type);
    END IF;
END
$$;
