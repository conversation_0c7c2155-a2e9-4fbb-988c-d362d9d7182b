#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار صفحة mod_details.html
Test script for mod_details.html page
"""

import os
import sys
import json
import time
import logging
import threading
import webbrowser
from datetime import datetime
from flask import Flask, render_template_string, request

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

# إنشاء تطبيق Flask للاختبار
app = Flask(__name__)

# تحميل قالب mod_details.html
try:
    with open("mod_details.html", "r", encoding="utf-8") as f:
        MOD_DETAILS_TEMPLATE = f.read()
    logger.info("✅ تم تحميل قالب mod_details.html بنجاح")
except FileNotFoundError:
    logger.error("❌ ملف mod_details.html غير موجود")
    sys.exit(1)

# بيانات تجريبية للاختبار
TEST_MOD_DATA = {
    "id": "12345678-1234-1234-1234-123456789abc",
    "title": "مود اختبار رائع - تحسينات الصور",
    "image_urls": [
        "https://via.placeholder.com/800x600/FF6B6B/FFFFFF?text=الصورة+الأولى",
        "https://via.placeholder.com/800x600/4ECDC4/FFFFFF?text=الصورة+الثانية",
        "https://via.placeholder.com/800x600/45B7D1/FFFFFF?text=الصورة+الثالثة",
        "https://via.placeholder.com/800x600/96CEB4/FFFFFF?text=الصورة+الرابعة",
        "https://via.placeholder.com/800x600/F39C12/FFFFFF?text=الصورة+الخامسة",
        "https://via.placeholder.com/800x600/E74C3C/FFFFFF?text=الصورة+السادسة"
    ],
    "version": "2.0.0",
    "category": "addons",
    "description": "هذا مود اختبار محسن لاختبار عرض الصور المتعددة والتنقل بينها. يحتوي على 6 صور مختلفة لاختبار النظام الجديد.",
    "download_link": "https://example.com/download/test-mod-improved.mcaddon"
}

TEST_ADS_SETTINGS = {
    "ads_enabled": True,
    "ad_direct_link": "https://example.com/ad",
    "ad_display_mode": "on_download",
    "close_button_delay": 5
}

TEST_TASKS_SETTINGS = {
    "tasks_enabled": True
}

TEST_AVAILABLE_TASKS = [
    {
        "id": 1,
        "task_title": "اشترك في القناة",
        "task_description": "اشترك في قناة التليجرام",
        "task_type": "telegram",
        "task_url": "https://t.me/testchannel"
    },
    {
        "id": 2,
        "task_title": "شاهد الفيديو",
        "task_description": "شاهد الفيديو على يوتيوب",
        "task_type": "youtube",
        "task_url": "https://youtube.com/watch?v=test"
    }
]

TEST_PAGE_CUSTOMIZATION = {
    "site_name": "موقع المودات التجريبي",
    "page_theme": "dark",
    "show_all_images": True,
    "enable_mod_opening": True,
    "download_button_text_ar": "تحميل المود",
    "download_button_text_en": "Download Mod",
    "open_button_text_ar": "فتح المود",
    "open_button_text_en": "Open Mod",
    "version_label_ar": "الإصدار",
    "version_label_en": "Version",
    "category_label_ar": "التصنيف",
    "category_label_en": "Category",
    "description_label_ar": "الوصف",
    "description_label_en": "Description"
}

@app.route('/')
def index():
    """الصفحة الرئيسية للاختبار"""
    return """
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>اختبار صفحة تفاصيل المود</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; }
            .test-links { display: grid; gap: 15px; margin-top: 30px; }
            .test-link { display: block; padding: 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; text-align: center; transition: background 0.3s; }
            .test-link:hover { background: #0056b3; }
            .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #007bff; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🧪 اختبار صفحة تفاصيل المود</h1>
            
            <div class="info">
                <strong>ℹ️ معلومات الاختبار:</strong><br>
                • سيتم اختبار صفحة mod_details.html مع بيانات تجريبية<br>
                • يمكنك اختبار اللغة العربية والإنجليزية<br>
                • سيتم اختبار نظام الإعلانات والمهام<br>
                • جميع الروابط والوظائف ستعمل في وضع التجريب
            </div>
            
            <div class="test-links">
                <a href="/test-mod-details?lang=ar" class="test-link">
                    🇸🇦 اختبار باللغة العربية
                </a>
                <a href="/test-mod-details?lang=en" class="test-link">
                    🇺🇸 Test in English
                </a>
                <a href="/test-mod-details?lang=ar&theme=dark" class="test-link">
                    🌙 اختبار بالثيم الداكن
                </a>
                <a href="/test-mod-details?lang=ar&theme=light" class="test-link">
                    ☀️ اختبار بالثيم الفاتح
                </a>
                <a href="/test-mod-details?lang=ar&theme=telegram" class="test-link">
                    📱 اختبار بثيم التليجرام
                </a>
                <a href="/test-api" class="test-link">
                    🔧 اختبار API
                </a>
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/test-mod-details')
def test_mod_details():
    """اختبار صفحة تفاصيل المود"""
    # الحصول على المعاملات
    lang = request.args.get('lang', 'ar')
    theme = request.args.get('theme', 'dark')
    
    # تحديث إعدادات التخصيص حسب الثيم
    page_customization = TEST_PAGE_CUSTOMIZATION.copy()
    page_customization['page_theme'] = theme
    
    # تحضير البيانات
    mod_data = TEST_MOD_DATA
    mod_image_urls_json = json.dumps(mod_data['image_urls'])
    
    # تحديد النصوص حسب اللغة
    if lang == 'ar':
        site_name = "موقع المودات التجريبي"
        download_button_text = "تحميل المود"
        open_button_text = "فتح المود"
        version_label = "الإصدار"
        category_label = "التصنيف"
        description_label = "الوصف"
        mod_category = "إضافات"
    else:
        site_name = "Test Mods Website"
        download_button_text = "Download Mod"
        open_button_text = "Open Mod"
        version_label = "Version"
        category_label = "Category"
        description_label = "Description"
        mod_category = "Add-ons"
    
    try:
        # عرض الصفحة باستخدام القالب
        return render_template_string(
            MOD_DETAILS_TEMPLATE,
            mod_title=mod_data['title'],
            mod_image_url=mod_data['image_urls'][0],
            mod_image_urls_json=mod_image_urls_json,
            mod_version=mod_data['version'],
            mod_category=mod_category,
            mod_description=mod_data['description'],
            mod_download_url=mod_data['download_link'],
            download_button_text=download_button_text,
            open_button_text=open_button_text,
            version_label=version_label,
            category_label=category_label,
            description_label=description_label,
            site_name=site_name,
            enable_mod_opening=True,
            lang=lang,
            mod_id=mod_data['id'],
            user_id="test_user_123",
            channel_id="test_channel",
            ads_settings=TEST_ADS_SETTINGS,
            tasks_settings=TEST_TASKS_SETTINGS,
            available_tasks=TEST_AVAILABLE_TASKS,
            user_completed_tasks=[],
            user_is_completed=False,
            page_customization=page_customization
        )
    except Exception as e:
        logger.error(f"خطأ في عرض الصفحة: {e}")
        return f"""
        <html>
        <head><meta charset="UTF-8"><title>خطأ</title></head>
        <body style="font-family: Arial; padding: 40px; background: #f8f9fa;">
            <div style="max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h1 style="color: #dc3545;">❌ خطأ في عرض الصفحة</h1>
                <p><strong>تفاصيل الخطأ:</strong></p>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">{e}</pre>
                <a href="/" style="display: inline-block; margin-top: 20px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;">العودة للصفحة الرئيسية</a>
            </div>
        </body>
        </html>
        """, 500

@app.route('/test-api')
def test_api():
    """اختبار API"""
    return {
        "status": "success",
        "message": "API يعمل بشكل صحيح",
        "timestamp": datetime.now().isoformat(),
        "test_data": TEST_MOD_DATA
    }

def run_test_server():
    """تشغيل خادم الاختبار"""
    port = 5555
    logger.info(f"🚀 بدء تشغيل خادم الاختبار على المنفذ {port}")
    logger.info(f"🌐 رابط الاختبار: http://localhost:{port}")
    
    try:
        app.run(host='127.0.0.1', port=port, debug=True, use_reloader=False)
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل الخادم: {e}")

def open_browser():
    """فتح المتصفح تلقائياً"""
    time.sleep(2)  # انتظار تشغيل الخادم
    try:
        webbrowser.open('http://localhost:5555')
        logger.info("🌐 تم فتح المتصفح تلقائياً")
    except Exception as e:
        logger.warning(f"⚠️ فشل فتح المتصفح تلقائياً: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 اختبار صفحة mod_details.html")
    print("=" * 60)
    
    # فتح المتصفح في thread منفصل
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    # تشغيل الخادم
    run_test_server()
