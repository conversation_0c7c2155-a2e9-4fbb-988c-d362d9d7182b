# 🌐 موقع عرض المودات - InfinityFree Version (محدث)

هذا المجلد يحتوي على ملفات الموقع المحسنة للعمل على استضافة InfinityFree المجانية مع البيانات الصحيحة.

## ✅ التحديثات الجديدة

- ✅ تم تصحيح اسم الجدول من `minemods` إلى `mods`
- ✅ تم تحديث جميع الملفات لاستخدام البيانات الصحيحة
- ✅ تم إضافة اختبارات شاملة للاتصال
- ✅ تم تحسين API لدعم المزيد من العمليات

## 📁 الملفات الموجودة

### الملفات الأساسية:
- `index.php` - صفحة عرض تفاصيل المود الرئيسية (محدث)
- `api.php` - واجهة برمجة التطبيقات المحسنة (محدث)
- `config.php` - ملف الإعدادات الرئيسي (محدث)
- `style.css` - ملف التنسيقات
- `script.js` - ملف الجافاسكريبت

### ملفات الاختبار والإدارة:
- `test_curl.php` - اختبار شامل للاتصال (محدث)
- `logs.php` - عرض سجلات النظام
- `clear_log.php` - مسح السجلات
- `deploy.php` - نشر التحديثات

### ملفات إضافية:
- `404.html` - صفحة خطأ 404
- `500.html` - صفحة خطأ 500
- `robots.txt` - إعدادات محركات البحث
- `sitemap.xml` - خريطة الموقع

## 🔧 الإعداد

1. ارفع جميع الملفات إلى مجلد `htdocs` في استضافة InfinityFree
2. تأكد من أن إعدادات قاعدة البيانات صحيحة في `config.php`
3. اختبر الاتصال عبر زيارة `test_curl.php`
4. تحقق من عمل API عبر `api.php?path=/test`

## 🌐 الاستخدام

### صفحات المودات:
- للوصول لصفحة المود: `index.php?id=MOD_ID&lang=ar`
- مثال: `index.php?id=f0cb0668-14d6-4a1f-9fae-3d1a5b4520d3&lang=ar`

### API Endpoints:
- اختبار الاتصال: `api.php?path=/test`
- جلب مود واحد: `api.php?path=/mod&id=MOD_ID`
- جلب قائمة المودات: `api.php?path=/mods&limit=10`
- البحث في المودات: `api.php?path=/mods&search=shader`
- إحصائيات عامة: `api.php?path=/stats`

### الإدارة:
- عرض السجلات: `logs.php`
- مسح السجلات: `clear_log.php`

## 📊 قاعدة البيانات الصحيحة

- **URL**: https://ytqxxodyecdeosnqoure.supabase.co
- **Table**: `mods` (تم تصحيحه من `minemods`)
- **API Key**: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
- **إجمالي المودات**: 127 مود

## 🔍 الاختبار

### اختبار سريع:
قم بزيارة `test_curl.php` للتأكد من عمل الاتصال مع قاعدة البيانات.

### اختبار شامل:
1. **اختبار الاتصال الأساسي**: ✅ يجب أن يعطي HTTP 200
2. **اختبار جلب المودات**: ✅ يجب أن يعرض قائمة بالمودات
3. **اختبار API الداخلي**: ✅ يجب أن يعمل بشكل صحيح

## 🚀 نشر التحديثات

لنشر التحديثات على الاستضافة:
1. ارفع الملفات المحدثة
2. قم بزيارة `deploy.php` لتطبيق التحديثات
3. اختبر الموقع عبر `test_curl.php`

## 🎯 ملاحظات مهمة

- ✅ جميع الملفات محدثة لاستخدام الجدول الصحيح `mods`
- ✅ تم اختبار الاتصال وهو يعمل بنجاح
- ✅ API محسن ويدعم عمليات متعددة
- ✅ الموقع جاهز للاستخدام على InfinityFree

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من `test_curl.php` أولاً
2. راجع السجلات في `logs.php`
3. تأكد من صحة إعدادات قاعدة البيانات

## 🔗 روابط مفيدة

- [InfinityFree](https://infinityfree.net/) - الاستضافة المجانية
- [Supabase](https://supabase.co/) - قاعدة البيانات
- [Telegram Bot API](https://core.telegram.org/bots/api) - API البوت

## 📝 سجل التغييرات

### الإصدار 2.0 (محدث)
- ✅ تصحيح اسم الجدول إلى `mods`
- ✅ تحسين API مع endpoints جديدة
- ✅ إضافة اختبارات شاملة
- ✅ تحسين معالجة الأخطاء
- ✅ دعم أفضل للاستضافة المجانية

### الإصدار 1.0 (الأصلي)
- ✅ إنشاء الموقع الأساسي
- ✅ دعم عرض المودات
- ✅ تكامل مع Supabase
- ✅ دعم اللغتين العربية والإنجليزية
