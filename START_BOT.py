#!/usr/bin/env python3
"""
🎮 تشغيل بوت مودات ماين كرافت
🌐 مع حل مشكلة عدم عمل الصفحات على الأجهزة الأخرى

Minecraft Mods Bot Launcher
With fix for pages not working on other devices
"""

import os
import sys

def main():
    print("\n" + "="*60)
    print("🎮 MINECRAFT MODS BOT")
    print("🌐 حل مشكلة عدم عمل الصفحات على الأجهزة الأخرى")
    print("="*60)
    
    print("\n📋 اختر طريقة التشغيل:")
    print("1. 🚀 تشغيل مع HTTPS تلقائي (موصى به)")
    print("2. 🏠 تشغيل للشبكة المحلية فقط")
    print("3. ⚙️ إعداد HTTPS يدوي")
    print("4. 🤖 تشغيل عادي (مشكلة الأجهزة الأخرى)")
    print("5. 🧪 اختبار النظام")
    print("6. ❓ مساعدة")
    
    choice = input("\n🔢 أدخل رقم الخيار (1-6): ").strip()
    
    if choice == "1":
        print("\n🚀 تشغيل مع HTTPS تلقائي...")
        print("✨ سيتم حل مشكلة عدم عمل الصفحات على الأجهزة الأخرى")
        try:
            import start_with_https
        except ImportError:
            print("❌ ملف start_with_https.py غير موجود")
            
    elif choice == "2":
        print("\n🏠 تشغيل للشبكة المحلية...")
        print("📶 سيعمل فقط للأجهزة في نفس الشبكة")
        try:
            import local_network_setup
        except ImportError:
            print("❌ ملف local_network_setup.py غير موجود")
            
    elif choice == "3":
        print("\n⚙️ إعداد HTTPS يدوي...")
        print("🔧 للمستخدمين المتقدمين")
        try:
            import setup_https_access
        except ImportError:
            print("❌ ملف setup_https_access.py غير موجود")
            
    elif choice == "4":
        print("\n🤖 تشغيل عادي...")
        print("⚠️ تحذير: قد لا تعمل الصفحات على الأجهزة الأخرى")
        try:
            import main
        except ImportError:
            print("❌ ملف main.py غير موجود")
            
    elif choice == "5":
        print("\n🧪 اختبار النظام...")
        try:
            import test_enhanced_system
        except ImportError:
            print("❌ ملف test_enhanced_system.py غير موجود")
            
    elif choice == "6":
        show_help()
        
    else:
        print("❌ خيار غير صالح")
        main()

def show_help():
    """عرض المساعدة"""
    print("\n" + "="*60)
    print("❓ مساعدة - Help")
    print("="*60)
    
    print("\n🚨 المشكلة الشائعة:")
    print("صفحات عرض المودات لا تعمل على الأجهزة الأخرى")
    print("(تظهر رسالة 'لم يتم العثور على الصفحة')")
    
    print("\n✅ الحل:")
    print("استخدم الخيار رقم 1: 'تشغيل مع HTTPS تلقائي'")
    
    print("\n📋 متطلبات الحل:")
    print("• اتصال بالإنترنت")
    print("• حساب مجاني على ngrok.com (اختياري)")
    
    print("\n🔧 خطوات الحل:")
    print("1. اختر الخيار رقم 1")
    print("2. انتظر تحميل ngrok تلقائياً")
    print("3. ستحصل على رابط HTTPS عام")
    print("4. شارك الرابط مع المستخدمين")
    
    print("\n📱 للاختبار:")
    print("• افتح الرابط على هاتفك")
    print("• جرب من شبكة مختلفة")
    print("• شارك مع أصدقائك")
    
    print("\n📞 للدعم:")
    print("• تواصل مع المطور: @Kim880198")
    print("• راجع ملف FIX_HTTPS_PROBLEM.md")
    
    input("\n⏸️ اضغط Enter للعودة...")
    main()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البرنامج")
    except Exception as e:
        print(f"\n💥 خطأ: {e}")
        print("📞 للمساعدة، تواصل مع @Kim880198")
