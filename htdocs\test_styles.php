<?php
/**
 * ملف اختبار أساليب التصميم
 * Style Templates Test File
 * 
 * استخدم هذا الملف لاختبار أساليب التصميم المختلفة
 * Use this file to test different style templates
 * 
 * الرابط: https://sendaddons.fwh.is/test_styles.php?style=telegram
 */

// إعدادات قاعدة البيانات Supabase
$SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
$SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";

// الحصول على الستايل من الرابط
$test_style = isset($_GET['style']) ? $_GET['style'] : 'default';
$user_id = isset($_GET['user_id']) ? $_GET['user_id'] : '7513880877';

// دالة جلب إعدادات تخصيص الصفحة
function getPageCustomizationSettings($user_id, $supabase_url, $supabase_key) {
    if (!$user_id) return null;

    $url = $supabase_url . "/rest/v1/page_customization_settings?user_id=eq." . $user_id;

    $headers = array(
        'apikey: ' . $supabase_key,
        'Authorization: Bearer ' . $supabase_key,
        'Content-Type: application/json'
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code == 200) {
        $data = json_decode($response, true);
        return !empty($data) ? $data[0] : null;
    }

    return null;
}

// جلب إعدادات التخصيص
$page_customization = getPageCustomizationSettings($user_id, $SUPABASE_URL, $SUPABASE_KEY);

// إعدادات الأساليب المختلفة
$style_templates = [
    'default' => [
        'name' => 'الافتراضي',
        'bg_color' => '#1a1a1a',
        'header_color' => '#FFA500',
        'text_color' => '#ffffff',
        'button_color' => '#FFA500',
        'accent_color' => '#FFD700',
        'font_family' => 'Press Start 2P'
    ],
    'telegram' => [
        'name' => 'تيليجرام',
        'bg_color' => '#0088cc',
        'header_color' => '#0088cc',
        'text_color' => '#ffffff',
        'button_color' => '#40a7e3',
        'accent_color' => '#64b5f6',
        'font_family' => 'Roboto'
    ],
    'tiktok' => [
        'name' => 'تيك توك',
        'bg_color' => '#000000',
        'header_color' => '#ff0050',
        'text_color' => '#ffffff',
        'button_color' => '#ff0050',
        'accent_color' => '#25f4ee',
        'font_family' => 'Poppins'
    ],
    'classic' => [
        'name' => 'كلاسيكي',
        'bg_color' => '#f5f5dc',
        'header_color' => '#8b4513',
        'text_color' => '#2f4f4f',
        'button_color' => '#cd853f',
        'accent_color' => '#daa520',
        'font_family' => 'Georgia'
    ],
    'professional' => [
        'name' => 'احترافي',
        'bg_color' => '#f8f9fa',
        'header_color' => '#2c3e50',
        'text_color' => '#2c3e50',
        'button_color' => '#3498db',
        'accent_color' => '#e74c3c',
        'font_family' => 'Inter'
    ]
];

// تطبيق الستايل المحدد
$current_style = $style_templates[$test_style] ?? $style_templates['default'];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أساليب التصميم - <?php echo $current_style['name']; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Roboto:wght@300;400;500;700&family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="style-templates.css">
    
    <style>
        :root {
            --bg-color: <?php echo $current_style['bg_color']; ?>;
            --header-color: <?php echo $current_style['header_color']; ?>;
            --text-color: <?php echo $current_style['text_color']; ?>;
            --button-color: <?php echo $current_style['button_color']; ?>;
            --accent-color: <?php echo $current_style['accent_color']; ?>;
            --font-family: '<?php echo $current_style['font_family']; ?>', sans-serif;
        }
        
        body {
            background: var(--bg-color);
            color: var(--text-color);
            font-family: var(--font-family);
        }
        
        .test-header {
            background: var(--header-color);
            color: var(--text-color);
            padding: 1rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-button {
            background: var(--button-color);
            color: var(--text-color);
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            margin: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: var(--accent-color);
            transform: translateY(-2px);
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid var(--accent-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem;
            backdrop-filter: blur(10px);
        }
        
        .style-info {
            background: rgba(0, 0, 0, 0.1);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
    </style>
</head>
<body class="style-template-<?php echo $test_style; ?>">
    <div class="test-header">
        <h1>🎨 اختبار أساليب التصميم</h1>
        <p>الستايل الحالي: <?php echo $current_style['name']; ?></p>
    </div>

    <div class="container mx-auto p-4">
        <!-- أزرار اختيار الستايل -->
        <div class="text-center mb-8">
            <h2 class="text-xl mb-4">اختر ستايل للاختبار:</h2>
            <?php foreach ($style_templates as $style_key => $style_data): ?>
                <a href="?style=<?php echo $style_key; ?>&user_id=<?php echo $user_id; ?>" 
                   class="test-button <?php echo $test_style == $style_key ? 'opacity-75' : ''; ?>">
                    <?php echo $style_data['name']; ?>
                </a>
            <?php endforeach; ?>
        </div>

        <!-- معلومات الستايل الحالي -->
        <div class="test-card">
            <h3 class="text-lg font-bold mb-4">معلومات الستايل الحالي:</h3>
            <div class="style-info">
                <p><strong>الاسم:</strong> <?php echo $current_style['name']; ?></p>
                <p><strong>لون الخلفية:</strong> <?php echo $current_style['bg_color']; ?></p>
                <p><strong>لون الهيدر:</strong> <?php echo $current_style['header_color']; ?></p>
                <p><strong>لون الأزرار:</strong> <?php echo $current_style['button_color']; ?></p>
                <p><strong>اللون المميز:</strong> <?php echo $current_style['accent_color']; ?></p>
                <p><strong>الخط:</strong> <?php echo $current_style['font_family']; ?></p>
            </div>
        </div>

        <!-- معلومات قاعدة البيانات -->
        <div class="test-card">
            <h3 class="text-lg font-bold mb-4">معلومات قاعدة البيانات:</h3>
            <div class="style-info">
                <p><strong>معرف المستخدم:</strong> <?php echo htmlspecialchars($user_id); ?></p>
                <?php if ($page_customization): ?>
                    <p><strong>✅ تم العثور على إعدادات التخصيص</strong></p>
                    <p><strong>الستايل المحفوظ:</strong> <?php echo htmlspecialchars($page_customization['style_template'] ?? 'غير محدد'); ?></p>
                    <p><strong>لون الخلفية المحفوظ:</strong> <?php echo htmlspecialchars($page_customization['custom_bg_color'] ?? 'غير محدد'); ?></p>
                    <p><strong>لون الهيدر المحفوظ:</strong> <?php echo htmlspecialchars($page_customization['custom_header_color'] ?? 'غير محدد'); ?></p>
                    <details>
                        <summary>عرض جميع البيانات المحفوظة</summary>
                        <pre style="background: rgba(0,0,0,0.2); padding: 1rem; border-radius: 4px; margin-top: 1rem; font-size: 12px; overflow-x: auto;">
<?php echo json_encode($page_customization, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?>
                        </pre>
                    </details>
                <?php else: ?>
                    <p><strong>❌ لم يتم العثور على إعدادات التخصيص</strong></p>
                    <p>تأكد من أن المستخدم قام بحفظ إعدادات التخصيص في البوت</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- اختبار العناصر -->
        <div class="test-card">
            <h3 class="text-lg font-bold mb-4">اختبار العناصر:</h3>
            <button class="test-button">زر تجريبي</button>
            <button class="test-button">زر آخر</button>
            <div style="background: var(--accent-color); color: var(--text-color); padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                هذا نص تجريبي باللون المميز
            </div>
        </div>

        <!-- روابط مفيدة -->
        <div class="test-card">
            <h3 class="text-lg font-bold mb-4">روابط مفيدة:</h3>
            <p><a href="index.php?preview=1&user_id=<?php echo $user_id; ?>" class="test-button">معاينة الصفحة الرئيسية</a></p>
            <p><a href="?style=telegram&user_id=<?php echo $user_id; ?>" class="test-button">اختبار ستايل تيليجرام</a></p>
            <p><a href="?style=tiktok&user_id=<?php echo $user_id; ?>" class="test-button">اختبار ستايل تيك توك</a></p>
        </div>
    </div>
</body>
</html>
