#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة إدارة المميزات العامة على مستوى النظام
Test for global features management functionality
"""

import sys
import os
import json
from unittest.mock import Mock, AsyncMock

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_global_features_functions_exist():
    """اختبار وجود دوال إدارة المميزات العامة"""
    print("🔍 اختبار وجود دوال إدارة المميزات العامة...")
    
    try:
        from main import (
            # دوال إدارة الإعدادات العامة
            load_admin_settings,
            save_admin_settings,
            is_global_feature_enabled,
            set_global_feature_status,
            get_all_global_features_status,
            enable_all_global_features,
            disable_all_global_features,
            
            # دوال واجهة المستخدم
            admin_global_features_menu,
            admin_toggle_global_feature,
            admin_enable_all_global_features,
            admin_disable_all_global_features,
            
            # دوال التحقق المحدثة
            check_feature_access
        )
        print("✅ جميع الدوال موجودة")
        return True
    except ImportError as e:
        print(f"❌ دالة مفقودة: {e}")
        return False

def test_admin_settings_structure():
    """اختبار بنية إعدادات الأدمن المحدثة"""
    print("\n🏗️ اختبار بنية إعدادات الأدمن...")
    
    try:
        from main import load_admin_settings, save_admin_settings
        
        # تحميل الإعدادات
        settings = load_admin_settings()
        
        # التحقق من وجود المفاتيح المطلوبة
        required_keys = ['admin_preview_required', 'global_features_enabled']
        missing_keys = []
        
        for key in required_keys:
            if key not in settings:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"❌ مفاتيح مفقودة: {missing_keys}")
            return False
        
        # التحقق من بنية global_features_enabled
        global_features = settings.get('global_features_enabled', {})
        expected_features = [
            'unlimited_channels',
            'url_shortener_access',
            'custom_download_links',
            'publish_intervals_extended',
            'tasks_system_access',
            'page_customization_vip',
            'ads_system_access'
        ]
        
        missing_features = []
        for feature in expected_features:
            if feature not in global_features:
                missing_features.append(feature)
        
        if missing_features:
            print(f"❌ مميزات مفقودة في الإعدادات: {missing_features}")
            return False
        
        print("✅ بنية إعدادات الأدمن صحيحة")
        print(f"📊 عدد المميزات المدعومة: {len(expected_features)}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص بنية الإعدادات: {e}")
        return False

def test_global_features_management():
    """اختبار إدارة المميزات العامة"""
    print("\n🔧 اختبار إدارة المميزات العامة...")
    
    try:
        from main import (
            is_global_feature_enabled,
            set_global_feature_status,
            enable_all_global_features,
            disable_all_global_features,
            get_all_global_features_status
        )
        
        test_feature = 'url_shortener_access'
        
        # اختبار الحالة الافتراضية
        initial_status = is_global_feature_enabled(test_feature)
        print(f"✅ الحالة الافتراضية لـ {test_feature}: {initial_status}")
        
        # اختبار تعطيل ميزة
        set_global_feature_status(test_feature, False)
        disabled_status = is_global_feature_enabled(test_feature)
        if disabled_status:
            print("❌ فشل في تعطيل الميزة")
            return False
        print("✅ تعطيل الميزة: نجح")
        
        # اختبار تفعيل ميزة
        set_global_feature_status(test_feature, True)
        enabled_status = is_global_feature_enabled(test_feature)
        if not enabled_status:
            print("❌ فشل في تفعيل الميزة")
            return False
        print("✅ تفعيل الميزة: نجح")
        
        # اختبار تعطيل جميع المميزات
        disable_all_global_features()
        all_features = get_all_global_features_status()
        if any(all_features.values()):
            print("❌ فشل في تعطيل جميع المميزات")
            return False
        print("✅ تعطيل جميع المميزات: نجح")
        
        # اختبار تفعيل جميع المميزات
        enable_all_global_features()
        all_features = get_all_global_features_status()
        if not all(all_features.values()):
            print("❌ فشل في تفعيل جميع المميزات")
            return False
        print("✅ تفعيل جميع المميزات: نجح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة المميزات: {e}")
        return False

def test_feature_access_integration():
    """اختبار تكامل فحص الوصول للمميزات مع الإعدادات العامة"""
    print("\n🔐 اختبار تكامل فحص الوصول للمميزات...")
    
    try:
        from main import (
            check_feature_access,
            set_global_feature_status,
            create_user_invitation,
            grant_invitation_rewards
        )
        import asyncio
        
        # إنشاء مستخدم تجريبي
        test_user_id = "999888777"
        test_feature = "url_shortener"
        
        # منح المستخدم المميزة عبر نظام الدعوات
        create_user_invitation(test_user_id)
        grant_invitation_rewards(test_user_id, 3)  # 3 دعوات لتفعيل اختصار الروابط
        
        # اختبار الوصول مع تفعيل الميزة عامة
        set_global_feature_status('url_shortener_access', True)
        
        # محاكاة فحص الوصول (بدون context للاختبار)
        # في الواقع، هذا سيحتاج context للمميزات التي تتطلب اشتراك
        print("✅ تم إعداد المستخدم والميزة للاختبار")
        
        # اختبار تعطيل الميزة عامة
        set_global_feature_status('url_shortener_access', False)
        print("✅ تم تعطيل الميزة على مستوى النظام")
        
        # إعادة تفعيل الميزة
        set_global_feature_status('url_shortener_access', True)
        print("✅ تم إعادة تفعيل الميزة على مستوى النظام")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تكامل فحص الوصول: {e}")
        return False

def test_admin_panel_integration():
    """اختبار تكامل الميزة مع لوحة الأدمن"""
    print("\n🎛️ اختبار تكامل لوحة الأدمن...")
    
    try:
        from main import admin_panel
        import inspect
        
        source = inspect.getsource(admin_panel)
        
        # التحقق من وجود زر إدارة المميزات العامة
        if "admin_global_features_menu" not in source:
            print("❌ زر إدارة المميزات العامة غير موجود في لوحة الأدمن")
            return False
        
        if "🌐 إدارة المميزات العامة" not in source:
            print("❌ نص زر إدارة المميزات العامة غير موجود")
            return False
        
        print("✅ زر إدارة المميزات العامة موجود في لوحة الأدمن")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص تكامل لوحة الأدمن: {e}")
        return False

def test_callback_handlers():
    """اختبار معالجات الأحداث للمميزات العامة"""
    print("\n🔗 اختبار معالجات الأحداث...")
    
    expected_patterns = [
        "admin_global_features_menu",
        "admin_toggle_global_feature_",
        "admin_enable_all_global_features",
        "admin_disable_all_global_features"
    ]
    
    try:
        # قراءة ملف main.py للبحث عن المعالجات
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        missing_handlers = []
        for pattern in expected_patterns:
            if pattern not in content:
                missing_handlers.append(pattern)
        
        if missing_handlers:
            print(f"❌ معالجات مفقودة: {missing_handlers}")
            return False
        
        print("✅ جميع معالجات الأحداث موجودة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص معالجات الأحداث: {e}")
        return False

async def test_mock_admin_functions():
    """اختبار الدوال مع mock objects"""
    print("\n🧪 اختبار الدوال مع mock objects...")
    
    try:
        from main import admin_global_features_menu, admin_toggle_global_feature
        
        # إنشاء mock objects
        update = Mock()
        update.callback_query = Mock()
        update.callback_query.answer = AsyncMock()
        update.callback_query.from_user = Mock()
        update.callback_query.from_user.id = int(os.environ.get("ADMIN_CHAT_ID", "7513880877"))
        update.callback_query.message = Mock()
        update.callback_query.message.photo = None
        update.callback_query.data = "admin_toggle_global_feature_url_shortener_access"
        
        context = Mock()
        context.user_data = {}
        context.bot = AsyncMock()
        
        # اختبار دالة القائمة الرئيسية
        await admin_global_features_menu(update, context)
        print("✅ دالة القائمة الرئيسية: نجحت")
        
        # اختبار دالة تبديل الميزة
        await admin_toggle_global_feature(update, context)
        print("✅ دالة تبديل الميزة: نجحت")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدوال: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار ميزة إدارة المميزات العامة على مستوى النظام")
    print("=" * 80)
    
    tests = [
        ("وجود الدوال", test_global_features_functions_exist),
        ("بنية إعدادات الأدمن", test_admin_settings_structure),
        ("إدارة المميزات العامة", test_global_features_management),
        ("تكامل فحص الوصول", test_feature_access_integration),
        ("تكامل لوحة الأدمن", test_admin_panel_integration),
        ("معالجات الأحداث", test_callback_handlers),
        ("اختبار الدوال", test_mock_admin_functions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📝 {test_name}:")
        print("-" * 50)
        try:
            if test_func.__name__ == 'test_mock_admin_functions':
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 نتائج الاختبار النهائية:")
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت! ميزة إدارة المميزات العامة جاهزة.")
        print("\n📋 الميزات المضافة:")
        print("• 🌐 زر إدارة المميزات العامة في لوحة الأدمن")
        print("• 🎛️ تفعيل/تعطيل المميزات على مستوى النظام")
        print("• 📊 عرض حالة جميع المميزات")
        print("• ✅ تفعيل جميع المميزات دفعة واحدة")
        print("• 🚫 تعطيل جميع المميزات دفعة واحدة")
        print("• 🔐 تكامل مع نظام فحص الوصول للمميزات")
        print("• 💾 حفظ الإعدادات في ملف admin_settings.json")
        print("• 🔄 تحديث فوري للإعدادات")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح لوحة الأدمن بإرسال /admin")
        print("2. اضغط على '🌐 إدارة المميزات العامة'")
        print("3. فعل أو عطل المميزات حسب الحاجة")
        print("4. التغييرات تطبق فوراً على جميع المستخدمين")
    else:
        print(f"\n⚠️ هناك {total - passed} اختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
