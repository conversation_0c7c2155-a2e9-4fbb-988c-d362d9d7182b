#!/usr/bin/env python3
"""
خادم ويب منفصل للاستضافة
Standalone web server for hosting
"""

import os
import sys
from pathlib import Path

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, str(Path(__file__).parent))

try:
    from web_server import app, run_web_server
    from telegram_web_app import run_telegram_web_app
    import threading
    import time
except ImportError as e:
    print(f"Error importing modules: {e}")
    sys.exit(1)

def start_all_servers():
    """تشغيل جميع الخوادم"""
    port = int(os.environ.get("PORT", 5000))
    
    print(f"🚀 Starting web servers on port {port}")
    
    # تشغيل خادم Telegram Web App في thread منفصل
    telegram_thread = threading.Thread(
        target=run_telegram_web_app,
        args=(port + 1,),
        daemon=True
    )
    telegram_thread.start()
    
    # تشغيل الخادم الرئيسي
    run_web_server(port)

if __name__ == "__main__":
    start_all_servers()
