#!/usr/bin/env python3
"""
إعداد الوصول العام عبر HTTPS
Setup Public HTTPS Access

هذا الملف يحل مشكلة عدم عمل Telegram Web App على الأجهزة الأخرى
This file solves the issue of Telegram Web App not working on other devices
"""

import os
import sys
import json
import time
import requests
import subprocess
import threading
import webbrowser
from pathlib import Path

def print_header():
    """طباعة رأس الإعداد"""
    print("\n" + "="*70)
    print("🌐 إعداد الوصول العام عبر HTTPS")
    print("🔒 Setup Public HTTPS Access for Telegram Web App")
    print("="*70)
    print("✅ سيحل مشكلة عدم عمل الصفحة على الأجهزة الأخرى")
    print("✅ Will fix the issue of pages not working on other devices")
    print("="*70 + "\n")

def check_ngrok_installation():
    """التحقق من تثبيت ngrok"""
    print("🔍 Checking ngrok installation...")
    
    try:
        result = subprocess.run(['ngrok', 'version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ ngrok found: {version}")
            return True
        else:
            print("❌ ngrok command failed")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ ngrok not found")
        return False

def install_ngrok_guide():
    """دليل تثبيت ngrok"""
    print("\n📥 دليل تثبيت ngrok:")
    print("="*50)
    print("1. 🌐 اذهب إلى: https://ngrok.com")
    print("2. 📝 قم بإنشاء حساب مجاني")
    print("3. 💾 حمل ngrok لنظام التشغيل الخاص بك:")
    print("   • Windows: ngrok-v3-stable-windows-amd64.zip")
    print("   • macOS: ngrok-v3-stable-darwin-amd64.zip")
    print("   • Linux: ngrok-v3-stable-linux-amd64.tgz")
    print("4. 📁 فك الضغط وضع ngrok في مجلد البوت")
    print("5. 🔑 احصل على authtoken من لوحة التحكم")
    print("6. ⚙️ قم بتشغيل: ngrok config add-authtoken YOUR_TOKEN")
    print("="*50)
    
    # فتح موقع ngrok
    try:
        print("\n🌐 Opening ngrok website...")
        webbrowser.open("https://ngrok.com/download")
    except Exception:
        pass
    
    return False

def get_ngrok_authtoken():
    """الحصول على authtoken من المستخدم"""
    print("\n🔑 إعداد ngrok authtoken:")
    print("1. اذهب إلى: https://dashboard.ngrok.com/get-started/your-authtoken")
    print("2. انسخ الـ authtoken")
    
    authtoken = input("\n🔐 أدخل authtoken (أو اضغط Enter للتخطي): ").strip()
    
    if authtoken:
        try:
            result = subprocess.run(['ngrok', 'config', 'add-authtoken', authtoken],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ Authtoken configured successfully")
                return True
            else:
                print(f"❌ Failed to configure authtoken: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Error configuring authtoken: {e}")
            return False
    else:
        print("⚠️ Skipping authtoken configuration")
        return True

def start_bot_servers():
    """تشغيل خوادم البوت"""
    print("\n🚀 Starting bot servers...")
    
    try:
        # تشغيل خادم Telegram Web App
        from telegram_web_app import run_telegram_web_app
        
        web_app_thread = threading.Thread(
            target=run_telegram_web_app,
            args=(5001,),
            daemon=True,
            name="HTTPSTelegramWebApp"
        )
        web_app_thread.start()
        print("✅ Telegram Web App server started on port 5001")
        
        # تشغيل الخادم الأساسي
        from web_server import run_web_server
        
        flask_thread = threading.Thread(
            target=run_web_server,
            args=(5000,),
            daemon=True,
            name="HTTPSFlaskServer"
        )
        flask_thread.start()
        print("✅ Flask server started on port 5000")
        
        # انتظار قصير للتأكد من تشغيل الخوادم
        time.sleep(3)
        return True
        
    except Exception as e:
        print(f"❌ Failed to start servers: {e}")
        return False

def start_ngrok_tunnel(port=5001):
    """تشغيل نفق ngrok"""
    print(f"\n🌐 Starting ngrok tunnel for port {port}...")
    
    try:
        # تشغيل ngrok في الخلفية
        process = subprocess.Popen([
            'ngrok', 'http', str(port), '--log=stdout'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # انتظار قصير لبدء التشغيل
        time.sleep(5)
        
        # الحصول على الرابط العام
        try:
            response = requests.get('http://localhost:4040/api/tunnels', timeout=10)
            if response.status_code == 200:
                data = response.json()
                tunnels = data.get('tunnels', [])
                
                https_url = None
                http_url = None
                
                for tunnel in tunnels:
                    public_url = tunnel.get('public_url', '')
                    if public_url.startswith('https://'):
                        https_url = public_url
                    elif public_url.startswith('http://'):
                        http_url = public_url
                
                if https_url:
                    print(f"✅ HTTPS tunnel created successfully!")
                    print(f"🔒 HTTPS URL: {https_url}")
                    return https_url, process
                elif http_url:
                    print(f"⚠️ Only HTTP tunnel available: {http_url}")
                    print("🔒 Telegram requires HTTPS for Web Apps")
                    return http_url, process
                else:
                    print("❌ No tunnels found")
                    return None, process
            else:
                print(f"❌ Failed to get tunnel info: {response.status_code}")
                return None, process
                
        except requests.RequestException as e:
            print(f"❌ Could not connect to ngrok API: {e}")
            return None, process
            
    except Exception as e:
        print(f"❌ Failed to start ngrok: {e}")
        return None, None

def update_env_file(https_url):
    """تحديث ملف .env بالرابط HTTPS"""
    print(f"\n📝 Updating .env file with HTTPS URL...")
    
    env_file = Path(".env")
    env_lines = []
    web_server_url_updated = False
    
    # قراءة الملف الموجود
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('WEB_SERVER_URL='):
                        env_lines.append(f"WEB_SERVER_URL={https_url}")
                        web_server_url_updated = True
                        print(f"✅ Updated WEB_SERVER_URL to {https_url}")
                    else:
                        env_lines.append(line)
        except Exception as e:
            print(f"⚠️ Error reading .env file: {e}")
    
    # إضافة WEB_SERVER_URL إذا لم يكن موجوداً
    if not web_server_url_updated:
        env_lines.append(f"WEB_SERVER_URL={https_url}")
        print(f"✅ Added WEB_SERVER_URL={https_url}")
    
    # حفظ الملف
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            for line in env_lines:
                f.write(line + '\n')
        print("✅ .env file updated successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to update .env file: {e}")
        return False

def test_https_access(https_url):
    """اختبار الوصول عبر HTTPS"""
    print(f"\n🧪 Testing HTTPS access...")
    
    test_urls = [
        f"{https_url}/telegram-mod-details?id=1&lang=ar",
        f"{https_url}/api/mod/1?lang=ar"
    ]
    
    for url in test_urls:
        try:
            print(f"📡 Testing: {url}")
            response = requests.get(url, timeout=15)
            if response.status_code in [200, 404]:
                print(f"✅ URL accessible (status: {response.status_code})")
            else:
                print(f"⚠️ URL returned status: {response.status_code}")
        except Exception as e:
            print(f"❌ Failed to access {url}: {e}")

def create_qr_code(https_url):
    """إنشاء QR code للرابط HTTPS"""
    print(f"\n📱 Creating QR code for easy access...")
    
    try:
        import qrcode
        from PIL import Image
        
        # إنشاء QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(https_url)
        qr.make(fit=True)
        
        # إنشاء الصورة
        img = qr.make_image(fill_color="black", back_color="white")
        img.save("https_qr_code.png")
        print("✅ QR code saved as https_qr_code.png")
        print("📱 يمكن للمستخدمين مسح الكود للوصول للصفحة")
        
        return True
        
    except ImportError:
        print("⚠️ qrcode library not installed")
        print("💡 Install with: pip install qrcode[pil]")
        return False
    except Exception as e:
        print(f"❌ Failed to create QR code: {e}")
        return False

def create_sharing_links(https_url):
    """إنشاء روابط للمشاركة"""
    sharing_content = f"""
# 🌐 روابط الوصول العام للبوت
# Public Access Links for Bot

## 🔒 الرابط الآمن (HTTPS)
{https_url}

## 📱 روابط مهمة
- صفحة المود: {https_url}/telegram-mod-details?id=1&lang=ar
- واجهة API: {https_url}/api/mod/1
- اختبار الاتصال: {https_url}/

## 📋 للمشاركة مع المستخدمين
انسخ هذا الرابط وشاركه مع المستخدمين:
{https_url}/telegram-mod-details?id=1&lang=ar

## 📱 للاختبار على الأجهزة المختلفة
1. افتح الرابط على هاتفك
2. افتح الرابط على كمبيوتر آخر
3. شارك الرابط مع أصدقائك للاختبار

## ⚠️ ملاحظات مهمة
- هذا الرابط صالح طالما ngrok يعمل
- لا تغلق terminal الذي يشغل ngrok
- للإنتاج، استخدم خدمة استضافة دائمة

---
تم إنشاء هذا الملف في: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    try:
        with open("sharing_links.txt", "w", encoding='utf-8') as f:
            f.write(sharing_content)
        print("✅ Sharing links saved as sharing_links.txt")
        return True
    except Exception as e:
        print(f"❌ Failed to create sharing links: {e}")
        return False

def show_success_info(https_url):
    """عرض معلومات النجاح"""
    print("\n" + "="*70)
    print("🎉 تم الإعداد بنجاح! الصفحة متاحة الآن لجميع الأجهزة")
    print("🌍 SUCCESS! Page is now accessible from all devices")
    print("="*70)
    print(f"\n🔒 الرابط الآمن: {https_url}")
    print(f"📱 صفحة المود: {https_url}/telegram-mod-details?id=1&lang=ar")
    print(f"🔌 API: {https_url}/api/mod/1")
    print("\n📋 كيفية الاستخدام:")
    print("1. 🤖 قم بتشغيل البوت: python main.py")
    print("2. 📱 أرسل مود من البوت")
    print("3. 🎮 اضغط على زر 'عرض التفاصيل'")
    print("4. ✨ ستفتح الصفحة على أي جهاز في العالم!")
    print("\n🧪 للاختبار:")
    print(f"• افتح هذا الرابط على هاتفك: {https_url}")
    print("• شارك الرابط مع أصدقائك")
    print("• جرب من شبكات مختلفة")
    print("\n⚠️ ملاحظات مهمة:")
    print("• 🔄 يجب إبقاء ngrok يعمل")
    print("• 🌐 الرابط صالح طالما ngrok يعمل")
    print("• 🔒 للإنتاج، استخدم خدمة استضافة دائمة")
    print("• 📄 راجع sharing_links.txt للروابط")
    print("="*70)

def main():
    """الدالة الرئيسية"""
    try:
        print_header()
        
        # التحقق من ngrok
        if not check_ngrok_installation():
            install_ngrok_guide()
            input("\n⏸️ اضغط Enter بعد تثبيت ngrok...")
            if not check_ngrok_installation():
                print("❌ ngrok still not found. Please install it first.")
                return
        
        # إعداد authtoken
        get_ngrok_authtoken()
        
        # تشغيل خوادم البوت
        if not start_bot_servers():
            print("❌ Failed to start bot servers")
            return
        
        # تشغيل نفق ngrok
        https_url, ngrok_process = start_ngrok_tunnel(5001)
        if not https_url:
            print("❌ Failed to create ngrok tunnel")
            return
        
        # التحقق من أن الرابط HTTPS
        if not https_url.startswith('https://'):
            print("⚠️ Warning: URL is not HTTPS. Telegram Web Apps require HTTPS.")
            print("🔧 Try upgrading to ngrok paid plan or use a different service.")
        
        # تحديث ملف .env
        update_env_file(https_url)
        
        # اختبار الوصول
        test_https_access(https_url)
        
        # إنشاء QR code
        create_qr_code(https_url)
        
        # إنشاء روابط المشاركة
        create_sharing_links(https_url)
        
        # عرض معلومات النجاح
        show_success_info(https_url)
        
        print("\n🔄 Servers are running... Press Ctrl+C to stop")
        
        try:
            # إبقاء البرنامج يعمل
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping servers...")
            if ngrok_process:
                ngrok_process.terminate()
            print("✅ Stopped successfully")
        
    except Exception as e:
        print(f"\n💥 Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
