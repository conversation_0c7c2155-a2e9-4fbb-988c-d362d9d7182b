# بوت نشر مودات ماين كرافت - Supabase Edition

## نظرة عامة

هذا بوت تليجرام متطور لنشر مودات ماين كرافت تلقائياً للمستخدمين. البوت يستخدم قاعدة بيانات Supabase لتخزين وإدارة المودات بدلاً من الملفات المحلية.

## ✨ الميزات الرئيسية

### 🎯 إدارة المودات
- **جلب تلقائي** من قاعدة بيانات Supabase
- **دعم متعدد اللغات** (العربية والإنجليزية)
- **تصنيف المودات** (مودات، شادرز، خرائط، تكستر باكس، أدونز)
- **معلومات شاملة** (الإصدار، نوع المحمل، الوصف، الصور)

### 👥 إدارة المستخدمين
- **ربط القنوات** - كل مستخدم يربط قناته الخاصة
- **جدولة مخصصة** - تحديد فترات النشر لكل مستخدم
- **معاينة المودات** - إمكانية معاينة المود قبل النشر
- **حظر المودات** - المستخدم يمكنه حظر مودات معينة
- **إحصائيات مفصلة** - تتبع النشاط والتفاعل

### 🛡️ نظام الموافقات
- **موافقة المسؤول** - إمكانية تفعيل/إيقاف موافقة المسؤول
- **معاينة المستخدم** - المستخدم يوافق أو يرفض المود
- **نظام انتظار ذكي** - إدارة طوابير الانتظار والمهل الزمنية

### 🔧 لوحة تحكم المسؤول
- **إحصائيات شاملة** - عدد المستخدمين، المودات، النشاط
- **إدارة المستخدمين** - عرض وإدارة جميع المستخدمين
- **إعادة النشر** - إعادة نشر مودات معينة
- **حذف المودات** - حذف مودات من قاعدة البيانات
- **بث الرسائل** - إرسال رسائل لجميع المستخدمين أو مجموعات محددة

## 🗄️ بنية قاعدة البيانات

### جدول `minemods`
| العمود | النوع | الوصف |
|--------|-------|--------|
| `id` | int4 | المعرف الفريد (مفتاح أساسي) |
| `name` | text | اسم المود |
| `description` | text | وصف المود الأساسي |
| `image_path` | text | رابط صورة المود |
| `download_link` | text | رابط تحميل المود |
| `mc_version` | text | إصدار ماين كرافت المدعوم |
| `mod_loader` | text | نوع المحمل (Forge, Fabric, Addons, إلخ) |
| `mod_data_json` | jsonb | بيانات إضافية (أوصاف متعددة اللغات، نوع المود) |
| `created_at` | timestamptz | تاريخ ووقت الإنشاء |

### مثال على `mod_data_json`:
```json
{
  "description_ar": "وصف المود باللغة العربية",
  "description_en": "Mod description in English",
  "mod_type": "mod"
}
```

## 🚀 التثبيت والتشغيل

### الطريقة السريعة (مستحسنة):

#### على Windows:
```cmd
start_bot.bat
```

#### على Linux/Mac:
```bash
./start_bot.sh
```

### الطريقة اليدوية:

#### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 2. اختبار الاتصال بـ Supabase
```bash
python test_supabase.py
```

#### 3. تشغيل البوت
```bash
python main.py
```

### طرق أخرى للتشغيل:

#### تشغيل مع فحص أولي:
```bash
python run_bot.py
```

#### اختبار سريع:
```bash
python quick_test.py
```

#### إعداد شامل:
```bash
python setup.py
```

## ⚙️ إعدادات Supabase

```python
SUPABASE_URL = "https://wnjbrdubiegjuycumuoh.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## 📁 هيكل الملفات

```
bot telegram/
├── main.py                 # الملف الرئيسي للبوت
├── supabase_client.py      # وحدة الاتصال بـ Supabase
├── test_supabase.py        # اختبار الاتصال بقاعدة البيانات
├── requirements.txt        # المتطلبات
├── README.md              # هذا الملف
└── __pycache__/           # ملفات Python المترجمة
```

## 🔧 الوظائف الرئيسية في supabase_client.py

| الوظيفة | الوصف |
|---------|--------|
| `get_all_mods()` | جلب جميع المودات من قاعدة البيانات |
| `get_mod_by_id(mod_id)` | جلب مود واحد بالمعرف |
| `delete_mod_from_db(mod_id)` | حذف مود من قاعدة البيانات |
| `add_mod_to_db(mod_data)` | إضافة مود جديد |
| `update_mod_in_db(mod_id, mod_data)` | تحديث مود موجود |
| `get_mods_count()` | الحصول على عدد المودات |
| `search_mods(search_term)` | البحث في المودات |

## 🎮 كيفية استخدام البوت

### للمستخدمين:
1. **ابدأ المحادثة** - أرسل `/start` للبوت
2. **اختر اللغة** - عربية أو إنجليزية
3. **اربط قناتك** - أضف البوت كمشرف في قناتك وأرسل معرف القناة
4. **اضبط الإعدادات** - حدد فترة النشر وفعّل/أوقف المعاينة
5. **استمتع** - البوت سيرسل مودات جديدة تلقائياً!

### للمسؤولين:
1. **لوحة التحكم** - أرسل `/admin` للوصول للوحة التحكم
2. **مراقبة الإحصائيات** - تابع نشاط المستخدمين والمودات
3. **إدارة المحتوى** - احذف أو أعد نشر المودات
4. **تواصل مع المستخدمين** - أرسل رسائل جماعية

## 🔄 التحديثات المطبقة

### ✅ ما تم إصلاحه:
- **إصلاح اتصال Supabase** - تم إضافة عميل Supabase المناسب
- **توحيد طريقة جلب البيانات** - جميع الدوال تستخدم مكتبة supabase الآن
- **تحسين معالجة الأخطاء** - رسائل خطأ أوضح وتسجيل أفضل
- **تنظيف الكود** - إزالة الاستيرادات غير المستخدمة

### 🎯 الحالة الحالية:
- ✅ **البوت يعمل بالكامل** مع قاعدة بيانات Supabase
- ✅ **جلب المودات** يتم من قاعدة البيانات بنجاح
- ✅ **جميع الوظائف** تعمل كما هو متوقع
- ✅ **لا توجد ملفات محلية** للمودات - كل شيء في Supabase

## 📊 إحصائيات الاختبار

```
🧪 اختبار اتصال Supabase
✅ تم الاتصال بنجاح! عدد المودات في قاعدة البيانات: 2
✅ تم جلب 2 مود بنجاح
✅ جميع الاختبارات نجحت!
🚀 البوت جاهز للعمل مع Supabase
```

## 🔧 حل المشاكل الشائعة

### مشكلة: `'Updater' object has no attribute '_Updater__polling_cleanup_cb'`
**الحل:** تحديث مكتبة python-telegram-bot
```bash
pip install --upgrade python-telegram-bot
```

### مشكلة: `AsyncClient.__init__() got an unexpected keyword argument 'proxies'`
**الحل:** تحديث مكتبة httpx
```bash
pip install --upgrade httpx
```

### مشكلة: خطأ في الاتصال بـ Supabase
**الحل:**
1. تحقق من إعدادات Supabase في `supabase_client.py`
2. تأكد من صحة URL ومفتاح API
3. تحقق من الاتصال بالإنترنت

### مشكلة: البوت لا يرسل رسائل للقنوات
**الحل:**
1. تأكد من أن البوت مضاف كمشرف في القناة
2. تحقق من صلاحيات النشر للبوت
3. تأكد من صحة معرف القناة

### مشكلة: لا توجد مودات في قاعدة البيانات
**الحل:**
1. أضف مودات يدوياً إلى جدول `minemods` في Supabase
2. تحقق من صحة بنية الجدول
3. شغّل `python test_supabase.py` للتأكد

## 🆘 الدعم والمساعدة

إذا واجهت أي مشاكل:
1. **تحقق من الاتصال** - شغّل `python test_supabase.py`
2. **راجع السجلات** - ابحث عن رسائل الخطأ في وحدة التحكم
3. **تأكد من الصلاحيات** - البوت يحتاج صلاحيات مشرف في القنوات
4. **استخدم ملفات التشغيل** - `start_bot.bat` أو `start_bot.sh` للتشغيل التلقائي

## 📋 متطلبات النظام

- **Python:** 3.8 أو أحدث
- **نظام التشغيل:** Windows, Linux, macOS
- **الذاكرة:** 512MB RAM كحد أدنى
- **التخزين:** 100MB مساحة فارغة
- **الشبكة:** اتصال إنترنت مستقر

---

**البوت جاهز للاستخدام! 🚀**

### 🎯 الحالة الحالية: ✅ يعمل بالكامل
- ✅ الاتصال بـ Supabase يعمل
- ✅ جلب المودات من قاعدة البيانات
- ✅ جميع وظائف البوت تعمل
- ✅ لا توجد أخطاء في الكود
- ✅ متوافق مع Python 3.13

- ✅ جلب المودات من Supabase بدلاً من الملفات المحلية
- ✅ حذف المودات من قاعدة البيانات
- ✅ دعم البحث في المودات
- ✅ إضافة وتحديث المودات
- ✅ دعم البيانات متعددة اللغات (عربي/إنجليزي)
- ✅ تحويل البيانات تلقائياً من تنسيق قاعدة البيانات إلى تنسيق البوت

### ملاحظات:

- البوت يحتفظ بجميع الوظائف الأصلية
- تم الحفاظ على التوافق مع الكود الموجود
- يمكن إضافة المودات مباشرة إلى قاعدة البيانات عبر واجهة Supabase
- البوت يتعامل مع الأخطاء بشكل آمن ويعود إلى قائمة فارغة في حالة فشل الاتصال

### إضافة مودات جديدة:

يمكن إضافة المودات مباشرة في قاعدة البيانات Supabase أو استخدام الدوال المتوفرة:

```python
mod_data = {
    'title': 'اسم المود',
    'description': {
        'ar': 'الوصف بالعربية',
        'en': 'Description in English'
    },
    'download_url': 'رابط التحميل',
    'image_url': 'رابط الصورة',
    'version': '1.20.1',
    'mod_type': 'mod',
    'for': 'forge'
}

add_mod_to_db(mod_data)
```
