#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار رسائل التحذير للمميزات المقفلة
Test feature locked messages
"""

import sys
import os
import asyncio
from unittest.mock import Mock, AsyncMock

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_feature_locked_message():
    """اختبار دالة عرض رسالة الميزة المقفلة"""
    print("🔍 اختبار دالة show_feature_locked_message...")
    
    try:
        from main import (
            show_feature_locked_message,
            get_user_invitation_level,
            get_user_invitation_stats
        )
        
        # إنشاء mock objects
        update = Mock()
        update.callback_query = Mock()
        update.callback_query.message = Mock()
        update.callback_query.message.photo = None
        
        context = Mock()
        context.bot = AsyncMock()
        context.bot.send_message = AsyncMock()
        
        # محاكاة مستخدم عادي (ليس أدمن)
        test_user_id = "123456789"
        
        print(f"📋 اختبار للمستخدم: {test_user_id}")
        
        # اختبار ميزة اختصار الروابط (تتطلب 3 دعوات)
        print("🔗 اختبار ميزة اختصار الروابط...")
        await show_feature_locked_message(
            update=update,
            context=context,
            user_id=test_user_id,
            feature_type="url_shortener",
            lang="ar"
        )
        print("✅ تم إرسال رسالة اختصار الروابط بنجاح")
        
        # اختبار ميزة نظام المهام (تتطلب 10 دعوات)
        print("📋 اختبار ميزة نظام المهام...")
        await show_feature_locked_message(
            update=update,
            context=context,
            user_id=test_user_id,
            feature_type="tasks_system",
            lang="ar"
        )
        print("✅ تم إرسال رسالة نظام المهام بنجاح")
        
        # اختبار ميزة القنوات غير المحدودة (تتطلب دعوة واحدة)
        print("📺 اختبار ميزة القنوات غير المحدودة...")
        await show_feature_locked_message(
            update=update,
            context=context,
            user_id=test_user_id,
            feature_type="unlimited_channels",
            lang="ar"
        )
        print("✅ تم إرسال رسالة القنوات غير المحدودة بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار show_feature_locked_message: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_check_feature_access():
    """اختبار دالة التحقق من الوصول للمميزات"""
    print("\n🔐 اختبار دالة check_feature_access...")
    
    try:
        from main import check_feature_access, is_admin_user
        
        # اختبار مستخدم عادي
        test_user_id = "123456789"
        
        # التأكد من أن المستخدم ليس أدمن
        is_admin = is_admin_user(test_user_id)
        print(f"👤 المستخدم {test_user_id} أدمن: {is_admin}")
        
        # إنشاء mock context
        context = Mock()
        
        # اختبار الوصول لميزة اختصار الروابط
        has_access = await check_feature_access(test_user_id, 'url_shortener', context)
        print(f"🔗 الوصول لاختصار الروابط: {has_access}")
        
        # اختبار الوصول لميزة نظام المهام
        has_access = await check_feature_access(test_user_id, 'tasks_system', context)
        print(f"📋 الوصول لنظام المهام: {has_access}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار check_feature_access: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_invitation_stats():
    """اختبار دوال إحصائيات الدعوات"""
    print("\n📊 اختبار دوال إحصائيات الدعوات...")
    
    try:
        from main import get_user_invitation_level, get_user_invitation_stats
        
        test_user_id = "123456789"
        
        # اختبار مستوى المستخدم
        user_level = get_user_invitation_level(test_user_id)
        print(f"🏆 مستوى المستخدم: {user_level}")
        
        # اختبار إحصائيات المستخدم
        user_stats = get_user_invitation_stats(test_user_id)
        print(f"📈 إحصائيات المستخدم: {user_stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إحصائيات الدعوات: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نظام رسائل التحذير للمميزات المقفلة")
    print("=" * 60)
    
    tests = [
        ("اختبار رسالة الميزة المقفلة", test_feature_locked_message),
        ("اختبار التحقق من الوصول", test_check_feature_access),
        ("اختبار إحصائيات الدعوات", test_invitation_stats)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📝 {test_name}:")
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام يعمل بشكل صحيح.")
        print("\n💡 ملاحظة: لاختبار النظام في البوت الحقيقي:")
        print("   1. استخدم حساب مستخدم عادي (ليس الأدمن)")
        print("   2. أو استخدم الأمر لإلغاء مميزات الأدمن مؤقتاً")
        print("   3. ثم جرب الوصول لميزة اختصار الروابط")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    asyncio.run(main())
