<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة تفاصيل المود</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-link {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .test-link:hover {
            background-color: #0056b3;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .description {
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار صفحة تفاصيل المود</h1>
    
    <div class="test-container">
        <h2>اختبار باللغة العربية</h2>
        <p class="description">اختبار عرض صفحة تفاصيل المود باللغة العربية مع معرف مود وهمي</p>
        <a href="mod_details.html?id=1&lang=ar&user_id=123456&channel=-1001234567890" class="test-link" target="_blank">
            🔗 اختبار عربي - مود رقم 1
        </a>
        <a href="mod_details.html?id=2&lang=ar&user_id=123456&channel=-1001234567890" class="test-link" target="_blank">
            🔗 اختبار عربي - مود رقم 2
        </a>
    </div>

    <div class="test-container">
        <h2>English Language Test</h2>
        <p class="description">Test mod details page display in English with dummy mod ID</p>
        <a href="mod_details.html?id=1&lang=en&user_id=123456&channel=-1001234567890" class="test-link" target="_blank">
            🔗 English Test - Mod #1
        </a>
        <a href="mod_details.html?id=2&lang=en&user_id=123456&channel=-1001234567890" class="test-link" target="_blank">
            🔗 English Test - Mod #2
        </a>
    </div>

    <div class="test-container">
        <h2>اختبار بدون معاملات</h2>
        <p class="description">اختبار الصفحة بدون معاملات لرؤية معالجة الأخطاء</p>
        <a href="mod_details.html" class="test-link" target="_blank">
            ⚠️ اختبار بدون معرف مود
        </a>
        <a href="mod_details.html?id=999999&lang=ar" class="test-link" target="_blank">
            ⚠️ اختبار مع معرف مود غير موجود
        </a>
    </div>

    <div class="test-container">
        <h2>ملاحظات للاختبار</h2>
        <ul>
            <li>تأكد من أن قاعدة البيانات Supabase تحتوي على مودات بالمعرفات المستخدمة</li>
            <li>تحقق من أن الصفحة تعرض النصوص باللغة الصحيحة</li>
            <li>تأكد من أن اتجاه النص يتغير حسب اللغة (RTL للعربية، LTR للإنجليزية)</li>
            <li>اختبر زر التحميل للتأكد من أنه يعمل بشكل صحيح</li>
            <li>تحقق من معالجة الأخطاء عند عدم وجود المود</li>
        </ul>
    </div>

    <script>
        // إضافة بعض المعلومات المفيدة للاختبار
        console.log('🧪 صفحة اختبار تفاصيل المود');
        console.log('📍 الرابط الحالي:', window.location.href);
        console.log('🌐 اللغة المتصفح:', navigator.language);
        
        // إضافة معلومات عن المعاملات المتاحة
        const urlParams = new URLSearchParams(window.location.search);
        console.log('📋 معاملات الرابط المتاحة:');
        for (const [key, value] of urlParams) {
            console.log(`  ${key}: ${value}`);
        }
    </script>
</body>
</html>
