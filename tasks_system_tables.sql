-- جداول نظام المهام للبوت
-- Tasks System Tables for Supabase (PostgreSQL)

-- 1. جدول إعدادات نظام المهام للمستخدمين
CREATE TABLE IF NOT EXISTS user_tasks_settings (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL UNIQUE,
    channel_id VARCHAR(255) NOT NULL,
    
    -- إعدادات النظام الأساسية
    tasks_enabled BOOLEAN DEFAULT FALSE,
    require_tasks_for_download BOOLEAN DEFAULT TRUE,
    
    -- إعد<PERSON><PERSON>ت المستخدمين المكتملين
    remember_completed_users BOOLEAN DEFAULT TRUE,
    show_tasks_to_completed_users BOOLEAN DEFAULT FALSE,
    
    -- إ<PERSON>د<PERSON>ا<PERSON> الإعلانات للمهام
    tasks_ad_enabled BOOLEAN DEFAULT FALSE,
    tasks_ad_direct_link TEXT NULL,
    tasks_ad_network VARCHAR(100) DEFAULT 'custom',
    
    -- إح<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    total_task_completions INTEGER DEFAULT 0,
    total_unique_completers INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. جدول المهام المتاحة
CREATE TABLE IF NOT EXISTS available_tasks (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL, -- صاحب القناة
    channel_id VARCHAR(255) NOT NULL,
    
    -- معلومات المهمة
    task_type VARCHAR(50) NOT NULL, -- 'youtube', 'telegram', 'twitter', 'discord', 'instagram', 'website'
    task_title VARCHAR(255) NOT NULL,
    task_description TEXT,
    task_url TEXT NOT NULL,
    task_verification_text VARCHAR(255), -- نص للتحقق (اختياري)
    
    -- إعدادات المهمة
    is_active BOOLEAN DEFAULT TRUE,
    is_required BOOLEAN DEFAULT TRUE,
    task_order INTEGER DEFAULT 0,
    
    -- إحصائيات المهمة
    completion_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 3. جدول إكمال المهام من قبل المستخدمين
CREATE TABLE IF NOT EXISTS user_task_completions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL, -- المستخدم الذي أكمل المهمة
    task_id INTEGER NOT NULL REFERENCES available_tasks(id) ON DELETE CASCADE,
    channel_owner_id VARCHAR(255) NOT NULL, -- صاحب القناة
    
    -- معلومات الإكمال
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    verification_data TEXT, -- بيانات إضافية للتحقق
    is_verified BOOLEAN DEFAULT TRUE,
    
    -- فهرس مركب لمنع التكرار
    UNIQUE(user_id, task_id)
);

-- 4. جدول المستخدمين الذين أكملوا جميع المهام
CREATE TABLE IF NOT EXISTS completed_users (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    channel_owner_id VARCHAR(255) NOT NULL,
    channel_id VARCHAR(255) NOT NULL,
    
    -- معلومات الإكمال
    first_completion_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_completion_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    total_completions INTEGER DEFAULT 1,
    
    -- حالة المستخدم
    is_active BOOLEAN DEFAULT TRUE,
    bypass_tasks BOOLEAN DEFAULT FALSE, -- تجاوز المهام نهائياً
    
    -- فهرس مركب لمنع التكرار
    UNIQUE(user_id, channel_owner_id)
);

-- 5. جدول إحصائيات تحميل المودات مع المهام
CREATE TABLE IF NOT EXISTS mod_download_stats (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    mod_id VARCHAR(255) NOT NULL,
    channel_owner_id VARCHAR(255) NOT NULL,
    
    -- معلومات التحميل
    downloaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    had_tasks BOOLEAN DEFAULT FALSE,
    completed_tasks_before_download BOOLEAN DEFAULT FALSE,
    tasks_ad_shown BOOLEAN DEFAULT FALSE,
    
    -- بيانات إضافية
    user_agent TEXT,
    ip_address INET
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_tasks_settings_user_id ON user_tasks_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_tasks_settings_channel_id ON user_tasks_settings(channel_id);

CREATE INDEX IF NOT EXISTS idx_available_tasks_user_id ON available_tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_available_tasks_channel_id ON available_tasks(channel_id);
CREATE INDEX IF NOT EXISTS idx_available_tasks_type ON available_tasks(task_type);
CREATE INDEX IF NOT EXISTS idx_available_tasks_active ON available_tasks(is_active);

CREATE INDEX IF NOT EXISTS idx_user_task_completions_user_id ON user_task_completions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_task_completions_task_id ON user_task_completions(task_id);
CREATE INDEX IF NOT EXISTS idx_user_task_completions_channel_owner ON user_task_completions(channel_owner_id);

CREATE INDEX IF NOT EXISTS idx_completed_users_user_id ON completed_users(user_id);
CREATE INDEX IF NOT EXISTS idx_completed_users_channel_owner ON completed_users(channel_owner_id);
CREATE INDEX IF NOT EXISTS idx_completed_users_active ON completed_users(is_active);

CREATE INDEX IF NOT EXISTS idx_mod_download_stats_user_id ON mod_download_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_mod_download_stats_mod_id ON mod_download_stats(mod_id);
CREATE INDEX IF NOT EXISTS idx_mod_download_stats_channel_owner ON mod_download_stats(channel_owner_id);
CREATE INDEX IF NOT EXISTS idx_mod_download_stats_downloaded_at ON mod_download_stats(downloaded_at);

-- إضافة التعليقات للجداول
COMMENT ON TABLE user_tasks_settings IS 'إعدادات نظام المهام لكل مستخدم/قناة';
COMMENT ON TABLE available_tasks IS 'المهام المتاحة التي يضعها أصحاب القنوات';
COMMENT ON TABLE user_task_completions IS 'سجل إكمال المهام من قبل المستخدمين';
COMMENT ON TABLE completed_users IS 'المستخدمون الذين أكملوا جميع المهام';
COMMENT ON TABLE mod_download_stats IS 'إحصائيات تحميل المودات مع نظام المهام';

-- إضافة التعليقات للأعمدة المهمة
COMMENT ON COLUMN user_tasks_settings.tasks_enabled IS 'هل نظام المهام مفعل';
COMMENT ON COLUMN user_tasks_settings.remember_completed_users IS 'هل يتذكر المستخدمين الذين أكملوا المهام';
COMMENT ON COLUMN available_tasks.task_type IS 'نوع المهمة: youtube, telegram, twitter, discord, instagram, website';
COMMENT ON COLUMN available_tasks.task_verification_text IS 'نص للتحقق من إكمال المهمة';
COMMENT ON COLUMN completed_users.bypass_tasks IS 'تجاوز المهام نهائياً لهذا المستخدم';

-- إنشاء دوال لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء triggers لتحديث updated_at
CREATE TRIGGER update_user_tasks_settings_updated_at
    BEFORE UPDATE ON user_tasks_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_available_tasks_updated_at
    BEFORE UPDATE ON available_tasks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- دالة للتحقق من إكمال جميع المهام المطلوبة
CREATE OR REPLACE FUNCTION check_all_required_tasks_completed(
    p_user_id VARCHAR(255),
    p_channel_owner_id VARCHAR(255)
) RETURNS BOOLEAN AS $$
DECLARE
    required_tasks_count INTEGER;
    completed_tasks_count INTEGER;
BEGIN
    -- عدد المهام المطلوبة والنشطة
    SELECT COUNT(*) INTO required_tasks_count
    FROM available_tasks
    WHERE user_id = p_channel_owner_id
    AND is_active = TRUE
    AND is_required = TRUE;

    -- عدد المهام المكتملة من قبل المستخدم
    SELECT COUNT(*) INTO completed_tasks_count
    FROM user_task_completions utc
    JOIN available_tasks at ON utc.task_id = at.id
    WHERE utc.user_id = p_user_id
    AND utc.channel_owner_id = p_channel_owner_id
    AND at.is_active = TRUE
    AND at.is_required = TRUE
    AND utc.is_verified = TRUE;

    RETURN (completed_tasks_count >= required_tasks_count AND required_tasks_count > 0);
END;
$$ LANGUAGE plpgsql;

-- دالة لإضافة مستخدم إلى قائمة المكتملين
CREATE OR REPLACE FUNCTION add_completed_user(
    p_user_id VARCHAR(255),
    p_channel_owner_id VARCHAR(255),
    p_channel_id VARCHAR(255)
) RETURNS VOID AS $$
BEGIN
    INSERT INTO completed_users (user_id, channel_owner_id, channel_id)
    VALUES (p_user_id, p_channel_owner_id, p_channel_id)
    ON CONFLICT (user_id, channel_owner_id)
    DO UPDATE SET
        last_completion_at = CURRENT_TIMESTAMP,
        total_completions = completed_users.total_completions + 1,
        is_active = TRUE;
END;
$$ LANGUAGE plpgsql;
