# 🎮 صفحة عرض مودات ماين كرافت - InfinityFree Version

## 📋 نظرة عامة

هذا المشروع عبارة عن صفحة ويب مستقلة لعرض تفاصيل مودات ماين كرافت، مصممة خصيصاً للعمل على استضافة InfinityFree المجانية. الصفحة تتصل مباشرة بقاعدة بيانات Supabase لجلب بيانات المودات وعرضها بشكل تفاعلي وجذاب.

## 🎯 الهدف الرئيسي

إنشاء بديل مستقل لصفحة عرض المودات التي تعمل بدون الحاجة إلى:
- ❌ خادم محلي (Flask)
- ❌ ngrok للوصول العام
- ❌ إبقاء الكمبيوتر مفتوحاً
- ✅ **يعمل 24/7 على الاستضافة المجانية**

## 🌟 المميزات

### ✨ المميزات الأساسية
- **عرض تفاصيل المود**: عنوان، وصف، إصدار، تصنيف
- **معرض صور تفاعلي**: عرض صور متعددة مع إمكانية التنقل
- **دعم اللغات**: العربية والإنجليزية
- **تحميل تفاعلي**: مؤشر تقدم وإحصائيات التحميل
- **تصميم متجاوب**: يعمل على جميع الأجهزة

### 🎯 المميزات المتقدمة
- **نظام الإعلانات**: عرض إعلانات قابلة للتخصيص
- **نظام المهام**: مهام للمستخدمين قبل التحميل
- **فتح ماين كرافت**: فتح المود مباشرة في اللعبة
- **حفظ حالة التحميل**: تذكر المودات المحملة
- **إشعارات تفاعلية**: تنبيهات جميلة للمستخدم

## 📁 هيكل الملفات

```
hosting_files/
├── 📄 الملفات الأساسية
│   ├── index.php          # الصفحة الرئيسية (PHP)
│   ├── api.php           # واجهة برمجة التطبيقات
│   ├── config.php        # ملف الإعدادات الرئيسي
│   ├── style.css         # ملف التصميم والأنماط
│   ├── script.js         # ملف الجافاسكريبت
│   └── .htaccess         # إعدادات الخادم وإعادة التوجيه
│
├── 🚨 صفحات الأخطاء
│   ├── 404.html          # صفحة خطأ 404 (غير موجود)
│   └── 500.html          # صفحة خطأ 500 (خطأ خادم)
│
├── 🔧 أدوات الإدارة
│   ├── deploy.php        # صفحة الإعداد والفحص
│   ├── logs.php          # عارض السجلات
│   └── clear_log.php     # مسح السجلات
│
├── 🌐 ملفات SEO
│   ├── robots.txt        # ملف محركات البحث
│   └── sitemap.xml       # خريطة الموقع
│
├── 📚 الوثائق
│   ├── README.md         # هذا الملف
│   ├── INSTALLATION.md   # دليل التثبيت المفصل
│   └── update_bot.py     # سكريبت تحديث البوت
│
└── 📊 السجلات (يتم إنشاؤها تلقائياً)
    └── logs/
        └── app.log       # سجل التطبيق
```

## 🚀 التثبيت السريع (5 دقائق)

### الخطوة 1: تحضير الاستضافة ⚡
```bash
1. اذهب إلى https://www.infinityfree.com
2. سجل حساب مجاني
3. أنشئ موقع جديد
4. احصل على تفاصيل FTP
```

### الخطوة 2: رفع الملفات 📤
```bash
1. ارفع جميع الملفات إلى مجلد htdocs
2. تأكد من رفع .htaccess
3. احتفظ بهيكل المجلدات كما هو
```

### الخطوة 3: تحديث الإعدادات ⚙️
```php
// في config.php
define('SUPABASE_URL', 'رابط_قاعدة_البيانات');
define('SUPABASE_KEY', 'مفتاح_API');
```

### الخطوة 4: اختبار التثبيت ✅
```
1. اذهب إلى: https://your-domain.com/deploy.php?setup=true
2. تحقق من جميع الفحوصات
3. اختبر عرض مود: https://your-domain.com/?id=1&lang=ar
```

### الخطوة 5: تحديث البوت 🤖
```bash
# في مجلد البوت
python hosting_files/update_bot.py
# أدخل رابط موقعك عندما يُطلب منك
```

## 📋 متطلبات النظام

### متطلبات الاستضافة
- ✅ PHP 7.4+ (متوفر في InfinityFree)
- ✅ دعم cURL (متوفر)
- ✅ دعم .htaccess (متوفر)
- ✅ 100MB مساحة تخزين (متوفر 5GB)

### متطلبات قاعدة البيانات
- ✅ حساب Supabase نشط
- ✅ جدول `minemods` مع البيانات
- ✅ مفاتيح API صحيحة

## 🔗 استخدام الصفحة

### رابط الوصول
```
https://your-domain.com/?id=MOD_ID&lang=ar&user_id=USER_ID&channel=CHANNEL_ID
```

### المعاملات المطلوبة
- `id`: معرف المود في قاعدة البيانات
- `lang`: اللغة (ar للعربية، en للإنجليزية)
- `user_id`: معرف المستخدم (اختياري)
- `channel`: معرف القناة (اختياري)

### مثال على الرابط
```
https://your-domain.com/?id=123&lang=ar&user_id=456&channel=789
```

## 🛠️ التخصيص

### تغيير الألوان
في ملف `style.css`، يمكنك تعديل:
```css
:root {
    --primary-color: #FFA500;
    --secondary-color: #FFD700;
    --background-color: #1a1a1a;
    --text-color: white;
}
```

### إضافة مميزات جديدة
1. أضف الكود في `script.js` للوظائف الجديدة
2. أضف التصميم في `style.css`
3. أضف API endpoints جديدة في `api.php`

## 📊 قاعدة البيانات

### جدول `minemods`
```sql
CREATE TABLE minemods (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    description_ar TEXT,
    image_urls JSONB,
    download_url TEXT,
    version TEXT,
    category TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### جدول `user_ads_settings`
```sql
CREATE TABLE user_ads_settings (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    ads_enabled BOOLEAN DEFAULT true,
    ad_display_mode TEXT DEFAULT 'on_download',
    ad_direct_link TEXT,
    close_button_delay INTEGER DEFAULT 5,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### 1. الصفحة لا تظهر
- تأكد من رفع جميع الملفات
- تحقق من إعدادات `.htaccess`
- تأكد من دعم PHP في الاستضافة

#### 2. بيانات المود لا تظهر
- تحقق من إعدادات Supabase
- تأكد من صحة معرف المود
- راجع أخطاء PHP في لوحة التحكم

#### 3. الصور لا تظهر
- تأكد من صحة روابط الصور في قاعدة البيانات
- تحقق من إعدادات CORS في Supabase

## 📱 التوافق

### المتصفحات المدعومة
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### الأجهزة المدعومة
- أجهزة الكمبيوتر
- الهواتف الذكية
- الأجهزة اللوحية

## 🔒 الأمان

### إعدادات الأمان المطبقة
- حماية من XSS
- حماية من CSRF
- تشفير الاتصالات
- تحديد صلاحيات الملفات

### نصائح أمنية
1. غيّر مفاتيح API بانتظام
2. استخدم HTTPS دائماً
3. راقب سجلات الوصول
4. حدّث الملفات بانتظام

## 📈 الأداء

### تحسينات الأداء المطبقة
- ضغط الملفات
- تخزين مؤقت للصور
- تحميل كسول للمحتوى
- تحسين استعلامات قاعدة البيانات

### نصائح لتحسين الأداء
1. استخدم CDN للصور
2. قلل حجم الصور
3. فعّل الضغط في الخادم
4. استخدم التخزين المؤقت

## 🆘 الدعم

### الحصول على المساعدة
1. راجع هذا الملف أولاً
2. تحقق من سجلات الأخطاء
3. تأكد من إعدادات قاعدة البيانات
4. اتصل بدعم الاستضافة إذا لزم الأمر

### معلومات إضافية
- الإصدار: 1.0.0
- تاريخ الإنشاء: 2024
- اللغات المدعومة: العربية، الإنجليزية
- الترخيص: مجاني للاستخدام الشخصي

---

**ملاحظة**: هذا المشروع مصمم خصيصاً للعمل مع بوت تيليجرام لنشر مودات ماين كرافت. تأكد من تحديث روابط الصفحة في البوت بعد رفعها على الاستضافة.
