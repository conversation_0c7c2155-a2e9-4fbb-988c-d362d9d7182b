#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لاتصال البوت مع قاعدة البيانات للاستضافة
Comprehensive Connection Test for Hosting
"""

import requests
import json
import sys
from datetime import datetime

# بيانات الاتصال الصحيحة
SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4"
TABLE_NAME = "mods"

# Headers للطلبات
HEADERS = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json',
    'Prefer': 'return=representation'
}

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

def print_result(test_name, success, details=""):
    """طباعة نتيجة الاختبار"""
    status = "✅ نجح" if success else "❌ فشل"
    print(f"{test_name}: {status}")
    if details:
        print(f"   التفاصيل: {details}")

def test_basic_connection():
    """اختبار الاتصال الأساسي"""
    print_header("اختبار الاتصال الأساسي")
    
    try:
        url = f"{SUPABASE_URL}/rest/v1/"
        response = requests.get(url, headers=HEADERS, timeout=10)
        
        success = response.status_code == 200
        details = f"HTTP {response.status_code}"
        
        print_result("الاتصال الأساسي", success, details)
        
        if not success:
            print(f"   Response: {response.text}")
        
        return success
        
    except Exception as e:
        print_result("الاتصال الأساسي", False, str(e))
        return False

def test_table_access():
    """اختبار الوصول لجدول المودات"""
    print_header("اختبار الوصول لجدول المودات")
    
    try:
        url = f"{SUPABASE_URL}/rest/v1/{TABLE_NAME}?limit=1"
        response = requests.get(url, headers=HEADERS, timeout=10)
        
        success = response.status_code == 200
        details = f"HTTP {response.status_code}"
        
        if success:
            data = response.json()
            details += f" - عدد السجلات: {len(data)}"
        
        print_result(f"الوصول لجدول {TABLE_NAME}", success, details)
        
        if not success:
            print(f"   Response: {response.text}")
            
            # محاولة اختبار جداول أخرى
            alternative_tables = ["minemods", "minecraft_mods", "addons"]
            print("\n🔍 اختبار جداول بديلة...")
            
            for table in alternative_tables:
                try:
                    alt_url = f"{SUPABASE_URL}/rest/v1/{table}?limit=1"
                    alt_response = requests.get(alt_url, headers=HEADERS, timeout=10)
                    
                    if alt_response.status_code == 200:
                        alt_data = alt_response.json()
                        print(f"   ✅ جدول {table} موجود - عدد السجلات: {len(alt_data)}")
                        return True
                    else:
                        print(f"   ❌ جدول {table} غير موجود - HTTP {alt_response.status_code}")
                        
                except Exception as e:
                    print(f"   ❌ خطأ في اختبار جدول {table}: {e}")
        
        return success
        
    except Exception as e:
        print_result(f"الوصول لجدول {TABLE_NAME}", False, str(e))
        return False

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print_header("اختبار عمليات قاعدة البيانات")
    
    # اختبار القراءة
    try:
        url = f"{SUPABASE_URL}/rest/v1/{TABLE_NAME}?select=id,name&limit=5"
        response = requests.get(url, headers=HEADERS, timeout=10)
        
        read_success = response.status_code == 200
        print_result("قراءة البيانات", read_success, f"HTTP {response.status_code}")
        
        if read_success:
            data = response.json()
            print(f"   📊 تم جلب {len(data)} سجل")
            
            if data:
                print("   📋 عينة من البيانات:")
                for i, item in enumerate(data[:3], 1):
                    item_id = item.get('id', 'غير محدد')
                    item_name = item.get('name', 'غير محدد')
                    print(f"      {i}. ID: {item_id}, Name: {item_name}")
        
        return read_success
        
    except Exception as e:
        print_result("قراءة البيانات", False, str(e))
        return False

def test_count_records():
    """اختبار عد السجلات"""
    print_header("اختبار عد السجلات")
    
    try:
        url = f"{SUPABASE_URL}/rest/v1/{TABLE_NAME}?select=id"
        headers_with_count = HEADERS.copy()
        headers_with_count['Prefer'] = 'count=exact'
        
        response = requests.head(url, headers=headers_with_count, timeout=10)
        
        success = response.status_code == 200
        
        if success:
            count_header = response.headers.get('Content-Range', '0')
            if '/' in count_header:
                total_count = int(count_header.split('/')[-1])
                details = f"إجمالي السجلات: {total_count}"
            else:
                details = "لم يتم العثور على عدد السجلات"
        else:
            details = f"HTTP {response.status_code}"
        
        print_result("عد السجلات", success, details)
        return success
        
    except Exception as e:
        print_result("عد السجلات", False, str(e))
        return False

def test_search_functionality():
    """اختبار وظيفة البحث"""
    print_header("اختبار وظيفة البحث")
    
    try:
        # البحث عن كلمة شائعة
        search_term = "mod"
        url = f"{SUPABASE_URL}/rest/v1/{TABLE_NAME}?or=(name.ilike.*{search_term}*,description.ilike.*{search_term}*)&limit=3"
        response = requests.get(url, headers=HEADERS, timeout=10)
        
        success = response.status_code == 200
        
        if success:
            data = response.json()
            details = f"نتائج البحث عن '{search_term}': {len(data)} نتيجة"
        else:
            details = f"HTTP {response.status_code}"
        
        print_result("البحث", success, details)
        return success
        
    except Exception as e:
        print_result("البحث", False, str(e))
        return False

def run_comprehensive_test():
    """تشغيل الاختبار الشامل"""
    print("🚀 بدء الاختبار الشامل لاتصال قاعدة البيانات")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # معلومات الاتصال
    print_header("معلومات الاتصال")
    print(f"🌐 Database URL: {SUPABASE_URL}")
    print(f"📊 Table Name: {TABLE_NAME}")
    print(f"🔑 API Key: {SUPABASE_KEY[:20]}...")
    
    # تشغيل الاختبارات
    tests = [
        ("الاتصال الأساسي", test_basic_connection),
        ("الوصول للجدول", test_table_access),
        ("عمليات قاعدة البيانات", test_database_operations),
        ("عد السجلات", test_count_records),
        ("وظيفة البحث", test_search_functionality)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # ملخص النتائج
    print_header("ملخص النتائج")
    
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    print(f"📊 الاختبارات المنجزة: {passed_tests}/{total_tests}")
    print(f"📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n📋 تفاصيل النتائج:")
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
    
    # التوصيات
    print_header("التوصيات")
    
    if all(results.values()):
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ البوت جاهز للاستضافة")
        print("🚀 يمكنك الآن رفع البوت على الاستضافة المجانية")
    else:
        print("⚠️ بعض الاختبارات فشلت")
        
        if not results.get("الاتصال الأساسي", False):
            print("❌ مشكلة في الاتصال الأساسي - تحقق من رابط Supabase")
        
        if not results.get("الوصول للجدول", False):
            print("❌ مشكلة في الوصول للجدول - تحقق من اسم الجدول والصلاحيات")
        
        print("🔧 يرجى إصلاح المشاكل قبل رفع البوت للاستضافة")
    
    return all(results.values())

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
