#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل البوت مع فحص الاتصال بـ Supabase
"""

import sys
import os
import asyncio
import logging

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_supabase_connection():
    """فحص الاتصال بـ Supabase قبل تشغيل البوت"""
    print("🔄 فحص الاتصال بـ Supabase...")

    try:
        from supabase_client import get_mods_count, get_all_mods

        # اختبار الاتصال
        count = get_mods_count()
        print(f"✅ تم الاتصال بنجاح! عدد المودات: {count}")

        if count > 0:
            # اختبار جلب المودات
            mods = get_all_mods()
            print(f"✅ تم جلب {len(mods)} مود بنجاح")
            return True
        else:
            print("⚠️ قاعدة البيانات فارغة - لا توجد مودات")
            return True  # يمكن تشغيل البوت حتى لو كانت قاعدة البيانات فارغة

    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ Supabase: {e}")
        return False

def main():
    """الدالة الرئيسية لتشغيل البوت"""
    print("=" * 60)
    print("🤖 بوت نشر مودات ماين كرافت - Supabase Edition")
    print("=" * 60)

    # فحص الاتصال بـ Supabase
    if not check_supabase_connection():
        print("\n❌ فشل في الاتصال بـ Supabase!")
        print("🔧 يرجى التحقق من:")
        print("   - إعدادات Supabase في supabase_client.py")
        print("   - الاتصال بالإنترنت")
        print("   - صحة مفاتيح API")
        sys.exit(1)

    print("\n🚀 بدء تشغيل البوت...")
    print("📋 البوت سيعمل في الخلفية...")
    print("⏹️ اضغط Ctrl+C لإيقاف البوت")
    print("=" * 60)

    try:
        # استيراد وتشغيل البوت
        from main import main as run_bot
        asyncio.run(run_bot())

    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف البوت بواسطة المستخدم")
        print("✅ البوت توقف بأمان")

    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البوت: {e}")
        logging.exception("خطأ في تشغيل البوت")
        sys.exit(1)

if __name__ == "__main__":
    main()
