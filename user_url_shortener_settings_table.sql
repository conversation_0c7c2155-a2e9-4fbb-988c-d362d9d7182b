-- جدول إعدادات اختصار الروابط للمستخدمين
-- User URL Shortener Settings Table for Supabase (PostgreSQL)

CREATE TABLE IF NOT EXISTS user_url_shortener_settings (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL UNIQUE,
    channel_id VARCHAR(255) NOT NULL,

    -- إعدادات اختصار الروابط الأساسية
    shortener_enabled BOOLEAN DEFAULT FALSE,
    api_url TEXT NOT NULL,
    api_key TEXT NOT NULL,
    service_name VARCHAR(100) DEFAULT 'custom', -- 'linkjust', 'shortlink', 'tinyurl', 'bitly', 'custom'

    -- إعدادات إضافية
    use_custom_alias BOOLEAN DEFAULT TRUE, -- استخدام اسم مستعار مخصص
    alias_prefix VARCHAR(50) DEFAULT 'mod', -- بادئة الاسم المستعار

    -- إحصائيات
    total_urls_shortened INTEGER DEFAULT 0,
    total_clicks INTEGER DEFAULT 0,
    last_shortened_at TIMESTAMP WITH TIME ZONE NULL,
    
    -- تواريخ الإنشاء والتحديث
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_url_shortener_settings_user_id ON user_url_shortener_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_url_shortener_settings_channel_id ON user_url_shortener_settings(channel_id);
CREATE INDEX IF NOT EXISTS idx_user_url_shortener_settings_enabled ON user_url_shortener_settings(shortener_enabled);
CREATE INDEX IF NOT EXISTS idx_user_url_shortener_settings_service ON user_url_shortener_settings(service_name);
CREATE INDEX IF NOT EXISTS idx_user_url_shortener_settings_created_at ON user_url_shortener_settings(created_at);

-- إضافة تعليقات للجدول (PostgreSQL syntax)
COMMENT ON TABLE user_url_shortener_settings IS 'جدول لحفظ إعدادات اختصار الروابط لكل مستخدم';
COMMENT ON COLUMN user_url_shortener_settings.user_id IS 'معرف المستخدم الفريد';
COMMENT ON COLUMN user_url_shortener_settings.channel_id IS 'معرف القناة المرتبطة';
COMMENT ON COLUMN user_url_shortener_settings.shortener_enabled IS 'تفعيل/إيقاف ميزة اختصار الروابط';
COMMENT ON COLUMN user_url_shortener_settings.api_url IS 'رابط API الخاص بموقع الاختصار';
COMMENT ON COLUMN user_url_shortener_settings.api_key IS 'مفتاح API للموقع';
COMMENT ON COLUMN user_url_shortener_settings.service_name IS 'اسم خدمة الاختصار المستخدمة';
COMMENT ON COLUMN user_url_shortener_settings.use_custom_alias IS 'استخدام اسم مستعار مخصص للروابط';
COMMENT ON COLUMN user_url_shortener_settings.alias_prefix IS 'بادئة الاسم المستعار';
COMMENT ON COLUMN user_url_shortener_settings.total_urls_shortened IS 'إجمالي عدد الروابط المختصرة';
COMMENT ON COLUMN user_url_shortener_settings.total_clicks IS 'إجمالي عدد النقرات على الروابط المختصرة';
COMMENT ON COLUMN user_url_shortener_settings.last_shortened_at IS 'تاريخ آخر اختصار رابط';

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_user_url_shortener_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث updated_at
DROP TRIGGER IF EXISTS trigger_update_user_url_shortener_settings_updated_at ON user_url_shortener_settings;
CREATE TRIGGER trigger_update_user_url_shortener_settings_updated_at
    BEFORE UPDATE ON user_url_shortener_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_user_url_shortener_settings_updated_at();

-- إضافة قيود إضافية للتحقق من صحة البيانات
-- إضافة قيود إضافية للتحقق من صحة البيانات
ALTER TABLE user_url_shortener_settings 
DROP CONSTRAINT IF EXISTS check_api_url_format;
ALTER TABLE user_url_shortener_settings 
ADD CONSTRAINT check_api_url_format 
CHECK (api_url ~ '^https?://.*');

ALTER TABLE user_url_shortener_settings 
DROP CONSTRAINT IF EXISTS check_api_key_length;
ALTER TABLE user_url_shortener_settings 
ADD CONSTRAINT check_api_key_length 
CHECK (LENGTH(api_key) >= 10);

ALTER TABLE user_url_shortener_settings 
DROP CONSTRAINT IF EXISTS check_service_name_valid;
ALTER TABLE user_url_shortener_settings 
ADD CONSTRAINT check_service_name_valid 
CHECK (service_name IN ('linkjust', 'shortlink', 'tinyurl', 'bitly', 'custom'));

-- إضافة بيانات تجريبية (اختياري - يمكن حذفها في الإنتاج)
-- INSERT INTO user_url_shortener_settings (user_id, channel_id, shortener_enabled, api_url, api_key, service_name) 
-- VALUES ('123456789', '@testchannel', true, 'https://linkjust.com/api', 'test_api_key_here', 'linkjust');
