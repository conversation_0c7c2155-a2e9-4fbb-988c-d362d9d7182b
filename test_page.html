
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام المحسن</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background: #f1f8e9; }
        .error { border-color: #f44336; background: #ffebee; }
        .warning { border-color: #ff9800; background: #fff3e0; }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1976D2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار النظام المحسن</h1>
        
        <div class="test-item">
            <h3>🌐 اختبار الخوادم</h3>
            <button onclick="testServers()">اختبار الخوادم</button>
            <div id="server-results"></div>
        </div>
        
        <div class="test-item">
            <h3>📱 اختبار Telegram Web App</h3>
            <button onclick="testWebApp()">اختبار صفحة المود</button>
            <div id="webapp-results"></div>
        </div>
        
        <div class="test-item">
            <h3>🔌 اختبار API</h3>
            <button onclick="testAPI()">اختبار API</button>
            <div id="api-results"></div>
        </div>
        
        <div class="test-item">
            <h3>📊 معلومات النظام</h3>
            <p><strong>Flask Server:</strong> http://localhost:5000</p>
            <p><strong>Telegram Web App:</strong> http://localhost:5001</p>
            <p><strong>API Endpoint:</strong> http://localhost:5001/api/mod/1</p>
        </div>
    </div>

    <script>
        async function testServers() {
            const results = document.getElementById('server-results');
            results.innerHTML = '<p>جاري الاختبار...</p>';
            
            try {
                // اختبار Flask
                const flaskResponse = await fetch('http://localhost:5000/');
                const flaskStatus = flaskResponse.ok ? '✅ يعمل' : '❌ خطأ';
                
                // اختبار Telegram Web App
                const webappResponse = await fetch('http://localhost:5001/telegram-mod-details?id=1&lang=ar');
                const webappStatus = webappResponse.ok ? '✅ يعمل' : '❌ خطأ';
                
                results.innerHTML = `
                    <p>Flask Server: ${flaskStatus}</p>
                    <p>Telegram Web App: ${webappStatus}</p>
                `;
                results.className = 'success';
            } catch (error) {
                results.innerHTML = `<p>❌ خطأ في الاختبار: ${error.message}</p>`;
                results.className = 'error';
            }
        }
        
        async function testWebApp() {
            const results = document.getElementById('webapp-results');
            results.innerHTML = '<p>جاري فتح صفحة المود...</p>';
            
            // فتح صفحة المود في نافذة جديدة
            window.open('http://localhost:5001/telegram-mod-details?id=1&lang=ar', '_blank');
            
            results.innerHTML = '<p>✅ تم فتح صفحة المود في نافذة جديدة</p>';
            results.className = 'success';
        }
        
        async function testAPI() {
            const results = document.getElementById('api-results');
            results.innerHTML = '<p>جاري اختبار API...</p>';
            
            try {
                const response = await fetch('http://localhost:5001/api/mod/1?lang=ar');
                const data = await response.json();
                
                if (data.success) {
                    results.innerHTML = `
                        <p>✅ API يعمل بشكل صحيح</p>
                        <p>عنوان المود: ${data.mod.title || 'غير محدد'}</p>
                    `;
                    results.className = 'success';
                } else {
                    results.innerHTML = `<p>⚠️ API يعمل لكن لا توجد بيانات: ${data.error}</p>`;
                    results.className = 'warning';
                }
            } catch (error) {
                results.innerHTML = `<p>❌ خطأ في API: ${error.message}</p>`;
                results.className = 'error';
            }
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            setTimeout(testServers, 1000);
        };
    </script>
</body>
</html>
