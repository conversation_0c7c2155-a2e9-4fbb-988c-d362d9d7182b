#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل البوت مع التهيئة التلقائية الكاملة
مناسب للاستضافة المحلية والسحابية
"""

import os
import sys
import time
import subprocess
import threading
import logging
from datetime import datetime

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_start.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def detect_environment():
    """كشف بيئة التشغيل (محلي أم استضافة)"""
    # فحص متغيرات البيئة الشائعة للاستضافة
    hosting_indicators = [
        'HEROKU', 'RAILWAY', 'VERCEL', 'NETLIFY', 'RENDER',
        'DOCKER', 'KUBERNETES', 'AWS', 'GOOGLE_CLOUD',
        'AZURE', 'DIGITALOCEAN', 'PELLA'
    ]
    
    for indicator in hosting_indicators:
        if any(indicator in key for key in os.environ.keys()):
            logger.info(f"🌐 تم كشف بيئة استضافة: {indicator}")
            return "hosting"
    
    # فحص إذا كان ngrok متاح (بيئة محلية)
    try:
        result = subprocess.run(['ngrok', 'version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            logger.info("🏠 تم كشف بيئة محلية مع ngrok")
            return "local_with_ngrok"
    except:
        pass
    
    logger.info("🏠 تم كشف بيئة محلية بدون ngrok")
    return "local_without_ngrok"

def setup_environment_variables():
    """إعداد متغيرات البيئة المطلوبة"""
    logger.info("🔧 إعداد متغيرات البيئة...")
    
    # قراءة ملف .env إذا كان موجود
    env_file = '.env'
    if os.path.exists(env_file):
        logger.info("📄 قراءة ملف .env...")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
    
    # تعيين قيم افتراضية
    defaults = {
        'FLASK_PORT': '5000',
        'TELEGRAM_WEB_APP_PORT': '5001',
        'WEB_SERVER_URL': 'http://localhost:5001'
    }
    
    for key, default_value in defaults.items():
        if key not in os.environ:
            os.environ[key] = default_value
            logger.info(f"✅ تم تعيين {key} = {default_value}")

def check_dependencies():
    """فحص التبعيات المطلوبة"""
    logger.info("📦 فحص التبعيات...")
    
    required_modules = [
        'telegram', 'flask', 'requests', 'python-dotenv'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module.replace('-', '_'))
            logger.info(f"✅ {module}: متوفر")
        except ImportError:
            missing_modules.append(module)
            logger.warning(f"❌ {module}: مفقود")
    
    if missing_modules:
        logger.error(f"❌ تبعيات مفقودة: {', '.join(missing_modules)}")
        logger.info("💡 قم بتثبيتها باستخدام: pip install " + " ".join(missing_modules))
        return False
    
    logger.info("✅ جميع التبعيات متوفرة")
    return True

def start_ngrok_if_needed(environment):
    """تشغيل ngrok إذا كان مطلوب"""
    if environment != "local_with_ngrok":
        logger.info("⏭️ تخطي ngrok (غير مطلوب في هذه البيئة)")
        return True
    
    logger.info("🚀 تشغيل ngrok...")
    
    try:
        # فحص إذا كان ngrok يعمل بالفعل
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex(('127.0.0.1', 4040))
        sock.close()
        
        if result == 0:
            logger.info("✅ ngrok يعمل بالفعل")
            return True
        
        # تشغيل ngrok
        port = os.environ.get('TELEGRAM_WEB_APP_PORT', '5001')
        
        if os.path.exists("start_ngrok.bat"):
            subprocess.Popen(["start_ngrok.bat"], shell=True,
                           stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        else:
            subprocess.Popen(["ngrok", "http", port],
                           stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        # انتظار تشغيل ngrok
        for i in range(30):
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('127.0.0.1', 4040))
            sock.close()
            
            if result == 0:
                logger.info("✅ تم تشغيل ngrok بنجاح")
                return True
            
            time.sleep(1)
        
        logger.warning("⚠️ ngrok لم يبدأ في الوقت المحدد")
        return False
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل ngrok: {e}")
        return False

def update_ngrok_url_if_needed(environment):
    """تحديث رابط ngrok إذا كان مطلوب"""
    if environment != "local_with_ngrok":
        return True
    
    logger.info("🔄 تحديث رابط ngrok...")
    
    try:
        if os.path.exists("update_ngrok_url.py"):
            result = subprocess.run([sys.executable, "update_ngrok_url.py"],
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                logger.info("✅ تم تحديث رابط ngrok")
                return True
            else:
                logger.warning(f"⚠️ فشل تحديث رابط ngrok: {result.stderr}")
        else:
            logger.warning("⚠️ ملف update_ngrok_url.py غير موجود")
        
        return False
        
    except Exception as e:
        logger.error(f"❌ خطأ في تحديث رابط ngrok: {e}")
        return False

def start_bot():
    """تشغيل البوت الرئيسي"""
    logger.info("🤖 تشغيل البوت الرئيسي...")
    
    try:
        # تشغيل البوت
        subprocess.run([sys.executable, "main.py"])
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        raise

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء التشغيل التلقائي للبوت...")
    print("=" * 60)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 1. كشف البيئة
        environment = detect_environment()
        
        # 2. إعداد متغيرات البيئة
        setup_environment_variables()
        
        # 3. فحص التبعيات
        if not check_dependencies():
            logger.error("❌ فشل في فحص التبعيات")
            return False
        
        # 4. تشغيل ngrok إذا كان مطلوب
        ngrok_success = start_ngrok_if_needed(environment)
        
        # 5. تحديث رابط ngrok إذا كان مطلوب
        if ngrok_success:
            update_ngrok_url_if_needed(environment)
        
        # 6. تشغيل البوت
        logger.info("🎉 جميع الإعدادات جاهزة!")
        logger.info("=" * 60)
        
        start_bot()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ عام في التشغيل: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            logger.info("✅ تم إنهاء البوت بنجاح")
        else:
            logger.error("❌ فشل في تشغيل البوت")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف التشغيل بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ غير متوقع: {e}")
        sys.exit(1)
