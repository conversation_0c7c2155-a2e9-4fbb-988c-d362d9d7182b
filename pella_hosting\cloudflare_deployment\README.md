# Modetaris Bot - Cloudflare Pages Deployment 🚀

بوت Telegram متخصص في إدارة ونشر مودات Minecraft تلقائياً، مُحسن للعمل على Cloudflare Pages.

## المميزات الرئيسية ✨

- 🤖 **بوت Telegram ذكي** لإدارة المودات
- 🌐 **صفحات تحميل سريعة** مع Cloudflare CDN
- 📊 **قاعدة بيانات Supabase** للتخزين
- 🔒 **SSL مجاني** وحماية DDoS
- 📱 **واجهة متجاوبة** تعمل على جميع الأجهزة
- 🚀 **أداء عالي** مع التخزين المؤقت

## البنية التقنية 🏗️

```
├── functions/
│   └── api/
│       └── [[path]].js     # Cloudflare Pages Functions
├── dist/                   # ملفات الموقع المبنية
├── package.json           # تبعيات Node.js
├── wrangler.toml          # إعدادات Cloudflare
├── build.js               # سكريبت البناء
└── DEPLOYMENT_GUIDE.md    # دليل النشر التفصيلي
```

## المتطلبات 📋

- حساب [Cloudflare](https://cloudflare.com) مجاني
- حساب [GitHub](https://github.com) لاستضافة الكود
- بوت Telegram (احصل على Token من [@BotFather](https://t.me/BotFather))
- قاعدة بيانات [Supabase](https://supabase.com) مجانية

## التثبيت السريع ⚡

### 1. استنساخ المشروع
```bash
git clone https://github.com/YOUR_USERNAME/modetaris-bot.git
cd modetaris-bot
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. بناء المشروع
```bash
npm run build
```

### 4. النشر على Cloudflare Pages
اتبع الدليل التفصيلي في [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md)

## إعداد متغيرات البيئة 🔧

في لوحة تحكم Cloudflare Pages، أضف:

```env
BOT_TOKEN=your_telegram_bot_token
ADMIN_CHAT_ID=your_admin_telegram_id
SUPABASE_URL=your_supabase_project_url
SUPABASE_KEY=your_supabase_anon_key
ENVIRONMENT=production
HOSTING_PROVIDER=cloudflare
```

## API Endpoints 🔗

| المسار | الوصف | المثال |
|--------|--------|---------|
| `/api/health` | فحص حالة الخدمة | `GET /api/health` |
| `/api/webhook` | webhook للبوت | `POST /api/webhook` |
| `/api/mod/{id}` | صفحة عرض المود | `GET /api/mod/123` |
| `/api/download/{id}` | تحميل المود | `GET /api/download/123` |

## الاختبار 🧪

### اختبار محلي:
```bash
npm run dev
```

### اختبار الإنتاج:
```bash
# اختبار حالة الخدمة
curl https://your-site.pages.dev/api/health

# اختبار صفحة مود
curl https://your-site.pages.dev/api/mod/1
```

## الحدود والقيود ⚠️

### Cloudflare Pages (مجاني):
- ✅ 500 builds شهرياً
- ✅ 100,000 requests يومياً  
- ✅ 100MB حجم الموقع
- ✅ SSL مجاني
- ✅ CDN عالمي

### مناسب لـ:
- ✅ صفحات تحميل المودات
- ✅ عرض معلومات المودات
- ✅ واجهة البوت الأساسية
- ✅ API بسيط

### غير مناسب لـ:
- ❌ رفع ملفات كبيرة (>25MB)
- ❌ معالجة ملفات معقدة
- ❌ قواعد بيانات محلية
- ❌ تطبيقات تتطلب حالة مستمرة

## التطوير والمساهمة 🛠️

### هيكل المشروع:
```javascript
// functions/api/[[path]].js
export async function onRequest(context) {
    const { request, env } = context;
    // معالجة الطلبات هنا
}
```

### إضافة endpoint جديد:
```javascript
// في functions/api/[[path]].js
if (path.startsWith('/api/your-endpoint')) {
    return await handleYourEndpoint(request, env);
}
```

## الدعم والمساعدة 💬

- 📖 [دليل النشر التفصيلي](./DEPLOYMENT_GUIDE.md)
- 🌐 [وثائق Cloudflare Pages](https://developers.cloudflare.com/pages/)
- 💬 [مجتمع Cloudflare](https://community.cloudflare.com/)
- 🐛 [الإبلاغ عن مشاكل](https://github.com/YOUR_USERNAME/modetaris-bot/issues)

## الترخيص 📄

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**صُنع بـ ❤️ للمجتمع العربي لمودات Minecraft**
