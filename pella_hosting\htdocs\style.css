/*
 * ملف التصميم لصفحة عرض المودات - محسن للهواتف
 * CSS Styles for Mod Details Page - Mobile Optimized
 */

body {
    font-family: 'Press Start 2P', cursive;
    background-color: #1a1a1a;
    color: white;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* تصميم الهيدر المحسن */
header {
    background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    position: relative;
}

header h1 {
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 1px;
}

/* تصميم صورة القناة في الهيدر */
header img {
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

header img:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.pixel-border {
    box-shadow: inset 0 0 0 1px #AAAAAA;
    border: 1px solid #AAAAAA;
}

.mod-header {
    background-color: #2D2D2D;
    color: white;
    padding: 15px;
    text-align: center;
}

.mod-container {
    background-color: #2D2D2D;
}

.nav-button {
    background-color: #FFA500;
    color: white;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    flex-shrink: 0;
}

.thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    cursor: pointer;
    border: 1px solid #666;
    border-radius: 4px;
    transition: all 0.3s ease;
    opacity: 0.7;
    display: block;
    margin: 0 4px;
}

.thumbnail:hover {
    opacity: 1;
    border-color: #FFA500;
    transform: scale(1.05);
}

.thumbnail.active {
    border-color: #FFA500;
    opacity: 1;
    box-shadow: 0 0 8px rgba(255, 165, 0, 0.6);
}

.pixel-button {
    image-rendering: pixelated;
    background-color: #FFA500;
    color: white;
    border: 2px solid #FFD700;
    border-radius: 8px;
    padding: 12px 24px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 200px;
    min-height: 50px;
    font-weight: bold;
}

.pixel-button.downloading {
    background-color: #4CAF50;
    border-color: #45a049;
    animation: none;
    cursor: not-allowed;
}

.pixel-button.downloaded {
    background-color: #2196F3;
    border-color: #1976D2;
    animation: none;
}

.progress-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background-color: #4CAF50;
    width: 0%;
    transition: width 0.3s ease;
    z-index: 1;
}

.download-icon {
    display: inline-block;
    margin-right: 8px;
    font-size: 18px;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.download-success-animation {
    animation: downloadSuccess 0.6s ease-out;
}

@keyframes downloadSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); box-shadow: 0 0 20px #4CAF50; }
    100% { transform: scale(1); }
}

.pixel-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    background-color: #FF8C00;
}

.pixel-button.downloading:hover {
    transform: none;
}

.pixel-button.downloaded:hover {
    box-shadow: 0 6px 12px rgba(33, 150, 243, 0.4);
}

.notification-enter {
    animation: slideInRight 0.3s ease-out;
}

.notification-exit {
    animation: slideOutRight 0.3s ease-in;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

/* تحسين الإشعارات للهواتف */
@media (max-width: 768px) {
    .notification-enter,
    .notification-exit {
        position: fixed !important;
        top: 20px !important;
        left: 10px !important;
        right: 10px !important;
        max-width: none !important;
        width: auto !important;
        z-index: 9999 !important;
        font-size: 12px !important;
        padding: 12px !important;
    }

    .notification-enter {
        animation: slideInTop 0.3s ease-out;
    }

    .notification-exit {
        animation: slideOutTop 0.3s ease-in;
    }

    @keyframes slideInTop {
        from { transform: translateY(-100%); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    @keyframes slideOutTop {
        from { transform: translateY(0); opacity: 1; }
        to { transform: translateY(-100%); opacity: 0; }
    }
}

@keyframes pulse-animation {
    0% { transform: scale(1); box-shadow: inset -4px -4px 0 0 #d27e00, inset 4px 4px 0 0 #ffcb6b, 0 0 10px #FFD700, 0 0 20px #FFD700; }
    50% { transform: scale(1.05); box-shadow: inset -4px -4px 0 0 #d27e00, inset 4px 4px 0 0 #ffcb6b, 0 0 15px #FFD700, 0 0 30px #FFD700; }
    100% { transform: scale(1); box-shadow: inset -4px -4px 0 0 #d27e00, inset 4px 4px 0 0 #ffcb6b, 0 0 10px #FFD700, 0 0 20px #FFD700; }
}

.info-label {
    color: #FFA500;
    font-size: 14px;
}

.mod-info {
    font-size: 18px;
    color: white;
    letter-spacing: -1px;
}

.image-glow-effect {
    position: relative;
    overflow: hidden;
    background-color: #333;
    border-radius: 8px;
    border: 1px solid #555;
}

.image-glow-effect img {
    border-radius: 8px;
}

.image-glow-effect .particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: #FFD700;
    box-shadow: 0 0 2px #FFD700;
    opacity: 0;
    animation: emanate-squares-from-bottom 3s infinite ease-out;
}

@keyframes emanate-squares-from-bottom {
    0% { opacity: 0.9; transform: translate(0, 0) scale(0.5) rotate(0deg); }
    20% { opacity: 1; transform: translate(var(--tx, 0px), var(--ty, 0px)) scale(1) rotate(45deg); }
    80% { opacity: 0.5; transform: translate(calc(var(--tx, 0px) * 2.5), calc(var(--ty, 0px) * 2.5)) scale(0.8) rotate(90deg); }
    100% { opacity: 0; transform: translate(calc(var(--tx, 0px) * 3), calc(var(--ty, 0px) * 3)) scale(0) rotate(135deg); }
}

.new-feature {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: 0 0 15px rgba(255, 165, 0, 0.7); }
    100% { transform: scale(1); }
}

/* تحسينات للهواتف المحمولة */
@media (max-width: 768px) {
    body {
        font-size: 12px;
    }

    .container {
        padding: 8px;
        margin: 0;
    }

    /* تحسينات الهيدر للهواتف */
    header {
        padding: 12px 16px;
    }

    header h1 {
        font-size: 18px;
        line-height: 1.3;
    }

    header img {
        width: 36px;
        height: 36px;
    }

    .mod-header {
        padding: 12px;
        margin-bottom: 16px;
    }

    .mod-header h1 {
        font-size: 16px;
        line-height: 1.4;
        word-break: break-word;
    }

    .pixel-button {
        min-width: 90%;
        max-width: 320px;
        font-size: 14px;
        padding: 14px 20px;
        margin: 0 auto;
        display: block;
    }

    .nav-button {
        width: 40px;
        height: 40px;
        font-size: 18px;
        border-radius: 4px;
    }

    .thumbnail {
        width: 70px;
        height: 52px;
        margin: 0 2px;
    }

    #thumbnail-container {
        padding: 0 8px;
        gap: 4px;
    }

    .mod-info {
        font-size: 12px;
        line-height: 1.5;
    }

    .info-label {
        font-size: 10px;
        margin-bottom: 8px;
    }

    .grid {
        gap: 12px;
    }

    .mod-container {
        padding: 12px;
        margin-bottom: 16px;
    }

    /* تحسين عرض الصورة الرئيسية */
    .image-glow-effect {
        margin-bottom: 16px;
    }

    /* تحسين شريط التنقل بين الصور */
    .flex.items-center.justify-center {
        padding: 0 8px;
        margin-bottom: 16px;
    }

    /* تحسين زر التحميل الثابت */
    .fixed.bottom-0 {
        padding: 12px;
        background: linear-gradient(to top, rgba(26, 26, 26, 0.95) 0%, rgba(26, 26, 26, 0.8) 70%, transparent 100%);
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    /* تحسينات الهيدر للشاشات الصغيرة */
    header {
        padding: 10px 12px;
    }

    header h1 {
        font-size: 16px;
        line-height: 1.2;
    }

    header img {
        width: 32px;
        height: 32px;
    }

    .mod-header h1 {
        font-size: 14px;
        padding: 8px;
    }

    .pixel-button {
        font-size: 12px;
        padding: 12px 16px;
        min-width: 85%;
    }

    .thumbnail {
        width: 60px;
        height: 45px;
    }

    .nav-button {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .mod-info {
        font-size: 11px;
    }

    .info-label {
        font-size: 9px;
    }
}

/* تحسينات للشاشات المتوسطة */
@media (min-width: 769px) and (max-width: 1024px) {
    .container {
        max-width: 600px;
        margin: 0 auto;
    }

    .pixel-button {
        min-width: 250px;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
    .container {
        max-width: 800px;
        margin: 0 auto;
    }
}

/* تحسينات للمس والتفاعل */
@media (hover: none) and (pointer: coarse) {
    .thumbnail:hover {
        transform: none;
    }

    .thumbnail:active {
        transform: scale(0.95);
        opacity: 0.8;
    }

    .pixel-button:hover {
        transform: none;
    }

    .pixel-button:active {
        transform: scale(0.98);
    }
}

/* تحسينات للوضع الليلي */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #0a0a0a;
    }
    
    .mod-container {
        background-color: #1a1a1a;
    }
    
    .mod-header {
        background-color: #1a1a1a;
    }
}

/* تحسينات للطباعة */
@media print {
    .pixel-button,
    .nav-button,
    #ad-overlay {
        display: none !important;
    }
    
    body {
        background-color: white;
        color: black;
    }
    
    .mod-container,
    .mod-header {
        background-color: white;
        border: 1px solid black;
    }
}
