#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الاتصال بـ Supabase وجلب المودات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from supabase_client import get_all_mods, get_mod_by_id, get_mods_count

def test_supabase_connection():
    """اختبار الاتصال بـ Supabase"""
    print("🔄 اختبار الاتصال بـ Supabase...")
    
    try:
        # اختبار الحصول على عدد المودات
        count = get_mods_count()
        print(f"✅ تم الاتصال بنجاح! عدد المودات في قاعدة البيانات: {count}")
        
        if count > 0:
            # اختبار جلب جميع المودات
            print("\n🔄 جاري جلب جميع المودات...")
            mods = get_all_mods()
            print(f"✅ تم جلب {len(mods)} مود بنجاح")
            
            if mods:
                # عرض أول مود كمثال
                first_mod = mods[0]
                print(f"\n📋 مثال على أول مود:")
                print(f"   - المعرف: {first_mod.get('id')}")
                print(f"   - العنوان: {first_mod.get('title')}")
                print(f"   - الوصف (عربي): {first_mod.get('description', {}).get('ar', 'غير متوفر')[:100]}...")
                print(f"   - رابط التحميل: {first_mod.get('download_url', 'غير متوفر')}")
                print(f"   - الإصدار: {first_mod.get('version', 'غير متوفر')}")
                
                # اختبار جلب مود واحد بالمعرف
                mod_id = first_mod.get('id')
                print(f"\n🔄 اختبار جلب المود بالمعرف {mod_id}...")
                single_mod = get_mod_by_id(mod_id)
                if single_mod:
                    print(f"✅ تم جلب المود {mod_id} بنجاح")
                else:
                    print(f"❌ فشل في جلب المود {mod_id}")
        else:
            print("⚠️ قاعدة البيانات فارغة - لا توجد مودات")
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ Supabase: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 اختبار اتصال Supabase")
    print("=" * 50)
    
    success = test_supabase_connection()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ جميع الاختبارات نجحت!")
        print("🚀 البوت جاهز للعمل مع Supabase")
    else:
        print("❌ فشلت بعض الاختبارات")
        print("🔧 يرجى التحقق من إعدادات Supabase")
    print("=" * 50)
