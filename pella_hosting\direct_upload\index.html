<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modetaris <PERSON> مودات ماينكرافت</title>
    <meta name="description" content="بوت Telegram متخصص في إدارة ونشر مودات Minecraft تلقائياً">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            text-align: center;
            max-width: 600px;
            width: 100%;
            animation: fadeInUp 0.8s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .logo {
            font-size: 4em;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        h1 {
            color: #333;
            margin-bottom: 15px;
            font-size: 2.5em;
            font-weight: 700;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
        }
        
        .status {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            display: inline-block;
            margin-bottom: 30px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
            text-align: right;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border-right: 4px solid #667eea;
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .feature h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #666;
            line-height: 1.5;
        }
        
        .telegram-link {
            background: linear-gradient(45deg, #0088cc, #006699);
            color: white;
            padding: 18px 35px;
            border-radius: 30px;
            text-decoration: none;
            display: inline-block;
            font-size: 1.1em;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0, 136, 204, 0.3);
            margin: 20px 10px;
        }
        
        .telegram-link:hover {
            background: linear-gradient(45deg, #006699, #004466);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 136, 204, 0.4);
        }
        
        .api-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
            text-align: right;
        }
        
        .api-info h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .api-endpoint {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #333;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .telegram-link {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🎮</div>
        <h1>Modetaris Bot</h1>
        <div class="subtitle">بوتك المتخصص في مودات ماينكرافت</div>
        <div class="status">✅ البوت يعمل بنجاح على Cloudflare Pages</div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🚀</div>
                <h3>نشر تلقائي</h3>
                <p>نشر المودات الجديدة تلقائياً إلى قناة التليجرام</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🎨</div>
                <h3>تخصيص كامل</h3>
                <p>تخصيص صفحات العرض والتحميل حسب ذوقك</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <h3>إحصائيات متقدمة</h3>
                <p>تتبع التحميلات والمشاهدات بتفصيل</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🌐</div>
                <h3>سرعة عالية</h3>
                <p>استضافة على Cloudflare للحصول على أفضل أداء</p>
            </div>
        </div>
        
        <div class="api-info">
            <h3>🔗 API Endpoints المتاحة:</h3>
            <div class="api-endpoint">GET /api/health - فحص حالة الخدمة</div>
            <div class="api-endpoint">POST /api/webhook - webhook للبوت</div>
            <div class="api-endpoint">GET /api/mod/{id} - صفحة عرض المود</div>
            <div class="api-endpoint">GET /api/download/{id} - تحميل المود</div>
        </div>
        
        <a href="https://t.me/YOUR_BOT_USERNAME" class="telegram-link">
            🤖 ابدأ استخدام البوت
        </a>
        
        <a href="/api/health" class="telegram-link" style="background: linear-gradient(45deg, #4CAF50, #45a049);">
            🔍 اختبار API
        </a>
        
        <div class="footer">
            <p>🚀 مُستضاف على Cloudflare Pages | 🔒 SSL مجاني | 🌍 CDN عالمي</p>
            <p>صُنع بـ ❤️ للمجتمع العربي لمودات Minecraft</p>
        </div>
    </div>
    
    <script>
        // اختبار API تلقائياً
        fetch('/api/health')
            .then(response => response.json())
            .then(data => {
                console.log('API Status:', data);
                if (data.status === 'healthy') {
                    document.querySelector('.status').innerHTML = '✅ البوت يعمل بنجاح - API متصل';
                }
            })
            .catch(error => {
                console.log('API not ready yet:', error);
                document.querySelector('.status').innerHTML = '⚠️ البوت جاهز - في انتظار إعداد API';
            });
    </script>
</body>
</html>
