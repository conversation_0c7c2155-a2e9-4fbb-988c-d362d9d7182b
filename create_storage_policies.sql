-- إنشاء سياسات أمان لـ Supabase Storage
-- يجب تشغيل هذا الكود في SQL Editor في لوحة تحكم Supabase

-- 1. إنشاء حاوية للصور إذا لم تكن موجودة
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'image3',
    'image3', 
    true,
    52428800, -- 50 MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- 2. تفعيل RLS (Row Level Security) للحاوية
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- 3. إنشاء سياسة للسماح بالقراءة العامة للصور
CREATE POLICY "Public read access for image3" ON storage.objects
FOR SELECT USING (bucket_id = 'image3');

-- 4. إنشاء سياسة للسماح برفع الصور للجميع
CREATE POLICY "Public upload access for image3" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'image3');

-- 5. إنشاء سياسة للسماح بتحديث الصور للجميع
CREATE POLICY "Public update access for image3" ON storage.objects
FOR UPDATE USING (bucket_id = 'image3');

-- 6. إنشاء سياسة للسماح بحذف الصور للجميع
CREATE POLICY "Public delete access for image3" ON storage.objects
FOR DELETE USING (bucket_id = 'image3');

-- 7. إنشاء حاوية بديلة (avatars) إذا كانت image3 لا تعمل
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'avatars',
    'avatars', 
    true,
    52428800, -- 50 MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- 8. سياسات للحاوية البديلة avatars
CREATE POLICY "Public read access for avatars" ON storage.objects
FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Public upload access for avatars" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'avatars');

CREATE POLICY "Public update access for avatars" ON storage.objects
FOR UPDATE USING (bucket_id = 'avatars');

CREATE POLICY "Public delete access for avatars" ON storage.objects
FOR DELETE USING (bucket_id = 'avatars');

-- 9. إنشاء حاوية أخرى للصور العامة
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'public-images',
    'public-images', 
    true,
    52428800, -- 50 MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- 10. سياسات للحاوية العامة
CREATE POLICY "Public read access for public-images" ON storage.objects
FOR SELECT USING (bucket_id = 'public-images');

CREATE POLICY "Public upload access for public-images" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'public-images');

CREATE POLICY "Public update access for public-images" ON storage.objects
FOR UPDATE USING (bucket_id = 'public-images');

CREATE POLICY "Public delete access for public-images" ON storage.objects
FOR DELETE USING (bucket_id = 'public-images');

-- 11. التحقق من الحاويات المنشأة
SELECT * FROM storage.buckets WHERE id IN ('image3', 'avatars', 'public-images');

-- 12. التحقق من السياسات المنشأة
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'objects' AND schemaname = 'storage';
