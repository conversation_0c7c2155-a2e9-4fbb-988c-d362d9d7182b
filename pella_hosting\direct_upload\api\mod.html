<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحميل المود - Modetaris</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            max-width: 800px;
            width: 100%;
            animation: slideIn 0.6s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .header {
            background: linear-gradient(45deg, #333, #555);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .header .version {
            opacity: 0.8;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .mod-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            border-right: 5px solid #667eea;
        }
        
        .mod-image {
            width: 100%;
            max-width: 400px;
            height: 200px;
            object-fit: cover;
            border-radius: 15px;
            margin: 20px auto;
            display: block;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .download-section {
            text-align: center;
            margin: 40px 0;
        }
        
        .download-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 20px 40px;
            border: none;
            border-radius: 30px;
            font-size: 1.3em;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
            margin: 10px;
        }
        
        .download-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(76, 175, 80, 0.4);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .info-item {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        
        .info-item .icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .info-item .label {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .info-item .value {
            color: #666;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            color: #d32f2f;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .download-btn {
                width: 100%;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 id="mod-title">جاري التحميل...</h1>
            <div class="version" id="mod-version">Minecraft</div>
        </div>
        
        <div class="content">
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>جاري تحميل بيانات المود...</p>
            </div>
            
            <div id="mod-content" style="display: none;">
                <img id="mod-image" class="mod-image" src="" alt="صورة المود" style="display: none;">
                
                <div class="mod-info">
                    <h3>📝 وصف المود:</h3>
                    <p id="mod-description">وصف المود سيظهر هنا...</p>
                </div>
                
                <div class="info-grid">
                    <div class="info-item">
                        <div class="icon">🎮</div>
                        <div class="label">إصدار Minecraft</div>
                        <div class="value" id="info-version">-</div>
                    </div>
                    <div class="info-item">
                        <div class="icon">📁</div>
                        <div class="label">نوع المحتوى</div>
                        <div class="value" id="info-category">مود</div>
                    </div>
                    <div class="info-item">
                        <div class="icon">📊</div>
                        <div class="label">حالة المود</div>
                        <div class="value">متاح للتحميل</div>
                    </div>
                    <div class="info-item">
                        <div class="icon">🌐</div>
                        <div class="label">المصدر</div>
                        <div class="value">Modetaris Bot</div>
                    </div>
                </div>
                
                <div class="download-section">
                    <a href="#" id="download-link" class="download-btn">
                        📥 تحميل المود
                    </a>
                    <br>
                    <a href="/" class="download-btn" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                        🏠 العودة للرئيسية
                    </a>
                </div>
            </div>
            
            <div id="error-content" class="error" style="display: none;">
                <h3>❌ خطأ في تحميل المود</h3>
                <p>عذراً، لم نتمكن من العثور على هذا المود أو حدث خطأ في التحميل.</p>
                <a href="/" class="download-btn" style="background: linear-gradient(45deg, #667eea, #764ba2); margin-top: 20px;">
                    🏠 العودة للرئيسية
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // استخراج معرف المود من الرابط
        function getModId() {
            const path = window.location.pathname;
            const parts = path.split('/');
            return parts[parts.length - 1] || '1'; // افتراضي
        }
        
        // بيانات مود تجريبية (في التطبيق الحقيقي ستأتي من قاعدة البيانات)
        const sampleMods = {
            '1': {
                id: 1,
                name: 'Lucky Block Mod',
                description: 'مود الكتل المحظوظة يضيف كتل جديدة مليئة بالمفاجآت والكنوز. كل كتلة تحتوي على مكافآت أو تحديات مختلفة!',
                mc_version: '1.20.1',
                category: 'مودات الكتل',
                image_url: 'https://via.placeholder.com/400x200/4CAF50/white?text=Lucky+Block+Mod',
                download_link: 'https://example.com/download/lucky-block.mcpack'
            },
            '2': {
                id: 2,
                name: 'Dragon Mounts Mod',
                description: 'مود ركوب التنانين يتيح لك ترويض وركوب التنانين في عالم ماينكرافت. استكشف السماء مع رفيقك التنين!',
                mc_version: '1.19.4',
                category: 'مودات الحيوانات',
                image_url: 'https://via.placeholder.com/400x200/FF5722/white?text=Dragon+Mounts',
                download_link: 'https://example.com/download/dragon-mounts.mcpack'
            }
        };
        
        // تحميل بيانات المود
        function loadMod() {
            const modId = getModId();
            
            // محاكاة تأخير التحميل
            setTimeout(() => {
                const mod = sampleMods[modId];
                
                if (mod) {
                    displayMod(mod);
                } else {
                    showError();
                }
            }, 1500);
        }
        
        // عرض بيانات المود
        function displayMod(mod) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('mod-content').style.display = 'block';
            
            document.getElementById('mod-title').textContent = mod.name;
            document.getElementById('mod-version').textContent = `Minecraft ${mod.mc_version}`;
            document.getElementById('mod-description').textContent = mod.description;
            document.getElementById('info-version').textContent = mod.mc_version;
            document.getElementById('info-category').textContent = mod.category;
            document.getElementById('download-link').href = `/api/download/${mod.id}`;
            
            if (mod.image_url) {
                const img = document.getElementById('mod-image');
                img.src = mod.image_url;
                img.style.display = 'block';
            }
            
            // تحديث عنوان الصفحة
            document.title = `${mod.name} - تحميل مود ماينكرافت`;
        }
        
        // عرض رسالة خطأ
        function showError() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error-content').style.display = 'block';
        }
        
        // بدء التحميل عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', loadMod);
    </script>
</body>
</html>
