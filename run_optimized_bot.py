#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل البوت المحسن مع جميع الإصلاحات المطبقة
Optimized Bot Runner with All Fixes Applied
"""

import os
import sys
import asyncio
import logging
import time
from datetime import datetime

# إعداد التسجيل المحسن
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def check_requirements():
    """فحص المتطلبات الأساسية"""
    logger.info("🔍 فحص المتطلبات الأساسية...")
    
    required_files = [
        "main.py",
        "web_server.py", 
        "supabase_client.py",
        "requirements.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"❌ ملفات مفقودة: {missing_files}")
        return False
    
    # فحص متغيرات البيئة
    required_env_vars = ["BOT_TOKEN", "ADMIN_CHAT_ID", "SUPABASE_URL", "SUPABASE_KEY"]
    missing_env_vars = []
    
    for var in required_env_vars:
        if not os.environ.get(var):
            missing_env_vars.append(var)
    
    if missing_env_vars:
        logger.error(f"❌ متغيرات بيئة مفقودة: {missing_env_vars}")
        logger.info("💡 تأكد من وجود ملف .env مع القيم المطلوبة")
        return False
    
    logger.info("✅ جميع المتطلبات متوفرة")
    return True

def load_environment():
    """تحميل متغيرات البيئة"""
    logger.info("🔧 تحميل متغيرات البيئة...")
    
    # محاولة تحميل ملف .env
    env_file = ".env"
    if os.path.exists(env_file):
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            logger.info("✅ تم تحميل متغيرات البيئة من .env")
        except Exception as e:
            logger.warning(f"⚠️ فشل تحميل ملف .env: {e}")
    else:
        logger.info("ℹ️ ملف .env غير موجود، استخدام متغيرات البيئة الحالية")

def check_ports():
    """فحص المنافذ المطلوبة"""
    logger.info("🔌 فحص المنافذ المطلوبة...")
    
    import socket
    
    def is_port_available(port):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result != 0
        except:
            return True
    
    required_ports = [5001]  # منفذ خادم الويب
    
    for port in required_ports:
        if not is_port_available(port):
            logger.warning(f"⚠️ المنفذ {port} مستخدم بالفعل")
        else:
            logger.info(f"✅ المنفذ {port} متاح")

def setup_optimization():
    """إعداد التحسينات"""
    logger.info("⚡ إعداد التحسينات...")
    
    try:
        # تطبيق تحسينات الذاكرة
        import gc
        gc.set_threshold(700, 10, 10)  # تحسين garbage collection
        
        # تعيين حدود الموارد إذا كان متاحاً
        try:
            import resource
            # تحديد حد الذاكرة (100 MB)
            resource.setrlimit(resource.RLIMIT_AS, (100 * 1024 * 1024, -1))
        except:
            pass  # تجاهل إذا لم يكن متاحاً
        
        logger.info("✅ تم تطبيق التحسينات")
        
    except Exception as e:
        logger.warning(f"⚠️ فشل تطبيق بعض التحسينات: {e}")

def display_startup_info():
    """عرض معلومات البدء"""
    logger.info("=" * 60)
    logger.info("🤖 بوت مودات ماين كرافت المحسن")
    logger.info("🔧 مع جميع الإصلاحات المطبقة")
    logger.info(f"📅 تاريخ البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)
    
    # عرض الإصلاحات المطبقة
    fixes_applied = [
        "✅ إصلاح مشكلة طول الكابشن",
        "✅ إصلاح مهلة API Telegram", 
        "✅ إصلاح مشكلة Button_type_invalid",
        "✅ إصلاح مشكلة Query timeout",
        "✅ إصلاح البحث عن معرف المود",
        "✅ إصلاح أخطاء Web App 404"
    ]
    
    logger.info("🔧 الإصلاحات المطبقة:")
    for fix in fixes_applied:
        logger.info(f"   {fix}")
    
    logger.info("=" * 60)

async def run_bot():
    """تشغيل البوت الرئيسي"""
    logger.info("🚀 بدء تشغيل البوت...")
    
    try:
        # استيراد البوت الرئيسي
        from main import main
        
        # تشغيل البوت
        await main()
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {e}")
        raise

def main():
    """الدالة الرئيسية"""
    try:
        # عرض معلومات البدء
        display_startup_info()

        # تحميل متغيرات البيئة
        load_environment()

        # فحص المتطلبات
        if not check_requirements():
            logger.error("❌ فشل فحص المتطلبات. توقف التشغيل.")
            logger.info("💡 تشغيل أداة إعداد البيئة...")

            # محاولة تشغيل أداة إعداد البيئة
            try:
                import subprocess
                result = subprocess.run([sys.executable, "setup_environment.py"],
                                      capture_output=True, text=True, timeout=300)
                if result.returncode == 0:
                    logger.info("✅ تم إعداد البيئة بنجاح. إعادة فحص المتطلبات...")
                    load_environment()  # إعادة تحميل متغيرات البيئة
                    if not check_requirements():
                        logger.error("❌ فشل فحص المتطلبات حتى بعد الإعداد.")
                        sys.exit(1)
                else:
                    logger.error(f"❌ فشل إعداد البيئة: {result.stderr}")
                    sys.exit(1)
            except Exception as setup_error:
                logger.error(f"❌ فشل تشغيل أداة إعداد البيئة: {setup_error}")
                sys.exit(1)

        # فحص المنافذ
        check_ports()

        # إعداد التحسينات
        setup_optimization()

        logger.info("🎉 جميع الفحوصات نجحت. بدء تشغيل البوت...")
        logger.info("=" * 60)

        # تشغيل البوت
        asyncio.run(run_bot())

    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ فادح: {e}")
        logger.info("🔍 تشغيل أداة التشخيص للمساعدة...")

        # تشغيل أداة التشخيص عند حدوث خطأ
        try:
            import subprocess
            subprocess.run([sys.executable, "debug_issues.py"], timeout=60)
        except:
            pass

        sys.exit(1)

if __name__ == "__main__":
    main()
