# دليل إصلاح مشاكل البوت
# Bot Troubleshooting Guide

## المشاكل الشائعة وحلولها
## Common Issues and Solutions

### 1. مشكلة ConnectTimeout / TimedOut

**الأعراض:**
- `telegram.error.TimedOut: Timed out`
- `httpcore.ConnectTimeout`
- البوت لا يستطيع الاتصال بـ Telegram API

**الحلول:**

#### أ) استخدام ملف التشغيل المحسن:
```bash
python run_bot_fixed.py
```

#### ب) فحص الاتصال بالإنترنت:
```bash
ping google.com
ping api.telegram.org
```

#### ج) إعدادات البروكسي (إذا لزم الأمر):
```bash
# في ملف .env أضف:
HTTP_PROXY=http://your-proxy:port
HTTPS_PROXY=https://your-proxy:port
```

#### د) زيادة timeout في الكود:
- تم تحديث الكود ليستخدم timeout أطول (60 ثانية)
- تم إضافة إعادة المحاولة التلقائية

### 2. مشكلة ngrok

**الأعراض:**
- `⚠️ لم يتم العثور على رابط ngrok صالح`
- `Environment URL is not accessible`

**الحلول:**

#### أ) تثبيت ngrok:
```bash
# تحميل ngrok من الموقع الرسمي
# https://ngrok.com/download
```

#### ب) تسجيل الدخول في ngrok:
```bash
ngrok authtoken YOUR_AUTH_TOKEN
```

#### ج) تشغيل ngrok يدوياً:
```bash
ngrok http 5001
```

### 3. مشكلة متغيرات البيئة

**الأعراض:**
- البوت لا يبدأ
- رسائل خطأ حول متغيرات مفقودة

**الحل:**
تأكد من وجود ملف `.env` مع المتغيرات التالية:
```env
BOT_TOKEN=your_bot_token_here
ADMIN_CHAT_ID=your_admin_id_here
SUPABASE_URL=your_supabase_url_here
SUPABASE_KEY=your_supabase_key_here
```

### 4. مشكلة المنافذ (Ports)

**الأعراض:**
- `Port already in use`
- خطأ في تشغيل خادم الويب

**الحلول:**

#### أ) تغيير المنافذ في الكود:
```python
# في main.py، ابحث عن:
run_web_server(5000)  # غير إلى منفذ آخر
run_telegram_web_app_server(5001)  # غير إلى منفذ آخر
```

#### ب) إيقاف العمليات المستخدمة للمنافذ:
```bash
# Windows
netstat -ano | findstr :5000
taskkill /PID <PID> /F

# Linux/Mac
lsof -ti:5000 | xargs kill -9
```

### 5. مشكلة قاعدة البيانات

**الأعراض:**
- خطأ في الاتصال بـ Supabase
- فشل في حفظ البيانات

**الحلول:**

#### أ) التحقق من إعدادات Supabase:
- تأكد من صحة SUPABASE_URL
- تأكد من صحة SUPABASE_KEY
- تأكد من تفعيل Row Level Security

#### ب) إنشاء الجداول المطلوبة:
```sql
-- في Supabase SQL Editor
CREATE TABLE IF NOT EXISTS mods (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    download_link TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## نصائح للتحسين
## Optimization Tips

### 1. تحسين الأداء:
- استخدم `run_bot_fixed.py` بدلاً من `main.py`
- فعّل ملف `optimization_config.py`
- استخدم إعدادات الشبكة المحسنة

### 2. تحسين الاستقرار:
- زيادة timeout values
- استخدام إعادة المحاولة التلقائية
- مراقبة استخدام الذاكرة

### 3. تحسين الأمان:
- استخدم متغيرات البيئة للمعلومات الحساسة
- فعّل HTTPS للخوادم
- استخدم البروكسي إذا لزم الأمر

## أوامر مفيدة
## Useful Commands

### فحص حالة البوت:
```bash
# فحص العمليات الجارية
ps aux | grep python

# فحص استخدام المنافذ
netstat -tulpn | grep :5000
netstat -tulpn | grep :5001
```

### إعادة تشغيل البوت:
```bash
# إيقاف البوت
pkill -f main.py

# تشغيل البوت المحسن
python run_bot_fixed.py
```

### فحص السجلات:
```bash
# عرض آخر 50 سطر من السجل
tail -f -n 50 bot.log
```

## الحصول على المساعدة
## Getting Help

إذا استمرت المشاكل:

1. **تحقق من السجلات:** ابحث عن رسائل الخطأ التفصيلية
2. **فحص الاتصال:** تأكد من الاتصال بالإنترنت و Telegram
3. **تحديث المكتبات:** `pip install --upgrade -r requirements.txt`
4. **إعادة تشغيل النظام:** أحياناً يحل المشكلة

### معلومات النظام المطلوبة عند طلب المساعدة:
- نظام التشغيل
- إصدار Python
- رسالة الخطأ الكاملة
- محتوى ملف .env (بدون المعلومات الحساسة)

## ملاحظات مهمة
## Important Notes

- **لا تشارك رمز البوت أو مفاتيح قاعدة البيانات**
- **احتفظ بنسخة احتياطية من ملف .env**
- **راقب استخدام الموارد عند التشغيل على استضافة مجانية**
- **استخدم HTTPS في الإنتاج**
