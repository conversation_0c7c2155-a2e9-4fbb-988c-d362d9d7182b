#!/usr/bin/env python3
"""
تشغيل البوت مع إعدادات شبكة محسنة ومعالجة أخطاء متقدمة
Enhanced Bot Runner with Improved Network Settings and Error Handling
"""

import os
import sys
import asyncio
import logging
import time
import socket
import subprocess
from pathlib import Path

# إعداد الـ logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot_enhanced.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🚀 تشغيل البوت مع إعدادات محسنة")
    print("   Enhanced Bot Runner")
    print("=" * 60)

def check_python_version():
    """فحص إصدار Python"""
    logger.info("🐍 فحص إصدار Python...")
    
    if sys.version_info < (3, 8):
        logger.error("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    logger.info(f"✅ Python {sys.version.split()[0]} متوفر")
    return True

def check_required_files():
    """فحص الملفات المطلوبة"""
    logger.info("📁 فحص الملفات المطلوبة...")
    
    required_files = [
        "main.py",
        ".env",
        "requirements.txt"
    ]
    
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
        else:
            logger.info(f"✅ {file} موجود")
    
    if missing_files:
        logger.error(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    return True

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    logger.info("📦 فحص المكتبات المطلوبة...")
    
    try:
        import telegram
        logger.info(f"✅ python-telegram-bot {telegram.__version__} متوفر")
    except ImportError:
        logger.error("❌ python-telegram-bot غير مثبت")
        return False
    
    try:
        import requests
        logger.info(f"✅ requests متوفر")
    except ImportError:
        logger.error("❌ requests غير مثبت")
        return False
    
    try:
        import aiohttp
        logger.info(f"✅ aiohttp متوفر")
    except ImportError:
        logger.warning("⚠️ aiohttp غير مثبت (اختياري)")
    
    return True

def test_network_connectivity():
    """اختبار الاتصال بالشبكة"""
    logger.info("🌐 اختبار الاتصال بالشبكة...")
    
    # اختبار DNS
    dns_servers = ["8.8.8.8", "1.1.1.1"]
    dns_working = False
    
    for dns in dns_servers:
        try:
            socket.create_connection((dns, 53), timeout=10)
            logger.info(f"✅ DNS {dns} متاح")
            dns_working = True
            break
        except Exception as e:
            logger.warning(f"⚠️ DNS {dns} غير متاح: {e}")
    
    if not dns_working:
        logger.error("❌ جميع خوادم DNS غير متاحة")
        return False
    
    # اختبار حل أسماء النطاقات
    try:
        socket.gethostbyname("api.telegram.org")
        logger.info("✅ حل أسماء النطاقات يعمل")
    except Exception as e:
        logger.error(f"❌ فشل في حل أسماء النطاقات: {e}")
        return False
    
    # اختبار الوصول لـ Telegram
    try:
        import requests
        response = requests.get("https://api.telegram.org/bot123:test/getMe", timeout=15)
        if response.status_code in [200, 401, 404]:
            logger.info("✅ Telegram API قابل للوصول")
            return True
        else:
            logger.warning(f"⚠️ Telegram API استجاب بكود: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ فشل في الوصول لـ Telegram API: {e}")
        return False

def install_dependencies():
    """تثبيت المكتبات المطلوبة"""
    logger.info("📦 تثبيت المكتبات المطلوبة...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], check=True, capture_output=True)
        logger.info("✅ تم تحديث pip")
        
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, capture_output=True)
        logger.info("✅ تم تثبيت المكتبات")
        
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ فشل في تثبيت المكتبات: {e}")
        return False

def set_environment_variables():
    """تعيين متغيرات البيئة المحسنة"""
    logger.info("🔧 تعيين متغيرات البيئة المحسنة...")
    
    # إعدادات الشبكة المحسنة
    enhanced_settings = {
        "NETWORK_CONFIG_ENABLED": "true",
        "OPTIMIZATION_ENABLED": "true",
        "PYTHONHTTPSVERIFY": "1",
        "REQUESTS_CA_BUNDLE": "",
        "CURL_CA_BUNDLE": "",
        # إعدادات timeout محسنة
        "TELEGRAM_READ_TIMEOUT": "60",
        "TELEGRAM_WRITE_TIMEOUT": "60",
        "TELEGRAM_CONNECT_TIMEOUT": "60",
        "TELEGRAM_POOL_TIMEOUT": "60"
    }
    
    for key, value in enhanced_settings.items():
        os.environ[key] = value
        logger.info(f"✅ {key} = {value}")

def run_pre_checks():
    """تشغيل فحوصات ما قبل التشغيل"""
    logger.info("🔍 تشغيل فحوصات ما قبل التشغيل...")
    
    checks = [
        ("إصدار Python", check_python_version),
        ("الملفات المطلوبة", check_required_files),
        ("المكتبات المطلوبة", check_dependencies),
        ("الاتصال بالشبكة", test_network_connectivity)
    ]
    
    failed_checks = []
    
    for check_name, check_func in checks:
        logger.info(f"🔄 فحص: {check_name}")
        try:
            if not check_func():
                failed_checks.append(check_name)
                logger.error(f"❌ فشل: {check_name}")
            else:
                logger.info(f"✅ نجح: {check_name}")
        except Exception as e:
            logger.error(f"💥 خطأ في {check_name}: {e}")
            failed_checks.append(check_name)
        
        time.sleep(1)
    
    if failed_checks:
        logger.error(f"❌ فشل في الفحوصات: {', '.join(failed_checks)}")
        
        # محاولة إصلاح المكتبات
        if "المكتبات المطلوبة" in failed_checks:
            logger.info("🔧 محاولة إصلاح المكتبات...")
            if install_dependencies():
                logger.info("✅ تم إصلاح المكتبات، إعادة فحص...")
                if check_dependencies():
                    failed_checks.remove("المكتبات المطلوبة")
        
        # محاولة إصلاح الشبكة
        if "الاتصال بالشبكة" in failed_checks:
            logger.info("🔧 محاولة إصلاح الشبكة...")
            logger.info("💡 يرجى تشغيل: python auto_fix_network.py")
        
        return len(failed_checks) == 0
    
    return True

async def run_bot():
    """تشغيل البوت"""
    logger.info("🤖 بدء تشغيل البوت...")
    
    try:
        # تعيين متغيرات البيئة المحسنة
        set_environment_variables()
        
        # استيراد وتشغيل البوت
        from main import main
        await main()
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"💥 خطأ في تشغيل البوت: {e}")
        logger.error("🔧 حلول مقترحة:")
        logger.error("   1. تحقق من BOT_TOKEN في ملف .env")
        logger.error("   2. تحقق من الاتصال بالإنترنت")
        logger.error("   3. شغل: python diagnose_network.py")
        logger.error("   4. شغل: python auto_fix_network.py")
        raise

def main():
    """الدالة الرئيسية"""
    print_header()
    
    logger.info("🚀 بدء التشغيل المحسن للبوت...")
    
    # تشغيل فحوصات ما قبل التشغيل
    if not run_pre_checks():
        logger.error("❌ فشل في فحوصات ما قبل التشغيل")
        logger.error("🔧 يرجى إصلاح المشاكل والمحاولة مرة أخرى")
        sys.exit(1)
    
    logger.info("✅ جميع الفحوصات نجحت، بدء تشغيل البوت...")
    
    # تشغيل البوت
    try:
        asyncio.run(run_bot())
    except Exception as e:
        logger.error(f"💥 فشل في تشغيل البوت: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n💥 خطأ فادح: {e}")
        sys.exit(1)
