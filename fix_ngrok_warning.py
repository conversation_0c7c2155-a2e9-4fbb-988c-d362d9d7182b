#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة ngrok Browser Warning
"""

import os
import subprocess
import sys
import time
import requests
from datetime import datetime

def check_ngrok_running():
    """فحص إذا كان ngrok يعمل"""
    try:
        response = requests.get('http://localhost:4040/api/tunnels', timeout=3)
        return response.status_code == 200
    except:
        return False

def get_ngrok_url():
    """الحصول على رابط ngrok الحالي"""
    try:
        response = requests.get('http://localhost:4040/api/tunnels', timeout=3)
        if response.status_code == 200:
            data = response.json()
            tunnels = data.get('tunnels', [])
            for tunnel in tunnels:
                if tunnel.get('proto') == 'https':
                    return tunnel.get('public_url')
        return None
    except:
        return None

def restart_ngrok_with_config():
    """إعادة تشغيل ngrok مع إعدادات محسنة"""
    print("🔄 إعادة تشغيل ngrok مع إعدادات محسنة...")
    
    # إيقاف ngrok الحالي
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['taskkill', '/f', '/im', 'ngrok.exe'], 
                         capture_output=True, check=False)
        else:  # Linux/Mac
            subprocess.run(['pkill', 'ngrok'], 
                         capture_output=True, check=False)
        time.sleep(2)
    except:
        pass
    
    # تشغيل ngrok مع إعدادات محسنة
    try:
        # إنشاء ملف إعداد ngrok مؤقت
        config_content = """
version: "2"
authtoken: YOUR_AUTH_TOKEN_HERE
tunnels:
  web:
    proto: http
    addr: 5001
    bind_tls: true
    inspect: false
    host_header: rewrite
"""
        
        config_file = "ngrok_temp_config.yml"
        with open(config_file, 'w') as f:
            f.write(config_content)
        
        # تشغيل ngrok مع الإعداد
        cmd = ['ngrok', 'start', '--config', config_file, 'web']
        subprocess.Popen(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        print("⏳ انتظار تشغيل ngrok...")
        for i in range(30):
            if check_ngrok_running():
                print("✅ تم تشغيل ngrok بنجاح")
                # حذف ملف الإعداد المؤقت
                try:
                    os.remove(config_file)
                except:
                    pass
                return True
            time.sleep(1)
        
        print("❌ فشل في تشغيل ngrok")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل ngrok: {e}")
        return False

def update_web_server_headers():
    """تحديث headers في خادم الويب لتجنب ngrok warning"""
    print("🔧 تحديث headers في خادم الويب...")
    
    web_server_file = "web_server.py"
    if not os.path.exists(web_server_file):
        print("❌ ملف web_server.py غير موجود")
        return False
    
    try:
        # قراءة الملف
        with open(web_server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة middleware لتجنب ngrok warning
        middleware_code = '''
# إضافة middleware لتجنب ngrok browser warning
@app.before_request
def add_ngrok_headers():
    """إضافة headers لتجنب ngrok browser warning"""
    pass

@app.after_request
def after_request(response):
    """إضافة headers لتجنب ngrok browser warning"""
    response.headers['ngrok-skip-browser-warning'] = 'true'
    response.headers['User-Agent'] = 'TelegramBot/1.0'
    return response
'''
        
        # البحث عن مكان إدراج الكود
        if "ngrok-skip-browser-warning" not in content:
            # إدراج الكود بعد إنشاء app
            app_creation_line = "app = Flask(__name__)"
            if app_creation_line in content:
                content = content.replace(
                    app_creation_line,
                    app_creation_line + "\n" + middleware_code
                )
                
                # كتابة الملف المحدث
                with open(web_server_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ تم تحديث headers في خادم الويب")
                return True
        else:
            print("✅ headers محدثة بالفعل")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في تحديث headers: {e}")
        return False
    
    return False

def update_mod_details_html():
    """تحديث mod_details.html لإضافة headers مناسبة"""
    print("🔧 تحديث mod_details.html...")
    
    html_file = "mod_details.html"
    if not os.path.exists(html_file):
        print("❌ ملف mod_details.html غير موجود")
        return False
    
    try:
        # قراءة الملف
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة JavaScript لتجنب ngrok warning
        js_code = '''
<script>
// إضافة headers لتجنب ngrok browser warning
if (window.location.hostname.includes('ngrok')) {
    // تعيين headers للطلبات
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        if (args[1]) {
            args[1].headers = {
                ...args[1].headers,
                'ngrok-skip-browser-warning': 'true',
                'User-Agent': 'TelegramBot/1.0'
            };
        } else {
            args[1] = {
                headers: {
                    'ngrok-skip-browser-warning': 'true',
                    'User-Agent': 'TelegramBot/1.0'
                }
            };
        }
        return originalFetch.apply(this, args);
    };
}
</script>
'''
        
        # إدراج الكود قبل إغلاق head
        if "ngrok-skip-browser-warning" not in content:
            content = content.replace("</head>", js_code + "\n</head>")
            
            # كتابة الملف المحدث
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم تحديث mod_details.html")
            return True
        else:
            print("✅ mod_details.html محدث بالفعل")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في تحديث mod_details.html: {e}")
        return False

def test_ngrok_warning_fix():
    """اختبار إصلاح ngrok warning"""
    print("🧪 اختبار إصلاح ngrok warning...")
    
    ngrok_url = get_ngrok_url()
    if not ngrok_url:
        print("❌ لا يمكن الحصول على رابط ngrok")
        return False
    
    try:
        # اختبار الوصول مع headers مناسبة
        headers = {
            'ngrok-skip-browser-warning': 'true',
            'User-Agent': 'TelegramBot/1.0'
        }
        
        response = requests.get(ngrok_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            if "ngrok.com" not in response.text or "abuse" not in response.text.lower():
                print("✅ تم إصلاح ngrok warning بنجاح")
                return True
            else:
                print("⚠️ ngrok warning ما زال موجود")
                return False
        else:
            print(f"❌ خطأ في الوصول: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 بدء إصلاح مشكلة ngrok Browser Warning...")
    print("=" * 50)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    success_count = 0
    total_fixes = 3
    
    # 1. تحديث headers في خادم الويب
    if update_web_server_headers():
        success_count += 1
    
    # 2. تحديث mod_details.html
    if update_mod_details_html():
        success_count += 1
    
    # 3. اختبار الإصلاح
    if check_ngrok_running():
        if test_ngrok_warning_fix():
            success_count += 1
        else:
            print("💡 جرب إعادة تشغيل ngrok مع إعدادات محسنة...")
            if restart_ngrok_with_config():
                if test_ngrok_warning_fix():
                    success_count += 1
    else:
        print("⚠️ ngrok غير مُشغل، لا يمكن اختبار الإصلاح")
    
    print("\n" + "=" * 50)
    print("📊 نتائج الإصلاح:")
    print(f"✅ نجح: {success_count}/{total_fixes}")
    
    if success_count == total_fixes:
        print("\n🎉 تم إصلاح جميع مشاكل ngrok Browser Warning!")
        print("\n💡 الآن:")
        print("   1. أعد تشغيل البوت: python main.py")
        print("   2. اختبر أزرار صفحات المودات")
        print("   3. لن تظهر رسالة ngrok warning")
    else:
        print(f"\n⚠️ تم إصلاح {success_count} من {total_fixes} مشاكل")
        print("\n🔧 خطوات إضافية:")
        print("   1. تأكد من تشغيل ngrok")
        print("   2. أعد تشغيل خادم الويب")
        print("   3. جرب الاختبار مرة أخرى")
    
    return success_count == total_fixes

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح بواسطة المستخدم")
        exit(1)
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        exit(1)
