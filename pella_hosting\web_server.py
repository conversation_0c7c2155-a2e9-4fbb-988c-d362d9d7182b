import os
import logging
import json # Import json for handling image URLs and API responses
import re # Import re for regex patterns
from datetime import datetime # Import datetime for timestamps
from flask import Flask, render_template_string, request
import html # Import html for escaping
import supabase_client # Import the supabase_client

# Configure logging for the web server
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
web_logger = logging.getLogger(__name__)

app = Flask(__name__)

def update_user_activity(user_id):
    """تحديث آخر نشاط للمستخدم"""
    try:
        from datetime import datetime
        import json

        # تحديث ملف جميع المستخدمين
        all_users_file = "all_users.json"
        all_users = {}

        if os.path.exists(all_users_file):
            try:
                with open(all_users_file, 'r', encoding='utf-8') as f:
                    all_users = json.load(f)
            except (json.JSONDecodeError, IOError):
                all_users = {}

        user_id_str = str(user_id)
        if user_id_str in all_users:
            all_users[user_id_str]["last_activity"] = datetime.now().isoformat()
        else:
            all_users[user_id_str] = {
                "first_seen": datetime.now().isoformat(),
                "last_activity": datetime.now().isoformat(),
                "lang": "ar",
                "username": "",
                "full_name": ""
            }

        with open(all_users_file, 'w', encoding='utf-8') as f:
            json.dump(all_users, f, indent=4, ensure_ascii=False)

        web_logger.debug(f"Updated activity for user {user_id}")

    except Exception as e:
        web_logger.error(f"Error updating user activity for {user_id}: {e}")

# إضافة middleware لتجنب ngrok browser warning
@app.before_request
def add_ngrok_headers():
    """إضافة headers لتجنب ngrok browser warning"""
    pass

@app.after_request
def after_request(response):
    """إضافة headers لتجنب ngrok browser warning"""
    response.headers['ngrok-skip-browser-warning'] = 'true'
    response.headers['User-Agent'] = 'TelegramBot/1.0'
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Headers'] = 'ngrok-skip-browser-warning, User-Agent'
    return response
@app.after_request
def after_request_cors(response):
    """إضافة CORS headers للـ API"""
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, ngrok-skip-browser-warning'
    response.headers['ngrok-skip-browser-warning'] = 'true'
    return response


# Load the mod_details.html template once
try:
    with open("mod_details.html", "r", encoding="utf-8") as f:
        MOD_DETAILS_TEMPLATE = f.read()
except FileNotFoundError:
    web_logger.error("mod_details.html not found. Ensure it's in the same directory.")
    MOD_DETAILS_TEMPLATE = "<h1>Error: Mod details template not found.</h1>"

@app.route('/')
def index():
    return "<h1>Flask server is running!</h1>"

@app.route('/ad-click')
def ad_click():
    """تتبع نقرات الإعلانات وإعادة التوجيه"""
    user_id = request.args.get('user_id')
    ad_url = request.args.get('url')

    if not user_id or not ad_url:
        return "<h1>Error: Missing parameters.</h1>", 400

    # تحديث إحصائية النقرات
    supabase_client.update_ads_stats(user_id, 'clicks')

    # إعادة التوجيه للإعلان
    return f"""
    <html>
    <head>
        <meta http-equiv="refresh" content="0; url={ad_url}">
        <script>window.location.href = '{ad_url}';</script>
    </head>
    <body>
        <p>Redirecting to ad...</p>
    </body>
    </html>
    """

@app.route('/complete-task')
def complete_task():
    """تسجيل إكمال مهمة من قبل المستخدم"""
    user_id = request.args.get('user_id')
    task_id = request.args.get('task_id')
    channel_owner_id = request.args.get('channel_owner_id')

    if not user_id or not task_id or not channel_owner_id:
        return {"success": False, "error": "Missing parameters"}, 400

    try:
        # تسجيل إكمال المهمة
        success = supabase_client.mark_task_completed(user_id, int(task_id), channel_owner_id)

        if success:
            # التحقق من إكمال جميع المهام
            all_completed = supabase_client.check_user_completed_all_tasks(user_id, channel_owner_id)

            if all_completed:
                # إضافة المستخدم لقائمة المكتملين
                channel_id = request.args.get('channel_id', '')
                supabase_client.add_completed_user(user_id, channel_owner_id, channel_id)

            return {"success": True, "all_completed": all_completed}
        else:
            return {"success": False, "error": "Failed to mark task as completed"}, 500

    except Exception as e:
        return {"success": False, "error": str(e)}, 500

@app.route('/check-tasks-status')
def check_tasks_status():
    """التحقق من حالة إكمال المهام للمستخدم"""
    user_id = request.args.get('user_id')
    channel_owner_id = request.args.get('channel_owner_id')

    if not user_id or not channel_owner_id:
        return {"success": False, "error": "Missing parameters"}, 400

    try:
        # التحقق من إكمال جميع المهام
        all_completed = supabase_client.check_user_completed_all_tasks(user_id, channel_owner_id)

        # جلب المهام المكتملة
        completed_tasks = supabase_client.get_user_task_completions(user_id, channel_owner_id)
        completed_task_ids = [task['task_id'] for task in completed_tasks]

        return {
            "success": True,
            "all_completed": all_completed,
            "completed_task_ids": completed_task_ids
        }

    except Exception as e:
        return {"success": False, "error": str(e)}, 500

@app.route('/get-file-info')
def get_file_info():
    """الحصول على معلومات الملف للتحميل"""
    mod_id = request.args.get('mod_id')

    if not mod_id:
        return {"success": False, "error": "Missing mod_id"}, 400

    try:
        # جلب بيانات المود
        mod_data = supabase_client.get_mod_by_id(mod_id)

        if not mod_data:
            return {"success": False, "error": "Mod not found"}, 404

        download_url = mod_data.get('download_link', '')
        if not download_url:
            return {"success": False, "error": "No download URL found"}, 404

        # تحديد نوع الملف
        file_extension = '.mcaddon' if '.mcaddon' in download_url else '.mcpack'

        # تقدير حجم الملف (يمكن تحسينه لاحقاً بطلب HEAD request)
        estimated_size = 25.5  # ميجابايت (متوسط حجم المودات)

        # تحديد اسم الملف
        mod_title = mod_data.get('title', 'mod')
        safe_filename = ''.join(c for c in mod_title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"{safe_filename}{file_extension}"

        return {
            "success": True,
            "filename": filename,
            "size_mb": estimated_size,
            "file_extension": file_extension,
            "download_url": download_url
        }

    except Exception as e:
        web_logger.error(f"Error getting file info for mod {mod_id}: {e}")
        return {"success": False, "error": str(e)}, 500

@app.route('/api/mod/<mod_id>')
def get_mod_api(mod_id):
    """API endpoint للحصول على بيانات المود بصيغة JSON - محسن"""
    lang = request.args.get('lang', 'ar')
    user_id = request.args.get('user_id')
    channel_id = request.args.get('channel')

    web_logger.info(f"API request for mod {mod_id} (lang: {lang}, user: {user_id}, channel: {channel_id})")

    try:
        # تحديث آخر نشاط للمستخدم
        if user_id:
            update_user_activity(user_id)

        # التحقق من صحة معرف المود (UUID)
        import re
        uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        if not re.match(uuid_pattern, mod_id, re.IGNORECASE):
            web_logger.warning(f"Invalid mod ID format: {mod_id}")
            response = app.response_class(
                response=json.dumps({"error": "Invalid mod ID format", "mod_id": mod_id}),
                status=400,
                mimetype='application/json'
            )
            return response

        # جلب بيانات المود من قاعدة البيانات
        mod_data = supabase_client.get_mod_by_id(mod_id)

        if not mod_data:
            web_logger.warning(f"Mod {mod_id} not found in database")
            response = app.response_class(
                response=json.dumps({"error": "Mod not found", "mod_id": mod_id}),
                status=404,
                mimetype='application/json'
            )
            return response

    except Exception as e:
        web_logger.error(f"Error processing API request for mod {mod_id}: {e}")
        response = app.response_class(
            response=json.dumps({"error": "Internal server error", "details": str(e)}),
            status=500,
            mimetype='application/json'
        )
        return response

    # تحضير البيانات للإرجاع
    mod_response_data = {
        "id": mod_data.get('id'),
        "title": mod_data.get('title', 'N/A'),
        "image_urls": mod_data.get('image_urls', []),
        "version": mod_data.get('version', 'N/A'),
        "download_link": mod_data.get('download_link', ''),
        "category": mod_data.get('category', 'unknown'),
        "description": {},
        "telegram_description_ar": mod_data.get('telegram_description_ar'),
        "telegram_description_en": mod_data.get('telegram_description_en')
    }

    # إضافة الوصف حسب اللغة
    description_obj = mod_data.get('description', {})
    if isinstance(description_obj, dict):
        mod_response_data["description"] = description_obj
    else:
        mod_response_data["description"] = {"ar": str(description_obj), "en": str(description_obj)}

    # تحضير الاستجابة النهائية بالتنسيق المطلوب
    response_data = {
        "success": True,
        "mod": mod_response_data
    }

    web_logger.info(f"Successfully returning mod data for {mod_id}")

    # إرجاع استجابة JSON صحيحة
    response = app.response_class(
        response=json.dumps(response_data, ensure_ascii=False),
        status=200,
        mimetype='application/json'
    )
    return response

@app.route('/telegram-mod-details')
@app.route('/mod-details')
def mod_details():
    """Handle mod details requests with parameters and render the HTML template."""
    mod_id = request.args.get('id')
    lang = request.args.get('lang', 'ar')
    user_id = request.args.get('user_id')
    channel_id = request.args.get('channel')

    # التحقق من صحة المعاملات الأساسية
    if not mod_id:
        error_msg = "خطأ: معرف المود مفقود." if lang == 'ar' else "Error: Mod ID is missing."
        return f"<h1>{error_msg}</h1>", 400

    # التحقق من صحة معرف المود (يجب أن يكون UUID صالح)
    import re
    uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    if not re.match(uuid_pattern, mod_id, re.IGNORECASE):
        error_msg = "خطأ: معرف المود غير صالح." if lang == 'ar' else "Error: Invalid Mod ID."
        return f"<h1>{error_msg}</h1>", 400

    # جلب بيانات المود من قاعدة البيانات
    mod_data = supabase_client.get_mod_by_id(mod_id)

    if not mod_data:
        error_msg = "خطأ: المود غير موجود." if lang == 'ar' else "Error: Mod not found."
        return f"<h1>{error_msg}</h1>", 404

    # جلب إعدادات الإعلانات للمستخدم
    ads_settings = None
    if user_id:
        ads_settings = supabase_client.get_user_ads_settings(user_id)
        # تحديث إحصائية المشاهدات
        if ads_settings and ads_settings.get('ads_enabled'):
            supabase_client.update_ads_stats(user_id, 'views')

    # جلب إعدادات تخصيص الصفحة للمستخدم
    page_customization = None
    if user_id:
        page_customization = supabase_client.get_user_page_customization_settings(user_id)

    # جلب إعدادات نظام المهام
    tasks_settings = None
    available_tasks = []
    user_completed_tasks = []
    user_is_completed = False

    if channel_id and user_id:
        # البحث عن صاحب القناة من خلال channel_id
        # نحتاج للبحث في user_channels للعثور على صاحب القناة
        import json
        try:
            with open('user_channels.json', 'r', encoding='utf-8') as f:
                user_channels_data = json.load(f)

            channel_owner_id = None
            for uid, data in user_channels_data.items():
                if isinstance(data, dict) and data.get('channel_id') == channel_id:
                    channel_owner_id = uid
                    break

            if channel_owner_id:
                tasks_settings = supabase_client.get_user_tasks_settings(channel_owner_id)

                if tasks_settings and tasks_settings.get('tasks_enabled'):
                    available_tasks = supabase_client.get_available_tasks(channel_owner_id, active_only=True)
                    user_completed_tasks = supabase_client.get_user_task_completions(user_id, channel_owner_id)
                    user_is_completed = supabase_client.is_user_completed(user_id, channel_owner_id)

        except Exception as e:
            print(f"خطأ في جلب إعدادات المهام: {e}")

    # Extract mod details
    mod_title = mod_data.get('title', 'N/A') # Assuming title is now universal or handled by Supabase
    mod_image_urls = mod_data.get('image_urls', [])
    mod_image_url = mod_image_urls[0] if mod_image_urls else ''
    mod_version = mod_data.get('version', 'N/A')
    mod_download_url = mod_data.get('download_link', '#') # Fallback to '#'

    # Ensure image_urls is a JSON string for JavaScript
    mod_image_urls_json = json.dumps(mod_image_urls)

    # Determine mod description based on language and available fields
    description_text_raw = None
    if lang == "ar" and mod_data.get('telegram_description_ar'):
        description_text_raw = mod_data.get('telegram_description_ar')
    elif lang == "en" and mod_data.get('telegram_description_en'):
        description_text_raw = mod_data.get('telegram_description_en')
    else:
        description_obj = mod_data.get('description', {})
        if isinstance(description_obj, dict):
            description_text_raw = description_obj.get(lang, description_obj.get('en', 'N/A'))
        else:
            description_text_raw = str(description_obj) if description_obj else 'N/A'
    
    mod_description = html.escape(description_text_raw) if description_text_raw else 'N/A'

    # Determine mod category display name
    mod_category_key = mod_data.get('category', 'unknown').lower()
    category_names = {
        "ar": {
            "addons": "إضافات", "shaders": "شيدرات", "texture_packs": "حزم النسيج",
            "seeds": "بذور", "maps": "خرائط", "unknown": "غير محدد"
        },
        "en": {
            "addons": "Add-ons", "shaders": "Shaders", "texture_packs": "Texture Packs",
            "seeds": "Seeds", "maps": "Maps", "unknown": "Not specified"
        }
    }
    mod_category = category_names.get(lang, category_names['ar']).get(mod_category_key, category_names.get(lang, category_names['ar'])['unknown'])

    # تحديد النصوص والتسميات بناءً على إعدادات التخصيص أو القيم الافتراضية
    if page_customization:
        site_name = page_customization.get('site_name', 'Modetaris')
        show_all_images = page_customization.get('show_all_images', True)
        download_button_text = page_customization.get(f'download_button_text_{lang}',
                                                     "تحميل المود" if lang == 'ar' else "Download Mod")
        open_button_text = page_customization.get(f'open_button_text_{lang}',
                                                 "فتح المود" if lang == 'ar' else "Open Mod")
        version_label = page_customization.get(f'version_label_{lang}',
                                              "الإصدار" if lang == 'ar' else "Version")
        category_label = page_customization.get(f'category_label_{lang}',
                                               "تصنيف المود" if lang == 'ar' else "Mod Category")
        description_label = page_customization.get(f'description_label_{lang}',
                                                  "الوصف" if lang == 'ar' else "Description")
        enable_mod_opening = page_customization.get('enable_mod_opening', True)
    else:
        # القيم الافتراضية
        site_name = 'Modetaris'
        show_all_images = True
        download_button_text = "تحميل المود" if lang == 'ar' else "Download Mod"
        open_button_text = "فتح المود" if lang == 'ar' else "Open Mod"
        version_label = "الإصدار" if lang == 'ar' else "Version"
        category_label = "تصنيف المود" if lang == 'ar' else "Mod Category"
        description_label = "الوصف" if lang == 'ar' else "Description"
        enable_mod_opening = True

    # تحديد الصور المعروضة بناءً على إعدادات التخصيص
    if not show_all_images and mod_image_urls:
        # عرض الصورة الرئيسية فقط
        mod_image_urls_json = json.dumps([mod_image_urls[0]])
    else:
        # عرض جميع الصور
        mod_image_urls_json = json.dumps(mod_image_urls)

    return render_template_string(
        MOD_DETAILS_TEMPLATE,
        mod_title=mod_title,
        mod_image_url=mod_image_url,
        mod_image_urls_json=mod_image_urls_json,
        mod_version=mod_version,
        mod_category=mod_category,
        mod_description=mod_description,
        mod_download_url=mod_download_url,
        download_button_text=download_button_text,
        open_button_text=open_button_text,
        version_label=version_label,
        category_label=category_label,
        description_label=description_label,
        site_name=site_name,
        enable_mod_opening=enable_mod_opening,
        lang=lang,
        mod_id=mod_id,
        user_id=user_id,
        channel_id=channel_id,
        ads_settings=ads_settings,
        tasks_settings=tasks_settings,
        available_tasks=available_tasks,
        user_completed_tasks=user_completed_tasks,
        user_is_completed=user_is_completed,
        page_customization=page_customization  # تمرير إعدادات التخصيص
    )


@app.route('/api/test')
def test_api():
    """API endpoint للاختبار"""
    return {
        "status": "success",
        "message": "API يعمل بشكل صحيح",
        "timestamp": datetime.now().isoformat()
    }

@app.route('/api/test/mod/<mod_id>')
def test_mod_api(mod_id):
    """اختبار API للمود"""
    return {
        "status": "success",
        "mod_id": mod_id,
        "message": f"تم استلام طلب للمود {mod_id}",
        "valid_uuid": bool(re.match(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', mod_id, re.IGNORECASE))
    }

def run_web_server(port=5000):
    web_logger.info(f"Starting Flask web server on port {port}...")
    # Use 0.0.0.0 to make it accessible from outside localhost in some environments
    app.run(host='0.0.0.0', port=port, debug=False, use_reloader=False)

def run_telegram_web_app_server(port=5001):
    """تشغيل خادم Telegram Web App المحسن"""
    try:
        from telegram_web_app import run_telegram_web_app
        web_logger.info(f"Starting Telegram Web App server on port {port}...")
        run_telegram_web_app(port)
    except ImportError:
        web_logger.error("telegram_web_app module not found. Please ensure telegram_web_app.py exists.")
    except Exception as e:
        web_logger.error(f"Failed to start Telegram Web App server: {e}")

if __name__ == '__main__':
    # This block runs only when web_server.py is executed directly
    # For production, it will be run as a thread from main.py
    import threading

    # تشغيل الخادم الأساسي
    web_thread = threading.Thread(target=run_web_server, args=(5000,))
    web_thread.daemon = True
    web_thread.start()

    # تشغيل خادم Telegram Web App
    run_telegram_web_app_server(5001)
