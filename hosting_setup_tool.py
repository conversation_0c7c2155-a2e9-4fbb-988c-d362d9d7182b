#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إعداد صفحة عرض المودات للاستضافة
Hosting Setup Tool for Mod Display Page
"""

import os
import shutil
import json
import zipfile
from datetime import datetime

class HostingSetupTool:
    def __init__(self):
        self.hosting_files_dir = "hosting_files"
        self.required_files = {
            "index.php": "الصفحة الرئيسية",
            "api.php": "واجهة برمجة التطبيقات", 
            "config.php": "ملف الإعدادات",
            "style.css": "ملف التصميم",
            "script.js": "ملف الجافاسكريبت",
            ".htaccess": "إعدادات الخادم",
            "deploy.php": "صفحة الإعداد والاختبار",
            "logs.php": "عارض السجلات",
            "clear_log.php": "مسح السجلات",
            "404.html": "صفحة خطأ 404",
            "500.html": "صفحة خطأ 500",
            "robots.txt": "ملف محركات البحث",
            "sitemap.xml": "خريطة الموقع",
            "README.md": "دليل الاستخدام",
            "INSTALLATION.md": "دليل التثبيت",
            "update_bot.py": "سكريبت تحديث البوت"
        }
        
    def print_header(self):
        """طباعة رأس الأداة"""
        print("=" * 70)
        print("🚀 أداة إعداد صفحة عرض المودات للاستضافة")
        print("🌐 Hosting Setup Tool for Mod Display Page")
        print("=" * 70)
        
    def check_files(self):
        """فحص وجود الملفات المطلوبة"""
        print("\n🔍 فحص الملفات المطلوبة...")
        
        if not os.path.exists(self.hosting_files_dir):
            print(f"❌ مجلد {self.hosting_files_dir} غير موجود!")
            return False
            
        missing_files = []
        existing_files = []
        
        for file_name, description in self.required_files.items():
            file_path = os.path.join(self.hosting_files_dir, file_name)
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                existing_files.append((file_name, description, file_size))
                print(f"✅ {description} ({file_name}) - {file_size} بايت")
            else:
                missing_files.append((file_name, description))
                print(f"❌ {description} ({file_name}) - مفقود")
        
        if missing_files:
            print(f"\n⚠️ يوجد {len(missing_files)} ملف مفقود:")
            for file_name, description in missing_files:
                print(f"   - {description} ({file_name})")
            return False
        else:
            print(f"\n✅ جميع الملفات موجودة ({len(existing_files)} ملف)")
            return True
    
    def create_missing_files(self):
        """إنشاء الملفات المفقودة"""
        print("\n🔧 إنشاء الملفات المفقودة...")
        
        # إنشاء مجلد hosting_files إذا لم يكن موجوداً
        if not os.path.exists(self.hosting_files_dir):
            os.makedirs(self.hosting_files_dir)
            print(f"📁 تم إنشاء مجلد {self.hosting_files_dir}")
        
        # إنشاء الملفات الأساسية
        self.create_index_php()
        self.create_api_php()
        self.create_config_php()
        self.create_style_css()
        self.create_script_js()
        self.create_htaccess()
        self.create_deploy_php()
        self.create_error_pages()
        self.create_seo_files()
        self.create_documentation()
        
        print("✅ تم إنشاء جميع الملفات المطلوبة")
    
    def create_index_php(self):
        """إنشاء ملف index.php"""
        content = '''<?php
/**
 * صفحة عرض تفاصيل المودات - Hosting Version
 */

// إعدادات قاعدة البيانات Supabase
$SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
$SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";

// الحصول على معاملات الرابط
$mod_id = isset($_GET['id']) ? $_GET['id'] : null;
$lang = isset($_GET['lang']) ? $_GET['lang'] : 'ar';
$user_id = isset($_GET['user_id']) ? $_GET['user_id'] : null;
$channel_id = isset($_GET['channel']) ? $_GET['channel'] : null;

// التحقق من وجود معرف المود
if (!$mod_id) {
    $error_msg = $lang == 'ar' ? "خطأ: معرف المود مفقود." : "Error: Mod ID is missing.";
    die("<h1>$error_msg</h1>");
}

// دالة جلب بيانات المود من Supabase
function getModData($mod_id, $supabase_url, $supabase_key) {
    $url = $supabase_url . "/rest/v1/minemods?id=eq." . $mod_id;
    
    $headers = array(
        'apikey: ' . $supabase_key,
        'Authorization: Bearer ' . $supabase_key,
        'Content-Type: application/json'
    );
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200) {
        $data = json_decode($response, true);
        return !empty($data) ? $data[0] : null;
    }
    
    return null;
}

// جلب بيانات المود
$mod_data = getModData($mod_id, $SUPABASE_URL, $SUPABASE_KEY);

if (!$mod_data) {
    $error_msg = $lang == 'ar' ? "المود غير موجود." : "Mod not found.";
    die("<h1>$error_msg</h1>");
}

// تحضير البيانات للعرض
$mod_title = $mod_data['name'] ?? 'N/A';
$mod_image_urls = $mod_data['image_urls'] ?? [];
$mod_image_url = !empty($mod_image_urls) ? $mod_image_urls[0] : '';
$mod_version = $mod_data['version'] ?? 'N/A';
$mod_download_url = $mod_data['download_url'] ?? '#';

// الوصف حسب اللغة
$description_ar = $mod_data['description_ar'] ?? '';
$description_en = $mod_data['description'] ?? '';
$mod_description = $lang == 'ar' ? $description_ar : $description_en;
if (empty($mod_description)) {
    $mod_description = $lang == 'ar' ? 'لا يوجد وصف متاح' : 'No description available';
}

// تصنيف المود
$mod_category_key = strtolower($mod_data['category'] ?? 'unknown');
$category_names = array(
    'ar' => array(
        'addons' => 'إضافات',
        'shaders' => 'شيدرات', 
        'texture_packs' => 'حزم النسيج',
        'seeds' => 'بذور',
        'maps' => 'خرائط',
        'unknown' => 'غير محدد'
    ),
    'en' => array(
        'addons' => 'Add-ons',
        'shaders' => 'Shaders',
        'texture_packs' => 'Texture Packs', 
        'seeds' => 'Seeds',
        'maps' => 'Maps',
        'unknown' => 'Not specified'
    )
);

$mod_category = $category_names[$lang][$mod_category_key] ?? $category_names[$lang]['unknown'];
$download_button_text = $lang == 'ar' ? 'تحميل المود' : 'Download Mod';
$mod_image_urls_json = json_encode($mod_image_urls);
?>
<!DOCTYPE html>
<html id="html-root" lang="<?php echo $lang; ?>" dir="<?php echo $lang == 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title"><?php echo htmlspecialchars($mod_title); ?> - <?php echo $lang == 'ar' ? 'تفاصيل المود' : 'Mod Details'; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Header -->
    <header class="bg-yellow-500 text-white p-4 flex justify-center items-center">
        <div class="font-bold text-2xl">Modetaris</div>
    </header>

    <div class="container mx-auto p-4 pb-24">
        <!-- Mod Title -->
        <div class="mod-header pixel-border mb-4">
            <h1 class="text-2xl mod-title"><?php echo htmlspecialchars($mod_title); ?></h1>
        </div>

        <!-- Main Mod Image Display -->
        <div class="mod-container mb-4 p-1 image-glow-effect">
            <div class="relative w-full bg-gray-700" style="padding-top: 56.25%;">
                <img id="main-mod-image" class="absolute inset-0 w-full h-full object-cover" src="<?php echo htmlspecialchars($mod_image_url); ?>" alt="<?php echo $lang == 'ar' ? 'صورة المود' : 'Mod Image'; ?>">
            </div>
            <div class="particle p-bottom1" style="--tx: 2px; --ty: -25px;"></div>
            <div class="particle p-bottom2" style="--tx: 0px; --ty: -30px;"></div>
            <div class="particle p-bottom3" style="--tx: -2px; --ty: -28px;"></div>
        </div>

        <!-- Thumbnail Navigation and Controls -->
        <div class="flex items-center justify-center mb-4">
            <button id="prev-image" class="nav-button pixel-border mr-2"><</button>
            <div id="thumbnail-container" class="flex flex-grow justify-center items-center space-x-2 overflow-x-auto">
                <!-- Thumbnails will be inserted here by JavaScript -->
            </div>
            <button id="next-image" class="nav-button pixel-border ml-2">></button>
        </div>

        <!-- Mod Info -->
        <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="mod-container pixel-border p-4">
                <p class="info-label version-label"><?php echo $lang == 'ar' ? 'الإصدار' : 'Version'; ?></p>
                <p class="mod-info mod-version"><?php echo htmlspecialchars($mod_version); ?></p>
            </div>
            <div class="mod-container pixel-border p-4">
                <p class="info-label loader-label"><?php echo $lang == 'ar' ? 'تصنيف المود' : 'Mod Category'; ?></p>
                <p class="mod-info mod-category"><?php echo htmlspecialchars($mod_category); ?></p>
            </div>
        </div>

        <!-- Mod Description -->
        <div class="mod-container pixel-border p-4 mb-6">
            <p class="info-label text-center description-label"><?php echo $lang == 'ar' ? 'الوصف' : 'Description'; ?></p>
            <p class="mod-info mt-2 text-center mod-description"><?php echo htmlspecialchars($mod_description); ?></p>
        </div>

        <!-- Download Button -->
        <div class="fixed bottom-0 left-0 right-0 p-4 z-50 flex justify-center">
            <button id="download-button" class="pixel-button text-xl py-3 px-10 new-feature" style="position: relative; overflow: hidden;" onclick="handleDownload()">
                <div class="progress-bar" id="progress-bar"></div>
                <span class="download-icon" id="download-icon">📥</span>
                <span id="download-text"><?php echo $download_button_text; ?></span>
            </button>
        </div>
    </div>

    <script>
        // بيانات المود
        const imageUrls = <?php echo $mod_image_urls_json; ?>;
        const modDownloadUrl = '<?php echo addslashes($mod_download_url); ?>';
        const modId = '<?php echo addslashes($mod_id); ?>';
        const modTitle = '<?php echo addslashes($mod_title); ?>';
        const lang = '<?php echo $lang; ?>';
        const userId = '<?php echo addslashes($user_id); ?>';
        
        let currentImageIndex = 0;
        let isDownloading = false;
        let isDownloaded = false;
        let downloadProgress = 0;
        
        // تحميل الجافاسكريبت الخارجي
        const script = document.createElement('script');
        script.src = 'script.js';
        document.head.appendChild(script);
    </script>
</body>
</html>'''
        
        file_path = os.path.join(self.hosting_files_dir, "index.php")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ تم إنشاء index.php")
    
    def create_api_php(self):
        """إنشاء ملف api.php"""
        content = '''<?php
/**
 * API للتعامل مع طلبات البيانات
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// إعدادات قاعدة البيانات Supabase
$SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
$SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";

// دالة إرسال طلب إلى Supabase
function makeSupabaseRequest($endpoint, $method = 'GET', $data = null) {
    global $SUPABASE_URL, $SUPABASE_KEY;
    
    $url = $SUPABASE_URL . $endpoint;
    
    $headers = array(
        'apikey: ' . $SUPABASE_KEY,
        'Authorization: Bearer ' . $SUPABASE_KEY,
        'Content-Type: application/json'
    );
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return array(
        'success' => $http_code >= 200 && $http_code < 300,
        'data' => json_decode($response, true),
        'http_code' => $http_code
    );
}

// معالجة طلب بيانات المود
if (isset($_GET['path']) && $_GET['path'] == '/mod') {
    $mod_id = $_GET['id'] ?? null;
    $lang = $_GET['lang'] ?? 'ar';
    
    if (!$mod_id) {
        http_response_code(400);
        echo json_encode(array('error' => 'Missing mod_id'));
        exit;
    }
    
    $response = makeSupabaseRequest("/rest/v1/minemods?id=eq.$mod_id");
    
    if (!$response['success'] || empty($response['data'])) {
        http_response_code(404);
        echo json_encode(array('error' => 'Mod not found'));
        exit;
    }
    
    $mod_data = $response['data'][0];
    
    // تحضير البيانات للإرجاع
    $result = array(
        'id' => $mod_data['id'],
        'title' => $mod_data['name'] ?? 'N/A',
        'image_urls' => $mod_data['image_urls'] ?? array(),
        'version' => $mod_data['version'] ?? 'N/A',
        'download_link' => $mod_data['download_url'] ?? '',
        'category' => $mod_data['category'] ?? 'unknown',
        'description' => array(
            'ar' => $mod_data['description_ar'] ?? '',
            'en' => $mod_data['description'] ?? ''
        )
    );
    
    echo json_encode($result);
    exit;
}

// معالجة طلب معلومات الملف
if (isset($_GET['path']) && $_GET['path'] == '/file-info') {
    $mod_id = $_GET['mod_id'] ?? null;
    
    if (!$mod_id) {
        http_response_code(400);
        echo json_encode(array('success' => false, 'error' => 'Missing mod_id'));
        exit;
    }
    
    $response = makeSupabaseRequest("/rest/v1/minemods?id=eq.$mod_id");
    
    if (!$response['success'] || empty($response['data'])) {
        http_response_code(404);
        echo json_encode(array('success' => false, 'error' => 'Mod not found'));
        exit;
    }
    
    $mod_data = $response['data'][0];
    $download_url = $mod_data['download_url'] ?? '';
    
    if (empty($download_url)) {
        http_response_code(404);
        echo json_encode(array('success' => false, 'error' => 'No download URL found'));
        exit;
    }
    
    // تحديد نوع الملف
    $file_extension = strpos($download_url, '.mcaddon') !== false ? '.mcaddon' : '.mcpack';
    
    // تقدير حجم الملف
    $estimated_size = 25.5; // ميجابايت
    
    // تحديد اسم الملف
    $mod_title = $mod_data['name'] ?? 'mod';
    $safe_filename = preg_replace('/[^a-zA-Z0-9\s\-_]/', '', $mod_title);
    $filename = trim($safe_filename) . $file_extension;
    
    echo json_encode(array(
        'success' => true,
        'filename' => $filename,
        'size_mb' => $estimated_size,
        'file_extension' => $file_extension,
        'download_url' => $download_url
    ));
    exit;
}

// إذا لم يتم العثور على endpoint
http_response_code(404);
echo json_encode(array('error' => 'Endpoint not found'));
?>'''
        
        file_path = os.path.join(self.hosting_files_dir, "api.php")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ تم إنشاء api.php")

    def create_config_php(self):
        """إنشاء ملف config.php"""
        content = '''<?php
/**
 * ملف الإعدادات الرئيسي
 */

// منع الوصول المباشر
if (!defined('INCLUDED')) {
    die('Direct access not allowed');
}

// إعدادات قاعدة البيانات Supabase
define('SUPABASE_URL', 'https://ytqxxodyecdeosnqoure.supabase.co');
define('SUPABASE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4');

// إعدادات الموقع
define('SITE_NAME', 'Modetaris');
define('SITE_DESCRIPTION', 'موقع مودات ماين كرافت');
define('DEFAULT_LANGUAGE', 'ar');

// إعدادات الأمان
define('ENABLE_LOGGING', true);
define('LOG_FILE', 'logs/app.log');

// دالة إنشاء مجلدات السجلات
function createLogDirectories() {
    $log_dir = dirname(LOG_FILE);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
}

// دالة تسجيل الأخطاء
function logError($message, $level = 'ERROR') {
    if (!ENABLE_LOGGING) return;

    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] [$level] $message" . PHP_EOL;

    createLogDirectories();
    file_put_contents(LOG_FILE, $log_entry, FILE_APPEND | LOCK_EX);
}

// دالة فحص البيئة
function checkEnvironment() {
    $requirements = [
        'php_version' => '7.4.0',
        'extensions' => ['curl', 'json', 'mbstring'],
        'functions' => ['file_get_contents', 'json_encode', 'json_decode']
    ];

    $errors = [];

    // فحص إصدار PHP
    if (version_compare(PHP_VERSION, $requirements['php_version'], '<')) {
        $errors[] = "PHP version {$requirements['php_version']} or higher required";
    }

    // فحص الإضافات
    foreach ($requirements['extensions'] as $extension) {
        if (!extension_loaded($extension)) {
            $errors[] = "PHP extension '$extension' is required";
        }
    }

    // فحص الدوال
    foreach ($requirements['functions'] as $function) {
        if (!function_exists($function)) {
            $errors[] = "PHP function '$function' is required";
        }
    }

    return empty($errors) ? true : $errors;
}
?>'''

        file_path = os.path.join(self.hosting_files_dir, "config.php")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ تم إنشاء config.php")

    def create_style_css(self):
        """إنشاء ملف style.css"""
        content = '''/*
 * ملف التصميم لصفحة عرض المودات
 */

body {
    font-family: 'Press Start 2P', cursive;
    background-color: #1a1a1a;
    color: white;
}

.pixel-border {
    box-shadow: inset 0 0 0 1px #AAAAAA;
    border: 1px solid #AAAAAA;
}

.mod-header {
    background-color: #2D2D2D;
    color: white;
    padding: 15px;
    text-align: center;
}

.mod-container {
    background-color: #2D2D2D;
}

.nav-button {
    background-color: #FFA500;
    color: white;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    flex-shrink: 0;
}

.thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-shadow: 0 0 5px rgba(255, 165, 0, 0.5), 0 0 10px rgba(255, 215, 0, 0.3);
}

.thumbnail.active {
    border-color: #FFA500;
    box-shadow: 0 0 10px #FFD700, 0 0 20px #DAA520;
}

.pixel-button {
    image-rendering: pixelated;
    background-color: #FFA500;
    color: white;
    border: 2px solid #FFD700;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    cursor: pointer;
    box-shadow: inset -4px -4px 0 0 #d27e00,
               inset 4px 4px 0 0 #ffcb6b,
               0 0 10px #FFD700,
               0 0 20px #FFD700;
    transition: all 0.1s;
    position: relative;
    overflow: hidden;
    animation: pulse-animation 1.5s infinite;
    min-width: 200px;
    min-height: 50px;
}

.pixel-button.downloading {
    background-color: #4CAF50;
    border-color: #45a049;
    animation: none;
    cursor: not-allowed;
}

.pixel-button.downloaded {
    background-color: #2196F3;
    border-color: #1976D2;
    animation: none;
}

.progress-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background-color: #4CAF50;
    width: 0%;
    transition: width 0.3s ease;
    z-index: 1;
}

.download-icon {
    display: inline-block;
    margin-right: 8px;
    font-size: 18px;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes pulse-animation {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.info-label {
    color: #FFA500;
    font-size: 14px;
}

.mod-info {
    font-size: 18px;
    color: white;
    letter-spacing: -1px;
}

.image-glow-effect {
    position: relative;
    overflow: hidden;
    background-color: #FFC300;
}

.new-feature {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: 0 0 15px rgba(255, 165, 0, 0.7); }
    100% { transform: scale(1); }
}

/* تحسينات للهواتف المحمولة */
@media (max-width: 768px) {
    .container {
        padding: 2px;
    }

    .mod-header {
        padding: 10px;
    }

    .mod-header h1 {
        font-size: 18px;
    }

    .pixel-button {
        min-width: 180px;
        font-size: 14px;
        padding: 8px 16px;
    }

    .nav-button {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .thumbnail {
        width: 60px;
        height: 45px;
    }

    .mod-info {
        font-size: 14px;
    }

    .info-label {
        font-size: 12px;
    }
}'''

        file_path = os.path.join(self.hosting_files_dir, "style.css")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ تم إنشاء style.css")

    def create_script_js(self):
        """إنشاء ملف script.js"""
        content = '''/**
 * ملف الجافاسكريبت لصفحة عرض المودات
 */

// متغيرات عامة
let modFileSize = 0;
let downloadedFileName = '';
let downloadStartTime = 0;

// عناصر DOM
const mainModImage = document.getElementById('main-mod-image');
const prevButton = document.getElementById('prev-image');
const nextButton = document.getElementById('next-image');
const thumbnailContainer = document.getElementById('thumbnail-container');

/**
 * تحديث الصورة الرئيسية
 */
function updateImage() {
    if (imageUrls.length > 0) {
        mainModImage.src = imageUrls[currentImageIndex];
    } else {
        mainModImage.src = '';
    }
    updateThumbnails();
}

/**
 * تحديث الصور المصغرة
 */
function updateThumbnails() {
    thumbnailContainer.innerHTML = '';
    if (imageUrls.length === 0) return;

    let startIndex, endIndex;

    if (imageUrls.length <= 3) {
        startIndex = 0;
        endIndex = imageUrls.length - 1;
    } else {
        startIndex = currentImageIndex - 1;
        endIndex = currentImageIndex + 1;

        if (startIndex < 0) {
            startIndex = 0;
            endIndex = 2;
        }
        if (endIndex >= imageUrls.length) {
            endIndex = imageUrls.length - 1;
            startIndex = imageUrls.length - 3;
        }
    }

    for (let i = startIndex; i <= endIndex; i++) {
        const thumbnail = document.createElement('img');
        thumbnail.src = imageUrls[i];
        thumbnail.classList.add('thumbnail', 'pixel-border');
        if (i === currentImageIndex) {
            thumbnail.classList.add('active');
        }
        thumbnail.dataset.index = i;
        thumbnail.addEventListener('click', (event) => {
            currentImageIndex = parseInt(event.target.dataset.index);
            updateImage();
        });
        thumbnailContainer.appendChild(thumbnail);
    }
}

/**
 * التنقل بين الصور
 */
if (prevButton) {
    prevButton.addEventListener('click', () => {
        currentImageIndex = (currentImageIndex - 1 + imageUrls.length) % imageUrls.length;
        updateImage();
    });
}

if (nextButton) {
    nextButton.addEventListener('click', () => {
        currentImageIndex = (currentImageIndex + 1) % imageUrls.length;
        updateImage();
    });
}

/**
 * معالجة التحميل
 */
function handleDownload() {
    if (isDownloading || isDownloaded) {
        if (isDownloaded) {
            openDownloadedFile();
        }
        return;
    }
    startDownload();
}

/**
 * بدء التحميل
 */
function startDownload() {
    isDownloading = true;
    downloadStartTime = Date.now();
    updateDownloadButton();
    simulateDownload();
}

/**
 * محاكاة التحميل
 */
async function simulateDownload() {
    const button = document.getElementById('download-button');
    const progressBar = document.getElementById('progress-bar');
    const downloadText = document.getElementById('download-text');
    const downloadIcon = document.getElementById('download-icon');

    // تقدير حجم الملف
    modFileSize = Math.random() * 50 + 10;
    const modFileExtension = modDownloadUrl.includes('.mcaddon') ? '.mcaddon' : '.mcpack';
    downloadedFileName = `${modTitle.replace(/[^a-zA-Z0-9]/g, '_')}${modFileExtension}`;

    // محاكاة سرعة التحميل
    const downloadSpeed = Math.random() * 5 + 2;
    const totalTime = (modFileSize / downloadSpeed) * 1000;

    let startTime = Date.now();

    const updateProgress = () => {
        const elapsed = Date.now() - startTime;
        downloadProgress = Math.min((elapsed / totalTime) * 100, 100);

        progressBar.style.width = downloadProgress + '%';

        if (downloadProgress < 100) {
            const downloadingText = lang === 'ar' ? 'جاري التحميل...' : 'Downloading...';
            downloadText.textContent = `${downloadingText} ${Math.round(downloadProgress)}%`;

            if (downloadProgress < 30) {
                downloadIcon.innerHTML = '<div class="spinner"></div>';
            } else if (downloadProgress < 70) {
                downloadIcon.textContent = '⬇️';
            } else {
                downloadIcon.textContent = '📦';
            }

            requestAnimationFrame(updateProgress);
        } else {
            completeDownload();
        }
    };

    // بدء التحميل الفعلي
    try {
        const downloadLink = document.createElement('a');
        downloadLink.href = modDownloadUrl;
        downloadLink.download = downloadedFileName || 'mod_file';
        downloadLink.target = '_blank';
        downloadLink.style.display = 'none';

        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        showDownloadStartNotification();
    } catch (error) {
        console.error('Error starting download:', error);
        window.open(modDownloadUrl, '_blank');
    }

    updateProgress();
}

/**
 * إكمال التحميل
 */
function completeDownload() {
    isDownloading = false;
    isDownloaded = true;
    downloadProgress = 100;

    const downloadEndTime = Date.now();
    const totalDownloadTime = (downloadEndTime - downloadStartTime) / 1000;
    const averageSpeed = (modFileSize / totalDownloadTime).toFixed(1);

    if (!downloadedFileName) {
        const modFileExtension = modDownloadUrl.includes('.mcaddon') ? '.mcaddon' : '.mcpack';
        downloadedFileName = `${modTitle.replace(/[^a-zA-Z0-9]/g, '_')}${modFileExtension}`;
    }

    updateDownloadButton();

    const downloadStats = {
        fileSize: modFileSize,
        downloadTime: totalDownloadTime,
        averageSpeed: averageSpeed,
        fileName: downloadedFileName,
        completedAt: new Date().toISOString()
    };

    localStorage.setItem(`download_stats_${modId}`, JSON.stringify(downloadStats));
    showDownloadCompleteNotification(downloadStats);
}

/**
 * تحديث زر التحميل
 */
function updateDownloadButton() {
    const button = document.getElementById('download-button');
    const downloadText = document.getElementById('download-text');
    const downloadIcon = document.getElementById('download-icon');
    const progressBar = document.getElementById('progress-bar');

    if (isDownloading) {
        button.classList.add('downloading');
        button.classList.remove('downloaded');
        const downloadingText = lang === 'ar' ? 'جاري التحميل...' : 'Downloading...';
        downloadText.textContent = `${downloadingText} ${Math.round(downloadProgress)}%`;
        progressBar.style.width = downloadProgress + '%';
    } else if (isDownloaded) {
        button.classList.remove('downloading');
        button.classList.add('downloaded');
        const openText = lang === 'ar' ? 'فتح المود' : 'Open Mod';
        downloadText.textContent = openText;
        downloadIcon.textContent = '📂';
        progressBar.style.width = '100%';
        progressBar.style.backgroundColor = '#2196F3';
    } else {
        button.classList.remove('downloading', 'downloaded');
        const downloadButtonText = lang === 'ar' ? 'تحميل المود' : 'Download Mod';
        downloadText.textContent = downloadButtonText;
        downloadIcon.textContent = '📥';
        progressBar.style.width = '0%';
    }
}

/**
 * فتح الملف المحمل
 */
function openDownloadedFile() {
    const minecraftUrl = `minecraft://import?url=${encodeURIComponent(modDownloadUrl)}`;

    try {
        window.location.href = minecraftUrl;
        showOpeningNotification();

        setTimeout(() => {
            showMinecraftInstructions();
        }, 2000);
    } catch (error) {
        console.error('Error opening Minecraft:', error);
        showMinecraftInstructions();
    }
}

/**
 * إظهار إشعار بدء التحميل
 */
function showDownloadStartNotification() {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 left-4 bg-blue-500 text-white p-3 rounded-lg shadow-lg z-50';
    const startText = lang === 'ar' ? 'بدء التحميل...' : 'Download started...';
    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <span class="text-lg">📥</span>
            <span>${startText}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (document.body.contains(notification)) {
            document.body.removeChild(notification);
        }
    }, 2000);
}

/**
 * إظهار إشعار فتح ماين كرافت
 */
function showOpeningNotification() {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 left-4 bg-blue-500 text-white p-3 rounded-lg shadow-lg z-50';
    const openingText = lang === 'ar' ? 'جاري فتح ماين كرافت...' : 'Opening Minecraft...';
    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <div class="spinner"></div>
            <span>${openingText}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (document.body.contains(notification)) {
            document.body.removeChild(notification);
        }
    }, 3000);
}

/**
 * إظهار إشعار اكتمال التحميل
 */
function showDownloadCompleteNotification(stats = null) {
    const button = document.getElementById('download-button');

    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-70 max-w-sm';
    const successText = lang === 'ar' ? 'تم التحميل بنجاح!' : 'Download Complete!';

    const clickText = lang === 'ar' ? 'انقر على الزر لفتح المود' : 'Click button to open mod';
    notification.innerHTML = `
        <div class="flex items-start space-x-2">
            <span class="text-2xl">✅</span>
            <div class="flex-1">
                <h4 class="font-bold">${successText}</h4>
                <p class="text-sm">${downloadedFileName}</p>
                <p class="text-xs opacity-75 mt-1">${clickText}</p>
            </div>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (document.body.contains(notification)) {
            document.body.removeChild(notification);
        }
    }, 7000);
}

/**
 * إظهار تعليمات ماين كرافت
 */
function showMinecraftInstructions() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-75 z-80 flex items-center justify-center';

    const instructionsTitle = lang === 'ar' ? 'تعليمات التثبيت' : 'Installation Instructions';
    const autoText = lang === 'ar' ? 'إذا لم يفتح ماين كرافت تلقائياً:' : 'If Minecraft did not open automatically:';
    const step1 = lang === 'ar' ? '1. افتح ماين كرافت' : '1. Open Minecraft';
    const step2 = lang === 'ar' ? '2. اذهب إلى الإعدادات' : '2. Go to Settings';
    const step3 = lang === 'ar' ? '3. اختر "التخزين"' : '3. Select "Storage"';
    const step4 = lang === 'ar' ? '4. اختر "استيراد"' : '4. Select "Import"';
    const step5 = lang === 'ar' ? '5. اختر الملف المحمل' : '5. Select the downloaded file';
    const openMinecraftText = lang === 'ar' ? 'فتح ماين كرافت' : 'Open Minecraft';
    const closeText = lang === 'ar' ? 'إغلاق' : 'Close';
    const direction = lang === 'ar' ? 'rtl' : 'ltr';

    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md mx-4 text-center">
            <div class="text-6xl mb-4">🎮</div>
            <h3 class="text-lg font-bold text-gray-800 mb-4">${instructionsTitle}</h3>
            <div class="text-sm text-gray-600 mb-6 text-right" dir="${direction}">
                ${autoText}
                <br><br>
                ${step1}<br>
                ${step2}<br>
                ${step3}<br>
                ${step4}<br>
                ${step5}
            </div>
            <div class="flex space-x-2 justify-center">
                <button onclick="tryOpenMinecraftAgain()" class="bg-green-500 text-white px-4 py-2 rounded">
                    ${openMinecraftText}
                </button>
                <button onclick="closeInstructionsModal()" class="bg-gray-500 text-white px-4 py-2 rounded">
                    ${closeText}
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    window.currentInstructionsModal = modal;
}

/**
 * محاولة فتح ماين كرافت مرة أخرى
 */
function tryOpenMinecraftAgain() {
    const minecraftUrl = `minecraft://import?url=${encodeURIComponent(modDownloadUrl)}`;
    window.open(minecraftUrl, '_blank');
    closeInstructionsModal();
}

/**
 * إغلاق نافذة التعليمات
 */
function closeInstructionsModal() {
    if (window.currentInstructionsModal) {
        document.body.removeChild(window.currentInstructionsModal);
        window.currentInstructionsModal = null;
    }
}

// التحميل الأولي
document.addEventListener('DOMContentLoaded', () => {
    updateImage();
});'''

        file_path = os.path.join(self.hosting_files_dir, "script.js")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ تم إنشاء script.js")

    def create_htaccess(self):
        """إنشاء ملف .htaccess"""
        content = '''# ملف .htaccess لصفحة عرض المودات

# تفعيل إعادة الكتابة
RewriteEngine On

# إعادة توجيه طلبات API
RewriteRule ^api/mod$ api.php?path=/mod [QSA,L]
RewriteRule ^api/file-info$ api.php?path=/file-info [QSA,L]

# إعادة توجيه صفحات تفاصيل المود
RewriteRule ^mod-details$ index.php [QSA,L]
RewriteRule ^telegram-mod-details$ index.php [QSA,L]

# إعادة توجيه الصفحة الرئيسية
RewriteRule ^$ index.php [QSA,L]

# حماية الملفات الحساسة
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files ".env">
    Order allow,deny
    Deny from all
</Files>

# تحسين الأداء
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# إعدادات الأمان
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# منع الوصول المباشر للملفات
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع عرض قائمة الملفات
Options -Indexes'''

        file_path = os.path.join(self.hosting_files_dir, ".htaccess")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ تم إنشاء .htaccess")

    def create_deploy_php(self):
        """إنشاء ملف deploy.php"""
        content = '''<?php
/**
 * صفحة اختبار التثبيت
 */

if (!isset($_GET['setup']) || $_GET['setup'] !== 'true') {
    die('Access denied. Add ?setup=true to run setup.');
}

// إعدادات قاعدة البيانات
$SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
$SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد موقع عرض المودات</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .status-ok { color: #10b981; }
        .status-error { color: #ef4444; }
        .status-warning { color: #f59e0b; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-6 max-w-4xl">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-center mb-6 text-gray-800">
                🚀 إعداد موقع عرض مودات ماين كرافت
            </h1>

            <div class="space-y-6">
                <!-- فحص البيئة -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h2 class="text-xl font-semibold mb-4">🔍 فحص البيئة</h2>
                    <div class="space-y-2">
                        <?php
                        // فحص إصدار PHP
                        echo '<div class="status-ok">✅ إصدار PHP: ' . PHP_VERSION . '</div>';

                        // فحص الإضافات المطلوبة
                        $extensions = ['curl', 'json', 'mbstring'];
                        foreach ($extensions as $ext) {
                            if (extension_loaded($ext)) {
                                echo '<div class="status-ok">✅ إضافة ' . $ext . ' متوفرة</div>';
                            } else {
                                echo '<div class="status-error">❌ إضافة ' . $ext . ' غير متوفرة</div>';
                            }
                        }
                        ?>
                    </div>
                </div>

                <!-- فحص الملفات -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h2 class="text-xl font-semibold mb-4">📁 فحص الملفات</h2>
                    <div class="space-y-2">
                        <?php
                        $required_files = [
                            'index.php' => 'الصفحة الرئيسية',
                            'api.php' => 'واجهة برمجة التطبيقات',
                            'style.css' => 'ملف التصميم',
                            'script.js' => 'ملف الجافاسكريبت',
                            '.htaccess' => 'إعدادات الخادم'
                        ];

                        foreach ($required_files as $file => $description) {
                            if (file_exists($file)) {
                                $size = filesize($file);
                                echo '<div class="status-ok">✅ ' . $description . ' (' . $file . ') - ' . number_format($size) . ' بايت</div>';
                            } else {
                                echo '<div class="status-error">❌ ' . $description . ' (' . $file . ') غير موجود</div>';
                            }
                        }
                        ?>
                    </div>
                </div>

                <!-- فحص الاتصال بقاعدة البيانات -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h2 class="text-xl font-semibold mb-4">🗄️ فحص قاعدة البيانات</h2>
                    <div class="space-y-2">
                        <?php
                        // اختبار الاتصال بـ Supabase
                        $test_url = $SUPABASE_URL . '/rest/v1/minemods?limit=1';
                        $headers = [
                            'apikey: ' . $SUPABASE_KEY,
                            'Authorization: Bearer ' . $SUPABASE_KEY
                        ];

                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $test_url);
                        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

                        $response = curl_exec($ch);
                        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        $error = curl_error($ch);
                        curl_close($ch);

                        if ($http_code === 200) {
                            $data = json_decode($response, true);
                            echo '<div class="status-ok">✅ الاتصال بقاعدة البيانات ناجح</div>';
                            echo '<div class="status-ok">✅ عدد المودات المتاحة: ' . count($data) . '</div>';
                        } else {
                            echo '<div class="status-error">❌ فشل الاتصال بقاعدة البيانات</div>';
                            echo '<div class="status-error">كود الخطأ: ' . $http_code . '</div>';
                            if ($error) {
                                echo '<div class="status-error">تفاصيل الخطأ: ' . htmlspecialchars($error) . '</div>';
                            }
                        }
                        ?>
                    </div>
                </div>

                <!-- روابط الاختبار -->
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h2 class="text-xl font-semibold mb-4">🔗 روابط الاختبار</h2>
                    <div class="space-y-2">
                        <?php
                        $base_url = 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
                        $test_mod_id = 1; // يمكن تغييره حسب البيانات المتاحة

                        echo '<div><a href="' . $base_url . '/" class="text-blue-600 hover:underline" target="_blank">🏠 الصفحة الرئيسية</a></div>';
                        echo '<div><a href="' . $base_url . '/?id=' . $test_mod_id . '&lang=ar" class="text-blue-600 hover:underline" target="_blank">🎮 اختبار عرض مود (عربي)</a></div>';
                        echo '<div><a href="' . $base_url . '/?id=' . $test_mod_id . '&lang=en" class="text-blue-600 hover:underline" target="_blank">🎮 اختبار عرض مود (إنجليزي)</a></div>';
                        echo '<div><a href="' . $base_url . '/api.php?path=/mod&id=' . $test_mod_id . '" class="text-blue-600 hover:underline" target="_blank">🔌 اختبار API</a></div>';
                        ?>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="flex space-x-4 justify-center">
                    <a href="/" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                        🏠 الذهاب للموقع
                    </a>
                    <button onclick="location.reload()" class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600">
                        🔄 إعادة الفحص
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>'''

        file_path = os.path.join(self.hosting_files_dir, "deploy.php")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ تم إنشاء deploy.php")

    def create_error_pages(self):
        """إنشاء صفحات الخطأ"""
        # صفحة 404
        content_404 = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - الصفحة غير موجودة</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Press Start 2P', cursive; background-color: #1a1a1a; color: white; }
        .error-icon { font-size: 120px; animation: bounce 2s infinite; }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-30px); }
            60% { transform: translateY(-15px); }
        }
    </style>
</head>
<body>
    <header class="bg-yellow-500 text-white p-4 flex justify-center items-center">
        <div class="font-bold text-2xl">Modetaris</div>
    </header>
    <div class="container mx-auto p-4 min-h-screen flex items-center justify-center">
        <div class="bg-gray-800 border border-gray-600 p-8 text-center max-w-md">
            <div class="error-icon mb-6">🎮</div>
            <h1 class="text-4xl mb-4 text-red-400">404</h1>
            <h2 class="text-xl mb-4">الصفحة غير موجودة</h2>
            <p class="text-sm mb-6 leading-relaxed">
                عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
            </p>
            <div class="space-y-4">
                <a href="/" class="bg-orange-500 text-white px-6 py-2 rounded block">
                    🏠 العودة للصفحة الرئيسية
                </a>
                <button onclick="history.back()" class="bg-gray-500 text-white px-6 py-2 rounded block w-full">
                    ⬅️ العودة للصفحة السابقة
                </button>
            </div>
        </div>
    </div>
</body>
</html>'''

        file_path = os.path.join(self.hosting_files_dir, "404.html")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content_404)

        # صفحة 500
        content_500 = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 - خطأ في الخادم</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Press Start 2P', cursive; background-color: #1a1a1a; color: white; }
        .error-icon { font-size: 120px; animation: shake 1s infinite; }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
            20%, 40%, 60%, 80% { transform: translateX(10px); }
        }
    </style>
</head>
<body>
    <header class="bg-yellow-500 text-white p-4 flex justify-center items-center">
        <div class="font-bold text-2xl">Modetaris</div>
    </header>
    <div class="container mx-auto p-4 min-h-screen flex items-center justify-center">
        <div class="bg-gray-800 border border-gray-600 p-8 text-center max-w-md">
            <div class="error-icon mb-6">⚠️</div>
            <h1 class="text-4xl mb-4 text-red-400">500</h1>
            <h2 class="text-xl mb-4">خطأ في الخادم</h2>
            <p class="text-sm mb-6 leading-relaxed">
                عذراً، حدث خطأ في الخادم. نحن نعمل على إصلاح المشكلة.
            </p>
            <div class="space-y-4">
                <button onclick="location.reload()" class="bg-green-500 text-white px-6 py-2 rounded block w-full">
                    🔄 إعادة المحاولة
                </button>
                <a href="/" class="bg-orange-500 text-white px-6 py-2 rounded block">
                    🏠 العودة للصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>
</body>
</html>'''

        file_path = os.path.join(self.hosting_files_dir, "500.html")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content_500)

        print("✅ تم إنشاء صفحات الخطأ (404.html, 500.html)")

    def create_seo_files(self):
        """إنشاء ملفات SEO"""
        # ملف robots.txt
        robots_content = '''# robots.txt for Minecraft Mods Site

User-agent: *
Allow: /
Allow: /style.css
Allow: /script.js

# منع الوصول للملفات الحساسة
Disallow: /config.php
Disallow: /api.php
Disallow: /deploy.php
Disallow: /logs/
Disallow: /.htaccess

# منع الوصول للملفات المؤقتة
Disallow: /*.tmp
Disallow: /*.log
Disallow: /*.bak

# معلومات إضافية
# Sitemap: https://your-domain.com/sitemap.xml
# Crawl-delay: 1'''

        file_path = os.path.join(self.hosting_files_dir, "robots.txt")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(robots_content)

        # ملف sitemap.xml
        sitemap_content = '''<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <!-- الصفحة الرئيسية -->
    <url>
        <loc>https://your-domain.com/</loc>
        <lastmod>2024-01-01</lastmod>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
    </url>

    <!-- صفحات المودات (يتم إنشاؤها ديناميكياً) -->
    <!-- يمكن إضافة المزيد من الروابط هنا -->
</urlset>'''

        file_path = os.path.join(self.hosting_files_dir, "sitemap.xml")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(sitemap_content)

        print("✅ تم إنشاء ملفات SEO (robots.txt, sitemap.xml)")

    def create_documentation(self):
        """إنشاء ملفات الوثائق"""
        # ملف README.md
        readme_content = '''# 🎮 صفحة عرض مودات ماين كرافت

## 📋 نظرة عامة

هذا المشروع عبارة عن صفحة ويب مستقلة لعرض تفاصيل مودات ماين كرافت، مصممة خصيصاً للعمل على الاستضافة المجانية.

## 🚀 التثبيت السريع

### 1. رفع الملفات
- ارفع جميع الملفات إلى مجلد htdocs في استضافتك
- تأكد من رفع ملف .htaccess

### 2. اختبار التثبيت
- اذهب إلى: https://your-domain.com/deploy.php?setup=true
- تحقق من جميع الفحوصات

### 3. اختبار الموقع
- اختبر عرض مود: https://your-domain.com/?id=1&lang=ar

## 🔧 الإعدادات

### تحديث بيانات Supabase
في ملفي index.php و api.php، غيّر:
```php
$SUPABASE_URL = "رابط_قاعدة_البيانات_الخاصة_بك";
$SUPABASE_KEY = "مفتاح_API_الخاص_بك";
```

## 📞 الدعم

للحصول على المساعدة:
1. راجع صفحة الإعداد: your-domain.com/deploy.php?setup=true
2. تحقق من إعدادات Supabase
3. تأكد من رفع جميع الملفات

---

**🎉 مبروك! موقعك الآن جاهز للعمل 24/7**'''

        file_path = os.path.join(self.hosting_files_dir, "README.md")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)

        # ملف INSTALLATION.md
        installation_content = '''# دليل التثبيت المفصل

## 🎯 الهدف
تثبيت صفحة عرض المودات على الاستضافة المجانية

## 📋 المتطلبات
- استضافة تدعم PHP 7.4+
- دعم cURL
- حساب Supabase نشط

## 🚀 خطوات التثبيت

### 1. إعداد الاستضافة
1. سجل في استضافة مجانية (InfinityFree, Pella, إلخ)
2. أنشئ موقع جديد
3. احصل على تفاصيل FTP

### 2. رفع الملفات
1. ارفع جميع الملفات إلى htdocs
2. تأكد من رفع .htaccess
3. احتفظ بهيكل المجلدات

### 3. تحديث الإعدادات
1. افتح index.php
2. غيّر SUPABASE_URL و SUPABASE_KEY
3. احفظ التغييرات

### 4. اختبار التثبيت
1. اذهب إلى deploy.php?setup=true
2. تحقق من جميع الفحوصات
3. اختبر عرض مود

### 5. تحديث البوت
1. غيّر رابط الموقع في البوت
2. اختبر الروابط الجديدة

## ✅ قائمة التحقق
- [ ] تم رفع جميع الملفات
- [ ] تم تحديث إعدادات Supabase
- [ ] صفحة deploy.php تعمل
- [ ] عرض المودات يعمل
- [ ] تم تحديث البوت

## 🆘 المساعدة
إذا واجهت مشاكل، راجع deploy.php?setup=true للتشخيص.'''

        file_path = os.path.join(self.hosting_files_dir, "INSTALLATION.md")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(installation_content)

        print("✅ تم إنشاء ملفات الوثائق (README.md, INSTALLATION.md)")

    def create_zip_package(self):
        """إنشاء ملف مضغوط للرفع"""
        print("\n📦 إنشاء ملف مضغوط للرفع...")

        zip_filename = f"hosting_files_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"

        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_name in self.required_files.keys():
                file_path = os.path.join(self.hosting_files_dir, file_name)
                if os.path.exists(file_path):
                    zipf.write(file_path, file_name)
                    print(f"  📄 تم إضافة {file_name}")

        file_size = os.path.getsize(zip_filename)
        print(f"✅ تم إنشاء الملف المضغوط: {zip_filename} ({file_size} بايت)")
        return zip_filename

    def generate_upload_instructions(self, zip_filename):
        """إنشاء تعليمات الرفع"""
        print("\n📋 إنشاء تعليمات الرفع...")

        instructions = f"""
# 🚀 تعليمات رفع الملفات على الاستضافة

## 📦 الملف المضغوط
تم إنشاء الملف: {zip_filename}

## 🌐 خطوات الرفع على InfinityFree

### 1. تسجيل الدخول
- اذهب إلى https://www.infinityfree.com
- سجل دخول لحسابك
- اختر موقعك

### 2. رفع الملفات
#### الطريقة الأولى: File Manager
1. اضغط على "File Manager" في لوحة التحكم
2. ادخل لمجلد "htdocs"
3. احذف أي ملفات موجودة (مثل default.php)
4. اضغط على "Upload Files"
5. ارفع الملف المضغوط {zip_filename}
6. فك الضغط عن الملف
7. انقل جميع الملفات إلى مجلد htdocs مباشرة

#### الطريقة الثانية: FTP
1. استخدم FileZilla أو أي برنامج FTP
2. اتصل بالخادم باستخدام بيانات FTP
3. ادخل لمجلد htdocs
4. ارفع جميع الملفات من مجلد hosting_files

### 3. التحقق من الرفع
- تأكد من وجود جميع الملفات في htdocs
- تأكد من رفع ملف .htaccess

## 🔧 خطوات ما بعد الرفع

### 1. اختبار التثبيت
اذهب إلى: https://your-domain.com/deploy.php?setup=true

### 2. تحديث إعدادات Supabase (إذا لزم الأمر)
افتح index.php و api.php وغيّر:
```php
$SUPABASE_URL = "رابط_قاعدة_البيانات_الخاصة_بك";
$SUPABASE_KEY = "مفتاح_API_الخاص_بك";
```

### 3. اختبار الموقع
- الصفحة الرئيسية: https://your-domain.com
- اختبار مود: https://your-domain.com/?id=1&lang=ar
- اختبار API: https://your-domain.com/api.php?path=/mod&id=1

### 4. تحديث البوت
في مجلد البوت، غيّر رابط الموقع في الملفات:
- main.py
- .env

## ✅ قائمة التحقق النهائية
- [ ] تم رفع جميع الملفات
- [ ] صفحة deploy.php تظهر جميع الفحوصات ناجحة
- [ ] عرض المودات يعمل بشكل صحيح
- [ ] API يرجع بيانات صحيحة
- [ ] تم تحديث البوت

## 🆘 في حالة المشاكل
1. راجع صفحة deploy.php?setup=true
2. تحقق من أخطاء PHP في لوحة التحكم
3. تأكد من إعدادات Supabase
4. تأكد من رفع جميع الملفات

---
تم إنشاء هذه التعليمات تلقائياً في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        instructions_file = f"upload_instructions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(instructions_file, 'w', encoding='utf-8') as f:
            f.write(instructions)

        print(f"✅ تم إنشاء ملف التعليمات: {instructions_file}")
        return instructions_file

    def update_bot_files(self):
        """تحديث ملفات البوت"""
        print("\n🤖 تحديث ملفات البوت...")

        website_url = input("🌐 أدخل رابط موقعك (مثال: https://sendaddons.fwh.is): ").strip()

        if not website_url:
            print("⚠️ لم يتم إدخال رابط الموقع، تخطي تحديث البوت")
            return

        website_url = website_url.rstrip('/')

        # إنشاء سكريبت تحديث البوت
        update_script = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تحديث البوت لاستخدام الموقع الجديد
تم إنشاؤه تلقائياً بواسطة أداة الإعداد
"""

import os
import re
import shutil
from datetime import datetime

def backup_file(file_path):
    """إنشاء نسخة احتياطية من الملف"""
    if os.path.exists(file_path):
        backup_path = f"{{file_path}}.backup_{{datetime.now().strftime('%Y%m%d_%H%M%S')}}"
        shutil.copy2(file_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {{backup_path}}")
        return backup_path
    return None

def update_main_py():
    """تحديث ملف main.py"""
    main_file = "main.py"

    if not os.path.exists(main_file):
        print(f"❌ ملف {{main_file}} غير موجود")
        return False

    backup_file(main_file)

    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # تحديث رابط الموقع
    old_patterns = [
        r'(WEB_SERVER_URL\s*=\s*["\'])([^"\']+)(["\'])',
        r'(https?://[^"\'\\s]+)',
    ]

    new_url = "{website_url}"

    for pattern in old_patterns:
        content = re.sub(pattern, lambda m: m.group(1) + new_url + m.group(3) if len(m.groups()) == 3 else new_url, content)

    with open(main_file, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"✅ تم تحديث {{main_file}}")
    return True

def update_env_file():
    """تحديث ملف .env"""
    env_file = ".env"

    if not os.path.exists(env_file):
        print(f"❌ ملف {{env_file}} غير موجود")
        return False

    backup_file(env_file)

    with open(env_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    updated = False
    for i, line in enumerate(lines):
        if line.startswith('WEB_SERVER_URL='):
            lines[i] = f"WEB_SERVER_URL={website_url}\\n"
            updated = True
            break

    if not updated:
        lines.append(f"\\nWEB_SERVER_URL={website_url}\\n")

    with open(env_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)

    print(f"✅ تم تحديث {{env_file}}")
    return True

def main():
    print("🔄 تحديث البوت لاستخدام الموقع الجديد...")
    print(f"🌐 الموقع الجديد: {website_url}")

    success = True

    if not update_main_py():
        success = False

    if not update_env_file():
        success = False

    if success:
        print("\\n✅ تم تحديث البوت بنجاح!")
        print("🎉 البوت الآن يستخدم الموقع الجديد")
        print("\\n📋 الخطوات التالية:")
        print("1. اختبر البوت: python start_bot_final.py")
        print("2. تحقق من عمل الروابط")
        print(f"3. اختبر الموقع: {website_url}/deploy.php?setup=true")
    else:
        print("\\n❌ حدثت أخطاء أثناء التحديث")

if __name__ == "__main__":
    main()
'''

        update_script_file = os.path.join(self.hosting_files_dir, "update_bot.py")
        with open(update_script_file, 'w', encoding='utf-8') as f:
            f.write(update_script)

        print(f"✅ تم إنشاء سكريبت تحديث البوت: {update_script_file}")
        print(f"🌐 الموقع المحدد: {website_url}")

        return website_url

    def run_interactive_menu(self):
        """تشغيل القائمة التفاعلية"""
        while True:
            print("\n" + "=" * 70)
            print("🎮 قائمة أداة إعداد صفحة عرض المودات")
            print("=" * 70)
            print("1. 🔍 فحص الملفات الموجودة")
            print("2. 🔧 إنشاء الملفات المفقودة")
            print("3. 📦 إنشاء ملف مضغوط للرفع")
            print("4. 📋 إنشاء تعليمات الرفع")
            print("5. 🤖 تحديث ملفات البوت")
            print("6. 🚀 تنفيذ جميع الخطوات")
            print("7. ❌ خروج")
            print("=" * 70)

            choice = input("اختر رقم العملية (1-7): ").strip()

            if choice == "1":
                self.check_files()

            elif choice == "2":
                self.create_missing_files()

            elif choice == "3":
                if self.check_files():
                    zip_file = self.create_zip_package()
                    print(f"\n✅ تم إنشاء الملف المضغوط: {zip_file}")
                else:
                    print("\n❌ يجب إنشاء الملفات المفقودة أولاً")

            elif choice == "4":
                if self.check_files():
                    zip_file = self.create_zip_package()
                    instructions_file = self.generate_upload_instructions(zip_file)
                    print(f"\n✅ تم إنشاء التعليمات: {instructions_file}")
                else:
                    print("\n❌ يجب إنشاء الملفات المفقودة أولاً")

            elif choice == "5":
                website_url = self.update_bot_files()
                if website_url:
                    print(f"\n✅ تم تحديث البوت للموقع: {website_url}")

            elif choice == "6":
                print("\n🚀 تنفيذ جميع الخطوات...")

                # فحص الملفات
                files_exist = self.check_files()

                # إنشاء الملفات المفقودة
                if not files_exist:
                    self.create_missing_files()

                # إنشاء الملف المضغوط
                zip_file = self.create_zip_package()

                # إنشاء التعليمات
                instructions_file = self.generate_upload_instructions(zip_file)

                # تحديث البوت
                website_url = self.update_bot_files()

                print("\n" + "=" * 70)
                print("🎉 تم إنجاز جميع الخطوات بنجاح!")
                print("=" * 70)
                print(f"📦 الملف المضغوط: {zip_file}")
                print(f"📋 ملف التعليمات: {instructions_file}")
                if website_url:
                    print(f"🌐 رابط الموقع: {website_url}")
                print("\n📋 الخطوات التالية:")
                print("1. ارفع الملف المضغوط على استضافتك")
                print("2. فك الضغط في مجلد htdocs")
                print("3. اختبر الموقع باستخدام deploy.php?setup=true")
                print("4. شغّل سكريبت تحديث البوت")
                print("=" * 70)

            elif choice == "7":
                print("\n👋 شكراً لاستخدام أداة إعداد صفحة عرض المودات!")
                print("🎮 نتمنى لك التوفيق في مشروعك!")
                break

            else:
                print("\n❌ اختيار غير صحيح، يرجى اختيار رقم من 1 إلى 7")

            input("\n⏸️ اضغط Enter للمتابعة...")

def main():
    """الدالة الرئيسية"""
    tool = HostingSetupTool()
    tool.print_header()

    print("\n🎯 هذه الأداة ستساعدك في:")
    print("   • إنشاء جميع الملفات المطلوبة لصفحة عرض المودات")
    print("   • تحضير ملف مضغوط جاهز للرفع على الاستضافة")
    print("   • إنشاء تعليمات مفصلة للتثبيت")
    print("   • تحديث البوت ليستخدم الموقع الجديد")
    print("   • ضمان عمل الموقع 24/7 بدون مشاكل")

    print("\n🌟 المميزات:")
    print("   ✅ يعمل على جميع الاستضافات المجانية")
    print("   ✅ لا يحتاج خادم محلي أو ngrok")
    print("   ✅ دعم كامل للغتين العربية والإنجليزية")
    print("   ✅ نظام إعلانات ومهام متقدم")
    print("   ✅ تصميم متجاوب لجميع الأجهزة")

    # بدء القائمة التفاعلية
    tool.run_interactive_menu()

if __name__ == "__main__":
    main()
