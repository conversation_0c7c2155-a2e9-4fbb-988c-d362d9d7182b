#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة نشر البوت على Pella (استضافة مجانية)
"""

import os
import json
import zipfile
import shutil
from datetime import datetime

def create_deployment_package():
    """إنشاء حزمة النشر"""
    print("📦 إنشاء حزمة النشر...")
    
    # الملفات المطلوبة للنشر
    required_files = [
        'main.py',
        'web_server.py',
        'supabase_client.py',
        'requirements.txt',
        '.env',
        'static/',
        'templates/'
    ]
    
    # إنشاء مجلد مؤقت
    deploy_dir = 'deployment_package'
    if os.path.exists(deploy_dir):
        shutil.rmtree(deploy_dir)
    os.makedirs(deploy_dir)
    
    # نسخ الملفات
    for item in required_files:
        if os.path.exists(item):
            if os.path.isfile(item):
                shutil.copy2(item, deploy_dir)
                print(f"✅ تم نسخ: {item}")
            elif os.path.isdir(item):
                shutil.copytree(item, os.path.join(deploy_dir, item))
                print(f"✅ تم نسخ مجلد: {item}")
        else:
            print(f"⚠️ ملف غير موجود: {item}")
    
    return deploy_dir

def create_pella_config():
    """إنشاء ملف إعداد Pella"""
    config = {
        "name": "telegram-mod-bot",
        "version": "1.0.0",
        "description": "Telegram Bot for Mod Publishing",
        "main": "main.py",
        "scripts": {
            "start": "python main.py",
            "web": "python web_server.py"
        },
        "runtime": {
            "python": "3.9"
        },
        "resources": {
            "cpu": "0.1",
            "memory": "100MB",
            "disk": "5GB"
        },
        "env": {
            "PORT": "5001",
            "ENVIRONMENT": "production"
        },
        "buildpacks": [
            "heroku/python"
        ]
    }
    
    with open('deployment_package/pella.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ تم إنشاء ملف pella.json")

def create_procfile():
    """إنشاء Procfile لـ Pella"""
    procfile_content = """web: python web_server.py
bot: python main.py"""
    
    with open('deployment_package/Procfile', 'w') as f:
        f.write(procfile_content)
    
    print("✅ تم إنشاء Procfile")

def update_env_for_production():
    """تحديث ملف .env للإنتاج"""
    env_file = 'deployment_package/.env'
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إزالة رابط ngrok وتعيين متغير للإنتاج
        content = content.replace('WEB_SERVER_URL=https://3cf6-41-188-116-40.ngrok-free.app', 
                                'WEB_SERVER_URL=${PELLA_APP_URL}')
        content += '\n# Production settings\nPORT=5001\nENVIRONMENT=production\n'
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث ملف .env للإنتاج")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث .env: {e}")

def create_requirements_txt():
    """إنشاء ملف requirements.txt محدث"""
    requirements = """python-telegram-bot==20.7
supabase==2.3.4
python-dotenv==1.0.0
flask==3.0.0
requests==2.31.0
Pillow==10.1.0
google-generativeai==0.3.2
firebase-admin==6.4.0
cryptography==41.0.8
PyJWT==2.8.0
gunicorn==21.2.0"""
    
    with open('deployment_package/requirements.txt', 'w') as f:
        f.write(requirements)
    
    print("✅ تم إنشاء requirements.txt")

def create_deployment_guide():
    """إنشاء دليل النشر"""
    guide = """# 🚀 دليل نشر البوت على Pella

## 📋 الخطوات:

### 1. إنشاء حساب على Pella
- اذهب إلى: https://pella.io
- أنشئ حساب مجاني
- احصل على 0.1 CPU, 100 MB RAM, 5 GB Disk

### 2. رفع الملفات
```bash
# ضغط المجلد
zip -r telegram-bot.zip deployment_package/

# رفع على Pella
# استخدم واجهة الويب أو CLI
```

### 3. إعداد متغيرات البيئة
في لوحة تحكم Pella، أضف:
- BOT_TOKEN=your_bot_token
- SUPABASE_URL=your_supabase_url
- SUPABASE_KEY=your_supabase_key
- GEMINI_API_KEY=your_gemini_key
- WEB_SERVER_URL=https://your-app.pella.io

### 4. تشغيل التطبيق
```bash
# تشغيل الخادم
pella deploy

# مراقبة السجلات
pella logs --tail
```

## 🔧 إعدادات مهمة:

### Procfile:
```
web: python web_server.py
bot: python main.py
```

### pella.json:
```json
{
  "name": "telegram-mod-bot",
  "runtime": {"python": "3.9"},
  "resources": {
    "cpu": "0.1",
    "memory": "100MB",
    "disk": "5GB"
  }
}
```

## 🌐 الوصول للبوت:
بعد النشر، البوت سيكون متاح على:
- https://your-app.pella.io

## 🔄 التحديثات:
لتحديث البوت:
```bash
# تحديث الكود
git push pella main

# أو رفع ملف جديد
pella deploy --file telegram-bot.zip
```

## 📱 اختبار البوت:
1. تأكد من أن البوت يرد على الرسائل
2. اختبر أزرار صفحات المودات
3. تحقق من عمل الخادم على الرابط الجديد

## ⚠️ ملاحظات مهمة:
- احتفظ بنسخة احتياطية من ملف .env
- لا تشارك معلومات الحساب مع أحد
- راقب استخدام الموارد (CPU/RAM/Disk)
- استخدم HTTPS دائماً للأمان

## 🆘 في حالة المشاكل:
1. تحقق من السجلات: pella logs
2. تأكد من متغيرات البيئة
3. اختبر الاتصال بقاعدة البيانات
4. تواصل مع الدعم: <EMAIL>
"""
    
    with open('DEPLOYMENT_GUIDE.md', 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("✅ تم إنشاء دليل النشر: DEPLOYMENT_GUIDE.md")

def main():
    """الدالة الرئيسية"""
    print("🚀 إعداد البوت للنشر على Pella...")
    print("=" * 50)
    
    # إنشاء حزمة النشر
    deploy_dir = create_deployment_package()
    
    # إنشاء ملفات الإعداد
    create_pella_config()
    create_procfile()
    create_requirements_txt()
    update_env_for_production()
    create_deployment_guide()
    
    # إنشاء ملف مضغوط
    print("\n📦 إنشاء ملف مضغوط...")
    with zipfile.ZipFile('telegram-bot-pella.zip', 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(deploy_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, deploy_dir)
                zipf.write(file_path, arcname)
    
    print("✅ تم إنشاء: telegram-bot-pella.zip")
    
    print("\n" + "=" * 50)
    print("🎉 تم إعداد البوت للنشر بنجاح!")
    print("\n📋 الخطوات التالية:")
    print("1. اذهب إلى https://pella.io")
    print("2. أنشئ حساب مجاني")
    print("3. ارفع ملف telegram-bot-pella.zip")
    print("4. اقرأ DEPLOYMENT_GUIDE.md للتفاصيل")
    print("\n🌐 بعد النشر، البوت سيعمل 24/7 بدون ngrok!")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
    
    input("\n📱 اضغط Enter للخروج...")
