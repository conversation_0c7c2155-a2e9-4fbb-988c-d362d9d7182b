#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص مشاكل البوت
Bot Issues Diagnostic Tool
"""

import os
import json
import logging
import re
from datetime import datetime, timedelta

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BotDiagnostic:
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
        
    def check_caption_length_issues(self):
        """فحص مشاكل طول الكابشن"""
        logger.info("🔍 فحص مشاكل طول الكابشن...")
        
        try:
            # فحص الكود للتأكد من التحسينات
            with open('main.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
            # فحص وجود الإعدادات المحسنة
            if 'MAX_CAPTION_LENGTH = 900' in content:
                self.fixes_applied.append("✅ تم تطبيق إعدادات طول الكابشن المحسنة")
            else:
                self.issues_found.append("❌ إعدادات طول الكابشن غير محسنة")
                
            if 'SAFE_CAPTION_BUFFER' in content:
                self.fixes_applied.append("✅ تم إضافة هامش الأمان للكابشن")
            else:
                self.issues_found.append("❌ هامش الأمان للكابشن مفقود")
                
        except Exception as e:
            self.issues_found.append(f"❌ خطأ في فحص ملف main.py: {e}")
    
    def check_timeout_settings(self):
        """فحص إعدادات timeout"""
        logger.info("🔍 فحص إعدادات timeout...")
        
        try:
            with open('main.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
            # فحص وجود إعدادات timeout محسنة
            timeout_patterns = [
                r'read_timeout\(30\)',
                r'write_timeout\(30\)',
                r'connect_timeout\(30\)',
                r'pool_timeout\(30\)'
            ]
            
            found_timeouts = 0
            for pattern in timeout_patterns:
                if re.search(pattern, content):
                    found_timeouts += 1
                    
            if found_timeouts >= 3:
                self.fixes_applied.append("✅ تم تطبيق إعدادات timeout محسنة")
            else:
                self.issues_found.append("❌ إعدادات timeout غير محسنة")
                
        except Exception as e:
            self.issues_found.append(f"❌ خطأ في فحص إعدادات timeout: {e}")
    
    def check_button_fallback(self):
        """فحص نظام fallback للأزرار"""
        logger.info("🔍 فحص نظام fallback للأزرار...")
        
        try:
            with open('main.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'button_type_invalid' in content.lower():
                self.fixes_applied.append("✅ تم إضافة معالجة button_type_invalid")
            else:
                self.issues_found.append("❌ معالجة button_type_invalid مفقودة")
                
            if '_build_mod_post_content_fallback' in content:
                self.fixes_applied.append("✅ تم إضافة دالة fallback للأزرار")
            else:
                self.issues_found.append("❌ دالة fallback للأزرار مفقودة")
                
        except Exception as e:
            self.issues_found.append(f"❌ خطأ في فحص نظام fallback: {e}")
    
    def check_mod_id_resolution(self):
        """فحص دالة البحث عن معرف المود"""
        logger.info("🔍 فحص دالة البحث عن معرف المود...")
        
        try:
            with open('main.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'find_full_mod_id' in content:
                self.fixes_applied.append("✅ تم إضافة دالة البحث عن معرف المود")
            else:
                self.issues_found.append("❌ دالة البحث عن معرف المود مفقودة")
                
        except Exception as e:
            self.issues_found.append(f"❌ خطأ في فحص دالة البحث: {e}")
    
    def check_web_server_fixes(self):
        """فحص إصلاحات خادم الويب"""
        logger.info("🔍 فحص إصلاحات خادم الويب...")
        
        try:
            with open('web_server.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'update_user_activity' in content:
                self.fixes_applied.append("✅ تم إضافة دالة تحديث نشاط المستخدم")
            else:
                self.issues_found.append("❌ دالة تحديث نشاط المستخدم مفقودة")
                
            if 'Invalid mod ID format' in content:
                self.fixes_applied.append("✅ تم تحسين معالجة أخطاء API")
            else:
                self.issues_found.append("❌ معالجة أخطاء API غير محسنة")
                
        except Exception as e:
            self.issues_found.append(f"❌ خطأ في فحص خادم الويب: {e}")
    
    def check_log_files(self):
        """فحص ملفات السجل للأخطاء الحديثة"""
        logger.info("🔍 فحص ملفات السجل...")
        
        log_files = ['bot.log', 'telegram_bot.log', 'app.log']
        recent_errors = []
        
        for log_file in log_files:
            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        
                    # فحص آخر 100 سطر للأخطاء الحديثة
                    for line in lines[-100:]:
                        if any(error in line.lower() for error in 
                              ['error', 'timeout', 'failed', 'exception']):
                            recent_errors.append(line.strip())
                            
                except Exception as e:
                    logger.warning(f"فشل قراءة ملف السجل {log_file}: {e}")
        
        if recent_errors:
            self.issues_found.append(f"⚠️ وجدت {len(recent_errors)} أخطاء حديثة في السجلات")
        else:
            self.fixes_applied.append("✅ لا توجد أخطاء حديثة في السجلات")
    
    def check_environment_variables(self):
        """فحص متغيرات البيئة"""
        logger.info("🔍 فحص متغيرات البيئة...")
        
        required_vars = ['BOT_TOKEN', 'ADMIN_CHAT_ID', 'SUPABASE_URL', 'SUPABASE_KEY']
        missing_vars = []
        
        for var in required_vars:
            if not os.environ.get(var):
                missing_vars.append(var)
        
        if missing_vars:
            self.issues_found.append(f"❌ متغيرات بيئة مفقودة: {missing_vars}")
        else:
            self.fixes_applied.append("✅ جميع متغيرات البيئة متوفرة")
    
    def generate_report(self):
        """إنشاء تقرير التشخيص"""
        logger.info("📊 إنشاء تقرير التشخيص...")
        
        report = f"""
# 🔍 تقرير تشخيص البوت
## Bot Diagnostic Report

**تاريخ التشخيص**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## ✅ الإصلاحات المطبقة ({len(self.fixes_applied)})
"""
        
        for fix in self.fixes_applied:
            report += f"- {fix}\n"
        
        report += f"""
## ❌ المشاكل الموجودة ({len(self.issues_found)})
"""
        
        for issue in self.issues_found:
            report += f"- {issue}\n"
        
        if not self.issues_found:
            report += "🎉 **لا توجد مشاكل! البوت في حالة ممتازة**\n"
        
        report += f"""
## 📈 النتيجة الإجمالية
- **الإصلاحات المطبقة**: {len(self.fixes_applied)}
- **المشاكل المتبقية**: {len(self.issues_found)}
- **معدل الصحة**: {(len(self.fixes_applied) / (len(self.fixes_applied) + len(self.issues_found)) * 100):.1f}%

## 🔧 التوصيات
"""
        
        if self.issues_found:
            report += "1. قم بتطبيق الإصلاحات المفقودة\n"
            report += "2. راجع ملف COMPREHENSIVE_FIXES_APPLIED.md\n"
            report += "3. أعد تشغيل البوت بعد التطبيق\n"
        else:
            report += "✅ البوت في حالة ممتازة - لا حاجة لإجراءات إضافية\n"
        
        return report
    
    def run_full_diagnostic(self):
        """تشغيل التشخيص الكامل"""
        logger.info("🚀 بدء التشخيص الكامل...")
        
        self.check_caption_length_issues()
        self.check_timeout_settings()
        self.check_button_fallback()
        self.check_mod_id_resolution()
        self.check_web_server_fixes()
        self.check_log_files()
        self.check_environment_variables()
        
        report = self.generate_report()
        
        # حفظ التقرير
        with open('diagnostic_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        logger.info("✅ تم حفظ التقرير في diagnostic_report.md")

def main():
    """الدالة الرئيسية"""
    print("🔍 أداة تشخيص مشاكل البوت")
    print("=" * 50)
    
    diagnostic = BotDiagnostic()
    diagnostic.run_full_diagnostic()

if __name__ == "__main__":
    main()
