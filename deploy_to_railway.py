#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نشر البوت على Railway
Deploy Bot to Railway

هذا الملف يساعد في إعداد ونشر البوت على منصة Railway
This file helps setup and deploy the bot to Railway platform
"""

import os
import sys
import json
import subprocess
from pathlib import Path

def print_header():
    """طباعة رأس النشر"""
    print("\n" + "="*60)
    print("🚂 نشر البوت على Railway")
    print("🚀 Deploy Bot to Railway")
    print("="*60)
    print("📱 منصة استضافة مجانية موثوقة")
    print("🌐 Reliable free hosting platform")
    print("="*60 + "\n")

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 Checking deployment requirements...")
    
    required_files = [
        'main.py',
        'requirements.txt',
        'supabase_client.py',
        'web_server.py',
        'telegram_web_app.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    print("✅ All required files found")
    return True

def create_railway_files():
    """إنشاء ملفات Railway المطلوبة"""
    print("\n📝 Creating Railway configuration files...")
    
    # إنشاء Procfile
    procfile_content = """web: python run_enhanced_bot.py
"""
    
    try:
        with open("Procfile", "w", encoding='utf-8') as f:
            f.write(procfile_content)
        print("✅ Created Procfile")
    except Exception as e:
        print(f"❌ Failed to create Procfile: {e}")
        return False
    
    # إنشاء railway.json
    railway_config = {
        "build": {
            "builder": "NIXPACKS"
        },
        "deploy": {
            "startCommand": "python run_enhanced_bot.py",
            "healthcheckPath": "/",
            "healthcheckTimeout": 100,
            "restartPolicyType": "ON_FAILURE",
            "restartPolicyMaxRetries": 10
        }
    }
    
    try:
        with open("railway.json", "w", encoding='utf-8') as f:
            json.dump(railway_config, f, indent=2)
        print("✅ Created railway.json")
    except Exception as e:
        print(f"❌ Failed to create railway.json: {e}")
        return False
    
    # إنشاء nixpacks.toml للتحسين
    nixpacks_content = """[phases.setup]
nixPkgs = ['python39']

[phases.install]
cmds = ['pip install -r requirements.txt']

[phases.build]
cmds = ['echo "Build completed"']

[start]
cmd = 'python run_enhanced_bot.py'
"""
    
    try:
        with open("nixpacks.toml", "w", encoding='utf-8') as f:
            f.write(nixpacks_content)
        print("✅ Created nixpacks.toml")
    except Exception as e:
        print(f"❌ Failed to create nixpacks.toml: {e}")
        return False
    
    return True

def create_env_template():
    """إنشاء قالب متغيرات البيئة"""
    print("\n📋 Creating environment variables template...")
    
    env_template = """# Railway Environment Variables Template
# Copy these to your Railway project settings

# Required Variables
BOT_TOKEN=your_telegram_bot_token_here
ADMIN_CHAT_ID=your_telegram_user_id_here

# Database (Supabase)
SUPABASE_URL=your_supabase_project_url
SUPABASE_KEY=your_supabase_anon_key

# Web Server Settings
WEB_SERVER_URL=https://your-app-name.railway.app
FLASK_PORT=5000
TELEGRAM_WEB_APP_PORT=5001

# Optional Settings
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Security (Optional)
ENCRYPTION_SECRET=your_encryption_secret
JWT_SECRET=your_jwt_secret
MAX_REQUESTS_PER_MINUTE=30
MAX_REQUESTS_PER_HOUR=200
"""
    
    try:
        with open("railway.env.template", "w", encoding='utf-8') as f:
            f.write(env_template)
        print("✅ Created railway.env.template")
        return True
    except Exception as e:
        print(f"❌ Failed to create environment template: {e}")
        return False

def optimize_for_railway():
    """تحسين الإعدادات لـ Railway"""
    print("\n⚙️ Optimizing settings for Railway...")
    
    # إنشاء ملف إعدادات Railway
    railway_settings = {
        "memory_limit": "512MB",
        "cpu_limit": "0.5",
        "disk_limit": "1GB",
        "optimization": {
            "enable_compression": True,
            "enable_caching": True,
            "reduce_logging": True,
            "optimize_database": True
        },
        "health_check": {
            "enabled": True,
            "path": "/",
            "interval": 30,
            "timeout": 10
        }
    }
    
    try:
        with open("railway_settings.json", "w", encoding='utf-8') as f:
            json.dump(railway_settings, f, indent=2)
        print("✅ Created railway_settings.json")
    except Exception as e:
        print(f"❌ Failed to create Railway settings: {e}")
        return False
    
    return True

def create_deployment_script():
    """إنشاء سكريبت النشر"""
    print("\n📜 Creating deployment script...")
    
    deploy_script = """#!/bin/bash
# Railway Deployment Script

echo "🚂 Starting Railway deployment..."

# Install Railway CLI if not installed
if ! command -v railway &> /dev/null; then
    echo "📦 Installing Railway CLI..."
    npm install -g @railway/cli
fi

# Login to Railway (interactive)
echo "🔐 Please login to Railway..."
railway login

# Create new project or link existing
echo "🆕 Creating/linking Railway project..."
railway init

# Set environment variables
echo "⚙️ Setting environment variables..."
echo "Please set the following variables in Railway dashboard:"
echo "- BOT_TOKEN"
echo "- ADMIN_CHAT_ID" 
echo "- SUPABASE_URL"
echo "- SUPABASE_KEY"
echo "- WEB_SERVER_URL"

# Deploy
echo "🚀 Deploying to Railway..."
railway up

echo "✅ Deployment completed!"
echo "🌐 Your bot should be available at the Railway URL"
"""
    
    try:
        with open("deploy_railway.sh", "w", encoding='utf-8') as f:
            f.write(deploy_script)
        
        # Make executable on Unix systems
        if os.name != 'nt':
            os.chmod("deploy_railway.sh", 0o755)
        
        print("✅ Created deploy_railway.sh")
    except Exception as e:
        print(f"❌ Failed to create deployment script: {e}")
        return False
    
    return True

def create_readme_railway():
    """إنشاء دليل النشر على Railway"""
    readme_content = """# 🚂 نشر البوت على Railway

## خطوات النشر السريع:

### 1. إعداد المشروع
```bash
# تثبيت Railway CLI
npm install -g @railway/cli

# تسجيل الدخول
railway login

# إنشاء مشروع جديد
railway init
```

### 2. رفع الملفات
```bash
# رفع جميع الملفات
git add .
git commit -m "Initial deployment"
railway up
```

### 3. إعداد متغيرات البيئة
في لوحة تحكم Railway، أضف:
- `BOT_TOKEN`: توكن البوت من @BotFather
- `ADMIN_CHAT_ID`: معرف المسؤول
- `SUPABASE_URL`: رابط قاعدة البيانات
- `SUPABASE_KEY`: مفتاح قاعدة البيانات
- `WEB_SERVER_URL`: https://your-app.railway.app

### 4. تفعيل النطاق
```bash
# ربط نطاق مخصص (اختياري)
railway domain
```

## 🔧 استكشاف الأخطاء:

### مشكلة: فشل في البناء
```bash
# فحص السجلات
railway logs

# إعادة النشر
railway up --detach
```

### مشكلة: متغيرات البيئة
```bash
# عرض المتغيرات
railway variables

# إضافة متغير
railway variables set KEY=value
```

## 📊 مراقبة الأداء:
- استخدم `railway logs` لمراقبة السجلات
- تحقق من استهلاك الموارد في لوحة التحكم
- فعّل التنبيهات للأخطاء

## 🆘 الدعم:
- وثائق Railway: https://docs.railway.app
- مجتمع Railway: https://discord.gg/railway
- مطور البوت: @Kim880198
"""
    
    try:
        with open("RAILWAY_DEPLOYMENT.md", "w", encoding='utf-8') as f:
            f.write(readme_content)
        print("✅ Created RAILWAY_DEPLOYMENT.md")
        return True
    except Exception as e:
        print(f"❌ Failed to create Railway README: {e}")
        return False

def show_deployment_instructions():
    """عرض تعليمات النشر"""
    print("\n" + "="*60)
    print("🎉 إعداد Railway مكتمل!")
    print("🚀 RAILWAY SETUP COMPLETE!")
    print("="*60)
    print("\n📋 الخطوات التالية:")
    print("1. 🌐 إنشاء حساب على Railway: https://railway.app")
    print("2. 📦 تثبيت Railway CLI: npm install -g @railway/cli")
    print("3. 🔐 تسجيل الدخول: railway login")
    print("4. 🆕 إنشاء مشروع: railway init")
    print("5. ⚙️ إعداد متغيرات البيئة في لوحة التحكم")
    print("6. 🚀 النشر: railway up")
    print("\n📄 ملفات تم إنشاؤها:")
    print("• Procfile - ملف تشغيل Railway")
    print("• railway.json - إعدادات النشر")
    print("• nixpacks.toml - إعدادات البناء")
    print("• railway.env.template - قالب متغيرات البيئة")
    print("• deploy_railway.sh - سكريبت النشر التلقائي")
    print("• RAILWAY_DEPLOYMENT.md - دليل النشر")
    print("\n🔗 روابط مفيدة:")
    print("• Railway Dashboard: https://railway.app/dashboard")
    print("• Railway Docs: https://docs.railway.app")
    print("• Railway Discord: https://discord.gg/railway")
    print("="*60 + "\n")

def main():
    """الدالة الرئيسية"""
    try:
        print_header()
        
        # التحقق من المتطلبات
        if not check_requirements():
            sys.exit(1)
        
        # إنشاء ملفات Railway
        if not create_railway_files():
            sys.exit(1)
        
        # إنشاء قالب متغيرات البيئة
        if not create_env_template():
            sys.exit(1)
        
        # تحسين الإعدادات
        if not optimize_for_railway():
            sys.exit(1)
        
        # إنشاء سكريبت النشر
        if not create_deployment_script():
            sys.exit(1)
        
        # إنشاء دليل النشر
        if not create_readme_railway():
            sys.exit(1)
        
        # عرض التعليمات
        show_deployment_instructions()
        
    except KeyboardInterrupt:
        print("\n🛑 تم إلغاء الإعداد")
    except Exception as e:
        print(f"\n💥 خطأ في الإعداد: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
