#!/usr/bin/env python3
"""
تشغيل البوت مع وضع الشبكة المحسن
Run Bot with Enhanced Network Mode
"""

import os
import sys
import asyncio
import logging
import socket
import time
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_network_environment():
    """إعداد بيئة الشبكة المحسنة"""
    logger.info("🔧 إعداد بيئة الشبكة المحسنة...")
    
    # إعدادات DNS
    os.environ['PYTHONDONTWRITEBYTECODE'] = '1'
    os.environ['PYTHONUNBUFFERED'] = '1'
    
    # إعدادات timeout محسنة
    os.environ['TELEGRAM_CONNECT_TIMEOUT'] = '60'
    os.environ['TELEGRAM_READ_TIMEOUT'] = '60'
    os.environ['TELEGRAM_WRITE_TIMEOUT'] = '60'
    os.environ['TELEGRAM_POOL_TIMEOUT'] = '60'
    
    # تعطيل البروكسي
    for proxy_var in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']:
        if proxy_var in os.environ:
            del os.environ[proxy_var]
    
    # إعدادات SSL مرنة
    os.environ['PYTHONHTTPSVERIFY'] = '0'
    
    logger.info("✅ تم إعداد بيئة الشبكة")

def test_basic_connectivity():
    """اختبار الاتصال الأساسي"""
    logger.info("🌐 اختبار الاتصال الأساسي...")
    
    test_hosts = [
        ("*******", 53),
        ("*******", 53),
        ("google.com", 80)
    ]
    
    for host, port in test_hosts:
        try:
            socket.create_connection((host, port), timeout=10)
            logger.info(f"✅ نجح الاتصال مع {host}:{port}")
            return True
        except Exception as e:
            logger.warning(f"⚠️ فشل الاتصال مع {host}:{port}: {e}")
    
    logger.error("❌ فشل في جميع اختبارات الاتصال")
    return False

async def test_telegram_api():
    """اختبار Telegram API"""
    logger.info("📱 اختبار Telegram API...")
    
    try:
        import aiohttp
        import ssl
        
        # إنشاء SSL context مرن
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        timeout = aiohttp.ClientTimeout(total=30, connect=15)
        
        async with aiohttp.ClientSession(
            timeout=timeout,
            connector=aiohttp.TCPConnector(ssl=ssl_context)
        ) as session:
            
            # اختبار بسيط لـ Telegram
            test_url = "https://api.telegram.org/bot123:test/getMe"
            
            try:
                async with session.get(test_url) as response:
                    if response.status in [200, 401, 404]:  # أي استجابة تعني أن الاتصال يعمل
                        logger.info("✅ Telegram API قابل للوصول")
                        return True
            except Exception as e:
                logger.warning(f"⚠️ اختبار Telegram API: {e}")
                
        return False
        
    except ImportError:
        logger.warning("⚠️ aiohttp غير متوفر للاختبار")
        return True  # نفترض أنه يعمل

def create_enhanced_main():
    """إنشاء ملف main محسن للشبكة"""
    logger.info("📝 إنشاء إعدادات شبكة محسنة...")
    
    enhanced_config = '''
# إعدادات شبكة محسنة للبوت
import os
import ssl
import socket
import asyncio
from telegram.request import HTTPXRequest

# إعدادات timeout محسنة
ENHANCED_TIMEOUTS = {
    'connect_timeout': 60,
    'read_timeout': 60, 
    'write_timeout': 60,
    'pool_timeout': 60
}

# إنشاء SSL context مرن
def create_flexible_ssl_context():
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

# إنشاء request محسن
def create_enhanced_request():
    return HTTPXRequest(
        connection_pool_size=8,
        read_timeout=ENHANCED_TIMEOUTS['read_timeout'],
        write_timeout=ENHANCED_TIMEOUTS['write_timeout'],
        connect_timeout=ENHANCED_TIMEOUTS['connect_timeout'],
        pool_timeout=ENHANCED_TIMEOUTS['pool_timeout']
    )

# تطبيق الإعدادات
socket.setdefaulttimeout(60)
'''
    
    try:
        with open("enhanced_network_config.py", "w", encoding="utf-8") as f:
            f.write(enhanced_config)
        logger.info("✅ تم إنشاء enhanced_network_config.py")
        return True
    except Exception as e:
        logger.error(f"❌ فشل في إنشاء الإعدادات: {e}")
        return False

async def run_bot_with_retries():
    """تشغيل البوت مع إعادة المحاولة"""
    max_retries = 5
    retry_delay = 10
    
    for attempt in range(max_retries):
        try:
            logger.info(f"🚀 محاولة تشغيل البوت #{attempt + 1}")
            
            # تطبيق إعدادات الشبكة
            setup_network_environment()
            
            # استيراد البوت
            sys.path.insert(0, str(Path(__file__).parent))
            
            # محاولة استيراد main مع معالجة الأخطاء
            try:
                from main import main as bot_main
                await bot_main()
                break  # نجح التشغيل
                
            except ImportError as e:
                logger.error(f"❌ خطأ في استيراد main.py: {e}")
                break
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ فشل في المحاولة {attempt + 1}: {error_msg}")
            
            # تحليل نوع الخطأ
            if "getaddrinfo failed" in error_msg:
                logger.info("🔍 مشكلة DNS - محاولة حلول بديلة...")
                
                # تجربة تغيير DNS
                try:
                    import subprocess
                    if os.name == 'nt':  # Windows
                        subprocess.run(['ipconfig', '/flushdns'], capture_output=True)
                        logger.info("🔄 تم مسح DNS cache")
                except:
                    pass
                    
            elif "ConnectError" in error_msg:
                logger.info("🌐 مشكلة اتصال - فحص الشبكة...")
                if not test_basic_connectivity():
                    logger.error("❌ لا يوجد اتصال بالإنترنت")
                    break
            
            if attempt < max_retries - 1:
                logger.info(f"⏳ انتظار {retry_delay} ثانية قبل المحاولة التالية...")
                await asyncio.sleep(retry_delay)
                retry_delay += 5
            else:
                logger.error("❌ فشل في جميع المحاولات")
                raise

def print_troubleshooting_guide():
    """طباعة دليل استكشاف الأخطاء"""
    print("\n" + "=" * 60)
    print("🔧 دليل استكشاف الأخطاء")
    print("=" * 60)
    print("\n1. 🌐 مشاكل الشبكة:")
    print("   - تحقق من اتصال الإنترنت")
    print("   - جرب شبكة أخرى (هاتف محمول)")
    print("   - أعد تشغيل الراوتر")
    
    print("\n2. 🔒 مشاكل الأمان:")
    print("   - تعطيل مضاد الفيروسات مؤقتاً")
    print("   - تعطيل الجدار الناري مؤقتاً")
    print("   - إضافة Python للاستثناءات")
    
    print("\n3. 🔧 مشاكل DNS:")
    print("   - تغيير DNS إلى *******")
    print("   - مسح DNS cache")
    print("   - إعادة تشغيل الكمبيوتر")
    
    print("\n4. 📱 مشاكل Telegram:")
    print("   - تحقق من صحة Bot Token")
    print("   - تحقق من حالة Telegram API")
    print("   - جرب VPN إذا كان Telegram محجوب")
    
    print("\n5. 🐍 مشاكل Python:")
    print("   - تحديث pip: python -m pip install --upgrade pip")
    print("   - إعادة تثبيت المكتبات: pip install -r requirements.txt")
    print("   - تحقق من إصدار Python: python --version")
    
    print("\n💡 أدوات مساعدة:")
    print("   - python fix_dns_connection.py")
    print("   - python network_test.py")
    print("   - ping google.com")
    print("   - nslookup api.telegram.org")
    print("=" * 60)

def main():
    """الدالة الرئيسية"""
    print("🤖 تشغيل البوت مع وضع الشبكة المحسن")
    print("=" * 50)
    
    # فحص أولي
    if not test_basic_connectivity():
        print("\n❌ لا يوجد اتصال بالإنترنت!")
        print("🔧 جرب تشغيل: python fix_dns_connection.py")
        return
    
    # إنشاء الإعدادات المحسنة
    create_enhanced_main()
    
    print("\n🚀 بدء تشغيل البوت...")
    print("📋 البوت سيعمل مع إعدادات شبكة محسنة...")
    print("⏹️ اضغط Ctrl+C لإيقاف البوت")
    print("=" * 50)
    
    try:
        # تشغيل البوت
        asyncio.run(run_bot_with_retries())
        
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف البوت بواسطة المستخدم")
        print("✅ البوت توقف بأمان")
        
    except Exception as e:
        print(f"\n\n❌ خطأ في تشغيل البوت: {e}")
        print_troubleshooting_guide()
        
        # اقتراح حلول
        print("\n🔧 حلول مقترحة:")
        print("1. python fix_dns_connection.py")
        print("2. أعد تشغيل الكمبيوتر")
        print("3. جرب شبكة أخرى")
        print("4. تحقق من إعدادات الجدار الناري")
        
        sys.exit(1)

if __name__ == "__main__":
    main()
