const fs = require('fs');
const path = require('path');

// إنشاء مجلد dist
if (!fs.existsSync('dist')) {
    fs.mkdirSync('dist');
}

// إنشاء ملف index.html أساسي
const indexHtml = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modetaris Bot - مودات ماينكرافت</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .logo {
            font-size: 3em;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .status {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-bottom: 20px;
        }
        .telegram-link {
            background: #0088cc;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }
        .telegram-link:hover {
            background: #006699;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🎮</div>
        <h1>Modetaris Bot</h1>
        <div class="status">✅ البوت يعمل بنجاح</div>
        <div class="description">
            بوتك المتخصص في إدارة ونشر مودات ماينكرافت تلقائياً إلى قناة التليجرام الخاصة بك.
            <br><br>
            🚀 نشر تلقائي للمودات الجديدة<br>
            🎨 تخصيص كامل لصفحات العرض<br>
            📊 إحصائيات مفصلة ومتقدمة
        </div>
        <a href="https://t.me/YOUR_BOT_USERNAME" class="telegram-link">
            🤖 ابدأ استخدام البوت
        </a>
    </div>
</body>
</html>
`;

fs.writeFileSync('dist/index.html', indexHtml);

// إنشاء ملف _redirects للتوجيه
const redirects = `
/api/* /api/:splat 200
/* /index.html 200
`;

fs.writeFileSync('dist/_redirects', redirects);

console.log('✅ تم بناء الملفات بنجاح في مجلد dist');
