#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الإعدادات الآمن
Secure Configuration Management System
"""

import os
import sys
import logging
from typing import Optional, Dict, Any
from pathlib import Path

# محاولة استيراد python-dotenv
try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False
    print("⚠️ تحذير: python-dotenv غير مثبت. يرجى تثبيته باستخدام: pip install python-dotenv")

logger = logging.getLogger(__name__)

class SecureConfig:
    """نظام إدارة الإعدادات الآمن"""
    
    def __init__(self):
        self.config: Dict[str, Any] = {}
        self.required_vars = [
            'BOT_TOKEN',
            'ADMIN_CHAT_ID',
            'SUPABASE_URL',
            'SUPABASE_KEY'
        ]
        self.load_config()
    
    def load_config(self):
        """تحميل الإعدادات من متغيرات البيئة"""
        # تحميل ملف .env إذا كان متوفراً
        if DOTENV_AVAILABLE:
            env_file = Path('.env')
            if env_file.exists():
                load_dotenv(env_file)
                logger.info("تم تحميل ملف .env بنجاح")
            else:
                logger.warning("ملف .env غير موجود")
        
        # تحميل المتغيرات المطلوبة
        self._load_bot_config()
        self._load_database_config()
        self._load_security_config()
        self._load_optional_config()
        
        # التحقق من المتغيرات المطلوبة
        self._validate_required_vars()
    
    def _load_bot_config(self):
        """تحميل إعدادات البوت"""
        self.config.update({
            'BOT_TOKEN': self._get_env_var('BOT_TOKEN'),
            'ADMIN_CHAT_ID': self._get_env_var('ADMIN_CHAT_ID'),
            'ADMIN_USERNAME': self._get_env_var('ADMIN_USERNAME', ''),
            'ENVIRONMENT': self._get_env_var('ENVIRONMENT', 'production'),
            'DEBUG': self._get_bool_env('DEBUG', False),
            'LOG_LEVEL': self._get_env_var('LOG_LEVEL', 'INFO')
        })
    
    def _load_database_config(self):
        """تحميل إعدادات قاعدة البيانات"""
        self.config.update({
            'SUPABASE_URL': self._get_env_var('SUPABASE_URL'),
            'SUPABASE_KEY': self._get_env_var('SUPABASE_KEY'),
            'FIREBASE_PROJECT_ID': self._get_env_var('FIREBASE_PROJECT_ID', ''),
            'FIREBASE_STORAGE_BUCKET': self._get_env_var('FIREBASE_STORAGE_BUCKET', ''),
            'FIREBASE_API_KEY': self._get_env_var('FIREBASE_API_KEY', '')
        })
    
    def _load_security_config(self):
        """تحميل إعدادات الأمان"""
        self.config.update({
            'ENCRYPTION_SECRET': self._get_env_var('ENCRYPTION_SECRET', ''),
            'JWT_SECRET': self._get_env_var('JWT_SECRET', ''),
            'MAX_REQUESTS_PER_MINUTE': self._get_int_env('MAX_REQUESTS_PER_MINUTE', 30),
            'MAX_REQUESTS_PER_HOUR': self._get_int_env('MAX_REQUESTS_PER_HOUR', 200),
            'SESSION_TIMEOUT_HOURS': self._get_int_env('SESSION_TIMEOUT_HOURS', 24),
            'MAX_FILE_SIZE_MB': self._get_int_env('MAX_FILE_SIZE_MB', 50)
        })
    
    def _load_optional_config(self):
        """تحميل الإعدادات الاختيارية"""
        # إعدادات Gemini AI
        gemini_keys = self._get_env_var('GEMINI_API_KEYS', '')
        self.config['GEMINI_API_KEYS'] = [key.strip() for key in gemini_keys.split(',') if key.strip()]
        
        # إعدادات الشبكة
        self.config.update({
            'HTTP_PROXY': self._get_env_var('HTTP_PROXY', ''),
            'HTTPS_PROXY': self._get_env_var('HTTPS_PROXY', ''),
            'WEBHOOK_URL': self._get_env_var('WEBHOOK_URL', ''),
            'WEBHOOK_SECRET': self._get_env_var('WEBHOOK_SECRET', '')
        })
        
        # إعدادات التخزين
        self.config.update({
            'TEMP_STORAGE_PATH': self._get_env_var('TEMP_STORAGE_PATH', './temp'),
            'MAX_STORAGE_SIZE_GB': self._get_int_env('MAX_STORAGE_SIZE_GB', 10)
        })
        
        # إعدادات التنبيهات
        self.config.update({
            'SMTP_SERVER': self._get_env_var('SMTP_SERVER', ''),
            'SMTP_PORT': self._get_int_env('SMTP_PORT', 587),
            'SMTP_USERNAME': self._get_env_var('SMTP_USERNAME', ''),
            'SMTP_PASSWORD': self._get_env_var('SMTP_PASSWORD', ''),
            'ALERT_EMAIL': self._get_env_var('ALERT_EMAIL', '')
        })
    
    def _get_env_var(self, key: str, default: str = None) -> Optional[str]:
        """الحصول على متغير بيئة مع قيمة افتراضية"""
        value = os.environ.get(key, default)
        if value is None and key in self.required_vars:
            logger.error(f"متغير البيئة المطلوب {key} غير موجود")
        return value
    
    def _get_int_env(self, key: str, default: int) -> int:
        """الحصول على متغير بيئة كرقم صحيح"""
        try:
            return int(os.environ.get(key, default))
        except (ValueError, TypeError):
            logger.warning(f"قيمة غير صحيحة لمتغير البيئة {key}، استخدام القيمة الافتراضية {default}")
            return default
    
    def _get_bool_env(self, key: str, default: bool) -> bool:
        """الحصول على متغير بيئة كقيمة منطقية"""
        value = os.environ.get(key, '').lower()
        if value in ('true', '1', 'yes', 'on'):
            return True
        elif value in ('false', '0', 'no', 'off'):
            return False
        else:
            return default
    
    def _validate_required_vars(self):
        """التحقق من وجود المتغيرات المطلوبة"""
        missing_vars = []
        for var in self.required_vars:
            if not self.config.get(var):
                missing_vars.append(var)
        
        if missing_vars:
            error_msg = f"متغيرات البيئة المطلوبة مفقودة: {', '.join(missing_vars)}"
            logger.critical(error_msg)
            print(f"❌ خطأ حرج: {error_msg}")
            print("يرجى إنشاء ملف .env وإضافة المتغيرات المطلوبة")
            sys.exit(1)
    
    def get(self, key: str, default: Any = None) -> Any:
        """الحصول على قيمة إعداد"""
        return self.config.get(key, default)
    
    def is_debug(self) -> bool:
        """التحقق من وضع التطوير"""
        return self.config.get('DEBUG', False)
    
    def is_production(self) -> bool:
        """التحقق من وضع الإنتاج"""
        return self.config.get('ENVIRONMENT', 'production').lower() == 'production'
    
    def get_log_level(self) -> str:
        """الحصول على مستوى التسجيل"""
        return self.config.get('LOG_LEVEL', 'INFO').upper()
    
    def validate_token_format(self, token: str) -> bool:
        """التحقق من تنسيق توكن البوت"""
        if not token:
            return False
        
        # تنسيق توكن تيليجرام: xxxxxxxxx:xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
        import re
        pattern = r'^\d{8,10}:[a-zA-Z0-9_-]{35}$'
        return bool(re.match(pattern, token))
    
    def validate_chat_id(self, chat_id: str) -> bool:
        """التحقق من تنسيق معرف الدردشة"""
        if not chat_id:
            return False
        
        try:
            # معرف الدردشة يجب أن يكون رقماً
            int(chat_id)
            return True
        except ValueError:
            return False
    
    def validate_url(self, url: str) -> bool:
        """التحقق من تنسيق الرابط"""
        if not url:
            return False
        
        import re
        pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        return bool(re.match(pattern, url))
    
    def print_config_status(self):
        """طباعة حالة الإعدادات"""
        print("\n" + "="*50)
        print("🔧 حالة إعدادات البوت")
        print("="*50)
        
        # التحقق من الإعدادات الأساسية
        bot_token = self.get('BOT_TOKEN')
        admin_id = self.get('ADMIN_CHAT_ID')
        supabase_url = self.get('SUPABASE_URL')
        supabase_key = self.get('SUPABASE_KEY')
        
        print(f"🤖 توكن البوت: {'✅ موجود' if bot_token else '❌ مفقود'}")
        if bot_token and not self.validate_token_format(bot_token):
            print("   ⚠️ تحذير: تنسيق التوكن قد يكون غير صحيح")
        
        print(f"👤 معرف المسؤول: {'✅ موجود' if admin_id else '❌ مفقود'}")
        if admin_id and not self.validate_chat_id(admin_id):
            print("   ⚠️ تحذير: تنسيق معرف المسؤول قد يكون غير صحيح")
        
        print(f"🗄️ رابط Supabase: {'✅ موجود' if supabase_url else '❌ مفقود'}")
        if supabase_url and not self.validate_url(supabase_url):
            print("   ⚠️ تحذير: تنسيق رابط Supabase قد يكون غير صحيح")
        
        print(f"🔑 مفتاح Supabase: {'✅ موجود' if supabase_key else '❌ مفقود'}")
        
        # الإعدادات الاختيارية
        print(f"\n📊 الإعدادات الاختيارية:")
        print(f"   🔥 Firebase: {'✅ مكون' if self.get('FIREBASE_PROJECT_ID') else '❌ غير مكون'}")
        print(f"   🤖 Gemini AI: {'✅ مكون' if self.get('GEMINI_API_KEYS') else '❌ غير مكون'}")
        print(f"   🔒 التشفير: {'✅ مكون' if self.get('ENCRYPTION_SECRET') else '❌ غير مكون'}")
        print(f"   📧 التنبيهات: {'✅ مكونة' if self.get('SMTP_SERVER') else '❌ غير مكونة'}")
        
        # إعدادات الأمان
        print(f"\n🛡️ إعدادات الأمان:")
        print(f"   البيئة: {self.get('ENVIRONMENT')}")
        print(f"   وضع التطوير: {'✅ مفعل' if self.is_debug() else '❌ معطل'}")
        print(f"   حد الطلبات/دقيقة: {self.get('MAX_REQUESTS_PER_MINUTE')}")
        print(f"   حد الطلبات/ساعة: {self.get('MAX_REQUESTS_PER_HOUR')}")
        
        print("="*50)

# إنشاء مثيل عام للإعدادات
config = SecureConfig()

# دوال مساعدة للوصول السريع
def get_bot_token() -> str:
    """الحصول على توكن البوت"""
    return config.get('BOT_TOKEN', '')

def get_admin_id() -> str:
    """الحصول على معرف المسؤول"""
    return config.get('ADMIN_CHAT_ID', '')

def get_supabase_url() -> str:
    """الحصول على رابط Supabase"""
    return config.get('SUPABASE_URL', '')

def get_supabase_key() -> str:
    """الحصول على مفتاح Supabase"""
    return config.get('SUPABASE_KEY', '')

def is_debug_mode() -> bool:
    """التحقق من وضع التطوير"""
    return config.is_debug()

def is_production_mode() -> bool:
    """التحقق من وضع الإنتاج"""
    return config.is_production()

if __name__ == "__main__":
    # طباعة حالة الإعدادات عند تشغيل الملف مباشرة
    config.print_config_status()
