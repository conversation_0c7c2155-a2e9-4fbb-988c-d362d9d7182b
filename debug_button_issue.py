#!/usr/bin/env python3
"""
تشخيص مشكلة Button_type_invalid
Debug Button_type_invalid Issue

فحص مفصل لمشكلة الأزرار
Detailed investigation of button issues
"""

import sys
import traceback
import os
from pathlib import Path

def test_specific_mod():
    """اختبار المود المحدد الذي فشل"""
    print("🔍 Testing specific mod that failed...")
    
    try:
        # إضافة مسار المشروع
        sys.path.insert(0, str(Path(__file__).parent))
        
        # استيراد الدوال المطلوبة
        from main import _build_mod_post_content, load_mods
        
        # البحث عن المود الذي فشل
        mod_id = "0a1492c8-b29d-4269-b3f7-68cc576d3de0"
        
        print(f"   Looking for mod: {mod_id}")
        
        # تحميل جميع المودات
        all_mods = load_mods()
        print(f"   Total mods loaded: {len(all_mods)}")
        
        # البحث عن المود المحدد
        target_mod = None
        for mod in all_mods:
            if mod.get('id') == mod_id:
                target_mod = mod
                break
        
        if not target_mod:
            print(f"   ❌ Mod {mod_id} not found!")
            return False
        
        print(f"   ✅ Found mod: {target_mod.get('title', 'No title')}")
        
        # اختبار إنشاء المحتوى
        try:
            content = _build_mod_post_content(target_mod, "ar", "7513880877")
            
            if content and "reply_markup" in content:
                markup = content["reply_markup"]
                if markup and hasattr(markup, 'inline_keyboard'):
                    print(f"   ✅ Reply markup created with {len(markup.inline_keyboard)} rows")
                    
                    # فحص كل زر
                    for i, row in enumerate(markup.inline_keyboard):
                        for j, button in enumerate(row):
                            print(f"   Button [{i}][{j}]: {button.text}")
                            
                            # فحص نوع الزر
                            if hasattr(button, 'web_app') and button.web_app:
                                print(f"      Type: WebApp")
                                print(f"      URL: {button.web_app}")
                                
                                # التحقق من صحة تنسيق WebApp
                                if isinstance(button.web_app, dict):
                                    if 'url' in button.web_app:
                                        url = button.web_app['url']
                                        print(f"      ✅ WebApp URL: {url}")
                                        
                                        # التحقق من صحة الرابط
                                        if url.startswith('https://'):
                                            print(f"      ✅ HTTPS URL - should work")
                                        else:
                                            print(f"      ⚠️ Non-HTTPS URL - may cause issues")
                                    else:
                                        print(f"      ❌ WebApp missing 'url' key")
                                        return False
                                else:
                                    print(f"      ❌ WebApp is not a dict: {type(button.web_app)}")
                                    return False
                                    
                            elif hasattr(button, 'url') and button.url:
                                print(f"      Type: URL")
                                print(f"      URL: {button.url}")
                                
                            elif hasattr(button, 'callback_data') and button.callback_data:
                                print(f"      Type: Callback")
                                print(f"      Data: {button.callback_data}")
                            
                            else:
                                print(f"      ❌ Unknown button type")
                                return False
                    
                    return True
                else:
                    print(f"   ❌ No inline_keyboard in markup")
                    return False
            else:
                print(f"   ❌ No reply_markup in content")
                return False
                
        except Exception as e:
            print(f"   ❌ Content creation failed: {e}")
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        traceback.print_exc()
        return False

def test_button_creation_manually():
    """اختبار إنشاء الأزرار يدوياً"""
    print("\n🔍 Testing manual button creation...")
    
    try:
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        
        # اختبار إنشاء زر WebApp مع الرابط الفعلي
        test_url = "https://3cf6-41-188-116-40.ngrok-free.app/telegram-mod-details?id=0a1492c8-b29d-4269-b3f7-68cc576d3de0&user_id=7513880877&channel=-1002433545184&lang=ar"
        
        print(f"   Testing URL: {test_url}")
        
        # طريقة 1: dict format
        try:
            button1 = InlineKeyboardButton("🎮 Test WebApp", web_app={"url": test_url})
            print("   ✅ Dict format WebApp button created")
        except Exception as e:
            print(f"   ❌ Dict format failed: {e}")
            return False
        
        # طريقة 2: URL button
        try:
            button2 = InlineKeyboardButton("🌐 Test URL", url=test_url)
            print("   ✅ URL button created")
        except Exception as e:
            print(f"   ❌ URL button failed: {e}")
            return False
        
        # إنشاء keyboard
        try:
            keyboard = InlineKeyboardMarkup([[button1], [button2]])
            print("   ✅ Keyboard created successfully")
            return True
        except Exception as e:
            print(f"   ❌ Keyboard creation failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Manual test failed: {e}")
        return False

def test_url_validation():
    """اختبار صحة الرابط"""
    print("\n🔍 Testing URL validation...")
    
    try:
        # إضافة مسار المشروع
        sys.path.insert(0, str(Path(__file__).parent))
        
        from main import is_valid_url
        
        test_url = "https://3cf6-41-188-116-40.ngrok-free.app/telegram-mod-details?id=0a1492c8-b29d-4269-b3f7-68cc576d3de0&user_id=7513880877&channel=-1002433545184&lang=ar"
        
        print(f"   Testing URL: {test_url}")
        
        is_valid = is_valid_url(test_url)
        print(f"   URL validation result: {is_valid}")
        
        if is_valid:
            print("   ✅ URL is valid")
            return True
        else:
            print("   ❌ URL is invalid")
            return False
            
    except Exception as e:
        print(f"❌ URL validation test failed: {e}")
        return False

def test_environment_settings():
    """اختبار إعدادات البيئة"""
    print("\n🔍 Testing environment settings...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        web_server_url = os.environ.get("WEB_SERVER_URL")
        disable_web_app = os.environ.get("DISABLE_WEB_APP", "false").lower() == "true"
        
        print(f"   WEB_SERVER_URL: {web_server_url}")
        print(f"   DISABLE_WEB_APP: {disable_web_app}")
        
        if disable_web_app:
            print("   ⚠️ WebApp is disabled - this might be the issue!")
            return False
        
        if not web_server_url:
            print("   ❌ No WEB_SERVER_URL set")
            return False
        
        if not web_server_url.startswith('https://'):
            print("   ⚠️ WEB_SERVER_URL is not HTTPS")
            return False
        
        print("   ✅ Environment settings look good")
        return True
        
    except Exception as e:
        print(f"❌ Environment test failed: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🐛 Button_type_invalid Debug Tool")
    print("=" * 60)
    
    tests = [
        ("Environment Settings", test_environment_settings),
        ("URL Validation", test_url_validation),
        ("Manual Button Creation", test_button_creation_manually),
        ("Specific Mod Test", test_specific_mod),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"💥 {test_name} crashed: {e}")
            results[test_name] = False
    
    # تقرير النتائج
    print("\n" + "=" * 60)
    print("📊 Debug Results:")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All tests passed! The issue might be elsewhere.")
        print("💡 Possible causes:")
        print("   • Telegram API temporary issue")
        print("   • Network connectivity problem")
        print("   • Bot token permissions")
    else:
        print("❌ Some tests failed! Check the issues above.")
        print("🔧 Recommended actions:")
        
        if not results.get("Environment Settings", True):
            print("   • Check WEB_SERVER_URL and DISABLE_WEB_APP settings")
        
        if not results.get("URL Validation", True):
            print("   • Fix URL validation function")
        
        if not results.get("Manual Button Creation", True):
            print("   • Check Telegram library version")
        
        if not results.get("Specific Mod Test", True):
            print("   • Check mod data and content building")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 Debug tool crashed: {e}")
        traceback.print_exc()
        sys.exit(1)
