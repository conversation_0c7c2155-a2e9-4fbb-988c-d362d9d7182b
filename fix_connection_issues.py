#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لمشاكل الاتصال في البوت
Comprehensive fix for bot connection issues
"""

import os
import sys
import json
import logging
import subprocess
import time
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)

logger = logging.getLogger(__name__)

def check_and_kill_processes():
    """فحص وإيقاف العمليات المتضاربة"""
    logger.info("🔍 فحص العمليات المتضاربة...")
    
    try:
        # فحص المنافذ المستخدمة
        ports_to_check = [5000, 5001]
        
        for port in ports_to_check:
            try:
                if os.name == 'nt':  # Windows
                    result = subprocess.run(
                        ['netstat', '-ano'], 
                        capture_output=True, 
                        text=True, 
                        timeout=10
                    )
                    
                    for line in result.stdout.split('\n'):
                        if f':{port}' in line and 'LISTENING' in line:
                            # استخراج PID
                            parts = line.split()
                            if len(parts) >= 5:
                                pid = parts[-1]
                                logger.warning(f"⚠️ المنفذ {port} مستخدم بواسطة العملية {pid}")
                                
                                # محاولة إيقاف العملية
                                try:
                                    subprocess.run(['taskkill', '/PID', pid, '/F'], 
                                                 capture_output=True, timeout=5)
                                    logger.info(f"✅ تم إيقاف العملية {pid}")
                                except:
                                    logger.warning(f"⚠️ فشل في إيقاف العملية {pid}")
                else:  # Linux/Mac
                    result = subprocess.run(
                        ['lsof', f'-ti:{port}'], 
                        capture_output=True, 
                        text=True, 
                        timeout=10
                    )
                    
                    if result.stdout.strip():
                        pids = result.stdout.strip().split('\n')
                        for pid in pids:
                            try:
                                subprocess.run(['kill', '-9', pid], timeout=5)
                                logger.info(f"✅ تم إيقاف العملية {pid} على المنفذ {port}")
                            except:
                                logger.warning(f"⚠️ فشل في إيقاف العملية {pid}")
                                
            except Exception as e:
                logger.warning(f"⚠️ خطأ في فحص المنفذ {port}: {e}")
                
    except Exception as e:
        logger.error(f"❌ خطأ في فحص العمليات: {e}")

def fix_proxy_settings():
    """إصلاح إعدادات البروكسي"""
    logger.info("🔧 فحص وإصلاح إعدادات البروكسي...")
    
    # إزالة إعدادات البروكسي المتضاربة
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    
    for var in proxy_vars:
        if os.environ.get(var):
            logger.info(f"🔧 إزالة متغير البروكسي: {var}")
            del os.environ[var]
    
    logger.info("✅ تم تنظيف إعدادات البروكسي")

def fix_network_config():
    """إصلاح إعدادات الشبكة"""
    logger.info("🌐 إصلاح إعدادات الشبكة...")
    
    try:
        # تحديث ملف network_config.py
        network_config_path = Path("network_config.py")
        
        if network_config_path.exists():
            # قراءة الملف الحالي
            with open(network_config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إصلاح إعدادات البروكسي
            if "'enabled': True" in content:
                content = content.replace("'enabled': True", "'enabled': False")
                logger.info("🔧 تم تعطيل البروكسي في network_config.py")
            
            # كتابة الملف المحدث
            with open(network_config_path, 'w', encoding='utf-8') as f:
                f.write(content)
                
            logger.info("✅ تم تحديث إعدادات الشبكة")
        else:
            logger.warning("⚠️ ملف network_config.py غير موجود")
            
    except Exception as e:
        logger.error(f"❌ خطأ في إصلاح إعدادات الشبكة: {e}")

def clean_temp_files():
    """تنظيف الملفات المؤقتة"""
    logger.info("🧹 تنظيف الملفات المؤقتة...")
    
    temp_patterns = [
        "*.pyc",
        "__pycache__",
        "*.log",
        ".pytest_cache"
    ]
    
    try:
        import glob
        import shutil
        
        for pattern in temp_patterns:
            files = glob.glob(pattern, recursive=True)
            for file in files:
                try:
                    if os.path.isdir(file):
                        shutil.rmtree(file)
                    else:
                        os.remove(file)
                    logger.info(f"🗑️ تم حذف: {file}")
                except:
                    pass
                    
        logger.info("✅ تم تنظيف الملفات المؤقتة")
        
    except Exception as e:
        logger.warning(f"⚠️ خطأ في تنظيف الملفات المؤقتة: {e}")

def test_connections():
    """اختبار الاتصالات"""
    logger.info("🧪 اختبار الاتصالات...")
    
    try:
        import socket
        
        # اختبار الاتصال بالإنترنت
        try:
            socket.create_connection(("8.8.8.8", 53), timeout=10)
            logger.info("✅ الاتصال بالإنترنت يعمل")
        except:
            logger.error("❌ لا يوجد اتصال بالإنترنت")
            return False
        
        # اختبار الاتصال مع Telegram
        try:
            socket.create_connection(("api.telegram.org", 443), timeout=10)
            logger.info("✅ الاتصال مع Telegram يعمل")
        except:
            logger.warning("⚠️ مشكلة في الاتصال مع Telegram")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الاتصالات: {e}")
        return False

def restart_services():
    """إعادة تشغيل الخدمات"""
    logger.info("🔄 إعادة تشغيل الخدمات...")
    
    try:
        # إيقاف ngrok إذا كان يعمل
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(['taskkill', '/IM', 'ngrok.exe', '/F'], 
                             capture_output=True, timeout=5)
            else:  # Linux/Mac
                subprocess.run(['pkill', 'ngrok'], capture_output=True, timeout=5)
            logger.info("🔄 تم إيقاف ngrok")
        except:
            pass
        
        # انتظار قصير
        time.sleep(2)
        
        logger.info("✅ تم إعادة تشغيل الخدمات")
        
    except Exception as e:
        logger.warning(f"⚠️ خطأ في إعادة تشغيل الخدمات: {e}")

def main():
    """الدالة الرئيسية للإصلاح"""
    print("=" * 60)
    print("🔧 إصلاح شامل لمشاكل الاتصال")
    print("🛠️ Comprehensive Connection Issues Fix")
    print("=" * 60)
    
    steps = [
        ("🔍 فحص وإيقاف العمليات المتضاربة", check_and_kill_processes),
        ("🔧 إصلاح إعدادات البروكسي", fix_proxy_settings),
        ("🌐 إصلاح إعدادات الشبكة", fix_network_config),
        ("🧹 تنظيف الملفات المؤقتة", clean_temp_files),
        ("🔄 إعادة تشغيل الخدمات", restart_services),
        ("🧪 اختبار الاتصالات", test_connections),
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        try:
            result = step_func()
            if result is not False:
                success_count += 1
                print(f"✅ {step_name} - نجح")
            else:
                print(f"❌ {step_name} - فشل")
        except Exception as e:
            print(f"❌ {step_name} - خطأ: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {success_count}/{len(steps)} خطوات نجحت")
    
    if success_count >= len(steps) - 1:
        print("🎉 تم إصلاح معظم المشاكل!")
        print("✅ يمكنك الآن تشغيل البوت:")
        print("   python run_bot_fixed.py")
    else:
        print("⚠️ لا تزال هناك بعض المشاكل")
        print("🔧 راجع الأخطاء أعلاه")
        print("📖 راجع ملف TROUBLESHOOTING.md للمساعدة")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الإصلاح: {e}")
        sys.exit(1)
