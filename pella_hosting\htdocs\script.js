/**
 * ملف الجافاسكريبت لصفحة عرض المودات - محسن للهواتف
 * JavaScript for Mod Details Page - Mobile Optimized
 */

// متغيرات عامة
let modFileSize = 0;
let downloadedFileName = '';
let downloadStartTime = 0;

// منع إغلاق الصفحة عند الضغط على زر الرجوع في الهاتف
window.addEventListener('beforeunload', function(event) {
    // لا نمنع الإغلاق، فقط نتأكد من حفظ البيانات
    if (isDownloading) {
        // يمكن إضافة منطق حفظ حالة التحميل هنا
    }
});

// تحسين التنقل للهواتف
window.addEventListener('popstate', function(event) {
    // معالجة زر الرجوع بطريقة أفضل
    if (isDownloading) {
        event.preventDefault();
        const confirmMessage = lang === 'ar' ?
            'جاري التحميل. هل تريد المغادرة؟' :
            'Download in progress. Do you want to leave?';

        if (confirm(confirmMessage)) {
            window.history.back();
        } else {
            window.history.pushState(null, null, window.location.href);
        }
    }
});

// عناصر DOM
const mainModImage = document.getElementById('main-mod-image');
const prevButton = document.getElementById('prev-image');
const nextButton = document.getElementById('next-image');
const thumbnailContainer = document.getElementById('thumbnail-container');

/**
 * تحديث الصورة الرئيسية
 */
function updateImage() {
    if (imageUrls.length > 0) {
        mainModImage.src = imageUrls[currentImageIndex];
    } else {
        mainModImage.src = '';
    }
    updateThumbnails();
}

/**
 * تحديث الصور المصغرة - محسن للهواتف
 */
function updateThumbnails() {
    thumbnailContainer.innerHTML = '';
    if (imageUrls.length === 0) return;

    // عرض جميع الصور المصغرة مع تمرير أفقي
    for (let i = 0; i < imageUrls.length; i++) {
        const thumbnail = document.createElement('img');
        thumbnail.src = imageUrls[i];
        thumbnail.classList.add('thumbnail');
        thumbnail.loading = 'lazy'; // تحميل كسول للأداء

        if (i === currentImageIndex) {
            thumbnail.classList.add('active');
        }

        thumbnail.dataset.index = i;
        thumbnail.addEventListener('click', (event) => {
            event.preventDefault();
            currentImageIndex = parseInt(event.target.dataset.index);
            updateImage();

            // تمرير الصورة المحددة إلى المنتصف
            thumbnail.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'center'
            });
        });

        // إضافة معالج اللمس للهواتف
        thumbnail.addEventListener('touchstart', (event) => {
            event.preventDefault();
            thumbnail.style.transform = 'scale(0.95)';
        });

        thumbnail.addEventListener('touchend', (event) => {
            event.preventDefault();
            thumbnail.style.transform = 'scale(1)';
            currentImageIndex = parseInt(event.target.dataset.index);
            updateImage();
        });

        thumbnailContainer.appendChild(thumbnail);
    }

    // إخفاء أزرار التنقل إذا كان هناك صورة واحدة فقط
    const prevButton = document.getElementById('prev-image');
    const nextButton = document.getElementById('next-image');

    if (imageUrls.length <= 1) {
        prevButton.style.display = 'none';
        nextButton.style.display = 'none';
    } else {
        prevButton.style.display = 'flex';
        nextButton.style.display = 'flex';
    }
}

/**
 * التنقل بين الصور
 */
prevButton.addEventListener('click', () => {
    currentImageIndex = (currentImageIndex - 1 + imageUrls.length) % imageUrls.length;
    updateImage();
});

nextButton.addEventListener('click', () => {
    currentImageIndex = (currentImageIndex + 1) % imageUrls.length;
    updateImage();
});

/**
 * معالجة التحميل - محسن للهواتف
 */
function handleDownload() {
    // منع النقرات المتعددة
    const button = document.getElementById('download-button');
    if (button.disabled || isDownloading) {
        return;
    }

    // تعطيل الزر مؤقتاً لمنع النقرات المتعددة
    button.disabled = true;
    setTimeout(() => {
        button.disabled = false;
    }, 1000);

    if (adsSettings && adsSettings.ads_enabled && !adShown) {
        const displayMode = adsSettings.ad_display_mode;
        if (displayMode === 'on_download') {
            showAd();
        } else {
            downloadMod();
        }
    } else {
        downloadMod();
    }
}

/**
 * عرض الإعلان
 */
function showAd() {
    if (!adsSettings || !adsSettings.ad_direct_link) {
        downloadMod();
        return;
    }

    const overlay = document.getElementById('ad-overlay');
    const closeBtn = document.getElementById('close-ad-btn');
    const countdown = document.getElementById('countdown');

    overlay.classList.remove('hidden');
    adShown = true;

    // فتح الإعلان في نافذة جديدة
    window.open(adsSettings.ad_direct_link, '_blank');

    // إظهار زر الإغلاق بعد التأخير المحدد
    const closeDelay = adsSettings.close_button_delay || 5;
    let timeLeft = closeDelay;

    const countdownInterval = setInterval(() => {
        const timeText = lang === 'ar' ? 
            `يمكن الإغلاق خلال ${timeLeft} ثانية` : 
            `Can close in ${timeLeft} seconds`;
        countdown.textContent = timeText;
        timeLeft--;

        if (timeLeft < 0) {
            clearInterval(countdownInterval);
            closeBtn.classList.remove('hidden');
            countdown.textContent = '';
        }
    }, 1000);

    // بدء التحميل تلقائياً
    setTimeout(() => {
        downloadMod();
    }, 1000);
}

/**
 * إغلاق الإعلان
 */
function closeAd() {
    const overlay = document.getElementById('ad-overlay');
    overlay.classList.add('hidden');
}

/**
 * تحميل المود
 */
function downloadMod() {
    if (isDownloading || isDownloaded) {
        if (isDownloaded) {
            openDownloadedFile();
        }
        return;
    }
    startDownload();
}

/**
 * بدء التحميل
 */
function startDownload() {
    isDownloading = true;
    downloadStartTime = Date.now();
    updateDownloadButton();
    simulateDownload();
}

/**
 * محاكاة التحميل
 */
async function simulateDownload() {
    const button = document.getElementById('download-button');
    const progressBar = document.getElementById('progress-bar');
    const downloadText = document.getElementById('download-text');
    const downloadIcon = document.getElementById('download-icon');

    // تقدير حجم الملف
    modFileSize = Math.random() * 50 + 10;
    const modFileExtension = modDownloadUrl.includes('.mcaddon') ? '.mcaddon' : '.mcpack';
    downloadedFileName = `${modTitle.replace(/[^a-zA-Z0-9]/g, '_')}${modFileExtension}`;

    // محاكاة سرعة التحميل
    const downloadSpeed = Math.random() * 5 + 2;
    const totalTime = (modFileSize / downloadSpeed) * 1000;

    let startTime = Date.now();

    const updateProgress = () => {
        const elapsed = Date.now() - startTime;
        downloadProgress = Math.min((elapsed / totalTime) * 100, 100);

        progressBar.style.width = downloadProgress + '%';

        if (downloadProgress < 100) {
            const downloadingText = lang === 'ar' ? 'جاري التحميل...' : 'Downloading...';
            const downloadedMB = (modFileSize * downloadProgress / 100).toFixed(1);
            const speedMBps = downloadSpeed.toFixed(1);

            const remainingMB = modFileSize - (modFileSize * downloadProgress / 100);
            const remainingSeconds = Math.ceil(remainingMB / downloadSpeed);
            const remainingTime = remainingSeconds > 60 ?
                `${Math.ceil(remainingSeconds / 60)}${lang === 'ar' ? 'د' : 'm'}` :
                `${remainingSeconds}${lang === 'ar' ? 'ث' : 's'}`;

            downloadText.textContent = `${downloadingText} ${Math.round(downloadProgress)}%`;

            const button = document.getElementById('download-button');
            const downloadedText = lang === 'ar' ? 'المحمل:' : 'Downloaded:';
            const speedText = lang === 'ar' ? 'السرعة:' : 'Speed:';
            const timeRemainingText = lang === 'ar' ? 'الوقت المتبقي:' : 'Time remaining:';
            
            button.title = `${downloadedText} ${downloadedMB}/${modFileSize.toFixed(1)} MB\n${speedText} ${speedMBps} MB/s\n${timeRemainingText} ${remainingTime}`;

            if (downloadProgress < 30) {
                downloadIcon.innerHTML = '<div class="spinner"></div>';
            } else if (downloadProgress < 70) {
                downloadIcon.textContent = '⬇️';
            } else {
                downloadIcon.textContent = '📦';
            }

            requestAnimationFrame(updateProgress);
        } else {
            completeDownload();
        }
    };

    // بدء التحميل الفعلي - محسن للهواتف
    try {
        // إنشاء iframe مخفي للتحميل
        const downloadFrame = document.createElement('iframe');
        downloadFrame.style.display = 'none';
        downloadFrame.src = modDownloadUrl;
        document.body.appendChild(downloadFrame);

        // إزالة الإطار بعد فترة
        setTimeout(() => {
            if (document.body.contains(downloadFrame)) {
                document.body.removeChild(downloadFrame);
            }
        }, 5000);

        showDownloadStartNotification();
    } catch (error) {
        console.error('Error starting download:', error);
        // في حالة الفشل، استخدام الطريقة التقليدية
        try {
            const downloadLink = document.createElement('a');
            downloadLink.href = modDownloadUrl;
            downloadLink.download = downloadedFileName || 'mod_file';
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        } catch (linkError) {
            // كحل أخير، فتح في نافذة جديدة
            window.open(modDownloadUrl, '_blank');
        }
    }

    updateProgress();
}

/**
 * إكمال التحميل
 */
function completeDownload() {
    isDownloading = false;
    isDownloaded = true;
    downloadProgress = 100;

    const downloadEndTime = Date.now();
    const totalDownloadTime = (downloadEndTime - downloadStartTime) / 1000;
    const averageSpeed = (modFileSize / totalDownloadTime).toFixed(1);

    if (!downloadedFileName) {
        const modFileExtension = modDownloadUrl.includes('.mcaddon') ? '.mcaddon' : '.mcpack';
        downloadedFileName = `${modTitle.replace(/[^a-zA-Z0-9]/g, '_')}${modFileExtension}`;
    }

    updateDownloadButton();

    const downloadStats = {
        fileSize: modFileSize,
        downloadTime: totalDownloadTime,
        averageSpeed: averageSpeed,
        fileName: downloadedFileName,
        completedAt: new Date().toISOString()
    };

    localStorage.setItem(`download_stats_${modId}`, JSON.stringify(downloadStats));
    showDownloadCompleteNotification(downloadStats);
}

/**
 * تحديث زر التحميل
 */
function updateDownloadButton() {
    const button = document.getElementById('download-button');
    const downloadText = document.getElementById('download-text');
    const downloadIcon = document.getElementById('download-icon');
    const progressBar = document.getElementById('progress-bar');

    if (isDownloading) {
        button.classList.add('downloading');
        button.classList.remove('downloaded');
        const downloadingText = lang === 'ar' ? 'جاري التحميل...' : 'Downloading...';
        downloadText.textContent = `${downloadingText} ${Math.round(downloadProgress)}%`;
        progressBar.style.width = downloadProgress + '%';
    } else if (isDownloaded) {
        button.classList.remove('downloading');
        button.classList.add('downloaded');
        const openText = lang === 'ar' ? 'فتح المود' : 'Open Mod';
        downloadText.textContent = openText;
        downloadIcon.textContent = '📂';
        progressBar.style.width = '100%';
        progressBar.style.backgroundColor = '#2196F3';
    } else {
        button.classList.remove('downloading', 'downloaded');
        const downloadButtonText = lang === 'ar' ? 'تحميل المود' : 'Download Mod';
        downloadText.textContent = downloadButtonText;
        downloadIcon.textContent = '📥';
        progressBar.style.width = '0%';
    }
}

/**
 * فتح الملف المحمل
 */
function openDownloadedFile() {
    const minecraftUrl = `minecraft://import?url=${encodeURIComponent(modDownloadUrl)}`;

    try {
        window.location.href = minecraftUrl;
        showOpeningNotification();

        setTimeout(() => {
            showMinecraftInstructions();
        }, 2000);
    } catch (error) {
        console.error('Error opening Minecraft:', error);
        showMinecraftInstructions();
    }
}

/**
 * إظهار إشعار بدء التحميل
 */
function showDownloadStartNotification() {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 left-4 bg-blue-500 text-white p-3 rounded-lg shadow-lg z-50 notification-enter';
    const startText = lang === 'ar' ? 'بدء التحميل...' : 'Download started...';
    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <span class="text-lg">📥</span>
            <span>${startText}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.remove('notification-enter');
            notification.classList.add('notification-exit');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }
    }, 2000);
}

/**
 * إظهار إشعار فتح ماين كرافت
 */
function showOpeningNotification() {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 left-4 bg-blue-500 text-white p-3 rounded-lg shadow-lg z-50 notification-enter';
    const openingText = lang === 'ar' ? 'جاري فتح ماين كرافت...' : 'Opening Minecraft...';
    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <div class="spinner"></div>
            <span>${openingText}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.remove('notification-enter');
            notification.classList.add('notification-exit');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }
    }, 3000);
}

/**
 * إظهار إشعار اكتمال التحميل
 */
function showDownloadCompleteNotification(stats = null) {
    const button = document.getElementById('download-button');
    button.classList.add('download-success-animation');

    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-70 notification-enter max-w-sm';
    const successText = lang === 'ar' ? 'تم التحميل بنجاح!' : 'Download Complete!';

    let statsHtml = '';
    if (stats) {
        const timeText = stats.downloadTime < 60 ?
            `${stats.downloadTime.toFixed(1)}${lang === 'ar' ? 'ث' : 's'}` :
            `${(stats.downloadTime / 60).toFixed(1)}${lang === 'ar' ? 'د' : 'm'}`;

        const sizeText = lang === 'ar' ? 'الحجم:' : 'Size:';
        const timeLabel = lang === 'ar' ? 'الوقت:' : 'Time:';
        const speedLabel = lang === 'ar' ? 'السرعة:' : 'Speed:';

        statsHtml = `
            <div class="text-xs opacity-75 mt-2 space-y-1">
                <div>${sizeText} ${stats.fileSize.toFixed(1)} MB</div>
                <div>${timeLabel} ${timeText}</div>
                <div>${speedLabel} ${stats.averageSpeed} MB/s</div>
            </div>
        `;
    }

    const clickText = lang === 'ar' ? 'انقر على الزر لفتح المود' : 'Click button to open mod';
    notification.innerHTML = `
        <div class="flex items-start space-x-2">
            <span class="text-2xl">✅</span>
            <div class="flex-1">
                <h4 class="font-bold">${successText}</h4>
                <p class="text-sm">${downloadedFileName}</p>
                <p class="text-xs opacity-75 mt-1">${clickText}</p>
                ${statsHtml}
            </div>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        button.classList.remove('download-success-animation');
    }, 600);

    setTimeout(() => {
        notification.classList.remove('notification-enter');
        notification.classList.add('notification-exit');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 7000);
}

/**
 * إظهار تعليمات ماين كرافت
 */
function showMinecraftInstructions() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-75 z-80 flex items-center justify-center';

    const instructionsTitle = lang === 'ar' ? 'تعليمات التثبيت' : 'Installation Instructions';
    const autoText = lang === 'ar' ? 'إذا لم يفتح ماين كرافت تلقائياً:' : 'If Minecraft did not open automatically:';
    const step1 = lang === 'ar' ? '1. افتح ماين كرافت' : '1. Open Minecraft';
    const step2 = lang === 'ar' ? '2. اذهب إلى الإعدادات' : '2. Go to Settings';
    const step3 = lang === 'ar' ? '3. اختر "التخزين"' : '3. Select "Storage"';
    const step4 = lang === 'ar' ? '4. اختر "استيراد"' : '4. Select "Import"';
    const step5 = lang === 'ar' ? '5. اختر الملف المحمل' : '5. Select the downloaded file';
    const openMinecraftText = lang === 'ar' ? 'فتح ماين كرافت' : 'Open Minecraft';
    const closeText = lang === 'ar' ? 'إغلاق' : 'Close';
    const direction = lang === 'ar' ? 'rtl' : 'ltr';

    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md mx-4 text-center">
            <div class="text-6xl mb-4">🎮</div>
            <h3 class="text-lg font-bold text-gray-800 mb-4">${instructionsTitle}</h3>
            <div class="text-sm text-gray-600 mb-6 text-right" dir="${direction}">
                ${autoText}
                <br><br>
                ${step1}<br>
                ${step2}<br>
                ${step3}<br>
                ${step4}<br>
                ${step5}
            </div>
            <div class="flex space-x-2 justify-center">
                <button onclick="tryOpenMinecraftAgain()" class="bg-green-500 text-white px-4 py-2 rounded">
                    ${openMinecraftText}
                </button>
                <button onclick="closeInstructionsModal()" class="bg-gray-500 text-white px-4 py-2 rounded">
                    ${closeText}
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    window.currentInstructionsModal = modal;
}

/**
 * محاولة فتح ماين كرافت مرة أخرى
 */
function tryOpenMinecraftAgain() {
    const minecraftUrl = `minecraft://import?url=${encodeURIComponent(modDownloadUrl)}`;
    window.open(minecraftUrl, '_blank');
    closeInstructionsModal();
}

/**
 * إغلاق نافذة التعليمات
 */
function closeInstructionsModal() {
    if (window.currentInstructionsModal) {
        document.body.removeChild(window.currentInstructionsModal);
        window.currentInstructionsModal = null;
    }
}

/**
 * التحقق من عرض الإعلان
 */
function checkAdDisplay() {
    if (adsSettings && adsSettings.ads_enabled && !adShown) {
        const displayMode = adsSettings.ad_display_mode;
        const delay = adsSettings.ad_delay_seconds || 3;

        if (displayMode === 'after_page_load' || displayMode === 'after_delay') {
            setTimeout(() => {
                showAd();
            }, delay * 1000);
        }
    }
}

// التحميل الأولي
document.addEventListener('DOMContentLoaded', () => {
    updateImage();
    checkAdDisplay();
});
