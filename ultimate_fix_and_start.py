#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الحل النهائي الشامل - إصلاح جميع المشاكل وتشغيل البوت
"""

import os
import sys
import time
import subprocess
import requests
import socket
from datetime import datetime

def print_header():
    """طباعة رأس البرنامج"""
    print("🚀 الحل النهائي الشامل - إصلاح وتشغيل البوت")
    print("=" * 60)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

def install_missing_dependencies():
    """تثبيت التبعيات المفقودة"""
    print("📦 فحص وتثبيت التبعيات...")
    
    required_packages = [
        'python-dotenv',
        'python-telegram-bot',
        'flask',
        'requests',
        'supabase'
    ]
    
    installed_count = 0
    
    for package in required_packages:
        try:
            # محاولة استيراد الحزمة
            if package == 'python-telegram-bot':
                import telegram
            elif package == 'python-dotenv':
                import dotenv
            elif package == 'flask':
                import flask
            elif package == 'requests':
                import requests
            elif package == 'supabase':
                import supabase
            
            print(f"   ✅ {package}: متوفر")
            installed_count += 1
            
        except ImportError:
            print(f"   ⏳ تثبيت {package}...")
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                             check=True, capture_output=True)
                print(f"   ✅ تم تثبيت {package}")
                installed_count += 1
            except subprocess.CalledProcessError as e:
                print(f"   ❌ فشل تثبيت {package}: {e}")
    
    print(f"📊 التبعيات: {installed_count}/{len(required_packages)} متوفرة")
    return installed_count == len(required_packages)

def check_port(port):
    """فحص إذا كان المنفذ متاح"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        return result == 0
    except:
        return False

def kill_process_on_port(port):
    """إيقاف العملية على منفذ معين"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            for line in lines:
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        subprocess.run(['taskkill', '/F', '/PID', pid], 
                                     capture_output=True, check=False)
                        print(f"   🔄 تم إيقاف العملية {pid} على المنفذ {port}")
                        return True
        return False
    except:
        return False

def start_ngrok():
    """تشغيل ngrok مع إعدادات محسنة"""
    print("🚀 تشغيل ngrok...")
    
    # إيقاف ngrok إذا كان يعمل
    try:
        if os.name == 'nt':
            subprocess.run(['taskkill', '/f', '/im', 'ngrok.exe'], 
                         capture_output=True, check=False)
        time.sleep(2)
    except:
        pass
    
    # تشغيل ngrok جديد
    try:
        # تشغيل ngrok مع إعدادات محسنة
        cmd = ['ngrok', 'http', '5001', '--log=stdout']
        process = subprocess.Popen(cmd, stdout=subprocess.DEVNULL, 
                                 stderr=subprocess.DEVNULL)
        
        print("   ⏳ انتظار تشغيل ngrok...")
        for i in range(30):
            if check_port(4040):
                print("   ✅ تم تشغيل ngrok بنجاح")
                return True
            time.sleep(1)
        
        print("   ❌ فشل في تشغيل ngrok")
        return False
        
    except Exception as e:
        print(f"   ❌ خطأ في تشغيل ngrok: {e}")
        return False

def get_ngrok_url():
    """الحصول على رابط ngrok"""
    try:
        response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
        if response.status_code == 200:
            data = response.json()
            tunnels = data.get('tunnels', [])
            for tunnel in tunnels:
                if tunnel.get('proto') == 'https':
                    return tunnel.get('public_url')
        return None
    except:
        return None

def update_env_file():
    """تحديث ملف .env مع رابط ngrok الجديد"""
    print("🔄 تحديث ملف .env...")
    
    ngrok_url = get_ngrok_url()
    if not ngrok_url:
        print("   ❌ لا يمكن الحصول على رابط ngrok")
        return False
    
    try:
        # قراءة ملف .env الحالي
        env_content = {}
        if os.path.exists('.env'):
            with open('.env', 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_content[key.strip()] = value.strip()
        
        # تحديث الرابط
        env_content['WEB_SERVER_URL'] = ngrok_url
        
        # كتابة ملف .env محدث
        with open('.env', 'w', encoding='utf-8') as f:
            for key, value in env_content.items():
                f.write(f"{key}={value}\n")
        
        print(f"   ✅ تم تحديث WEB_SERVER_URL: {ngrok_url}")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في تحديث ملف .env: {e}")
        return False

def start_web_server():
    """تشغيل خادم الويب"""
    print("🌐 تشغيل خادم الويب...")
    
    # إيقاف خادم الويب إذا كان يعمل
    if check_port(5001):
        print("   🔄 إيقاف خادم الويب الحالي...")
        kill_process_on_port(5001)
        time.sleep(2)
    
    try:
        # تشغيل خادم الويب في الخلفية
        cmd = [sys.executable, 'web_server.py']
        process = subprocess.Popen(cmd, stdout=subprocess.DEVNULL, 
                                 stderr=subprocess.DEVNULL)
        
        print("   ⏳ انتظار تشغيل خادم الويب...")
        for i in range(15):
            if check_port(5001):
                print("   ✅ تم تشغيل خادم الويب بنجاح")
                return True
            time.sleep(1)
        
        print("   ❌ فشل في تشغيل خادم الويب")
        return False
        
    except Exception as e:
        print(f"   ❌ خطأ في تشغيل خادم الويب: {e}")
        return False

def test_system():
    """اختبار النظام بالكامل"""
    print("🧪 اختبار النظام...")
    
    tests = [
        ("ngrok API", lambda: check_port(4040)),
        ("خادم الويب", lambda: check_port(5001)),
        ("رابط ngrok", lambda: get_ngrok_url() is not None)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"   ✅ {test_name}: يعمل")
                passed += 1
            else:
                print(f"   ❌ {test_name}: لا يعمل")
        except:
            print(f"   ❌ {test_name}: خطأ في الاختبار")
    
    print(f"📊 الاختبارات: {passed}/{len(tests)} نجحت")
    return passed == len(tests)

def start_bot():
    """تشغيل البوت الرئيسي"""
    print("🤖 تشغيل البوت الرئيسي...")
    
    try:
        # تشغيل البوت
        subprocess.run([sys.executable, 'main.py'])
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البوت: {e}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    success_steps = 0
    total_steps = 6
    
    try:
        # الخطوة 1: تثبيت التبعيات
        if install_missing_dependencies():
            success_steps += 1
            print("✅ الخطوة 1: التبعيات جاهزة")
        else:
            print("❌ الخطوة 1: فشل في تثبيت التبعيات")
        
        # الخطوة 2: تشغيل ngrok
        if start_ngrok():
            success_steps += 1
            print("✅ الخطوة 2: ngrok يعمل")
        else:
            print("❌ الخطوة 2: فشل تشغيل ngrok")
        
        # الخطوة 3: تحديث ملف .env
        if update_env_file():
            success_steps += 1
            print("✅ الخطوة 3: ملف .env محدث")
        else:
            print("❌ الخطوة 3: فشل تحديث ملف .env")
        
        # الخطوة 4: تشغيل خادم الويب
        if start_web_server():
            success_steps += 1
            print("✅ الخطوة 4: خادم الويب يعمل")
        else:
            print("❌ الخطوة 4: فشل تشغيل خادم الويب")
        
        # الخطوة 5: اختبار النظام
        if test_system():
            success_steps += 1
            print("✅ الخطوة 5: جميع الاختبارات نجحت")
        else:
            print("❌ الخطوة 5: بعض الاختبارات فشلت")
        
        # عرض النتائج
        print("\n" + "=" * 60)
        print("📊 نتائج الإعداد:")
        print(f"✅ نجح: {success_steps}/{total_steps} خطوات")
        
        if success_steps >= 4:  # على الأقل 4 خطوات من 6
            success_steps += 1  # الخطوة 6
            print("✅ الخطوة 6: النظام جاهز للتشغيل")
            
            print("\n🎉 النظام جاهز! بدء تشغيل البوت...")
            print("=" * 60)
            
            # الخطوة 6: تشغيل البوت
            start_bot()
        else:
            print(f"\n⚠️ النظام غير جاهز ({success_steps}/{total_steps})")
            print("\n🔧 خطوات الإصلاح:")
            print("   1. تأكد من تثبيت ngrok")
            print("   2. تأكد من اتصال الإنترنت")
            print("   3. أعد تشغيل الكمبيوتر")
            print("   4. جرب مرة أخرى")
        
        return success_steps >= 4
        
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ تم تشغيل البوت بنجاح!")
        else:
            print("\n❌ فشل في تشغيل البوت")
        input("\n📱 اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التشغيل بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("\n📱 اضغط Enter للخروج...")
