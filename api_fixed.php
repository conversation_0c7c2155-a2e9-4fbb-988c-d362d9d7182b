<?php
/**
 * API محدث بالبيانات الصحيحة
 * Updated API with Correct Database
 */

// تضمين ملف الإعدادات
define('INCLUDED', true);
require_once 'config_fixed.php';

// إعداد headers للاستجابة
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// معالجة طلبات OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

/**
 * دالة إرجاع استجابة JSON
 */
function sendJsonResponse($data, $status_code = 200, $message = null) {
    http_response_code($status_code);
    
    $response = [
        'success' => $status_code >= 200 && $status_code < 300,
        'status_code' => $status_code,
        'timestamp' => date('c'),
        'data' => $data
    ];
    
    if ($message) {
        $response['message'] = $message;
    }
    
    if (!$response['success']) {
        $response['error'] = $data;
        unset($response['data']);
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

/**
 * دالة معالجة الأخطاء
 */
function handleError($message, $code = 500, $details = null) {
    logError("API Error: $message", 'ERROR', [
        'code' => $code,
        'details' => $details,
        'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
        'method' => $_SERVER['REQUEST_METHOD'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);
    
    $error_data = ['message' => $message];
    if ($details && DEBUG_MODE) {
        $error_data['details'] = $details;
    }
    
    sendJsonResponse($error_data, $code);
}

/**
 * دالة التحقق من صحة معرف المود
 */
function validateModId($mod_id) {
    if (empty($mod_id)) {
        return false;
    }
    
    // التحقق من أن المعرف رقم صحيح
    if (!is_numeric($mod_id) || $mod_id <= 0) {
        return false;
    }
    
    return true;
}

/**
 * دالة جلب بيانات المود مع معالجة أخطاء محسنة
 */
function getModData($mod_id) {
    if (!validateModId($mod_id)) {
        handleError('معرف المود غير صحيح', 400, 'Mod ID must be a positive integer');
    }
    
    logError("Fetching mod data for ID: $mod_id", 'INFO');
    
    // جلب البيانات من الجدول الصحيح
    $response = getModById($mod_id);
    
    if (!$response['success']) {
        // محاولة مع endpoint مختلف
        $endpoint_alt = "/rest/v1/" . MODS_TABLE . "?id=eq.$mod_id";
        $response = makeSupabaseRequest($endpoint_alt);
        
        if (!$response['success']) {
            handleError(
                'فشل في الاتصال بقاعدة البيانات', 
                503, 
                [
                    'supabase_error' => $response['error'],
                    'http_code' => $response['http_code'],
                    'table' => MODS_TABLE
                ]
            );
        }
    }
    
    if (empty($response['data'])) {
        // محاولة البحث في جميع المودات
        $search_response = getModsList(100, 0);
        
        if ($search_response['success'] && !empty($search_response['data'])) {
            // البحث يدوياً في النتائج
            foreach ($search_response['data'] as $mod) {
                if (isset($mod['id']) && $mod['id'] == $mod_id) {
                    return $mod;
                }
            }
        }
        
        handleError('المود غير موجود', 404, "Mod with ID $mod_id not found in table " . MODS_TABLE);
    }
    
    $mod_data = $response['data'][0];
    logError("Mod data retrieved successfully", 'INFO', ['mod_id' => $mod_id, 'mod_name' => $mod_data['name'] ?? 'Unknown']);
    
    return $mod_data;
}

/**
 * دالة تنسيق بيانات المود للإرجاع
 */
function formatModData($mod_data, $lang = 'ar') {
    $formatted = [
        'id' => $mod_data['id'] ?? null,
        'title' => $mod_data['name'] ?? 'N/A',
        'image_urls' => $mod_data['image_urls'] ?? [],
        'version' => $mod_data['version'] ?? 'N/A',
        'download_link' => $mod_data['download_url'] ?? '',
        'category' => $mod_data['category'] ?? 'unknown',
        'created_at' => $mod_data['created_at'] ?? null,
        'updated_at' => $mod_data['updated_at'] ?? null,
        'description' => [
            'ar' => $mod_data['description_ar'] ?? $mod_data['description'] ?? '',
            'en' => $mod_data['description_en'] ?? $mod_data['description'] ?? ''
        ],
        'telegram_description' => [
            'ar' => $mod_data['telegram_description_ar'] ?? null,
            'en' => $mod_data['telegram_description_en'] ?? null
        ]
    ];
    
    // إضافة معلومات إضافية إذا كانت متوفرة
    if (isset($mod_data['file_size'])) {
        $formatted['file_size'] = $mod_data['file_size'];
    }
    
    if (isset($mod_data['downloads_count'])) {
        $formatted['downloads_count'] = $mod_data['downloads_count'];
    }
    
    // تنظيف الصور
    if (!empty($formatted['image_urls']) && is_string($formatted['image_urls'])) {
        $formatted['image_urls'] = json_decode($formatted['image_urls'], true) ?: [];
    }
    
    return $formatted;
}

/**
 * معالجة طلب بيانات المود
 */
function handleModRequest() {
    $mod_id = $_GET['id'] ?? null;
    $lang = $_GET['lang'] ?? 'ar';
    
    if (!in_array($lang, ['ar', 'en'])) {
        $lang = 'ar';
    }
    
    logError("Processing mod request", 'INFO', ['mod_id' => $mod_id, 'lang' => $lang]);
    
    $mod_data = getModData($mod_id);
    $formatted_data = formatModData($mod_data, $lang);
    
    sendJsonResponse($formatted_data, 200, 'تم جلب بيانات المود بنجاح');
}

/**
 * معالجة طلب معلومات الملف
 */
function handleFileInfoRequest() {
    $mod_id = $_GET['mod_id'] ?? null;
    
    if (!validateModId($mod_id)) {
        handleError('معرف المود مطلوب', 400);
    }
    
    logError("Processing file info request", 'INFO', ['mod_id' => $mod_id]);
    
    $mod_data = getModData($mod_id);
    $download_url = $mod_data['download_url'] ?? '';
    
    if (empty($download_url)) {
        handleError('رابط التحميل غير متوفر', 404);
    }
    
    // تحديد نوع الملف
    $file_extension = '.mcpack';
    if (strpos($download_url, '.mcaddon') !== false) {
        $file_extension = '.mcaddon';
    } elseif (strpos($download_url, '.zip') !== false) {
        $file_extension = '.zip';
    }
    
    // تقدير حجم الملف
    $estimated_size = $mod_data['file_size'] ?? (rand(10, 50) + rand(0, 99) / 100);
    
    // تحديد اسم الملف
    $mod_title = $mod_data['name'] ?? 'mod';
    $safe_filename = preg_replace('/[^a-zA-Z0-9\s\-_\u0600-\u06FF]/', '', $mod_title);
    $safe_filename = trim($safe_filename);
    if (empty($safe_filename)) {
        $safe_filename = 'minecraft_mod_' . $mod_id;
    }
    $filename = $safe_filename . $file_extension;
    
    $file_info = [
        'filename' => $filename,
        'size_mb' => $estimated_size,
        'file_extension' => $file_extension,
        'download_url' => $download_url,
        'mod_id' => $mod_id,
        'mod_title' => $mod_title
    ];
    
    sendJsonResponse($file_info, 200, 'تم جلب معلومات الملف بنجاح');
}

/**
 * معالجة طلب اختبار الاتصال
 */
function handleTestRequest() {
    logError("Processing test request", 'INFO');
    
    $tests = [
        'api_status' => 'working',
        'timestamp' => date('c'),
        'php_version' => PHP_VERSION,
        'database_url' => SUPABASE_URL,
        'table_name' => MODS_TABLE,
        'supabase_connection' => testSupabaseConnection(),
        'environment' => checkEnvironment(),
        'mods_table_test' => makeSupabaseRequest("/rest/v1/" . MODS_TABLE . "?limit=1")
    ];
    
    sendJsonResponse($tests, 200, 'اختبار API ناجح');
}

/**
 * معالجة طلب قائمة المودات
 */
function handleModsListRequest() {
    $limit = min(max((int)($_GET['limit'] ?? 10), 1), 100);
    $offset = max((int)($_GET['offset'] ?? 0), 0);
    
    logError("Processing mods list request", 'INFO', ['limit' => $limit, 'offset' => $offset]);
    
    $response = getModsList($limit, $offset);
    
    if (!$response['success']) {
        handleError('فشل في جلب قائمة المودات', 503, $response['error']);
    }
    
    $mods_list = [];
    foreach ($response['data'] as $mod) {
        $mods_list[] = [
            'id' => $mod['id'],
            'title' => $mod['name'] ?? 'N/A',
            'image_url' => !empty($mod['image_urls']) ? (is_array($mod['image_urls']) ? $mod['image_urls'][0] : json_decode($mod['image_urls'], true)[0] ?? '') : '',
            'version' => $mod['version'] ?? 'N/A',
            'category' => $mod['category'] ?? 'unknown'
        ];
    }
    
    sendJsonResponse([
        'mods' => $mods_list,
        'total' => count($mods_list),
        'limit' => $limit,
        'offset' => $offset,
        'table' => MODS_TABLE
    ], 200, 'تم جلب قائمة المودات بنجاح');
}

// معالجة الطلبات الرئيسية
try {
    $request_uri = $_SERVER['REQUEST_URI'];
    $method = $_SERVER['REQUEST_METHOD'];
    
    // تسجيل الطلب
    logError("API Request received", 'INFO', [
        'method' => $method,
        'uri' => $request_uri,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
    ]);
    
    // استخراج المسار
    $path = parse_url($request_uri, PHP_URL_PATH);
    $path = str_replace('/api_fixed.php', '', $path);
    
    // معالجة الطلبات حسب المعاملات
    if (isset($_GET['path'])) {
        $endpoint = $_GET['path'];
    } elseif (isset($_GET['action'])) {
        $endpoint = $_GET['action'];
    } else {
        $endpoint = '/test';
    }
    
    switch ($endpoint) {
        case '/mod':
            handleModRequest();
            break;
            
        case '/file-info':
            handleFileInfoRequest();
            break;
            
        case '/test':
            handleTestRequest();
            break;
            
        case '/mods':
        case '/list':
            handleModsListRequest();
            break;
            
        default:
            handleError('نقطة النهاية غير موجودة', 404, "Endpoint '$endpoint' not found");
    }
    
} catch (Exception $e) {
    handleError('خطأ داخلي في الخادم', 500, [
        'exception' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
} catch (Error $e) {
    handleError('خطأ فادح في الخادم', 500, [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
