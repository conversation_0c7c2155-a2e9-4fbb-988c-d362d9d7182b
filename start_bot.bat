@echo off
chcp 65001 >nul
title بوت نشر مودات ماين كرافت - Supabase Edition

echo ============================================================
echo 🤖 بوت نشر مودات ماين كرافت - Supabase Edition
echo ============================================================
echo.

echo 🔄 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 🔧 يرجى تثبيت Python 3.8+ أولاً
    pause
    exit /b 1
)

echo ✅ Python موجود
echo.

echo 🔄 فحص المتطلبات...
python -c "import telegram, requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ المكتبات المطلوبة غير مثبتة
    echo 📦 تثبيت المتطلبات...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

echo ✅ المتطلبات متوفرة
echo.

echo 🧪 اختبار الاتصال بـ Supabase...
python test_supabase.py >nul 2>&1
if errorlevel 1 (
    echo ⚠️ مشكلة في الاتصال بـ Supabase
    echo 🔧 تحقق من إعدادات قاعدة البيانات
    echo.
    echo 🚀 سيتم تشغيل البوت على أي حال...
) else (
    echo ✅ الاتصال بـ Supabase يعمل بشكل صحيح
)

echo.
echo ============================================================
echo 🚀 بدء تشغيل البوت...
echo ⏹️ اضغط Ctrl+C لإيقاف البوت
echo ============================================================
echo.

python main.py

echo.
echo ============================================================
echo ⏹️ تم إيقاف البوت
echo ============================================================
pause
