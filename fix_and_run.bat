@echo off
chcp 65001 >nul
title إصلاح وتشغيل البوت - Bot Fix and Run

echo ===============================================
echo 🔧 أداة إصلاح وتشغيل البوت
echo    Bot Fix and Run Tool
echo ===============================================
echo.

echo 📋 الخطوات المتاحة:
echo    1. تشخيص سريع للشبكة
echo    2. إصلاح تلقائي للمشاكل
echo    3. تشغيل البوت مع وضع محسن
echo    4. اختبار الشبكة فقط
echo    5. تشغيل البوت العادي
echo    6. عرض دليل استكشاف الأخطاء
echo.

set /p choice="اختر رقم الخطوة (1-6): "

if "%choice%"=="1" goto diagnose_network
if "%choice%"=="2" goto auto_fix
if "%choice%"=="3" goto run_enhanced
if "%choice%"=="4" goto test_network
if "%choice%"=="5" goto run_normal
if "%choice%"=="6" goto show_guide
goto invalid_choice

:diagnose_network
echo.
echo 🔍 بدء التشخيص السريع للشبكة...
echo ===============================================
python diagnose_network.py
echo.
echo ✅ انتهى التشخيص. اضغط أي مفتاح للمتابعة...
pause >nul
goto menu

:auto_fix
echo.
echo 🔧 بدء الإصلاح التلقائي للمشاكل...
echo ===============================================
python auto_fix_network.py
echo.
echo ✅ انتهى الإصلاح. اضغط أي مفتاح للمتابعة...
pause >nul
goto menu

:run_enhanced
echo.
echo 🚀 تشغيل البوت مع وضع الشبكة المحسن...
echo ===============================================
python run_bot_offline_mode.py
goto end

:test_network
echo.
echo 🌐 اختبار الشبكة...
echo ===============================================
if exist network_test.py (
    python network_test.py
) else (
    echo ❌ ملف network_test.py غير موجود
    echo 💡 جرب تشغيل: python fix_dns_connection.py أولاً
)
echo.
echo ✅ انتهى الاختبار. اضغط أي مفتاح للمتابعة...
pause >nul
goto menu

:run_normal
echo.
echo 🤖 تشغيل البوت العادي...
echo ===============================================
python main.py
goto end

:show_guide
echo.
echo 📖 دليل استكشاف الأخطاء
echo ===============================================
echo.
echo 🌐 مشاكل الاتصال:
echo    - تحقق من اتصال الإنترنت
echo    - جرب شبكة أخرى (هاتف محمول)
echo    - أعد تشغيل الراوتر
echo.
echo 🔒 مشاكل الأمان:
echo    - تعطيل مضاد الفيروسات مؤقتاً
echo    - تعطيل الجدار الناري مؤقتاً
echo    - إضافة Python للاستثناءات
echo.
echo 🔧 مشاكل DNS:
echo    - تغيير DNS إلى *******
echo    - مسح DNS cache: ipconfig /flushdns
echo    - إعادة تشغيل الكمبيوتر
echo.
echo 📱 مشاكل Telegram:
echo    - تحقق من صحة Bot Token
echo    - تحقق من حالة Telegram API
echo    - جرب VPN إذا كان Telegram محجوب
echo.
echo 🐍 مشاكل Python:
echo    - تحديث pip: python -m pip install --upgrade pip
echo    - إعادة تثبيت: pip install -r requirements.txt
echo    - تحقق من الإصدار: python --version
echo.
echo اضغط أي مفتاح للعودة للقائمة...
pause >nul
goto menu

:invalid_choice
echo.
echo ❌ اختيار غير صحيح. جرب مرة أخرى.
echo.
goto menu

:menu
cls
echo ===============================================
echo 🔧 أداة إصلاح وتشغيل البوت
echo    Bot Fix and Run Tool
echo ===============================================
echo.
echo 📋 الخطوات المتاحة:
echo    1. تشخيص سريع للشبكة
echo    2. إصلاح تلقائي للمشاكل
echo    3. تشغيل البوت مع وضع محسن
echo    4. اختبار الشبكة فقط
echo    5. تشغيل البوت العادي
echo    6. عرض دليل استكشاف الأخطاء
echo    7. خروج
echo.

set /p choice="اختر رقم الخطوة (1-7): "

if "%choice%"=="1" goto diagnose_network
if "%choice%"=="2" goto auto_fix
if "%choice%"=="3" goto run_enhanced
if "%choice%"=="4" goto test_network
if "%choice%"=="5" goto run_normal
if "%choice%"=="6" goto show_guide
if "%choice%"=="7" goto end
goto invalid_choice

:end
echo.
echo 👋 شكراً لاستخدام أداة إصلاح البوت
echo ===============================================
pause
