-- ===================================================================
-- جدول روابط التحميل المخصصة للمستخدمين
-- Custom Download Links Table for Telegram Bot Users
-- ===================================================================

-- إنشاء قاعدة البيانات (اختياري)
-- CREATE DATABASE IF NOT EXISTS telegram_bot_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE telegram_bot_db;

-- ===================================================================
-- الجدول الرئيسي: custom_download_links
-- ===================================================================

CREATE TABLE IF NOT EXISTS custom_download_links (
    -- المعرف الفريد للرابط
    id BIGSERIAL PRIMARY KEY,
    
    -- معرف القناة (فريد لكل قناة)
    channel_id VARCHAR(255) NOT NULL UNIQUE,
    
    -- الرابط المخصص للتحميل
    custom_link TEXT NOT NULL,
    
    -- معرف المستخدم الذي أنشأ الرابط
    created_by VARCHAR(255) NOT NULL,
    
    -- تاريخ إنشاء الرابط
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- تاريخ آخر تحديث
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- حالة الرابط (نشط/غير نشط)
    is_active BOOLEAN DEFAULT TRUE,
    
    -- عدد مرات استخدام الرابط
    usage_count BIGINT DEFAULT 0,
    
    -- تاريخ آخر استخدام للرابط
    last_used_at TIMESTAMP NULL,
    
    -- ملاحظات إضافية (اختياري)
    notes TEXT NULL,
    
    -- نوع الرابط (direct, redirect, custom)
    link_type VARCHAR(20) DEFAULT 'custom' CHECK (link_type IN ('direct', 'redirect', 'custom')),
    
    -- معرف المجموعة/الفئة (لتجميع الروابط)
    category VARCHAR(100) DEFAULT 'general'
    
);

-- ===================================================================
-- إنشاء الفهارس لتحسين الأداء
-- ===================================================================

-- فهرس على معرف القناة (الأكثر استخداماً)
CREATE INDEX idx_channel_id ON custom_download_links(channel_id);

-- فهرس على منشئ الرابط
CREATE INDEX idx_created_by ON custom_download_links(created_by);

-- فهرس على حالة الرابط
CREATE INDEX idx_is_active ON custom_download_links(is_active);

-- فهرس على تاريخ الإنشاء
CREATE INDEX idx_created_at ON custom_download_links(created_at);

-- فهرس على عدد الاستخدامات
CREATE INDEX idx_usage_count ON custom_download_links(usage_count);

-- فهرس مركب على القناة والحالة
CREATE INDEX idx_channel_active ON custom_download_links(channel_id, is_active);

-- فهرس على نوع الرابط
CREATE INDEX idx_link_type ON custom_download_links(link_type);

-- ===================================================================
-- إضافة القيود والتحقق من البيانات
-- ===================================================================

-- التأكد من عدم فراغ الرابط
ALTER TABLE custom_download_links 
ADD CONSTRAINT chk_custom_link_not_empty 
CHECK (LENGTH(TRIM(custom_link)) > 0);

-- التأكد من أن عداد الاستخدام موجب
ALTER TABLE custom_download_links 
ADD CONSTRAINT chk_usage_count_positive 
CHECK (usage_count >= 0);

-- التأكد من صحة تنسيق معرف القناة
ALTER TABLE custom_download_links 
ADD CONSTRAINT chk_channel_id_format 
CHECK (channel_id ~ '^(@[a-zA-Z0-9_]+|-[0-9]+)$');

-- التأكد من صحة تنسيق الرابط
ALTER TABLE custom_download_links 
ADD CONSTRAINT chk_custom_link_format 
CHECK (custom_link ~ '^https?://[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}');

-- ===================================================================
-- إنشاء Triggers للتحديث التلقائي
-- ===================================================================


-- ===================================================================
-- إنشاء Views للاستعلامات الشائعة
-- ===================================================================

-- عرض للروابط النشطة فقط
CREATE OR REPLACE VIEW v_active_custom_links AS
SELECT 
    id,
    channel_id,
    custom_link,
    created_by,
    created_at,
    updated_at,
    usage_count,
    last_used_at,
    notes,
    link_type,
    category,
    EXTRACT(DAY FROM (CURRENT_DATE - created_at))::INTEGER as days_since_created,
    CASE 
        WHEN last_used_at IS NULL THEN 'لم يستخدم بعد'
        WHEN EXTRACT(DAY FROM (CURRENT_DATE - last_used_at))::INTEGER = 0 THEN 'اليوم'
        WHEN EXTRACT(DAY FROM (CURRENT_DATE - last_used_at))::INTEGER = 1 THEN 'أمس'
        ELSE CONCAT(EXTRACT(DAY FROM (CURRENT_DATE - last_used_at))::INTEGER, ' أيام مضت')
    END as last_usage_text
FROM custom_download_links 
WHERE is_active = TRUE;

-- عرض للإحصائيات العامة
CREATE OR REPLACE VIEW v_link_statistics AS
SELECT 
    COUNT(*) as total_links,
    COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_links,
    COUNT(CASE WHEN is_active = FALSE THEN 1 END) as inactive_links,
    SUM(usage_count) as total_usage,
    AVG(usage_count) as avg_usage_per_link,
    MAX(usage_count) as max_usage,
    COUNT(CASE WHEN usage_count = 0 THEN 1 END) as unused_links,
    COUNT(CASE WHEN last_used_at >= (NOW() - INTERVAL '7 days') THEN 1 END) as used_last_week
FROM custom_download_links;

-- ===================================================================
-- إنشاء Stored Procedures للعمليات الشائعة
-- ===================================================================

-- إجراء للحصول على إحصائيات رابط معين
CREATE OR REPLACE FUNCTION sp_GetCustomLinkStats(p_channel_id VARCHAR(255))
RETURNS TABLE (
    channel_id VARCHAR,
    custom_link TEXT,
    usage_count BIGINT,
    created_at TIMESTAMP,
    last_used_at TIMESTAMP,
    is_active BOOLEAN,
    link_type VARCHAR,
    category VARCHAR,
    days_since_created INTEGER,
    last_usage_text TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.channel_id,
        c.custom_link,
        c.usage_count,
        c.created_at,
        c.last_used_at,
        c.is_active,
        c.link_type,
        c.category,
        EXTRACT(DAY FROM (CURRENT_DATE - c.created_at))::INTEGER as days_since_created,
        CASE 
            WHEN c.last_used_at IS NULL THEN 'لم يستخدم بعد'
            WHEN EXTRACT(DAY FROM (CURRENT_DATE - c.last_used_at))::INTEGER = 0 THEN 'اليوم'
            WHEN EXTRACT(DAY FROM (CURRENT_DATE - c.last_used_at))::INTEGER = 1 THEN 'أمس'
            ELSE CONCAT(EXTRACT(DAY FROM (CURRENT_DATE - c.last_used_at))::INTEGER, ' أيام مضت')
        END as last_usage_text
    FROM custom_download_links c
    WHERE c.channel_id = p_channel_id;
END;
$$;

-- إجراء لتحديث عداد الاستخدام
CREATE OR REPLACE FUNCTION sp_IncrementLinkUsage(p_channel_id VARCHAR(255))
RETURNS BIGINT
LANGUAGE plpgsql
AS $$
DECLARE
    affected_rows BIGINT;
BEGIN
    UPDATE custom_download_links 
    SET 
        usage_count = usage_count + 1,
        last_used_at = CURRENT_TIMESTAMP
    WHERE channel_id = p_channel_id AND is_active = TRUE;
    
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    RETURN affected_rows;
END;
$$;

-- إجراء لإضافة أو تحديث رابط مخصص
CREATE OR REPLACE FUNCTION sp_UpsertCustomLink(
    p_channel_id VARCHAR(255),
    p_custom_link TEXT,
    p_created_by VARCHAR(255),
    p_link_type VARCHAR(20),
    p_category VARCHAR(100),
    p_notes TEXT
)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO custom_download_links 
    (channel_id, custom_link, created_by, link_type, category, notes)
    VALUES 
    (p_channel_id, p_custom_link, p_created_by, 
     COALESCE(p_link_type, 'custom'), 
     COALESCE(p_category, 'general'), 
     p_notes)
    ON CONFLICT (channel_id) DO UPDATE SET
        custom_link = EXCLUDED.custom_link,
        updated_at = CURRENT_TIMESTAMP,
        link_type = EXCLUDED.link_type,
        category = EXCLUDED.category,
        notes = EXCLUDED.notes,
        is_active = TRUE;
    
    RETURN 'SUCCESS';
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'ERROR: ' || SQLERRM;
END;
$$;

-- إجراء لحذف رابط مخصص
CREATE OR REPLACE FUNCTION sp_DeleteCustomLink(p_channel_id VARCHAR(255))
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    link_count INT DEFAULT 0;
BEGIN
    SELECT COUNT(*) INTO link_count 
    FROM custom_download_links 
    WHERE channel_id = p_channel_id;
    
    IF link_count > 0 THEN
        DELETE FROM custom_download_links 
        WHERE channel_id = p_channel_id;
        
        RETURN 'SUCCESS';
    ELSE
        RETURN 'ERROR: Link not found';
    END IF;
END;
$$;

-- إجراء لتنظيف الروابط القديمة غير المستخدمة
CREATE OR REPLACE FUNCTION sp_CleanupOldUnusedLinks(p_days_old INT)
RETURNS TABLE (deleted_links BIGINT, message TEXT)
LANGUAGE plpgsql
AS $$
DECLARE
    deleted_count BIGINT DEFAULT 0;
BEGIN
    DELETE FROM custom_download_links 
    WHERE usage_count = 0 
    AND (CURRENT_DATE - created_at) > (p_days_old || ' days')::INTERVAL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN QUERY SELECT deleted_count, CONCAT('Deleted ', deleted_count, ' unused links older than ', p_days_old, ' days');
END;
$$;

-- ===================================================================
-- إدراج بيانات تجريبية (اختياري)
-- ===================================================================

-- INSERT INTO custom_download_links 
-- (channel_id, custom_link, created_by, link_type, category, notes) 
-- VALUES
-- ('@test_channel', 'https://example.com/download', '123456789', 'custom', 'mods', 'Test link for development'),
-- ('-1001234567890', 'https://mysite.com/mods', '987654321', 'redirect', 'general', 'Main download portal');

-- ===================================================================
-- استعلامات مفيدة للإدارة والمراقبة
-- ===================================================================

-- 1. عرض جميع الروابط المخصصة مع الإحصائيات
-- SELECT * FROM v_active_custom_links ORDER BY usage_count DESC;

-- 2. عرض الروابط الأكثر استخداماً
-- SELECT channel_id, custom_link, usage_count, last_used_at 
-- FROM custom_download_links 
-- WHERE is_active = TRUE 
-- ORDER BY usage_count DESC 
-- LIMIT 10;

-- 3. عرض الروابط غير المستخدمة
-- SELECT channel_id, custom_link, created_at, DATEDIFF(CURRENT_DATE, DATE(created_at)) as days_old
-- FROM custom_download_links 
-- WHERE usage_count = 0 AND is_active = TRUE
-- ORDER BY created_at ASC;

-- 4. إحصائيات عامة
-- SELECT * FROM v_link_statistics;

-- 5. البحث عن رابط معين
-- SELECT * FROM custom_download_links WHERE channel_id = '@your_channel';

-- 6. تحديث حالة رابط
-- UPDATE custom_download_links SET is_active = FALSE WHERE channel_id = '@disabled_channel';

-- ===================================================================
-- نهاية الملف
-- ===================================================================
