#!/usr/bin/env python3
"""
إعداد سريع للبوت المحسن
Quick Setup for Enhanced Bot

هذا الملف يساعد في الإعداد السريع للبوت مع النظام المحسن
This file helps with quick setup of the enhanced bot system
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def print_header():
    """طباعة رأس الإعداد"""
    print("\n" + "="*60)
    print("🎮 MINECRAFT MODS BOT - QUICK SETUP")
    print("="*60)
    print("🚀 Setting up enhanced mod details system...")
    print("📱 With Telegram Web App support")
    print("👨‍💻 Developer: @Kim880198")
    print("="*60 + "\n")

def check_python_version():
    """التحقق من إصدار Python"""
    print("🔍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} is supported")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 Installing requirements...")
    
    try:
        # التحقق من وجود pip
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip is available")
        
        # تثبيت المتطلبات
        print("📥 Installing packages...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All packages installed successfully")
            return True
        else:
            print(f"❌ Failed to install packages: {result.stderr}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ pip is not available: {e}")
        return False
    except FileNotFoundError:
        print("❌ requirements.txt not found")
        return False

def setup_environment():
    """إعداد متغيرات البيئة"""
    print("\n⚙️ Setting up environment variables...")
    
    env_file = Path(".env")
    env_vars = {}
    
    # قراءة الملف الموجود إن وجد
    if env_file.exists():
        print("📄 Found existing .env file")
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and '=' in line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        env_vars[key] = value
        except Exception as e:
            print(f"⚠️ Error reading .env file: {e}")
    
    # المتغيرات المطلوبة
    required_vars = {
        'BOT_TOKEN': {
            'description': 'Telegram Bot Token (from @BotFather)',
            'example': '1234567890:ABCdefGHIjklMNOpqrsTUVwxyz'
        },
        'ADMIN_CHAT_ID': {
            'description': 'Your Telegram User ID (Admin)',
            'example': '123456789'
        }
    }
    
    # المتغيرات الاختيارية
    optional_vars = {
        'WEB_SERVER_URL': {
            'description': 'Public URL for web server (for production)',
            'default': 'http://127.0.0.1:5001',
            'example': 'https://your-app.herokuapp.com'
        },
        'FLASK_PORT': {
            'description': 'Flask server port',
            'default': '5000',
            'example': '5000'
        },
        'TELEGRAM_WEB_APP_PORT': {
            'description': 'Telegram Web App server port',
            'default': '5001',
            'example': '5001'
        },
        'SUPABASE_URL': {
            'description': 'Supabase database URL (optional)',
            'default': '',
            'example': 'https://your-project.supabase.co'
        },
        'SUPABASE_KEY': {
            'description': 'Supabase API key (optional)',
            'default': '',
            'example': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
        }
    }
    
    print("\n📝 Please provide the following information:")
    print("   (Press Enter to keep existing value or use default)\n")
    
    # جمع المتغيرات المطلوبة
    for var, info in required_vars.items():
        current_value = env_vars.get(var, '')
        if current_value:
            print(f"🔑 {var}")
            print(f"   Description: {info['description']}")
            print(f"   Current value: {current_value[:10]}{'...' if len(current_value) > 10 else ''}")
            new_value = input(f"   New value (Enter to keep current): ").strip()
            if new_value:
                env_vars[var] = new_value
            else:
                env_vars[var] = current_value
        else:
            print(f"🔑 {var} (REQUIRED)")
            print(f"   Description: {info['description']}")
            print(f"   Example: {info['example']}")
            value = input(f"   Enter value: ").strip()
            if not value:
                print(f"❌ {var} is required!")
                return False
            env_vars[var] = value
        print()
    
    # جمع المتغيرات الاختيارية
    print("📋 Optional settings (you can skip these):\n")
    for var, info in optional_vars.items():
        current_value = env_vars.get(var, info['default'])
        print(f"⚙️ {var}")
        print(f"   Description: {info['description']}")
        print(f"   Current/Default: {current_value}")
        print(f"   Example: {info['example']}")
        new_value = input(f"   New value (Enter to keep current/default): ").strip()
        if new_value:
            env_vars[var] = new_value
        elif var not in env_vars:
            env_vars[var] = info['default']
        print()
    
    # حفظ ملف .env
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write("# Telegram Bot Configuration\n")
            f.write("# Generated by quick_setup.py\n\n")
            
            f.write("# Required Settings\n")
            for var in required_vars.keys():
                f.write(f"{var}={env_vars[var]}\n")
            
            f.write("\n# Optional Settings\n")
            for var in optional_vars.keys():
                if env_vars.get(var):
                    f.write(f"{var}={env_vars[var]}\n")
        
        print("✅ Environment configuration saved to .env")
        return True
        
    except Exception as e:
        print(f"❌ Failed to save .env file: {e}")
        return False

def create_startup_script():
    """إنشاء ملف تشغيل"""
    print("\n📜 Creating startup scripts...")
    
    # ملف تشغيل Windows
    bat_content = """@echo off
echo Starting Enhanced Minecraft Mods Bot...
python run_enhanced_bot.py
pause
"""
    
    try:
        with open("start_bot.bat", "w", encoding='utf-8') as f:
            f.write(bat_content)
        print("✅ Created start_bot.bat for Windows")
    except Exception as e:
        print(f"⚠️ Failed to create start_bot.bat: {e}")
    
    # ملف تشغيل Linux/Mac
    sh_content = """#!/bin/bash
echo "Starting Enhanced Minecraft Mods Bot..."
python3 run_enhanced_bot.py
"""
    
    try:
        with open("start_bot.sh", "w", encoding='utf-8') as f:
            f.write(sh_content)
        
        # جعل الملف قابل للتنفيذ
        os.chmod("start_bot.sh", 0o755)
        print("✅ Created start_bot.sh for Linux/Mac")
    except Exception as e:
        print(f"⚠️ Failed to create start_bot.sh: {e}")

def test_setup():
    """اختبار الإعداد"""
    print("\n🧪 Testing setup...")
    
    # التحقق من ملف .env
    if not Path(".env").exists():
        print("❌ .env file not found")
        return False
    
    # التحقق من المتطلبات
    try:
        import telegram
        import flask
        import requests
        print("✅ All required packages are available")
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        return False
    
    # التحقق من متغيرات البيئة
    from dotenv import load_dotenv
    load_dotenv()
    
    if not os.environ.get('BOT_TOKEN'):
        print("❌ BOT_TOKEN not set")
        return False
    
    if not os.environ.get('ADMIN_CHAT_ID'):
        print("❌ ADMIN_CHAT_ID not set")
        return False
    
    print("✅ Environment variables are set")
    return True

def show_next_steps():
    """عرض الخطوات التالية"""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETED SUCCESSFULLY!")
    print("="*60)
    print("\n📋 Next steps:")
    print("1. 🚀 Start the bot:")
    print("   • Windows: double-click start_bot.bat")
    print("   • Linux/Mac: ./start_bot.sh")
    print("   • Manual: python run_enhanced_bot.py")
    print()
    print("2. 📱 Test the bot:")
    print("   • Send /start to your bot in Telegram")
    print("   • Check if mod details pages work")
    print("   • Test on different devices")
    print()
    print("3. 🌐 For public access:")
    print("   • Use ngrok: ngrok http 5001")
    print("   • Or deploy to Heroku/Railway/Render")
    print("   • Update WEB_SERVER_URL in .env")
    print()
    print("4. 📚 Read documentation:")
    print("   • TELEGRAM_WEB_APP_SETUP.md")
    print("   • Check logs in bot_enhanced.log")
    print()
    print("🆘 Need help? Contact @Kim880198")
    print("="*60 + "\n")

def main():
    """الدالة الرئيسية"""
    try:
        print_header()
        
        # التحقق من إصدار Python
        if not check_python_version():
            sys.exit(1)
        
        # تثبيت المتطلبات
        if not install_requirements():
            print("\n❌ Setup failed at requirements installation")
            sys.exit(1)
        
        # إعداد البيئة
        if not setup_environment():
            print("\n❌ Setup failed at environment configuration")
            sys.exit(1)
        
        # إنشاء ملفات التشغيل
        create_startup_script()
        
        # اختبار الإعداد
        if not test_setup():
            print("\n⚠️ Setup completed but some tests failed")
            print("   You may need to check your configuration")
        
        # عرض الخطوات التالية
        show_next_steps()
        
    except KeyboardInterrupt:
        print("\n🛑 Setup cancelled by user")
    except Exception as e:
        print(f"\n💥 Setup failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
