#!/usr/bin/env python3
"""
اختبار عملية إعداد القناة الجديدة
"""

import os
import sys
import json
import logging
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def load_env_file():
    """تحميل متغيرات البيئة من ملف .env"""
    try:
        if os.path.exists('.env'):
            with open('.env', 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key] = value
            print("✅ تم تحميل متغيرات البيئة من .env")
        else:
            print("⚠️ ملف .env غير موجود")
    except Exception as e:
        print(f"❌ خطأ في تحميل ملف .env: {e}")

def test_user_channels_functions():
    """اختبار دوال إدارة قنوات المستخدمين"""
    print("🧪 اختبار دوال إدارة قنوات المستخدمين...")
    
    try:
        # استيراد الدوال المطلوبة
        from main import (
            add_user_channel, 
            get_user_channels, 
            save_user_channel,
            get_user_default_channel,
            load_user_channels
        )
        
        print("✅ تم استيراد دوال إدارة القنوات بنجاح")
        
        # اختبار إضافة قناة جديدة
        test_user_id = 123456789
        test_channel_id = "@test_channel"
        test_interval = 60
        test_lang = "ar"
        
        print(f"🔧 اختبار إضافة قناة: {test_channel_id} للمستخدم {test_user_id}")
        
        # حفظ القناة
        success = save_user_channel(test_user_id, test_channel_id, test_interval, test_lang)
        
        if success:
            print("✅ تم حفظ القناة بنجاح")
            
            # التحقق من حفظ البيانات
            user_channels = get_user_channels(test_user_id)
            print(f"📊 قنوات المستخدم: {len(user_channels)} قناة")
            
            if test_channel_id in user_channels:
                print("✅ تم العثور على القناة في قائمة قنوات المستخدم")
                channel_data = user_channels[test_channel_id]
                print(f"📋 بيانات القناة: {channel_data}")
            else:
                print("❌ لم يتم العثور على القناة في قائمة قنوات المستخدم")
                return False
            
            # التحقق من القناة الافتراضية
            default_channel = get_user_default_channel(test_user_id)
            if default_channel:
                print(f"✅ القناة الافتراضية: {default_channel.get('channel_id')}")
            else:
                print("⚠️ لا توجد قناة افتراضية")
            
        else:
            print("❌ فشل في حفظ القناة")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال إدارة القنوات: {e}")
        return False

def test_start_command_logic():
    """اختبار منطق أمر /start"""
    print("🧪 اختبار منطق أمر /start...")
    
    try:
        from main import load_user_channels, get_user_lang
        
        # اختبار مستخدم جديد
        test_user_id = 987654321
        user_channels = load_user_channels()
        user_id_str = str(test_user_id)
        
        print(f"🔍 فحص المستخدم {test_user_id}...")
        
        if user_id_str not in user_channels:
            print("✅ المستخدم جديد - سيتم عرض اختيار اللغة")
            expected_flow = "language_selection"
        else:
            user_data = user_channels.get(user_id_str, {})
            
            # التحقق من النظام الجديد (قنوات متعددة)
            if 'channels' in user_data:
                user_channels_dict = user_data.get('channels', {})
                if len(user_channels_dict) > 0:
                    print("✅ المستخدم لديه قنوات - سيتم عرض القائمة الرئيسية")
                    expected_flow = "main_menu"
                else:
                    print("✅ المستخدم ليس لديه قنوات - سيتم طلب إضافة قناة")
                    expected_flow = "add_channel"
            # التحقق من النظام القديم
            elif user_data.get("channel_lang"):
                if user_data.get("channel_id"):
                    print("✅ المستخدم لديه قناة (نظام قديم) - سيتم عرض القائمة الرئيسية")
                    expected_flow = "main_menu"
                else:
                    print("✅ المستخدم اختار لغة لكن لا يوجد قناة - سيتم طلب إضافة قناة")
                    expected_flow = "add_channel"
            else:
                print("✅ المستخدم موجود لكن لم يختر لغة - سيتم عرض اختيار اللغة")
                expected_flow = "language_selection"
        
        print(f"📋 التدفق المتوقع: {expected_flow}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار منطق /start: {e}")
        return False

def test_channel_setup_flow():
    """اختبار تدفق إعداد القناة الكامل"""
    print("🧪 اختبار تدفق إعداد القناة الكامل...")
    
    try:
        from main import (
            save_user_channel,
            get_user_channels,
            load_user_channels,
            save_json_file
        )
        
        # محاكاة تدفق إعداد قناة جديدة
        test_user_id = 555666777
        test_channel_id = "@new_test_channel"
        test_interval = 120
        test_lang = "ar"
        
        print(f"🔧 محاكاة إعداد قناة جديدة...")
        print(f"   - المستخدم: {test_user_id}")
        print(f"   - القناة: {test_channel_id}")
        print(f"   - الفاصل الزمني: {test_interval} دقيقة")
        print(f"   - اللغة: {test_lang}")
        
        # الخطوة 1: حفظ القناة
        print("📝 الخطوة 1: حفظ القناة...")
        success = save_user_channel(test_user_id, test_channel_id, test_interval, test_lang)
        
        if not success:
            print("❌ فشل في حفظ القناة")
            return False
        
        print("✅ تم حفظ القناة بنجاح")
        
        # الخطوة 2: التحقق من البيانات المحفوظة
        print("🔍 الخطوة 2: التحقق من البيانات المحفوظة...")
        user_channels_data = load_user_channels()
        user_data = user_channels_data.get(str(test_user_id), {})
        
        if 'channels' in user_data:
            channels = user_data['channels']
            if test_channel_id in channels:
                channel_data = channels[test_channel_id]
                print(f"✅ بيانات القناة محفوظة: {channel_data}")
                
                # التحقق من الإعدادات
                if channel_data.get('publish_interval') == test_interval:
                    print("✅ الفاصل الزمني صحيح")
                else:
                    print(f"❌ الفاصل الزمني خطأ: {channel_data.get('publish_interval')} != {test_interval}")
                    return False
                
                if channel_data.get('channel_lang') == test_lang:
                    print("✅ لغة القناة صحيحة")
                else:
                    print(f"❌ لغة القناة خطأ: {channel_data.get('channel_lang')} != {test_lang}")
                    return False
                
            else:
                print("❌ القناة غير موجودة في البيانات المحفوظة")
                return False
        else:
            print("❌ بنية البيانات خطأ - لا توجد قنوات")
            return False
        
        # الخطوة 3: التحقق من القناة الافتراضية
        print("🔍 الخطوة 3: التحقق من القناة الافتراضية...")
        default_channel_id = user_data.get('user_settings', {}).get('default_channel')
        
        if default_channel_id == test_channel_id:
            print("✅ تم تعيين القناة كافتراضية")
        else:
            print(f"❌ القناة الافتراضية خطأ: {default_channel_id} != {test_channel_id}")
            return False
        
        print("✅ جميع خطوات إعداد القناة نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تدفق إعداد القناة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 بدء اختبار إعداد القناة")
    print("=" * 50)
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # تحميل متغيرات البيئة
    load_env_file()
    
    # قائمة الاختبارات
    tests = [
        ("اختبار دوال إدارة قنوات المستخدمين", test_user_channels_functions),
        ("اختبار منطق أمر /start", test_start_command_logic),
        ("اختبار تدفق إعداد القناة الكامل", test_channel_setup_flow)
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for test_name, test_function in tests:
        print(f"🧪 {test_name}...")
        try:
            if test_function():
                print(f"✅ {test_name} - نجح")
                success_count += 1
            else:
                print(f"❌ {test_name} - فشل")
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
        print()
    
    print("=" * 50)
    print(f"📊 النتائج: {success_count}/{total_count} اختبارات نجحت")
    
    if success_count == total_count:
        print("🎉 جميع الاختبارات نجحت! إعداد القناة يعمل بشكل صحيح.")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    return success_count == total_count

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
