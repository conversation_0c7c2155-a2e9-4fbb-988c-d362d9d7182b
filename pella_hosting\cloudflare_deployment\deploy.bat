@echo off
chcp 65001 >nul
title Cloudflare Pages Deployment

echo 🚀 بدء عملية النشر على Cloudflare Pages...
echo 🚀 Starting Cloudflare Pages deployment...
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    echo ❌ Node.js is not installed. Please install Node.js first
    echo.
    echo 📥 تحميل من: https://nodejs.org/
    echo 📥 Download from: https://nodejs.org/
    pause
    exit /b 1
)

REM التحقق من وجود npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm غير مثبت. يرجى تثبيت npm أولاً
    echo ❌ npm is not installed. Please install npm first
    pause
    exit /b 1
)

REM تثبيت التبعيات
echo 📦 تثبيت التبعيات...
echo 📦 Installing dependencies...
call npm install

if errorlevel 1 (
    echo ❌ فشل في تثبيت التبعيات
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

REM بناء المشروع
echo.
echo 🔨 بناء المشروع...
echo 🔨 Building project...
call npm run build

if errorlevel 1 (
    echo ❌ فشل في بناء المشروع
    echo ❌ Failed to build project
    pause
    exit /b 1
)

REM التحقق من وجود wrangler
wrangler --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ⚠️ wrangler غير مثبت. هل تريد تثبيته؟
    echo ⚠️ wrangler is not installed. Do you want to install it?
    echo.
    set /p install_wrangler="اكتب Y للتثبيت أو N للتخطي (Y/N): "
    
    if /i "%install_wrangler%"=="Y" (
        echo 📦 تثبيت wrangler...
        echo 📦 Installing wrangler...
        call npm install -g wrangler
        
        if errorlevel 1 (
            echo ❌ فشل في تثبيت wrangler
            echo ❌ Failed to install wrangler
            goto manual_upload
        )
        
        echo ✅ تم تثبيت wrangler بنجاح
        echo ✅ wrangler installed successfully
        echo.
        
        REM محاولة النشر
        echo 🌐 نشر على Cloudflare Pages...
        echo 🌐 Deploying to Cloudflare Pages...
        call wrangler pages publish dist
        
        if errorlevel 1 (
            echo ❌ فشل في النشر التلقائي
            echo ❌ Automatic deployment failed
            goto manual_upload
        ) else (
            echo ✅ تم النشر بنجاح!
            echo ✅ Deployment successful!
            goto success
        )
    ) else (
        goto manual_upload
    )
) else (
    REM wrangler موجود، محاولة النشر
    echo.
    echo 🌐 نشر على Cloudflare Pages...
    echo 🌐 Deploying to Cloudflare Pages...
    call wrangler pages publish dist
    
    if errorlevel 1 (
        echo ❌ فشل في النشر التلقائي
        echo ❌ Automatic deployment failed
        goto manual_upload
    ) else (
        echo ✅ تم النشر بنجاح!
        echo ✅ Deployment successful!
        goto success
    )
)

:manual_upload
echo.
echo 📁 الملفات جاهزة في مجلد dist\
echo 📁 Files are ready in dist\ folder
echo.
echo 🌐 يمكنك رفعها يدوياً على Cloudflare Pages:
echo 🌐 You can upload them manually to Cloudflare Pages:
echo 1. اذهب إلى https://dash.cloudflare.com/
echo 1. Go to https://dash.cloudflare.com/
echo 2. اختر Pages من القائمة
echo 2. Select Pages from menu
echo 3. اضغط "Upload assets"
echo 3. Click "Upload assets"
echo 4. ارفع محتويات مجلد dist\
echo 4. Upload contents of dist\ folder

:success
echo.
echo 🎉 انتهت عملية الإعداد!
echo 🎉 Setup completed!
echo.
echo 📋 الخطوات التالية:
echo 📋 Next steps:
echo 1. ارفع الملفات على GitHub
echo 1. Upload files to GitHub
echo 2. اربط المستودع مع Cloudflare Pages
echo 2. Connect repository to Cloudflare Pages
echo 3. أضف متغيرات البيئة
echo 3. Add environment variables
echo 4. انشر المشروع
echo 4. Deploy the project
echo.
echo 📖 راجع DEPLOYMENT_GUIDE.md للتفاصيل
echo 📖 Check DEPLOYMENT_GUIDE.md for details
echo.

pause
