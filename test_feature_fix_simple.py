#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مبسط لإصلاح نظام رسائل التحذير للمميزات المقفلة
Simple test for feature access warning messages fix
"""

import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_all_functions_exist():
    """اختبار وجود جميع الدوال المطلوبة"""
    print("🔍 اختبار وجود جميع الدوال...")
    
    try:
        from main import (
            # دوال التحقق الأساسية
            show_feature_locked_message,
            check_feature_access,
            
            # دوال المميزات الجديدة
            unlimited_channels_menu,
            custom_download_links_menu,
            publish_intervals_extended_menu,
            page_customization_vip_menu,
            
            # دوال المميزات الموجودة
            url_shortener_menu,
            tasks_system_menu
        )
        print("✅ جميع الدوال موجودة")
        return True
    except ImportError as e:
        print(f"❌ دالة مفقودة: {e}")
        return False

def test_feature_info_complete():
    """اختبار اكتمال معلومات المميزات"""
    print("\n🏗️ اختبار اكتمال معلومات المميزات...")
    
    try:
        from main import show_feature_locked_message
        import inspect
        
        source = inspect.getsource(show_feature_locked_message)
        
        # المميزات المطلوبة
        required_features = [
            'ads_system',
            'unlimited_channels', 
            'url_shortener',
            'custom_download_links',
            'publish_intervals_extended',
            'tasks_system',
            'page_customization_vip'
        ]
        
        missing = []
        for feature in required_features:
            if f"'{feature}'" not in source:
                missing.append(feature)
        
        if missing:
            print(f"❌ مميزات مفقودة: {missing}")
            return False
        
        print("✅ جميع المميزات موجودة في show_feature_locked_message")
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_menu_functions_structure():
    """اختبار بنية دوال القوائم"""
    print("\n🔧 اختبار بنية دوال القوائم...")
    
    try:
        from main import (
            url_shortener_menu,
            tasks_system_menu,
            unlimited_channels_menu,
            custom_download_links_menu,
            publish_intervals_extended_menu,
            page_customization_vip_menu
        )
        import inspect
        
        functions = [
            ('url_shortener_menu', url_shortener_menu),
            ('tasks_system_menu', tasks_system_menu),
            ('unlimited_channels_menu', unlimited_channels_menu),
            ('custom_download_links_menu', custom_download_links_menu),
            ('publish_intervals_extended_menu', publish_intervals_extended_menu),
            ('page_customization_vip_menu', page_customization_vip_menu)
        ]
        
        all_good = True
        for name, func in functions:
            source = inspect.getsource(func)
            
            # التحقق من الاستخدام الصحيح
            has_check_access = "await check_feature_access" in source
            has_show_message = "show_feature_locked_message" in source
            
            if has_check_access and has_show_message:
                print(f"✅ {name}: يستخدم النظام الموحد")
            else:
                print(f"❌ {name}: لا يستخدم النظام الموحد")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_invitation_requirements():
    """اختبار متطلبات الدعوات"""
    print("\n🎁 اختبار متطلبات الدعوات...")
    
    expected = {
        'unlimited_channels': 1,
        'url_shortener': 3,
        'custom_download_links': 3,
        'publish_intervals_extended': 5,
        'tasks_system': 10,
        'page_customization_vip': 10
    }
    
    try:
        from main import show_feature_locked_message
        import inspect
        
        source = inspect.getsource(show_feature_locked_message)
        
        all_correct = True
        for feature, count in expected.items():
            pattern = f"'{feature}'"
            if pattern in source:
                start = source.find(pattern)
                section = source[start:start + 500]
                
                if f"'requirement_count': {count}" in section:
                    print(f"✅ {feature}: {count} دعوات")
                else:
                    print(f"❌ {feature}: متطلبات خاطئة")
                    all_correct = False
            else:
                print(f"❌ {feature}: غير موجود")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار إصلاح نظام رسائل التحذير للمميزات المقفلة")
    print("=" * 70)
    
    tests = [
        ("وجود الدوال", test_all_functions_exist),
        ("معلومات المميزات", test_feature_info_complete),
        ("بنية دوال القوائم", test_menu_functions_structure),
        ("متطلبات الدعوات", test_invitation_requirements)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n📝 {name}:")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {name}: نجح")
            else:
                print(f"❌ {name}: فشل")
        except Exception as e:
            print(f"❌ {name}: خطأ - {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 النتائج النهائية:")
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 تم إصلاح نظام رسائل التحذير بنجاح!")
        print("\n📋 الإصلاحات المطبقة:")
        print("• ✅ تحديث tasks_system_menu لاستخدام النظام الموحد")
        print("• ✅ إضافة unlimited_channels_menu")
        print("• ✅ إضافة custom_download_links_menu")
        print("• ✅ إضافة publish_intervals_extended_menu")
        print("• ✅ إضافة page_customization_vip_menu")
        print("• ✅ إضافة معالجات الأحداث للدوال الجديدة")
        print("• ✅ جميع المميزات تستخدم show_feature_locked_message")
        
        print("\n🎯 النتيجة:")
        print("الآن جميع المميزات ستعرض رسائل تحذيرية موحدة ومفصلة")
        print("عندما يحاول المستخدم الوصول إليها بدون استيفاء المتطلبات.")
        
    else:
        print(f"\n⚠️ هناك {total - passed} مشاكل تحتاج إصلاح.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
