<?php
/**
 * عارض السجلات
 * Log Viewer
 */

define('INCLUDED', true);
require_once 'config.php';

// التحقق من الصلاحيات (يمكن إضافة نظام مصادقة هنا)
$access_key = $_GET['key'] ?? '';
$required_key = 'admin123'; // يجب تغييره في الإنتاج

if ($access_key !== $required_key) {
    die('Access denied. Invalid key.');
}

// إنشاء مجلد السجلات إذا لم يكن موجوداً
createLogDirectories();

// الحصول على ملفات السجلات
$log_dir = dirname(LOG_FILE);
$log_files = [];

if (is_dir($log_dir)) {
    $files = scandir($log_dir);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'log') {
            $log_files[] = $file;
        }
    }
}

// الملف المحدد للعرض
$selected_file = $_GET['file'] ?? 'app.log';
$log_content = '';

if (in_array($selected_file, $log_files)) {
    $file_path = $log_dir . '/' . $selected_file;
    if (file_exists($file_path)) {
        $log_content = file_get_contents($file_path);
    }
}

// تصفية السجلات
$filter_level = $_GET['level'] ?? '';
$filter_date = $_GET['date'] ?? '';
$search_term = $_GET['search'] ?? '';

if ($log_content && ($filter_level || $filter_date || $search_term)) {
    $lines = explode("\n", $log_content);
    $filtered_lines = [];
    
    foreach ($lines as $line) {
        $include = true;
        
        // تصفية حسب المستوى
        if ($filter_level && strpos($line, "[$filter_level]") === false) {
            $include = false;
        }
        
        // تصفية حسب التاريخ
        if ($filter_date && strpos($line, $filter_date) === false) {
            $include = false;
        }
        
        // تصفية حسب البحث
        if ($search_term && stripos($line, $search_term) === false) {
            $include = false;
        }
        
        if ($include) {
            $filtered_lines[] = $line;
        }
    }
    
    $log_content = implode("\n", $filtered_lines);
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عارض السجلات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .log-line { font-family: 'Courier New', monospace; font-size: 12px; }
        .log-error { color: #ef4444; }
        .log-warning { color: #f59e0b; }
        .log-info { color: #10b981; }
        .log-debug { color: #6b7280; }
        .log-container { max-height: 600px; overflow-y: auto; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-6 max-w-6xl">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-center mb-6 text-gray-800">
                📊 عارض السجلات
            </h1>
            
            <!-- أدوات التحكم -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <input type="hidden" name="key" value="<?php echo htmlspecialchars($access_key); ?>">
                    
                    <!-- اختيار الملف -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ملف السجل</label>
                        <select name="file" class="w-full border border-gray-300 rounded px-3 py-2">
                            <?php foreach ($log_files as $file): ?>
                                <option value="<?php echo htmlspecialchars($file); ?>" 
                                        <?php echo $file === $selected_file ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($file); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <!-- تصفية المستوى -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">المستوى</label>
                        <select name="level" class="w-full border border-gray-300 rounded px-3 py-2">
                            <option value="">جميع المستويات</option>
                            <option value="ERROR" <?php echo $filter_level === 'ERROR' ? 'selected' : ''; ?>>خطأ</option>
                            <option value="WARNING" <?php echo $filter_level === 'WARNING' ? 'selected' : ''; ?>>تحذير</option>
                            <option value="INFO" <?php echo $filter_level === 'INFO' ? 'selected' : ''; ?>>معلومات</option>
                            <option value="DEBUG" <?php echo $filter_level === 'DEBUG' ? 'selected' : ''; ?>>تصحيح</option>
                        </select>
                    </div>
                    
                    <!-- تصفية التاريخ -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">التاريخ</label>
                        <input type="date" name="date" value="<?php echo htmlspecialchars($filter_date); ?>" 
                               class="w-full border border-gray-300 rounded px-3 py-2">
                    </div>
                    
                    <!-- البحث -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">البحث</label>
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search_term); ?>" 
                               placeholder="ابحث في السجلات..." 
                               class="w-full border border-gray-300 rounded px-3 py-2">
                    </div>
                    
                    <!-- أزرار -->
                    <div class="flex space-x-2">
                        <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                            🔍 تصفية
                        </button>
                        <a href="?key=<?php echo urlencode($access_key); ?>&file=<?php echo urlencode($selected_file); ?>" 
                           class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 inline-block text-center">
                            🔄 إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
            
            <!-- معلومات الملف -->
            <?php if ($selected_file && in_array($selected_file, $log_files)): ?>
                <div class="bg-blue-50 p-4 rounded-lg mb-6">
                    <?php
                    $file_path = $log_dir . '/' . $selected_file;
                    $file_size = file_exists($file_path) ? filesize($file_path) : 0;
                    $file_modified = file_exists($file_path) ? filemtime($file_path) : 0;
                    $line_count = $log_content ? substr_count($log_content, "\n") + 1 : 0;
                    ?>
                    <h3 class="font-semibold mb-2">معلومات الملف</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div><strong>الاسم:</strong> <?php echo htmlspecialchars($selected_file); ?></div>
                        <div><strong>الحجم:</strong> <?php echo number_format($file_size); ?> بايت</div>
                        <div><strong>آخر تعديل:</strong> <?php echo $file_modified ? date('Y-m-d H:i:s', $file_modified) : 'غير محدد'; ?></div>
                        <div><strong>عدد الأسطر:</strong> <?php echo number_format($line_count); ?></div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- محتوى السجل -->
            <div class="bg-black text-green-400 p-4 rounded-lg log-container">
                <?php if ($log_content): ?>
                    <pre class="whitespace-pre-wrap"><?php
                    $lines = explode("\n", $log_content);
                    foreach ($lines as $line) {
                        if (empty(trim($line))) continue;
                        
                        $class = 'log-line';
                        if (strpos($line, '[ERROR]') !== false) {
                            $class .= ' log-error';
                        } elseif (strpos($line, '[WARNING]') !== false) {
                            $class .= ' log-warning';
                        } elseif (strpos($line, '[INFO]') !== false) {
                            $class .= ' log-info';
                        } elseif (strpos($line, '[DEBUG]') !== false) {
                            $class .= ' log-debug';
                        }
                        
                        echo '<div class="' . $class . '">' . htmlspecialchars($line) . '</div>';
                    }
                    ?></pre>
                <?php else: ?>
                    <div class="text-center text-gray-400 py-8">
                        📝 لا توجد سجلات للعرض
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- أدوات إضافية -->
            <div class="mt-6 flex flex-wrap gap-4 justify-center">
                <button onclick="downloadLog()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    💾 تحميل السجل
                </button>
                <button onclick="clearLog()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    🗑️ مسح السجل
                </button>
                <button onclick="location.reload()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    🔄 تحديث
                </button>
                <a href="deploy.php?setup=true" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    ⚙️ صفحة الإعداد
                </a>
            </div>
        </div>
    </div>

    <script>
        function downloadLog() {
            const content = <?php echo json_encode($log_content); ?>;
            const filename = <?php echo json_encode($selected_file); ?>;
            
            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
        
        function clearLog() {
            if (confirm('هل أنت متأكد من مسح السجل؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                const key = <?php echo json_encode($access_key); ?>;
                const file = <?php echo json_encode($selected_file); ?>;
                
                fetch('clear_log.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `key=${encodeURIComponent(key)}&file=${encodeURIComponent(file)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم مسح السجل بنجاح');
                        location.reload();
                    } else {
                        alert('فشل في مسح السجل: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('خطأ: ' + error.message);
                });
            }
        }
        
        // تحديث تلقائي كل دقيقة
        setInterval(() => {
            location.reload();
        }, 60000);
    </script>
</body>
</html>
