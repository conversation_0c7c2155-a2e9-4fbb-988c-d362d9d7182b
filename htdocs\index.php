<?php
/**
 * صفحة عرض تفاصيل المودات - InfinityFree Version
 * Mod Details Page for InfinityFree Hosting
 * 
 * هذا الملف يعمل على استضافة InfinityFree المجانية
 * This file works on InfinityFree hosting
 */

// إعدادات قاعدة البيانات Supabase
$SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
$SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";

// الحصول على معاملات الرابط
$mod_id = isset($_GET['id']) ? $_GET['id'] : null;
$lang = isset($_GET['lang']) ? $_GET['lang'] : 'ar';
$user_id = isset($_GET['user_id']) ? $_GET['user_id'] : null;
$channel_id = isset($_GET['channel']) ? $_GET['channel'] : null;
$is_preview = isset($_GET['preview']) && $_GET['preview'] == '1';

// التحقق من وجود معرف المود (إلا في وضع المعاينة)
if (!$mod_id && !$is_preview) {
    $error_msg = $lang == 'ar' ? "خطأ: معرف المود مفقود." : "Error: Mod ID is missing.";
    die("<h1>$error_msg</h1>");
}

/**
 * دالة جلب بيانات المود من Supabase
 */
function getModData($mod_id, $supabase_url, $supabase_key) {
    $url = $supabase_url . "/rest/v1/mods?id=eq." . $mod_id;
    
    $headers = array(
        'apikey: ' . $supabase_key,
        'Authorization: Bearer ' . $supabase_key,
        'Content-Type: application/json'
    );
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200) {
        $data = json_decode($response, true);
        return !empty($data) ? $data[0] : null;
    }
    
    return null;
}

/**
 * دالة جلب إعدادات الإعلانات
 */
function getAdsSettings($user_id, $supabase_url, $supabase_key) {
    if (!$user_id) return null;

    $url = $supabase_url . "/rest/v1/user_ads_settings?user_id=eq." . $user_id;

    $headers = array(
        'apikey: ' . $supabase_key,
        'Authorization: Bearer ' . $supabase_key,
        'Content-Type: application/json'
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code == 200) {
        $data = json_decode($response, true);
        return !empty($data) ? $data[0] : null;
    }

    return null;
}

/**
 * دالة جلب إعدادات اختصار الروابط
 */
function getUrlShortenerSettings($user_id, $supabase_url, $supabase_key) {
    if (!$user_id) return null;

    $url = $supabase_url . "/rest/v1/user_url_shortener_settings?user_id=eq." . $user_id;

    $headers = array(
        'apikey: ' . $supabase_key,
        'Authorization: Bearer ' . $supabase_key,
        'Content-Type: application/json'
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code == 200) {
        $data = json_decode($response, true);
        return !empty($data) ? $data[0] : null;
    }

    return null;
}

/**
 * إنشاء alias قصير وفريد للرابط المختصر (أقل من 30 حرف)
 */
function createShortAlias($mod_id, $user_id) {
    if (empty($mod_id) || empty($user_id)) {
        return null;
    }

    // استخدام أول 8 أحرف من mod_id + آخر 6 أرقام من user_id
    $mod_short = substr($mod_id, 0, 8);
    $user_short = substr($user_id, -6);

    // إضافة timestamp قصير للتفرد
    $timestamp_short = substr(time(), -4); // آخر 4 أرقام من timestamp

    $alias = "m{$mod_short}u{$user_short}t{$timestamp_short}"; // مثال: m27da24e7u880877t1234 (19 حرف)

    // التأكد من أن الطول أقل من 30 حرف
    if (strlen($alias) > 30) {
        $alias = substr($alias, 0, 30);
    }

    return $alias;
}

/**
 * دالة اختصار الرابط باستخدام API
 */
function shortenUrlWithApi($original_url, $api_url, $api_key, $service_name, $alias = null) {
    if (empty($original_url) || empty($api_url) || empty($api_key)) {
        return array('success' => false, 'error' => 'Missing required parameters');
    }

    // تحديد نوع الخدمة
    $service_type = 'generic';
    if (strpos($api_url, 'cuty.io') !== false) {
        $service_type = 'cuty';
    } elseif (strpos($api_url, 'linkjust.com') !== false) {
        $service_type = 'linkjust';
    } elseif (strpos($api_url, 'linkvertise.com') !== false) {
        $service_type = 'linkvertise';
    } elseif (strpos($api_url, 'lootlabs.gg') !== false) {
        $service_type = 'lootlabs';
    } elseif (strpos($api_url, 'nitro-link.com') !== false) {
        $service_type = 'nitrolink';
    } elseif (strpos($api_url, 'swiftlnx.com') !== false) {
        $service_type = 'swiftlnx';
    } elseif (strpos($api_url, 'short-jambo.com') !== false) {
        $service_type = 'shortjambo';
    } elseif (strpos($api_url, 'linkslice.io') !== false) {
        $service_type = 'linkslice';
    }

    // تحضير المعاملات بناءً على نوع الخدمة
    if (in_array($service_type, ['cuty', 'linkjust', 'linkvertise', 'lootlabs'])) {
        $params = array(
            'api' => $api_key,
            'url' => $original_url
        );
        if ($alias) {
            $params['alias'] = $alias;
        }
    } elseif (in_array($service_type, ['nitrolink', 'swiftlnx', 'shortjambo', 'linkslice'])) {
        $params = array(
            'api_key' => $api_key,
            'url' => $original_url,
            'format' => 'json'
        );
        if ($alias) {
            $params['custom'] = $alias;
        }
    } else {
        $params = array(
            'api' => $api_key,
            'url' => $original_url
        );
        if ($alias) {
            $params['alias'] = $alias;
        }
    }

    // إرسال الطلب
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url . '?' . http_build_query($params));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code == 200) {
        // محاولة تحليل JSON
        $data = json_decode($response, true);

        if ($data) {
            $shortened_url = null;
            $success = false;

            // معالجة تنسيقات مختلفة للاستجابة
            if (in_array($service_type, ['cuty', 'linkjust', 'linkvertise', 'lootlabs'])) {
                if (($data['status'] ?? '') == 'success' || ($data['success'] ?? false) == true) {
                    $shortened_url = $data['shortenedUrl'] ?? $data['short_url'] ?? $data['url'] ?? null;
                    $success = !empty($shortened_url);
                }
            } elseif (in_array($service_type, ['nitrolink', 'swiftlnx', 'shortjambo', 'linkslice'])) {
                if (($data['error'] ?? 1) == 0 || ($data['status'] ?? '') == 'success') {
                    $shortened_url = $data['short_url'] ?? $data['shortenedUrl'] ?? $data['url'] ?? null;
                    $success = !empty($shortened_url);
                }
            } else {
                if (($data['status'] ?? '') == 'success' || ($data['success'] ?? false) == true) {
                    $shortened_url = $data['shortenedUrl'] ?? $data['short_url'] ?? $data['url'] ?? null;
                    $success = !empty($shortened_url);
                }
            }

            if ($success && $shortened_url) {
                return array('success' => true, 'shortened_url' => $shortened_url);
            } else {
                $error_msg = $data['message'] ?? $data['error_msg'] ?? $data['msg'] ?? 'Unknown error from API';
                return array('success' => false, 'error' => $error_msg);
            }
        } else {
            // إذا لم تكن JSON، تحقق من النص المباشر
            $response = trim($response);
            if (strpos($response, 'http') === 0) {
                return array('success' => true, 'shortened_url' => $response);
            } else {
                return array('success' => false, 'error' => 'Invalid response format: ' . $response);
            }
        }
    } else {
        return array('success' => false, 'error' => 'HTTP ' . $http_code . ': ' . $response);
    }
}

/**
 * دالة جلب إعدادات تخصيص الصفحة
 */
function getPageCustomizationSettings($user_id, $supabase_url, $supabase_key) {
    if (!$user_id) return null;

    $url = $supabase_url . "/rest/v1/page_customization_settings?user_id=eq." . $user_id;

    $headers = array(
        'apikey: ' . $supabase_key,
        'Authorization: Bearer ' . $supabase_key,
        'Content-Type: application/json'
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code == 200) {
        $data = json_decode($response, true);
        return !empty($data) ? $data[0] : null;
    }

    return null;
}

/**
 * دالة جلب رابط التحميل المخصص للقناة
 */
function getCustomDownloadLink($channel_id, $supabase_url, $supabase_key) {
    if (empty($channel_id)) {
        return null;
    }

    $url = $supabase_url . "/rest/v1/custom_download_links?channel_id=eq." . urlencode($channel_id);

    $headers = array(
        'apikey: ' . $supabase_key,
        'Authorization: Bearer ' . $supabase_key,
        'Content-Type: application/json'
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code === 200 && $response) {
        $data = json_decode($response, true);
        if (!empty($data) && isset($data[0]['custom_link'])) {
            return $data[0]['custom_link'];
        }
    }

    return null;
}

/**
 * دالة الحصول على رابط التحميل النهائي (مختصر أو أصلي)
 */
function getDownloadUrlForMod($mod_data, $user_id, $supabase_url, $supabase_key) {
    // الحصول على الرابط الأصلي
    $original_url = $mod_data['download_url'] ?? '';
    if (empty($original_url)) {
        return '';
    }

    // إذا لم يكن هناك user_id، إرجاع الرابط الأصلي
    if (empty($user_id)) {
        return $original_url;
    }

    // الحصول على إعدادات اختصار الروابط
    $shortener_settings = getUrlShortenerSettings($user_id, $supabase_url, $supabase_key);

    // إذا لم تكن الميزة مفعلة، إرجاع الرابط الأصلي
    if (!$shortener_settings || !($shortener_settings['shortener_enabled'] ?? false)) {
        return $original_url;
    }

    $api_url = $shortener_settings['api_url'] ?? '';
    $api_key = $shortener_settings['api_key'] ?? '';
    $service_name = $shortener_settings['service_name'] ?? 'custom';

    if (empty($api_url) || empty($api_key)) {
        return $original_url;
    }

    // إنشاء اسم مستعار فريد (محدود بـ 30 حرف)
    $mod_id = $mod_data['id'] ?? '';
    $alias = createShortAlias($mod_id, $user_id);

    // محاولة اختصار الرابط
    $result = shortenUrlWithApi($original_url, $api_url, $api_key, $service_name, $alias);

    if ($result['success']) {
        return $result['shortened_url'];
    } else {
        // في حالة الفشل، إرجاع الرابط الأصلي
        return $original_url;
    }
}

// جلب بيانات المود أو استخدام بيانات تجريبية للمعاينة
if ($is_preview) {
    // بيانات تجريبية للمعاينة
    $mod_data = array(
        'id' => 'preview-mod-id',
        'name' => $lang == 'ar' ? 'مود تجريبي للمعاينة' : 'Preview Demo Mod',
        'description' => $lang == 'ar' ? 'هذا مود تجريبي لمعاينة تصميم الصفحة. يمكنك رؤية كيف ستبدو صفحة المود مع التخصيصات التي قمت بها.' : 'This is a demo mod for page preview. You can see how your mod page will look with your customizations.',
        'description_ar' => 'هذا مود تجريبي لمعاينة تصميم الصفحة. يمكنك رؤية كيف ستبدو صفحة المود مع التخصيصات التي قمت بها.',
        'version' => '1.0.0',
        'category' => 'addons',
        'download_url' => '#preview-download',
        'image_urls' => array(
            'https://via.placeholder.com/800x450/4F46E5/FFFFFF?text=' . urlencode($lang == 'ar' ? 'صورة تجريبية 1' : 'Demo Image 1'),
            'https://via.placeholder.com/800x450/7C3AED/FFFFFF?text=' . urlencode($lang == 'ar' ? 'صورة تجريبية 2' : 'Demo Image 2'),
            'https://via.placeholder.com/800x450/059669/FFFFFF?text=' . urlencode($lang == 'ar' ? 'صورة تجريبية 3' : 'Demo Image 3')
        )
    );
} else {
    $mod_data = getModData($mod_id, $SUPABASE_URL, $SUPABASE_KEY);

    if (!$mod_data) {
        $error_msg = $lang == 'ar' ? "المود غير موجود." : "Mod not found.";
        die("<h1>$error_msg</h1>");
    }
}

// جلب إعدادات الإعلانات
$ads_settings = getAdsSettings($user_id, $SUPABASE_URL, $SUPABASE_KEY);

// جلب إعدادات تخصيص الصفحة
$page_customization = getPageCustomizationSettings($user_id, $SUPABASE_URL, $SUPABASE_KEY);

// تشخيص إعدادات التخصيص (للتطوير فقط)
if ($is_preview) {
    if ($page_customization) {
        error_log("Page customization data: " . json_encode($page_customization));
        error_log("Applied style_template: " . $style_template);
        error_log("Applied colors - BG: $custom_bg_color, Header: $custom_header_color, Button: $custom_button_color");
    } else {
        error_log("No page customization data found for user: " . $user_id);
    }
}

// جلب الرابط المخصص للقناة إذا كان متوفراً
$custom_download_link = null;
if ($channel_id) {
    $custom_download_link = getCustomDownloadLink($channel_id, $SUPABASE_URL, $SUPABASE_KEY);
}

// تحضير البيانات للعرض
$mod_title = $mod_data['name'] ?? 'N/A';
$mod_image_urls = $mod_data['image_urls'] ?? [];
$mod_image_url = !empty($mod_image_urls) ? $mod_image_urls[0] : '';
$mod_version = $mod_data['version'] ?? 'N/A';

// الحصول على رابط التحميل (مخصص أولاً، ثم مختصر، ثم أصلي)
if (!empty($custom_download_link)) {
    $mod_download_url = $custom_download_link;
} else {
    $mod_download_url = getDownloadUrlForMod($mod_data, $user_id, $SUPABASE_URL, $SUPABASE_KEY);
}

if (empty($mod_download_url)) {
    $mod_download_url = '#'; // رابط احتياطي
}

// الوصف حسب اللغة
$description_ar = $mod_data['description_ar'] ?? '';
$description_en = $mod_data['description'] ?? '';
$mod_description = $lang == 'ar' ? $description_ar : $description_en;
if (empty($mod_description)) {
    $mod_description = $lang == 'ar' ? 'لا يوجد وصف متاح' : 'No description available';
}

// تصنيف المود
$mod_category_key = strtolower($mod_data['category'] ?? 'unknown');
$category_names = array(
    'ar' => array(
        'addons' => 'إضافات',
        'shaders' => 'شيدرات', 
        'texture_packs' => 'حزم النسيج',
        'seeds' => 'بذور',
        'maps' => 'خرائط',
        'unknown' => 'غير محدد'
    ),
    'en' => array(
        'addons' => 'Add-ons',
        'shaders' => 'Shaders',
        'texture_packs' => 'Texture Packs', 
        'seeds' => 'Seeds',
        'maps' => 'Maps',
        'unknown' => 'Not specified'
    )
);

$mod_category = $category_names[$lang][$mod_category_key] ?? $category_names[$lang]['unknown'];

// نصوص الأزرار (مع التخصيص)
$download_button_text = $lang == 'ar' ? 'تحميل المود' : 'Download Mod';
if ($page_customization) {
    if ($lang == 'ar' && !empty($page_customization['download_button_text_ar'])) {
        $download_button_text = $page_customization['download_button_text_ar'];
    } elseif ($lang == 'en' && !empty($page_customization['download_button_text_en'])) {
        $download_button_text = $page_customization['download_button_text_en'];
    }
}

// اسم الموقع (مع التخصيص)
$site_name = 'Modetaris';
if ($page_customization && !empty($page_customization['site_name'])) {
    $site_name = $page_customization['site_name'];
}

// إعدادات عرض الصور
$show_all_images = true;
if ($page_customization && isset($page_customization['show_all_images'])) {
    $show_all_images = $page_customization['show_all_images'];
}

// تحضير الصور للعرض بناءً على إعدادات التخصيص
if (!$show_all_images && !empty($mod_image_urls)) {
    // عرض الصورة الرئيسية فقط
    $display_images = array($mod_image_urls[0]);
} else {
    // عرض جميع الصور
    $display_images = $mod_image_urls;
}

// تحويل مصفوفة الصور إلى JSON
$mod_image_urls_json = json_encode($display_images);

// إعدادات الألوان والثيم (مع التخصيص)
$theme = 'default';
$style_template = 'default';
$custom_bg_color = '#1a1a1a';
$custom_header_color = '#FFA500';
$custom_text_color = '#ffffff';
$custom_button_color = '#FFA500';
$custom_border_color = '#333333';
$custom_accent_color = '#FFD700';
$custom_card_color = '#2D2D2D';
$custom_shadow_color = 'rgba(0,0,0,0.3)';
$custom_font_family = 'Press Start 2P';
$custom_font_size = 'medium';
$enable_animations = true;
$enable_gradients = true;
$enable_shadows = true;
$layout_style = 'modern';
$border_radius = 'medium';

if ($page_customization) {
    $theme = $page_customization['page_theme'] ?? 'default';
    $style_template = $page_customization['style_template'] ?? 'default';
    $custom_bg_color = $page_customization['custom_bg_color'] ?? $custom_bg_color;
    $custom_header_color = $page_customization['custom_header_color'] ?? $custom_header_color;
    $custom_text_color = $page_customization['custom_text_color'] ?? $custom_text_color;
    $custom_button_color = $page_customization['custom_button_color'] ?? $custom_button_color;
    $custom_border_color = $page_customization['custom_border_color'] ?? $custom_border_color;
    $custom_accent_color = $page_customization['custom_accent_color'] ?? $custom_accent_color;
    $custom_card_color = $page_customization['custom_card_color'] ?? $custom_card_color;
    $custom_shadow_color = $page_customization['custom_shadow_color'] ?? $custom_shadow_color;
    $custom_font_family = $page_customization['custom_font_family'] ?? $custom_font_family;
    $custom_font_size = $page_customization['custom_font_size'] ?? $custom_font_size;
    $enable_animations = isset($page_customization['enable_animations']) ? $page_customization['enable_animations'] : $enable_animations;
    $enable_gradients = isset($page_customization['enable_gradients']) ? $page_customization['enable_gradients'] : $enable_gradients;
    $enable_shadows = isset($page_customization['enable_shadows']) ? $page_customization['enable_shadows'] : $enable_shadows;
    $layout_style = $page_customization['layout_style'] ?? $layout_style;
    $border_radius = $page_customization['border_radius'] ?? $border_radius;

    // إذا كان style_template محدد، تطبيق الإعدادات المناسبة
    if (isset($page_customization['style_template']) && $page_customization['style_template'] != 'default') {
        $style_template = $page_customization['style_template'];

        // تطبيق إعدادات الستايل إذا لم تكن محددة مسبقاً
        switch($style_template) {
            case 'telegram':
                if (!isset($page_customization['custom_bg_color'])) $custom_bg_color = '#0088cc';
                if (!isset($page_customization['custom_header_color'])) $custom_header_color = '#0088cc';
                if (!isset($page_customization['custom_text_color'])) $custom_text_color = '#ffffff';
                if (!isset($page_customization['custom_button_color'])) $custom_button_color = '#40a7e3';
                if (!isset($page_customization['custom_accent_color'])) $custom_accent_color = '#64b5f6';
                if (!isset($page_customization['custom_font_family'])) $custom_font_family = 'Roboto';
                break;
            case 'tiktok':
                if (!isset($page_customization['custom_bg_color'])) $custom_bg_color = '#000000';
                if (!isset($page_customization['custom_header_color'])) $custom_header_color = '#ff0050';
                if (!isset($page_customization['custom_text_color'])) $custom_text_color = '#ffffff';
                if (!isset($page_customization['custom_button_color'])) $custom_button_color = '#ff0050';
                if (!isset($page_customization['custom_accent_color'])) $custom_accent_color = '#25f4ee';
                if (!isset($page_customization['custom_font_family'])) $custom_font_family = 'Poppins';
                break;
            case 'classic':
                if (!isset($page_customization['custom_bg_color'])) $custom_bg_color = '#f5f5dc';
                if (!isset($page_customization['custom_header_color'])) $custom_header_color = '#8b4513';
                if (!isset($page_customization['custom_text_color'])) $custom_text_color = '#2f4f4f';
                if (!isset($page_customization['custom_button_color'])) $custom_button_color = '#cd853f';
                if (!isset($page_customization['custom_accent_color'])) $custom_accent_color = '#daa520';
                if (!isset($page_customization['custom_font_family'])) $custom_font_family = 'Georgia';
                break;
            case 'professional':
                if (!isset($page_customization['custom_bg_color'])) $custom_bg_color = '#f8f9fa';
                if (!isset($page_customization['custom_header_color'])) $custom_header_color = '#2c3e50';
                if (!isset($page_customization['custom_text_color'])) $custom_text_color = '#2c3e50';
                if (!isset($page_customization['custom_button_color'])) $custom_button_color = '#3498db';
                if (!isset($page_customization['custom_accent_color'])) $custom_accent_color = '#e74c3c';
                if (!isset($page_customization['custom_font_family'])) $custom_font_family = 'Inter';
                break;
        }
    }
}

// إعدادات الإعلانات للجافاسكريبت
$ads_settings_json = $ads_settings ? json_encode($ads_settings) : 'null';
?>
<!DOCTYPE html>
<html id="html-root" lang="<?php echo $lang; ?>" dir="<?php echo $lang == 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; font-src 'self' data: https://cdn.jsdelivr.net https://fonts.googleapis.com https://fonts.gstatic.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://telegram.org; img-src 'self' data: blob: https: http:; connect-src 'self' https: http: ws: wss:;">
    <title id="page-title"><?php echo htmlspecialchars($mod_title); ?> - <?php echo $lang == 'ar' ? 'تفاصيل المود' : 'Mod Details'; ?></title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='.9em' font-size='90'%3E🎮%3C/text%3E%3C/svg%3E">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Roboto:wght@300;400;500;700&family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="style-templates.css">

    <!-- Custom Style Template CSS -->
    <style>
        :root {
            --bg-color: <?php echo $custom_bg_color; ?>;
            --header-color: <?php echo $custom_header_color; ?>;
            --text-color: <?php echo $custom_text_color; ?>;
            --button-color: <?php echo $custom_button_color; ?>;
            --border-color: <?php echo $custom_border_color; ?>;
            --accent-color: <?php echo $custom_accent_color; ?>;
            --card-color: <?php echo $custom_card_color; ?>;
            --shadow-color: <?php echo $custom_shadow_color; ?>;
            --font-family: '<?php echo $custom_font_family; ?>', monospace;
            --font-size: <?php echo $custom_font_size; ?>;
            --border-radius: <?php
                switch($border_radius) {
                    case 'none': echo '0px'; break;
                    case 'small': echo '4px'; break;
                    case 'medium': echo '8px'; break;
                    case 'large': echo '16px'; break;
                    case 'round': echo '50px'; break;
                    default: echo '8px';
                }
            ?>;
        }

        /* Style Template Specific Styles */
        .style-template-<?php echo $style_template; ?> {
            font-family: var(--font-family);
        }

        <?php if ($style_template == 'telegram'): ?>
        /* Telegram Style */
        body {
            background: linear-gradient(135deg, #0088cc 0%, #40a7e3 100%);
            font-family: 'Roboto', sans-serif;
        }
        .mod-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--border-radius);
            box-shadow: 0 4px 12px rgba(0, 136, 204, 0.3);
        }
        .pixel-button {
            background: linear-gradient(45deg, #40a7e3, #64b5f6);
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }
        .pixel-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(64, 167, 227, 0.4);
        }

        <?php elseif ($style_template == 'tiktok'): ?>
        /* TikTok Style */
        body {
            background: linear-gradient(45deg, #000000 0%, #1a1a1a 50%, #000000 100%);
            font-family: 'Poppins', sans-serif;
            color: #ffffff;
        }
        .mod-container {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border: 2px solid #ff0050;
            border-radius: 20px;
            box-shadow: 0 0 20px rgba(255, 0, 80, 0.3);
        }
        .pixel-button {
            background: linear-gradient(45deg, #ff0050, #25f4ee);
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 0, 80, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 0, 80, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 0, 80, 0); }
        }

        <?php elseif ($style_template == 'classic'): ?>
        /* Classic Style */
        body {
            background: linear-gradient(to bottom, #f5f5dc 0%, #e6ddd4 100%);
            font-family: 'Georgia', serif;
            color: #2f4f4f;
        }
        .mod-container {
            background: #ffffff;
            border: 3px solid #8b4513;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(139, 69, 19, 0.2);
        }
        .pixel-button {
            background: linear-gradient(to bottom, #cd853f 0%, #daa520 100%);
            border: 2px solid #8b4513;
            border-radius: 4px;
            font-family: 'Georgia', serif;
            font-weight: bold;
        }
        .pixel-button:hover {
            background: linear-gradient(to bottom, #daa520 0%, #cd853f 100%);
        }

        <?php elseif ($style_template == 'professional'): ?>
        /* Professional Style */
        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-family: 'Inter', sans-serif;
            color: #2c3e50;
        }
        .mod-container {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        .pixel-button {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            border-radius: var(--border-radius);
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .pixel-button:hover {
            background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
            transform: translateY(-1px);
        }

        <?php else: ?>
        /* Default Style */
        body {
            background: var(--bg-color);
            font-family: var(--font-family);
            color: var(--text-color);
        }
        .mod-container {
            background: var(--card-color);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
        }
        .pixel-button {
            background: var(--button-color);
            border-radius: var(--border-radius);
        }
        <?php endif; ?>

        /* Common animations and effects */
        <?php if ($enable_animations): ?>
        .mod-container {
            transition: all 0.3s ease;
        }
        .mod-container:hover {
            transform: translateY(-2px);
        }
        <?php endif; ?>

        <?php if ($enable_shadows): ?>
        .mod-container {
            box-shadow: 0 4px 12px var(--shadow-color);
        }
        <?php endif; ?>

        /* Font size adjustments */
        <?php
        switch($custom_font_size) {
            case 'small':
                echo '.mod-info { font-size: 0.875rem; }';
                echo '.mod-title { font-size: 1.25rem; }';
                break;
            case 'large':
                echo '.mod-info { font-size: 1.125rem; }';
                echo '.mod-title { font-size: 2rem; }';
                break;
            case 'extra-large':
                echo '.mod-info { font-size: 1.25rem; }';
                echo '.mod-title { font-size: 2.5rem; }';
                break;
            default: // medium
                echo '.mod-info { font-size: 1rem; }';
                echo '.mod-title { font-size: 1.5rem; }';
        }
        ?>

        /* Layout style adjustments */
        <?php if ($layout_style == 'compact'): ?>
        .container { padding: 0.5rem; }
        .mod-container { margin-bottom: 0.5rem; padding: 0.75rem; }
        <?php elseif ($layout_style == 'spacious'): ?>
        .container { padding: 2rem; }
        .mod-container { margin-bottom: 2rem; padding: 2rem; }
        <?php elseif ($layout_style == 'minimal'): ?>
        .mod-container { border: none; box-shadow: none; background: transparent; }
        <?php endif; ?>
    </style>
</head>
<body class="style-template-<?php echo $style_template; ?>" data-style="<?php echo $style_template; ?>" data-layout="<?php echo $layout_style; ?>">
    <!-- Header -->
    <header class="bg-yellow-500 text-white p-4 relative">
        <div class="flex justify-between items-center">
            <!-- صورة القناة - اليسار -->
            <?php if ($page_customization && !empty($page_customization['channel_logo_url']) && ($page_customization['logo_position'] ?? 'right') == 'left'): ?>
                <div class="flex-shrink-0">
                    <img src="<?php echo htmlspecialchars($page_customization['channel_logo_url']); ?>"
                         alt="Channel Logo"
                         class="w-10 h-10 rounded-full border-2 border-white shadow-lg">
                </div>
            <?php else: ?>
                <div class="flex-shrink-0 w-10"></div> <!-- مساحة فارغة للتوازن -->
            <?php endif; ?>

            <!-- اسم الموقع - الوسط -->
            <div class="flex-grow text-center">
                <h1 class="font-bold text-2xl"><?php echo htmlspecialchars($site_name); ?></h1>
            </div>

            <!-- صورة القناة - اليمين -->
            <?php if ($page_customization && !empty($page_customization['channel_logo_url']) && ($page_customization['logo_position'] ?? 'right') == 'right'): ?>
                <div class="flex-shrink-0">
                    <img src="<?php echo htmlspecialchars($page_customization['channel_logo_url']); ?>"
                         alt="Channel Logo"
                         class="w-10 h-10 rounded-full border-2 border-white shadow-lg">
                </div>
            <?php else: ?>
                <div class="flex-shrink-0 w-10"></div> <!-- مساحة فارغة للتوازن -->
            <?php endif; ?>
        </div>
    </header>

    <div class="container mx-auto p-4 pb-24">
        <?php if ($is_preview): ?>
        <!-- Preview Notice -->
        <div class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-4 rounded">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm">
                        <?php echo $lang == 'ar' ?
                            '👀 <strong>وضع المعاينة:</strong> هذه معاينة لكيفية ظهور صفحة المود مع التخصيصات التي قمت بها.' :
                            '👀 <strong>Preview Mode:</strong> This is a preview of how your mod page will look with your customizations.'; ?>
                    </p>
                    <?php if ($page_customization): ?>
                        <p class="text-xs mt-2">
                            <strong>إعدادات التخصيص:</strong><br>
                            الستايل: <?php echo htmlspecialchars($style_template); ?><br>
                            لون الخلفية: <?php echo htmlspecialchars($custom_bg_color); ?><br>
                            لون الهيدر: <?php echo htmlspecialchars($custom_header_color); ?><br>
                            لون الأزرار: <?php echo htmlspecialchars($custom_button_color); ?><br>
                            الخط: <?php echo htmlspecialchars($custom_font_family); ?><br>
                            موضع الصورة: <?php echo htmlspecialchars($page_customization['logo_position'] ?? 'غير محدد'); ?><br>
                            رابط الصورة: <?php echo !empty($page_customization['channel_logo_url']) ? 'موجود' : 'غير موجود'; ?>
                        </p>
                    <?php else: ?>
                        <p class="text-xs mt-2 text-red-600">
                            <strong>تحذير:</strong> لا توجد إعدادات تخصيص للمستخدم <?php echo htmlspecialchars($user_id ?? 'غير محدد'); ?>
                        </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Mod Title -->
        <div class="mod-header pixel-border mb-4">
            <h1 class="text-2xl mod-title"><?php echo htmlspecialchars($mod_title); ?></h1>
        </div>

        <!-- Main Mod Image Display -->
        <div class="mod-container mb-4 p-1 image-glow-effect">
            <div class="relative w-full bg-gray-700" style="padding-top: 56.25%;">
                <img id="main-mod-image" class="absolute inset-0 w-full h-full object-cover" src="<?php echo htmlspecialchars($mod_image_url); ?>" alt="<?php echo $lang == 'ar' ? 'صورة المود' : 'Mod Image'; ?>">
            </div>
            <div class="particle p-bottom1" style="--tx: 2px; --ty: -25px;"></div>
            <div class="particle p-bottom2" style="--tx: 0px; --ty: -30px;"></div>
            <div class="particle p-bottom3" style="--tx: -2px; --ty: -28px;"></div>
        </div>

        <!-- Thumbnail Navigation and Controls -->
        <div class="flex items-center justify-center mb-4 px-2">
            <button id="prev-image" class="nav-button pixel-border mr-2" style="flex-shrink: 0;">‹</button>
            <div id="thumbnail-container" class="flex flex-grow justify-center items-center overflow-x-auto" style="scroll-behavior: smooth; -webkit-overflow-scrolling: touch;">
                <!-- Thumbnails will be inserted here by JavaScript -->
            </div>
            <button id="next-image" class="nav-button pixel-border ml-2" style="flex-shrink: 0;">›</button>
        </div>

        <!-- Mod Info -->
        <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="mod-container pixel-border p-4">
                <p class="info-label version-label"><?php echo $lang == 'ar' ? 'الإصدار' : 'Version'; ?></p>
                <p class="mod-info mod-version"><?php echo htmlspecialchars($mod_version); ?></p>
            </div>
            <div class="mod-container pixel-border p-4">
                <p class="info-label loader-label"><?php echo $lang == 'ar' ? 'تصنيف المود' : 'Mod Category'; ?></p>
                <p class="mod-info mod-category"><?php echo htmlspecialchars($mod_category); ?></p>
            </div>
        </div>

        <!-- Mod Description -->
        <div class="mod-container pixel-border p-4 mb-6">
            <p class="info-label text-center description-label"><?php echo $lang == 'ar' ? 'الوصف' : 'Description'; ?></p>
            <p class="mod-info mt-2 text-center mod-description"><?php echo htmlspecialchars($mod_description); ?></p>
        </div>

        <!-- Ad Overlay -->
        <div id="ad-overlay" class="fixed inset-0 bg-black bg-opacity-75 z-60 hidden flex items-center justify-center">
            <div class="bg-white rounded-lg p-6 max-w-md mx-4 text-center relative">
                <div id="ad-content" class="mb-4">
                    <h3 class="text-lg font-bold text-gray-800 mb-2"><?php echo $lang == 'ar' ? 'إعلان' : 'Advertisement'; ?></h3>
                    <p class="text-gray-600 mb-4"><?php echo $lang == 'ar' ? 'سيتم فتح الإعلان في نافذة جديدة...' : 'Ad will open in a new window...'; ?></p>
                </div>
                <button id="close-ad-btn" class="bg-red-500 text-white px-4 py-2 rounded hidden" onclick="closeAd()">
                    <?php echo $lang == 'ar' ? 'إغلاق' : 'Close'; ?>
                </button>
                <div id="countdown" class="text-sm text-gray-500 mt-2"></div>
            </div>
        </div>

        <!-- Download Button -->
        <div class="fixed bottom-0 left-0 right-0 p-4 z-50 flex justify-center">
            <button id="download-button" class="pixel-button text-xl py-3 px-10" style="position: relative; overflow: hidden;" onclick="handleDownload()">
                <div class="progress-bar" id="progress-bar"></div>
                <span class="download-icon" id="download-icon">📥</span>
                <span id="download-text"><?php echo $download_button_text; ?></span>
            </button>
        </div>
    </div>

    <script>
        // بيانات المود
        const imageUrls = <?php echo $mod_image_urls_json; ?>;
        const modDownloadUrl = '<?php echo addslashes($mod_download_url); ?>';
        const modId = '<?php echo addslashes($mod_id); ?>';
        const modTitle = '<?php echo addslashes($mod_title); ?>';
        const lang = '<?php echo $lang; ?>';
        const userId = '<?php echo addslashes($user_id); ?>';
        
        // إعدادات الإعلانات
        const adsSettings = <?php echo $ads_settings_json; ?>;
        
        let currentImageIndex = 0;
        let adShown = false;
        let isDownloading = false;
        let isDownloaded = false;
        let downloadProgress = 0;
        
        // تحميل الجافاسكريبت الخارجي
        const script = document.createElement('script');
        script.src = 'script.js';
        document.head.appendChild(script);
    </script>
</body>
</html>
