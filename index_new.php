<?php
/**
 * صفحة عرض تفاصيل المودات المحسنة مع معالجة أخطاء متقدمة
 * Enhanced Mod Details Page with Advanced Error Handling
 */

// تضمين ملف الإعدادات
define('INCLUDED', true);
require_once 'config_fixed.php';

// بدء جلسة للتتبع
session_start();

/**
 * دالة عرض صفحة خطأ
 */
function showErrorPage($title, $message, $details = null) {
    $lang = $_GET['lang'] ?? 'ar';
    $direction = $lang === 'ar' ? 'rtl' : 'ltr';
    
    logError("Showing error page: $title", 'ERROR', [
        'message' => $message,
        'details' => $details,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
    ]);
    
    ?>
    <!DOCTYPE html>
    <html lang="<?php echo $lang; ?>" dir="<?php echo $direction; ?>">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo htmlspecialchars($title); ?> - Modetaris</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); color: white; min-height: 100vh; }
            .error-container { background: rgba(45, 45, 45, 0.9); backdrop-filter: blur(10px); border: 1px solid #555; }
            .pulse { animation: pulse 2s infinite; }
            @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        </style>
    </head>
    <body class="flex items-center justify-center">
        <div class="error-container rounded-lg p-8 max-w-md mx-4 text-center">
            <div class="text-6xl mb-4 pulse">⚠️</div>
            <h1 class="text-2xl font-bold mb-4 text-red-400"><?php echo htmlspecialchars($title); ?></h1>
            <p class="text-gray-300 mb-6"><?php echo htmlspecialchars($message); ?></p>
            
            <?php if (DEBUG_MODE && $details): ?>
            <details class="text-left text-sm text-gray-400 mb-6">
                <summary class="cursor-pointer text-yellow-400">تفاصيل تقنية</summary>
                <pre class="mt-2 p-2 bg-gray-800 rounded text-xs overflow-auto"><?php echo htmlspecialchars(print_r($details, true)); ?></pre>
            </details>
            <?php endif; ?>
            
            <div class="space-y-3">
                <button onclick="location.reload()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition">
                    🔄 إعادة المحاولة
                </button>
                <a href="/" class="block w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded transition">
                    🏠 العودة للصفحة الرئيسية
                </a>
                <a href="/deploy.php?setup=true" class="block w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded transition">
                    🔧 صفحة التشخيص
                </a>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

/**
 * دالة التحقق من صحة المعاملات
 */
function validateParameters() {
    $mod_id = $_GET['id'] ?? null;
    $lang = $_GET['lang'] ?? 'ar';
    $user_id = $_GET['user_id'] ?? null;
    $channel_id = $_GET['channel'] ?? null;
    
    // التحقق من معرف المود
    if (empty($mod_id)) {
        showErrorPage(
            'معرف المود مفقود',
            'يرجى التأكد من صحة الرابط. معرف المود مطلوب لعرض التفاصيل.',
            ['missing_parameter' => 'id']
        );
    }
    
    if (!is_numeric($mod_id) || $mod_id <= 0) {
        showErrorPage(
            'معرف المود غير صحيح',
            'معرف المود يجب أن يكون رقم صحيح أكبر من صفر.',
            ['invalid_mod_id' => $mod_id]
        );
    }
    
    // التحقق من اللغة
    if (!in_array($lang, ['ar', 'en'])) {
        $lang = 'ar';
    }
    
    return [
        'mod_id' => (int)$mod_id,
        'lang' => $lang,
        'user_id' => $user_id,
        'channel_id' => $channel_id
    ];
}

/**
 * دالة جلب بيانات المود مع معالجة أخطاء شاملة
 */
function fetchModData($mod_id) {
    logError("Fetching mod data for display", 'INFO', ['mod_id' => $mod_id]);
    
    // اختبار الاتصال أولاً
    $connection_test = testSupabaseConnection();
    if (!$connection_test['success']) {
        showErrorPage(
            'خطأ في الاتصال بقاعدة البيانات',
            'لا يمكن الاتصال بقاعدة البيانات حالياً. يرجى المحاولة مرة أخرى لاحقاً.',
            $connection_test
        );
    }
    
    // جلب بيانات المود من الجدول الصحيح
    $response = getModById($mod_id);
    
    if (!$response['success']) {
        // محاولة مع طرق مختلفة
        $alternative_endpoints = [
            "/rest/v1/" . MODS_TABLE . "?id=eq.$mod_id",
            "/rest/v1/" . MODS_TABLE . "?select=*&id=eq.$mod_id",
            "/rest/v1/" . MODS_TABLE
        ];
        
        foreach ($alternative_endpoints as $endpoint) {
            logError("Trying alternative endpoint: $endpoint", 'INFO');
            $response = makeSupabaseRequest($endpoint);
            
            if ($response['success']) {
                // إذا كان الطلب الأخير (جلب جميع البيانات)، ابحث عن المود المطلوب
                if ($endpoint === "/rest/v1/" . MODS_TABLE && !empty($response['data'])) {
                    foreach ($response['data'] as $mod) {
                        if (isset($mod['id']) && $mod['id'] == $mod_id) {
                            $response['data'] = [$mod];
                            break;
                        }
                    }
                }
                break;
            }
        }
        
        if (!$response['success']) {
            showErrorPage(
                'خطأ في جلب البيانات',
                'حدث خطأ أثناء جلب بيانات المود من قاعدة البيانات.',
                [
                    'supabase_error' => $response['error'],
                    'http_code' => $response['http_code'],
                    'endpoints_tried' => $alternative_endpoints
                ]
            );
        }
    }
    
    if (empty($response['data'])) {
        showErrorPage(
            'المود غير موجود',
            'لم يتم العثور على المود المطلوب. يرجى التحقق من معرف المود.',
            ['mod_id' => $mod_id, 'response' => $response]
        );
    }
    
    $mod_data = $response['data'][0];
    logError("Mod data retrieved successfully", 'INFO', [
        'mod_id' => $mod_id,
        'mod_name' => $mod_data['name'] ?? 'Unknown'
    ]);
    
    return $mod_data;
}

/**
 * دالة تحضير البيانات للعرض
 */
function prepareDisplayData($mod_data, $lang) {
    // تحضير الصور
    $image_urls = $mod_data['image_urls'] ?? [];
    if (is_string($image_urls)) {
        $image_urls = json_decode($image_urls, true) ?: [];
    }
    if (empty($image_urls)) {
        $image_urls = ['https://via.placeholder.com/800x450/2D2D2D/FFFFFF?text=No+Image'];
    }
    
    // تحضير الوصف
    $description_ar = $mod_data['description_ar'] ?? '';
    $description_en = $mod_data['description'] ?? '';
    $description = $lang === 'ar' ? $description_ar : $description_en;
    if (empty($description)) {
        $description = $lang === 'ar' ? 'لا يوجد وصف متاح' : 'No description available';
    }
    
    // تحضير التصنيف
    $category_key = strtolower($mod_data['category'] ?? 'unknown');
    $category_names = [
        'ar' => [
            'addons' => 'إضافات',
            'shaders' => 'شيدرات',
            'texture_packs' => 'حزم النسيج',
            'seeds' => 'بذور',
            'maps' => 'خرائط',
            'unknown' => 'غير محدد'
        ],
        'en' => [
            'addons' => 'Add-ons',
            'shaders' => 'Shaders',
            'texture_packs' => 'Texture Packs',
            'seeds' => 'Seeds',
            'maps' => 'Maps',
            'unknown' => 'Not specified'
        ]
    ];
    
    return [
        'title' => $mod_data['name'] ?? 'N/A',
        'image_urls' => $image_urls,
        'main_image' => $image_urls[0],
        'version' => $mod_data['version'] ?? 'N/A',
        'download_url' => $mod_data['download_url'] ?? '#',
        'description' => $description,
        'category' => $category_names[$lang][$category_key] ?? $category_names[$lang]['unknown'],
        'created_at' => $mod_data['created_at'] ?? null,
        'file_size' => $mod_data['file_size'] ?? null
    ];
}

// معالجة الطلب الرئيسي
try {
    // التحقق من المعاملات
    $params = validateParameters();
    
    // جلب بيانات المود
    $mod_data = fetchModData($params['mod_id']);
    
    // تحضير البيانات للعرض
    $display_data = prepareDisplayData($mod_data, $params['lang']);
    
    // تحضير النصوص حسب اللغة
    $texts = [
        'ar' => [
            'mod_details' => 'تفاصيل المود',
            'version' => 'الإصدار',
            'category' => 'تصنيف المود',
            'description' => 'الوصف',
            'download_mod' => 'تحميل المود',
            'mod_image' => 'صورة المود'
        ],
        'en' => [
            'mod_details' => 'Mod Details',
            'version' => 'Version',
            'category' => 'Mod Category',
            'description' => 'Description',
            'download_mod' => 'Download Mod',
            'mod_image' => 'Mod Image'
        ]
    ];
    
    $t = $texts[$params['lang']];
    $direction = $params['lang'] === 'ar' ? 'rtl' : 'ltr';
    
} catch (Exception $e) {
    showErrorPage(
        'خطأ في النظام',
        'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
        [
            'exception' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    );
}

// تسجيل عرض الصفحة بنجاح
logError("Page displayed successfully", 'INFO', [
    'mod_id' => $params['mod_id'],
    'mod_title' => $display_data['title'],
    'lang' => $params['lang'],
    'user_id' => $params['user_id']
]);
?>
<!DOCTYPE html>
<html id="html-root" lang="<?php echo $params['lang']; ?>" dir="<?php echo $direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($display_data['title']); ?> - <?php echo $t['mod_details']; ?></title>
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="<?php echo htmlspecialchars(substr($display_data['description'], 0, 160)); ?>">
    <meta name="keywords" content="minecraft, mods, <?php echo htmlspecialchars($display_data['category']); ?>, <?php echo htmlspecialchars($display_data['title']); ?>">
    <meta name="author" content="Modetaris">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($display_data['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars(substr($display_data['description'], 0, 200)); ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars($display_data['main_image']); ?>">
    <meta property="og:type" content="website">
    
    <!-- Stylesheets -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css?v=<?php echo time(); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎮</text></svg>">
</head>
<body>
    <!-- Header -->
    <header class="bg-yellow-500 text-white p-4 flex justify-center items-center">
        <div class="font-bold text-2xl">Modetaris</div>
    </header>

    <div class="container mx-auto p-4 pb-24">
        <!-- Mod Title -->
        <div class="mod-header pixel-border mb-4">
            <h1 class="text-2xl mod-title"><?php echo htmlspecialchars($display_data['title']); ?></h1>
        </div>

        <!-- Main Mod Image Display -->
        <div class="mod-container mb-4 p-1 image-glow-effect">
            <div class="relative w-full bg-gray-700" style="padding-top: 56.25%;">
                <img id="main-mod-image" 
                     class="absolute inset-0 w-full h-full object-cover" 
                     src="<?php echo htmlspecialchars($display_data['main_image']); ?>" 
                     alt="<?php echo $t['mod_image']; ?>"
                     onerror="this.src='https://via.placeholder.com/800x450/2D2D2D/FFFFFF?text=Image+Not+Found'">
            </div>
            <div class="particle p-bottom1" style="--tx: 2px; --ty: -25px;"></div>
            <div class="particle p-bottom2" style="--tx: 0px; --ty: -30px;"></div>
            <div class="particle p-bottom3" style="--tx: -2px; --ty: -28px;"></div>
        </div>

        <!-- Thumbnail Navigation and Controls -->
        <?php if (count($display_data['image_urls']) > 1): ?>
        <div class="flex items-center justify-center mb-4">
            <button id="prev-image" class="nav-button pixel-border mr-2">&lt;</button>
            <div id="thumbnail-container" class="flex flex-grow justify-center items-center space-x-2 overflow-x-auto">
                <!-- Thumbnails will be inserted here by JavaScript -->
            </div>
            <button id="next-image" class="nav-button pixel-border ml-2">&gt;</button>
        </div>
        <?php endif; ?>

        <!-- Mod Info -->
        <div class="grid grid-cols-2 gap-4 mb-6">
            <div class="mod-container pixel-border p-4">
                <p class="info-label version-label"><?php echo $t['version']; ?></p>
                <p class="mod-info mod-version"><?php echo htmlspecialchars($display_data['version']); ?></p>
            </div>
            <div class="mod-container pixel-border p-4">
                <p class="info-label loader-label"><?php echo $t['category']; ?></p>
                <p class="mod-info mod-category"><?php echo htmlspecialchars($display_data['category']); ?></p>
            </div>
        </div>

        <!-- Mod Description -->
        <div class="mod-container pixel-border p-4 mb-6">
            <p class="info-label text-center description-label"><?php echo $t['description']; ?></p>
            <p class="mod-info mt-2 text-center mod-description"><?php echo htmlspecialchars($display_data['description']); ?></p>
        </div>

        <!-- Download Button -->
        <div class="fixed bottom-0 left-0 right-0 p-4 z-50 flex justify-center">
            <button id="download-button" 
                    class="pixel-button text-xl py-3 px-10 new-feature" 
                    style="position: relative; overflow: hidden;" 
                    onclick="handleDownload()">
                <div class="progress-bar" id="progress-bar"></div>
                <span class="download-icon" id="download-icon">📥</span>
                <span id="download-text"><?php echo $t['download_mod']; ?></span>
            </button>
        </div>
    </div>

    <!-- JavaScript Variables -->
    <script>
        // بيانات المود
        const imageUrls = <?php echo json_encode($display_data['image_urls'], JSON_UNESCAPED_UNICODE); ?>;
        const modDownloadUrl = <?php echo json_encode($display_data['download_url'], JSON_UNESCAPED_UNICODE); ?>;
        const modId = <?php echo json_encode($params['mod_id'], JSON_UNESCAPED_UNICODE); ?>;
        const modTitle = <?php echo json_encode($display_data['title'], JSON_UNESCAPED_UNICODE); ?>;
        const lang = <?php echo json_encode($params['lang'], JSON_UNESCAPED_UNICODE); ?>;
        const userId = <?php echo json_encode($params['user_id'], JSON_UNESCAPED_UNICODE); ?>;
        
        let currentImageIndex = 0;
        let isDownloading = false;
        let isDownloaded = false;
        let downloadProgress = 0;
        
        // معلومات إضافية للتصحيح
        const debugInfo = {
            modData: <?php echo json_encode($mod_data, JSON_UNESCAPED_UNICODE); ?>,
            displayData: <?php echo json_encode($display_data, JSON_UNESCAPED_UNICODE); ?>,
            params: <?php echo json_encode($params, JSON_UNESCAPED_UNICODE); ?>
        };
        
        console.log('Mod page loaded successfully', debugInfo);
    </script>
    
    <!-- Load external JavaScript -->
    <script src="script.js?v=<?php echo time(); ?>"></script>
    
    <!-- Error handling script -->
    <script>
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
            // يمكن إرسال الخطأ إلى الخادم للتسجيل
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled Promise Rejection:', e.reason);
        });
    </script>
</body>
</html>
