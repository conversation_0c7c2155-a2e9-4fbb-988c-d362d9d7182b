#!/usr/bin/env python3
"""
إعداد الوصول عبر الشبكة المحلية
Local Network Access Setup

هذا الملف يساعد في إعداد الوصول للصفحات عبر الشبكة المحلية
This file helps setup access to pages via local network
"""

import os
import sys
import socket
import threading
import time
import requests
from pathlib import Path

def print_header():
    """طباعة رأس الإعداد"""
    print("\n" + "="*60)
    print("🏠 إعداد الوصول عبر الشبكة المحلية")
    print("📶 Local Network Access Setup")
    print("="*60)
    print("📱 سيتمكن جميع الأجهزة في نفس الشبكة من الوصول")
    print("🌐 All devices on the same network can access")
    print("="*60 + "\n")

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        # طريقة موثوقة للحصول على IP المحلي
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        try:
            # طريقة بديلة
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            return local_ip
        except Exception:
            return "127.0.0.1"

def get_all_network_interfaces():
    """الحصول على جميع واجهات الشبكة"""
    interfaces = []
    
    try:
        import netifaces
        
        for interface in netifaces.interfaces():
            addrs = netifaces.ifaddresses(interface)
            if netifaces.AF_INET in addrs:
                for addr in addrs[netifaces.AF_INET]:
                    ip = addr.get('addr')
                    if ip and not ip.startswith('127.'):
                        interfaces.append({
                            'interface': interface,
                            'ip': ip,
                            'netmask': addr.get('netmask', '*************')
                        })
    except ImportError:
        # إذا لم تكن مكتبة netifaces متوفرة
        local_ip = get_local_ip()
        if local_ip != "127.0.0.1":
            interfaces.append({
                'interface': 'default',
                'ip': local_ip,
                'netmask': '*************'
            })
    
    return interfaces

def check_port_availability(ip, port):
    """التحقق من توفر المنفذ"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((ip, port))
        sock.close()
        return result != 0  # True إذا كان المنفذ متاح
    except Exception:
        return False

def start_local_servers(host_ip):
    """تشغيل الخوادم على IP المحلي"""
    print(f"\n🚀 Starting servers on {host_ip}...")
    
    try:
        # تحديث إعدادات Flask لقبول الاتصالات من جميع الأجهزة
        os.environ['FLASK_HOST'] = host_ip
        
        # تشغيل خادم Telegram Web App
        from telegram_web_app import app as web_app
        
        web_app_thread = threading.Thread(
            target=lambda: web_app.run(host=host_ip, port=5001, debug=False),
            daemon=True,
            name="LocalTelegramWebApp"
        )
        web_app_thread.start()
        print(f"✅ Telegram Web App server started on {host_ip}:5001")
        
        # تشغيل الخادم الأساسي
        from web_server import app as flask_app
        
        flask_thread = threading.Thread(
            target=lambda: flask_app.run(host=host_ip, port=5000, debug=False),
            daemon=True,
            name="LocalFlaskServer"
        )
        flask_thread.start()
        print(f"✅ Flask server started on {host_ip}:5000")
        
        # انتظار قصير للتأكد من تشغيل الخوادم
        time.sleep(3)
        return True
        
    except Exception as e:
        print(f"❌ Failed to start servers: {e}")
        return False

def update_env_file(local_url):
    """تحديث ملف .env بالرابط المحلي"""
    print(f"\n📝 Updating .env file with local URL...")
    
    env_file = Path(".env")
    env_lines = []
    web_server_url_updated = False
    
    # قراءة الملف الموجود
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('WEB_SERVER_URL='):
                        env_lines.append(f"WEB_SERVER_URL={local_url}")
                        web_server_url_updated = True
                        print(f"✅ Updated WEB_SERVER_URL to {local_url}")
                    else:
                        env_lines.append(line)
        except Exception as e:
            print(f"⚠️ Error reading .env file: {e}")
    
    # إضافة WEB_SERVER_URL إذا لم يكن موجوداً
    if not web_server_url_updated:
        env_lines.append(f"WEB_SERVER_URL={local_url}")
        print(f"✅ Added WEB_SERVER_URL={local_url}")
    
    # حفظ الملف
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            for line in env_lines:
                f.write(line + '\n')
        print("✅ .env file updated successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to update .env file: {e}")
        return False

def test_local_access(local_url):
    """اختبار الوصول المحلي"""
    print(f"\n🧪 Testing local network access...")
    
    test_urls = [
        f"{local_url}/telegram-mod-details?id=1&lang=ar",
        f"{local_url}/api/mod/1?lang=ar"
    ]
    
    for url in test_urls:
        try:
            print(f"📡 Testing: {url}")
            response = requests.get(url, timeout=10)
            if response.status_code in [200, 404]:
                print(f"✅ URL accessible (status: {response.status_code})")
            else:
                print(f"⚠️ URL returned status: {response.status_code}")
        except Exception as e:
            print(f"❌ Failed to access {url}: {e}")

def scan_network_devices(base_ip):
    """فحص الأجهزة المتصلة بالشبكة"""
    print(f"\n🔍 Scanning network devices...")
    
    # استخراج الشبكة الأساسية (مثل 192.168.1)
    network_base = '.'.join(base_ip.split('.')[:-1])
    
    active_devices = []
    
    def ping_device(ip):
        try:
            import subprocess
            import platform
            
            # تحديد أمر ping حسب نظام التشغيل
            if platform.system().lower() == "windows":
                cmd = ["ping", "-n", "1", "-w", "1000", ip]
            else:
                cmd = ["ping", "-c", "1", "-W", "1", ip]
            
            result = subprocess.run(cmd, capture_output=True, timeout=2)
            if result.returncode == 0:
                active_devices.append(ip)
        except Exception:
            pass
    
    # فحص أول 20 جهاز في الشبكة
    threads = []
    for i in range(1, 21):
        ip = f"{network_base}.{i}"
        thread = threading.Thread(target=ping_device, args=(ip,))
        thread.start()
        threads.append(thread)
    
    # انتظار انتهاء جميع الفحوصات
    for thread in threads:
        thread.join()
    
    if active_devices:
        print(f"✅ Found {len(active_devices)} active devices:")
        for device in active_devices:
            print(f"   📱 {device}")
    else:
        print("⚠️ No active devices found (this is normal)")
    
    return active_devices

def create_connection_guide(local_url, local_ip):
    """إنشاء دليل الاتصال"""
    guide_content = f"""
# 📱 دليل الاتصال بالصفحات من الأجهزة الأخرى
# Connection Guide for Other Devices

## 🌐 الرابط الأساسي / Main URL
{local_url}

## 📱 روابط مهمة / Important Links
- صفحة المود / Mod Page: {local_url}/telegram-mod-details?id=1&lang=ar
- API: {local_url}/api/mod/1
- الخادم الأساسي / Main Server: http://{local_ip}:5000

## 📋 خطوات الاتصال / Connection Steps

### للهواتف الذكية / For Smartphones:
1. 📶 تأكد من الاتصال بنفس شبكة WiFi
2. 🌐 افتح المتصفح واذهب إلى: {local_url}
3. 📱 أو امسح QR Code إذا كان متوفراً

### للكمبيوتر / For Computers:
1. 🖥️ تأكد من الاتصال بنفس الشبكة
2. 🌐 افتح أي متصفح واذهب إلى: {local_url}
3. 🔖 احفظ الرابط في المفضلة

### للأجهزة اللوحية / For Tablets:
1. 📶 تأكد من الاتصال بنفس شبكة WiFi
2. 🌐 افتح المتصفح واذهب إلى: {local_url}
3. 📌 أضف الرابط للشاشة الرئيسية

## 🔧 استكشاف الأخطاء / Troubleshooting

### إذا لم تعمل الصفحة / If page doesn't work:
1. ✅ تأكد من أن جميع الأجهزة على نفس الشبكة
2. 🔥 تأكد من إيقاف Firewall مؤقتاً
3. 🔄 أعد تشغيل الخادم
4. 📱 جرب رابط مختلف: http://{local_ip}:5001

### رسائل خطأ شائعة / Common Error Messages:
- "لم يتم العثور على الصفحة" → تحقق من الرابط
- "انتهت مهلة الاتصال" → تحقق من الشبكة
- "رفض الاتصال" → تحقق من تشغيل الخادم

## 📞 للمساعدة / For Help:
- تواصل مع المطور: @Kim880198
- تحقق من سجلات الأخطاء في: bot_enhanced.log

---
تم إنشاء هذا الدليل تلقائياً في: {time.strftime('%Y-%m-%d %H:%M:%S')}
This guide was generated automatically on: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    try:
        with open("connection_guide.txt", "w", encoding='utf-8') as f:
            f.write(guide_content)
        print("✅ Connection guide saved as connection_guide.txt")
    except Exception as e:
        print(f"❌ Failed to create connection guide: {e}")

def show_local_access_info(local_url, local_ip):
    """عرض معلومات الوصول المحلي"""
    print("\n" + "="*60)
    print("🏠 إعداد الشبكة المحلية مكتمل!")
    print("📶 LOCAL NETWORK SETUP COMPLETE!")
    print("="*60)
    print(f"\n🌐 الرابط المحلي: {local_url}")
    print(f"📱 صفحة المود: {local_url}/telegram-mod-details?id=1&lang=ar")
    print(f"🔌 API: {local_url}/api/mod/1")
    print(f"🖥️ عنوان IP: {local_ip}")
    print("\n📋 كيفية الوصول من الأجهزة الأخرى:")
    print("1. 📶 تأكد من أن الجهاز متصل بنفس شبكة WiFi")
    print("2. 🌐 افتح المتصفح واذهب إلى الرابط أعلاه")
    print("3. 📱 أو استخدم الرابط في رسائل البوت")
    print("\n⚠️ ملاحظات مهمة:")
    print("• 📶 يجب أن تكون جميع الأجهزة على نفس الشبكة")
    print("• 🔥 قد تحتاج لإيقاف Firewall مؤقتاً")
    print("• 🔄 إذا لم تعمل، جرب إعادة تشغيل الراوتر")
    print("• 📄 راجع connection_guide.txt للتفاصيل")
    print("="*60)

def main():
    """الدالة الرئيسية"""
    try:
        print_header()
        
        # الحصول على عنوان IP المحلي
        local_ip = get_local_ip()
        print(f"🔍 Local IP detected: {local_ip}")
        
        # عرض جميع واجهات الشبكة
        interfaces = get_all_network_interfaces()
        if interfaces:
            print("\n📶 Available network interfaces:")
            for i, interface in enumerate(interfaces):
                print(f"   {i+1}. {interface['interface']}: {interface['ip']}")
            
            # السماح للمستخدم باختيار الواجهة
            if len(interfaces) > 1:
                try:
                    choice = input(f"\n🔢 Choose interface (1-{len(interfaces)}) or Enter for default: ").strip()
                    if choice and choice.isdigit():
                        idx = int(choice) - 1
                        if 0 <= idx < len(interfaces):
                            local_ip = interfaces[idx]['ip']
                            print(f"✅ Selected IP: {local_ip}")
                except Exception:
                    print(f"⚠️ Using default IP: {local_ip}")
        
        local_url = f"http://{local_ip}:5001"
        
        # التحقق من توفر المنافذ
        if not check_port_availability(local_ip, 5001):
            print(f"⚠️ Port 5001 might be in use on {local_ip}")
        
        # تشغيل الخوادم
        if not start_local_servers(local_ip):
            print("❌ Failed to start servers")
            return
        
        # تحديث ملف .env
        update_env_file(local_url)
        
        # اختبار الوصول المحلي
        test_local_access(local_url)
        
        # فحص الأجهزة في الشبكة
        scan_network_devices(local_ip)
        
        # إنشاء دليل الاتصال
        create_connection_guide(local_url, local_ip)
        
        # عرض معلومات الوصول
        show_local_access_info(local_url, local_ip)
        
        print("\n🔄 Servers are running... Press Ctrl+C to stop")
        
        try:
            # إبقاء البرنامج يعمل
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping servers...")
            print("✅ Stopped successfully")
        
    except Exception as e:
        print(f"\n💥 Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
