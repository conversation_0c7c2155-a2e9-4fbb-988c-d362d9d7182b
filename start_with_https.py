#!/usr/bin/env python3
"""
تشغيل البوت مع HTTPS تلقائياً
Start Bot with Automatic HTTPS

هذا الملف يحل مشكلة عدم عمل صفحات المودات على الأجهزة الأخرى
This file solves the issue of mod pages not working on other devices
"""

import os
import sys
import time
import subprocess
import threading
import requests
from pathlib import Path

def print_startup_banner():
    """طباعة شعار البدء"""
    print("\n" + "="*70)
    print("🎮 MINECRAFT MODS BOT - HTTPS AUTO SETUP")
    print("🌐 حل مشكلة عدم عمل الصفحات على الأجهزة الأخرى")
    print("🔒 Solving the issue of pages not working on other devices")
    print("="*70)
    print("✨ سيتم إعداد HTTPS تلقائياً للوصول من جميع الأجهزة")
    print("✨ HTTPS will be set up automatically for access from all devices")
    print("="*70 + "\n")

def check_ngrok():
    """التحقق من وجود ngrok"""
    try:
        result = subprocess.run(['ngrok', 'version'], 
                              capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except:
        return False

def auto_setup_ngrok():
    """إعداد ngrok تلقائياً"""
    print("🔍 Checking for ngrok...")
    
    if check_ngrok():
        print("✅ ngrok found!")
        return True
    
    print("❌ ngrok not found. Setting up automatically...")
    
    # محاولة تحميل ngrok تلقائياً
    try:
        import platform
        import zipfile
        import urllib.request
        
        system = platform.system().lower()
        
        if system == "windows":
            url = "https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-windows-amd64.zip"
            filename = "ngrok.zip"
        elif system == "darwin":
            url = "https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-darwin-amd64.zip"
            filename = "ngrok.zip"
        else:
            url = "https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-linux-amd64.tgz"
            filename = "ngrok.tgz"
        
        print(f"📥 Downloading ngrok for {system}...")
        urllib.request.urlretrieve(url, filename)
        
        print("📦 Extracting ngrok...")
        if filename.endswith('.zip'):
            with zipfile.ZipFile(filename, 'r') as zip_ref:
                zip_ref.extractall('.')
        else:
            import tarfile
            with tarfile.open(filename, 'r:gz') as tar_ref:
                tar_ref.extractall('.')
        
        # حذف الملف المضغوط
        os.remove(filename)
        
        # جعل ngrok قابل للتنفيذ على Linux/Mac
        if system != "windows":
            os.chmod("ngrok", 0o755)
        
        print("✅ ngrok installed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Failed to auto-install ngrok: {e}")
        print("\n📋 Manual installation required:")
        print("1. Go to: https://ngrok.com/download")
        print("2. Download ngrok for your system")
        print("3. Extract and place ngrok in this folder")
        print("4. Run this script again")
        return False

def start_ngrok_tunnel(port=5001):
    """تشغيل نفق ngrok"""
    print(f"🚀 Starting ngrok tunnel for port {port}...")
    
    try:
        # تشغيل ngrok في الخلفية
        process = subprocess.Popen([
            'ngrok', 'http', str(port), '--log=stdout'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # انتظار قصير لبدء التشغيل
        time.sleep(5)
        
        # الحصول على الرابط العام
        for attempt in range(3):
            try:
                response = requests.get('http://localhost:4040/api/tunnels', timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    tunnels = data.get('tunnels', [])
                    
                    for tunnel in tunnels:
                        public_url = tunnel.get('public_url', '')
                        if public_url.startswith('https://'):
                            print(f"✅ HTTPS tunnel created: {public_url}")
                            return public_url, process
                    
                    # إذا لم يوجد HTTPS، استخدم HTTP
                    for tunnel in tunnels:
                        public_url = tunnel.get('public_url', '')
                        if public_url.startswith('http://'):
                            print(f"⚠️ Only HTTP tunnel available: {public_url}")
                            return public_url, process
                
                print(f"⏳ Attempt {attempt + 1}/3: Waiting for ngrok to start...")
                time.sleep(3)
                
            except requests.RequestException:
                if attempt < 2:
                    print(f"⏳ Attempt {attempt + 1}/3: Waiting for ngrok API...")
                    time.sleep(3)
                else:
                    print("❌ Could not connect to ngrok API")
        
        return None, process
        
    except Exception as e:
        print(f"❌ Failed to start ngrok: {e}")
        return None, None

def update_env_file(https_url):
    """تحديث ملف .env"""
    print("📝 Updating .env file...")
    
    env_file = Path(".env")
    env_lines = []
    updated = False
    
    # قراءة الملف الموجود
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('WEB_SERVER_URL='):
                        env_lines.append(f"WEB_SERVER_URL={https_url}")
                        updated = True
                        print(f"✅ Updated WEB_SERVER_URL to {https_url}")
                    else:
                        env_lines.append(line)
        except Exception as e:
            print(f"⚠️ Error reading .env file: {e}")
    
    # إضافة WEB_SERVER_URL إذا لم يكن موجوداً
    if not updated:
        env_lines.append(f"WEB_SERVER_URL={https_url}")
        print(f"✅ Added WEB_SERVER_URL={https_url}")
    
    # حفظ الملف
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            for line in env_lines:
                f.write(line + '\n')
        print("✅ .env file updated successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to update .env file: {e}")
        return False

def start_bot_servers():
    """تشغيل خوادم البوت"""
    print("🤖 Starting bot servers...")
    
    try:
        # تشغيل خادم Telegram Web App
        from telegram_web_app import run_telegram_web_app
        
        web_app_thread = threading.Thread(
            target=run_telegram_web_app,
            args=(5001,),
            daemon=True,
            name="HTTPSTelegramWebApp"
        )
        web_app_thread.start()
        print("✅ Telegram Web App server started on port 5001")
        
        # تشغيل الخادم الأساسي
        from web_server import run_web_server
        
        flask_thread = threading.Thread(
            target=run_web_server,
            args=(5000,),
            daemon=True,
            name="HTTPSFlaskServer"
        )
        flask_thread.start()
        print("✅ Flask server started on port 5000")
        
        # انتظار قصير للتأكد من تشغيل الخوادم
        time.sleep(2)
        return True
        
    except Exception as e:
        print(f"❌ Failed to start servers: {e}")
        return False

def start_telegram_bot():
    """تشغيل بوت تيليجرام"""
    print("📱 Starting Telegram bot...")
    
    try:
        # تشغيل البوت الرئيسي
        import main
        print("✅ Telegram bot started successfully")
        
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        print(f"❌ Failed to start Telegram bot: {e}")
        raise

def show_success_info(https_url):
    """عرض معلومات النجاح"""
    print("\n" + "="*70)
    print("🎉 تم الإعداد بنجاح! الصفحة متاحة الآن لجميع الأجهزة")
    print("🌍 SUCCESS! Pages are now accessible from all devices")
    print("="*70)
    print(f"\n🔒 الرابط الآمن: {https_url}")
    print(f"📱 صفحة المود: {https_url}/telegram-mod-details?id=1&lang=ar")
    print(f"🔌 API: {https_url}/api/mod/1")
    print("\n📋 كيفية الاستخدام:")
    print("1. 🤖 البوت يعمل الآن")
    print("2. 📱 أرسل مود من البوت")
    print("3. 🎮 اضغط على زر 'عرض التفاصيل'")
    print("4. ✨ ستفتح الصفحة على أي جهاز في العالم!")
    print("\n🧪 للاختبار:")
    print(f"• افتح هذا الرابط على هاتفك: {https_url}")
    print("• شارك الرابط مع أصدقائك")
    print("• جرب من شبكات مختلفة")
    print("\n⚠️ ملاحظات مهمة:")
    print("• 🔄 يجب إبقاء هذا البرنامج يعمل")
    print("• 🌐 الرابط صالح طالما البرنامج يعمل")
    print("• 🔒 للإنتاج، استخدم خدمة استضافة دائمة")
    print("="*70)

def main():
    """الدالة الرئيسية"""
    try:
        print_startup_banner()
        
        # إعداد ngrok تلقائياً
        if not auto_setup_ngrok():
            print("❌ ngrok setup failed. Please install manually.")
            return
        
        # تشغيل خوادم البوت
        if not start_bot_servers():
            print("❌ Failed to start bot servers")
            return
        
        # تشغيل نفق ngrok
        https_url, ngrok_process = start_ngrok_tunnel(5001)
        if not https_url:
            print("❌ Failed to create ngrok tunnel")
            return
        
        # تحديث ملف .env
        update_env_file(https_url)
        
        # عرض معلومات النجاح
        show_success_info(https_url)
        
        print("\n🔄 All systems running... Press Ctrl+C to stop")
        
        # تشغيل بوت تيليجرام (blocking)
        start_telegram_bot()
        
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
        print("✅ Stopped successfully")
    except Exception as e:
        print(f"\n💥 Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
