#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص حالة الخوادم والمنافذ
"""

import socket
import requests
import subprocess
import sys

def check_port(host, port):
    """فحص إذا كان المنفذ مفتوح"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def check_process_on_port(port):
    """فحص العملية التي تستخدم المنفذ"""
    try:
        if sys.platform == "win32":
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            for line in lines:
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        return f"PID {pid}"
        else:
            result = subprocess.run(['lsof', '-i', f':{port}'], capture_output=True, text=True)
            if result.stdout:
                return result.stdout.strip()
    except:
        pass
    return None

def main():
    print("🔍 فحص حالة الخوادم والمنافذ...")
    print("=" * 50)
    
    # فحص المنافذ المهمة
    ports_to_check = [
        (5000, "Flask Web Server"),
        (5001, "Telegram Web App Server"),
        (4040, "ngrok API")
    ]
    
    for port, description in ports_to_check:
        print(f"\n📡 فحص {description} (المنفذ {port}):")
        
        if check_port('127.0.0.1', port):
            print(f"   ✅ المنفذ {port} مفتوح")
            
            # فحص العملية
            process_info = check_process_on_port(port)
            if process_info:
                print(f"   📋 العملية: {process_info}")
            
            # اختبار HTTP إذا كان خادم ويب
            if port in [5000, 5001]:
                try:
                    response = requests.get(f'http://127.0.0.1:{port}/', timeout=3)
                    print(f"   🌐 HTTP Status: {response.status_code}")
                    if response.status_code == 200:
                        print(f"   ✅ الخادم يستجيب بشكل طبيعي")
                    else:
                        print(f"   ⚠️ الخادم يستجيب ولكن بخطأ")
                except Exception as e:
                    print(f"   ❌ خطأ في الاتصال HTTP: {e}")
        else:
            print(f"   ❌ المنفذ {port} مغلق أو غير متاح")
            print(f"   💡 {description} غير مُشغل")
    
    # فحص ngrok خاص
    print(f"\n🔗 فحص حالة ngrok:")
    try:
        response = requests.get('http://localhost:4040/api/tunnels', timeout=3)
        if response.status_code == 200:
            data = response.json()
            tunnels = data.get('tunnels', [])
            if tunnels:
                print(f"   ✅ ngrok يعمل مع {len(tunnels)} نفق:")
                for tunnel in tunnels:
                    public_url = tunnel.get('public_url', 'N/A')
                    local_addr = tunnel.get('config', {}).get('addr', 'N/A')
                    print(f"      🌐 {public_url} → {local_addr}")
            else:
                print(f"   ⚠️ ngrok يعمل ولكن لا توجد أنفاق")
        else:
            print(f"   ❌ ngrok API يعطي خطأ: {response.status_code}")
    except:
        print(f"   ❌ ngrok غير مُشغل أو غير متاح")
    
    print(f"\n" + "=" * 50)
    print("📋 التوصيات:")
    
    # التوصيات بناءً على النتائج
    if not check_port('127.0.0.1', 5001):
        print("❌ خادم Telegram Web App (المنفذ 5001) غير مُشغل")
        print("   🔧 الحل: تشغيل البوت الرئيسي (python main.py)")
    
    if not check_port('127.0.0.1', 5000):
        print("❌ خادم Flask (المنفذ 5000) غير مُشغل")
        print("   🔧 الحل: تشغيل web_server.py منفصل أو التأكد من تشغيله في main.py")
    
    if not check_port('127.0.0.1', 4040):
        print("❌ ngrok غير مُشغل")
        print("   🔧 الحل: تشغيل ngrok http 5001")
    
    print("\n🚀 خطوات الإصلاح الموصى بها:")
    print("1. تشغيل ngrok: start_ngrok.bat")
    print("2. تشغيل البوت: python main.py")
    print("3. اختبار الخوادم: python test_web_server_fix.py")

if __name__ == "__main__":
    main()
