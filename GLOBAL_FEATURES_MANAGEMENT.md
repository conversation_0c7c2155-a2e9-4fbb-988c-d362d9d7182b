# 🌐 ميزة إدارة المميزات العامة على مستوى النظام

## 📋 نظرة عامة

تم إضافة ميزة قوية تمكن الأدمن من التحكم الكامل في توفر المميزات على مستوى النظام بالكامل. هذه الميزة تتيح تفعيل أو تعطيل أي ميزة لجميع المستخدمين بغض النظر عن عدد دعواتهم أو اشتراكاتهم.

## 🎯 الهدف

توفير أداة تحكم شاملة للأدمن تمكن من:
- **التحكم الكامل** في توفر المميزات على مستوى النظام
- **تعطيل المميزات مؤقتاً** للصيانة أو التطوير
- **تفعيل المميزات للجميع** في المناسبات الخاصة
- **إدارة مرنة** للمميزات بدون تعديل الكود

## 🛠️ المكونات المضافة

### 1. توسيع نظام إعدادات الأدمن
```json
{
  "admin_preview_required": true,
  "global_features_enabled": {
    "unlimited_channels": true,
    "url_shortener_access": true,
    "custom_download_links": true,
    "publish_intervals_extended": true,
    "tasks_system_access": true,
    "page_customization_vip": true,
    "ads_system_access": true
  }
}
```

### 2. دوال إدارة المميزات العامة
- `is_global_feature_enabled(feature_name)` - التحقق من تفعيل ميزة
- `set_global_feature_status(feature_name, enabled)` - تعيين حالة ميزة
- `get_all_global_features_status()` - الحصول على حالة جميع المميزات
- `enable_all_global_features()` - تفعيل جميع المميزات
- `disable_all_global_features()` - تعطيل جميع المميزات

### 3. تحديث نظام فحص الوصول
تم تحديث دالة `check_feature_access()` لتشمل فحص الإعدادات العامة:

```python
# التحقق من تفعيل الميزة على مستوى النظام أولاً
if not is_global_feature_enabled(global_feature_name):
    logger.debug(f"Feature '{feature_type}' is globally disabled")
    return False
```

### 4. واجهة إدارة المميزات العامة
- **زر في لوحة الأدمن:** "🌐 إدارة المميزات العامة"
- **عرض حالة المميزات** مع أيقونات واضحة
- **أزرار تحكم فردية** لكل ميزة
- **أزرار تحكم جماعية** لجميع المميزات

## 📊 المميزات المدعومة

| الميزة | المفتاح في الإعدادات | الوصف |
|-------|---------------------|--------|
| 📺 قنوات غير محدودة | `unlimited_channels` | إمكانية ربط عدد غير محدود من القنوات |
| 🔗 اختصار الروابط | `url_shortener_access` | استخدام خدمات اختصار الروابط |
| 🔗 روابط تحميل مخصصة | `custom_download_links` | روابط تحميل مخصصة للمودات |
| ⏰ أوقات نشر موسعة | `publish_intervals_extended` | أوقات نشر إضافية (10 و 15 دقيقة) |
| 📋 نظام المهام | `tasks_system_access` | إنشاء وإدارة المهام للمستخدمين |
| 🎨 تخصيص صفحات VIP | `page_customization_vip` | تخصيص متقدم لصفحات عرض المودات |
| 💰 نظام الإعلانات | `ads_system_access` | إضافة إعلانات للربح من البوت |

## 🔧 كيفية الاستخدام

### الخطوة 1: الوصول للميزة
1. افتح لوحة تحكم الأدمن بإرسال `/admin`
2. اضغط على زر "🌐 إدارة المميزات العامة"

### الخطوة 2: عرض حالة المميزات
سيتم عرض قائمة بجميع المميزات مع حالتها الحالية:
- ✅ = مفعلة على مستوى النظام
- ❌ = معطلة على مستوى النظام

### الخطوة 3: إدارة المميزات
- **لتفعيل ميزة:** اضغط على زر "✅ تفعيل [اسم الميزة]"
- **لتعطيل ميزة:** اضغط على زر "🚫 تعطيل [اسم الميزة]"
- **لتفعيل جميع المميزات:** اضغط على "✅ تفعيل جميع المميزات"
- **لتعطيل جميع المميزات:** اضغط على "🚫 تعطيل جميع المميزات"

### الخطوة 4: التأكيد والتطبيق
- التغييرات تطبق فوراً على جميع المستخدمين
- سيتم تحديث العرض لإظهار الحالة الجديدة
- جميع التغييرات يتم تسجيلها في ملف السجل

## 🎨 مثال على واجهة المستخدم

```
🌐 إدارة المميزات العامة

🎛️ يمكنك تفعيل أو تعطيل المميزات على مستوى النظام بالكامل.

⚠️ تنبيه: عند تعطيل ميزة، ستصبح غير متاحة لجميع المستخدمين 
حتى لو كانوا يملكون الدعوات المطلوبة.

📋 حالة المميزات الحالية:
✅ 📺 قنوات غير محدودة
❌ 🔗 اختصار الروابط
✅ 🔗 روابط تحميل مخصصة
✅ ⏰ أوقات نشر موسعة
❌ 📋 نظام المهام
✅ 🎨 تخصيص صفحات VIP
✅ 💰 نظام الإعلانات

[🚫 تعطيل قنوات غير محدودة]
[✅ تفعيل اختصار الروابط]
[🚫 تعطيل روابط تحميل مخصصة]
[🚫 تعطيل أوقات نشر موسعة]
[✅ تفعيل نظام المهام]
[🚫 تعطيل تخصيص صفحات VIP]
[🚫 تعطيل نظام الإعلانات]

[🔄 تحديث العرض]
[✅ تفعيل جميع المميزات] [🚫 تعطيل جميع المميزات]
[🔙 العودة للوحة التحكم]
```

## ⚙️ آلية العمل

### 1. التحقق من الوصول للمميزات
```python
async def check_feature_access(user_id, feature_type, context=None):
    # 1. التحقق من صلاحيات الأدمن
    if is_admin_user(user_id):
        return True
    
    # 2. التحقق من تفعيل الميزة على مستوى النظام
    if not is_global_feature_enabled(global_feature_name):
        return False
    
    # 3. التحقق من متطلبات المستخدم (دعوات/اشتراكات)
    # ... باقي المنطق
```

### 2. حفظ الإعدادات
```python
def set_global_feature_status(feature_name, enabled):
    settings = load_admin_settings()
    settings["global_features_enabled"][feature_name] = enabled
    save_admin_settings(settings)
    logger.info(f"Global feature '{feature_name}' set to {enabled}")
```

## 🔄 سيناريوهات الاستخدام

### 1. صيانة مؤقتة
```
الهدف: تعطيل ميزة معينة للصيانة
الإجراء: تعطيل الميزة → إجراء الصيانة → إعادة التفعيل
النتيجة: المستخدمون لا يمكنهم الوصول للميزة مؤقتاً
```

### 2. مناسبة خاصة
```
الهدف: تفعيل جميع المميزات للجميع في مناسبة
الإجراء: تفعيل جميع المميزات → انتهاء المناسبة → إعادة التعطيل
النتيجة: جميع المستخدمين يحصلون على وصول مؤقت
```

### 3. اختبار ميزة جديدة
```
الهدف: اختبار ميزة جديدة مع مجموعة محدودة
الإجراء: تعطيل الميزة عامة → تفعيلها لمستخدمين محددين
النتيجة: تحكم دقيق في من يمكنه الوصول للميزة
```

## 🛡️ الأمان والتحكم

- **صلاحيات الأدمن فقط:** جميع دوال الإدارة محمية بفحص صلاحيات الأدمن
- **تسجيل العمليات:** جميع التغييرات يتم تسجيلها في ملف السجل
- **حفظ آمن:** الإعدادات محفوظة في ملف JSON منفصل
- **استعادة الإعدادات:** إمكانية استعادة الإعدادات الافتراضية

## 📁 الملفات المحدثة

### `main.py`
- **توسيع دوال الإعدادات:** `load_admin_settings()`, `save_admin_settings()`
- **دوال إدارة المميزات العامة:** 6 دوال جديدة
- **تحديث فحص الوصول:** `check_feature_access()`
- **واجهة المستخدم:** 4 دوال جديدة للواجهة
- **معالجات الأحداث:** 4 معالجات جديدة

### `admin_settings.json`
```json
{
  "admin_preview_required": false,
  "global_features_enabled": {
    "unlimited_channels": true,
    "url_shortener_access": true,
    "custom_download_links": true,
    "publish_intervals_extended": true,
    "tasks_system_access": true,
    "page_customization_vip": true,
    "ads_system_access": true
  }
}
```

## 🧪 الاختبار

تم إنشاء سكريبت اختبار شامل `test_global_features_management.py` يتحقق من:

1. ✅ وجود جميع الدوال الجديدة
2. ✅ صحة بنية إعدادات الأدمن المحدثة
3. ✅ عمل دوال إدارة المميزات العامة
4. ✅ تكامل فحص الوصول مع الإعدادات العامة
5. ✅ تكامل الميزة مع لوحة الأدمن
6. ✅ معالجات الأحداث
7. ✅ اختبار الدوال مع mock objects

## 🎯 الفوائد

### للأدمن:
- **تحكم كامل** في توفر المميزات
- **مرونة في الإدارة** بدون تعديل الكود
- **إدارة سريعة** للمناسبات والصيانة
- **تتبع دقيق** لجميع التغييرات

### للنظام:
- **استقرار عالي** مع معالجة شاملة للأخطاء
- **أداء محسن** مع فحص سريع للإعدادات
- **قابلية التوسع** لإضافة مميزات جديدة
- **أمان محكم** مع حماية الصلاحيات

## 🔄 للاستخدام

1. **تشغيل البوت** بالطريقة العادية
2. **الوصول للوحة الأدمن** بإرسال `/admin`
3. **اختيار إدارة المميزات العامة** من القائمة
4. **إدارة المميزات** حسب الحاجة
5. **التغييرات تطبق فوراً** على جميع المستخدمين

الميزة جاهزة للاستخدام الفوري! 🚀
