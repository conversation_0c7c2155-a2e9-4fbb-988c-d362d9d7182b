@echo off
chcp 65001 >nul
title بوت نشر مودات ماين كرافت - الإصدار النهائي

echo.
echo ========================================
echo 🤖 بوت نشر مودات ماين كرافت
echo 🚀 الإصدار النهائي مع جميع الإصلاحات
echo ========================================
echo.

echo 🔍 فحص النظام...

:: فحص Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo.
    echo 🔧 يرجى تثبيت Python من:
    echo    https://python.org/downloads
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر

:: فحص الملفات المطلوبة
if not exist "main.py" (
    echo ❌ ملف main.py غير موجود
    pause
    exit /b 1
)

if not exist ".env" (
    echo ❌ ملف .env غير موجود
    echo.
    echo 🔧 يرجى إنشاء ملف .env مع الإعدادات التالية:
    echo    BOT_TOKEN=your_bot_token_here
    echo    ADMIN_CHAT_ID=your_admin_id_here
    echo    SUPABASE_URL=your_supabase_url_here
    echo    SUPABASE_KEY=your_supabase_key_here
    echo.
    pause
    exit /b 1
)

echo ✅ الملفات الأساسية موجودة

:: فحص اتصال الإنترنت
echo.
echo 🌐 فحص الاتصال بالإنترنت...
ping -n 1 8.8.8.8 >nul 2>&1
if errorlevel 1 (
    echo ❌ لا يوجد اتصال بالإنترنت
    echo.
    echo 🔧 يرجى التحقق من:
    echo    - الاتصال بالإنترنت
    echo    - إعدادات الجدار الناري
    echo    - إعدادات البروكسي
    echo.
    pause
    exit /b 1
)

echo ✅ الاتصال بالإنترنت متاح

:: تشغيل الإصلاحات النهائية
echo.
echo 🔧 تطبيق الإصلاحات النهائية...
if exist "final_connection_fix.py" (
    python final_connection_fix.py
    if errorlevel 1 (
        echo ⚠️ بعض الإصلاحات فشلت، لكن سنحاول المتابعة...
    ) else (
        echo ✅ تم تطبيق جميع الإصلاحات بنجاح
    )
) else (
    echo ℹ️ ملف الإصلاحات غير موجود، المتابعة...
)

:: تثبيت المتطلبات
echo.
echo 📦 تثبيت/تحديث المتطلبات...
if exist "requirements.txt" (
    python -m pip install --upgrade pip >nul 2>&1
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo ⚠️ مشاكل في تثبيت بعض المتطلبات، لكن سنحاول المتابعة...
    ) else (
        echo ✅ تم تثبيت المتطلبات بنجاح
    )
) else (
    echo ⚠️ ملف requirements.txt غير موجود
)

:: اختبار سريع للاتصال
echo.
echo 🧪 اختبار سريع للاتصال...
if exist "test_connection.py" (
    python test_connection.py
) else (
    echo ℹ️ ملف اختبار الاتصال غير موجود
)

echo.
echo ========================================
echo 🚀 بدء تشغيل البوت...
echo ⏹️ اضغط Ctrl+C لإيقاف البوت
echo ========================================
echo.

:: تشغيل البوت بالترتيب المفضل
if exist "start_bot_final.py" (
    echo 📱 استخدام start_bot_final.py...
    python start_bot_final.py
) else if exist "run_bot_fixed.py" (
    echo 📱 استخدام run_bot_fixed.py...
    python run_bot_fixed.py
) else (
    echo 📱 استخدام main.py...
    python main.py
)

:: معالجة النتيجة
if errorlevel 1 (
    echo.
    echo ❌ البوت توقف بخطأ
    echo.
    echo 🔧 نصائح لحل المشكلة:
    echo    1. تأكد من صحة إعدادات .env
    echo    2. تأكد من استقرار الاتصال بالإنترنت
    echo    3. جرب إعادة تشغيل الكمبيوتر
    echo    4. جرب تشغيل البوت كمسؤول
    echo    5. راجع ملف TROUBLESHOOTING.md
    echo.
) else (
    echo.
    echo ✅ البوت توقف بأمان
    echo.
)

pause
