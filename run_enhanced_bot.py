#!/usr/bin/env python3
"""
تشغيل البوت المحسن مع Telegram Web App
Enhanced Bot Runner with Telegram Web App Support

هذا الملف يقوم بتشغيل البوت مع النظام المحسن لعرض تفاصيل المودات
This file runs the bot with the enhanced mod details display system
"""

import os
import sys
import logging
import asyncio
import threading
import time
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# إعداد التسجيل المحسن
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot_enhanced.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    logger.info("🔍 Checking requirements...")
    
    required_packages = [
        'telegram',
        'flask',
        'flask_cors',
        'requests',
        'httpx'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            logger.error(f"❌ {package} is missing")
    
    if missing_packages:
        logger.error(f"Missing packages: {missing_packages}")
        logger.info("Please run: pip install -r requirements.txt")
        return False
    
    return True

def check_environment():
    """التحقق من متغيرات البيئة"""
    logger.info("🔍 Checking environment variables...")
    
    required_env_vars = {
        'BOT_TOKEN': 'Telegram Bot Token',
        'ADMIN_CHAT_ID': 'Admin Telegram ID'
    }
    
    optional_env_vars = {
        'WEB_SERVER_URL': 'Web Server URL (defaults to localhost)',
        'FLASK_PORT': 'Flask Server Port (defaults to 5000)',
        'TELEGRAM_WEB_APP_PORT': 'Telegram Web App Port (defaults to 5001)',
        'SUPABASE_URL': 'Supabase Database URL',
        'SUPABASE_KEY': 'Supabase API Key'
    }
    
    missing_required = []
    
    # التحقق من المتغيرات المطلوبة
    for var, description in required_env_vars.items():
        if not os.environ.get(var):
            missing_required.append(f"{var} ({description})")
            logger.error(f"❌ Missing required environment variable: {var}")
        else:
            logger.info(f"✅ {var} is set")
    
    # عرض المتغيرات الاختيارية
    for var, description in optional_env_vars.items():
        if os.environ.get(var):
            logger.info(f"✅ {var} is set")
        else:
            logger.info(f"ℹ️  {var} not set ({description})")
    
    if missing_required:
        logger.error("Missing required environment variables:")
        for var in missing_required:
            logger.error(f"  - {var}")
        return False
    
    return True

def setup_default_environment():
    """إعداد متغيرات البيئة الافتراضية"""
    defaults = {
        'WEB_SERVER_URL': 'http://127.0.0.1:5001',
        'FLASK_PORT': '5000',
        'TELEGRAM_WEB_APP_PORT': '5001'
    }
    
    for key, value in defaults.items():
        if not os.environ.get(key):
            os.environ[key] = value
            logger.info(f"🔧 Set default {key}={value}")

def start_web_servers():
    """تشغيل خوادم الويب"""
    logger.info("🚀 Starting web servers...")
    
    try:
        # تشغيل الخادم الأساسي
        from web_server import run_web_server
        flask_port = int(os.environ.get('FLASK_PORT', 5000))
        
        flask_thread = threading.Thread(
            target=run_web_server,
            args=(flask_port,),
            daemon=True,
            name="FlaskServer"
        )
        flask_thread.start()
        logger.info(f"✅ Flask server started on port {flask_port}")
        
        # تشغيل خادم Telegram Web App
        from telegram_web_app import run_telegram_web_app
        web_app_port = int(os.environ.get('TELEGRAM_WEB_APP_PORT', 5001))
        
        web_app_thread = threading.Thread(
            target=run_telegram_web_app,
            args=(web_app_port,),
            daemon=True,
            name="TelegramWebApp"
        )
        web_app_thread.start()
        logger.info(f"✅ Telegram Web App server started on port {web_app_port}")
        
        # انتظار قصير للتأكد من تشغيل الخوادم
        time.sleep(2)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to start web servers: {e}")
        return False

def test_web_servers():
    """اختبار خوادم الويب"""
    logger.info("🧪 Testing web servers...")
    
    import requests
    
    # اختبار الخادم الأساسي
    try:
        flask_port = os.environ.get('FLASK_PORT', 5000)
        response = requests.get(f"http://localhost:{flask_port}/", timeout=5)
        if response.status_code == 200:
            logger.info("✅ Flask server is responding")
        else:
            logger.warning(f"⚠️ Flask server returned status {response.status_code}")
    except Exception as e:
        logger.error(f"❌ Flask server test failed: {e}")
    
    # اختبار خادم Telegram Web App
    try:
        web_app_port = os.environ.get('TELEGRAM_WEB_APP_PORT', 5001)
        response = requests.get(f"http://localhost:{web_app_port}/telegram-mod-details?id=1&lang=ar", timeout=5)
        if response.status_code in [200, 404]:  # 404 مقبول إذا لم يكن هناك مود بـ ID=1
            logger.info("✅ Telegram Web App server is responding")
        else:
            logger.warning(f"⚠️ Telegram Web App server returned status {response.status_code}")
    except Exception as e:
        logger.error(f"❌ Telegram Web App server test failed: {e}")

def start_telegram_bot():
    """تشغيل بوت تيليجرام"""
    logger.info("🤖 Starting Telegram bot...")
    
    try:
        # استيراد وتشغيل البوت الرئيسي
        import main
        
        # تشغيل البوت (هذا سيكون blocking)
        logger.info("✅ Telegram bot started successfully")
        
    except KeyboardInterrupt:
        logger.info("🛑 Bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Failed to start Telegram bot: {e}")
        raise

def display_startup_info():
    """عرض معلومات بدء التشغيل"""
    web_server_url = os.environ.get('WEB_SERVER_URL', 'http://127.0.0.1:5001')
    flask_port = os.environ.get('FLASK_PORT', 5000)
    web_app_port = os.environ.get('TELEGRAM_WEB_APP_PORT', 5001)
    
    print("\n" + "="*60)
    print("🎮 MINECRAFT MODS BOT - ENHANCED VERSION")
    print("="*60)
    print(f"📱 Telegram Web App: {web_server_url}")
    print(f"🌐 Flask Server: http://localhost:{flask_port}")
    print(f"🔧 Web App Server: http://localhost:{web_app_port}")
    print(f"👨‍💻 Developer: @Kim880198")
    print("="*60)
    print("✨ Features:")
    print("  • Enhanced mod details display")
    print("  • Multi-device support")
    print("  • Arabic & English support")
    print("  • Telegram Web App integration")
    print("  • Advanced security")
    print("="*60)
    print("🚀 Bot is running... Press Ctrl+C to stop")
    print("="*60 + "\n")

def main():
    """الدالة الرئيسية"""
    try:
        logger.info("🎮 Starting Enhanced Minecraft Mods Bot...")
        
        # التحقق من المتطلبات
        if not check_requirements():
            logger.error("❌ Requirements check failed")
            sys.exit(1)
        
        # التحقق من متغيرات البيئة
        if not check_environment():
            logger.error("❌ Environment check failed")
            sys.exit(1)
        
        # إعداد المتغيرات الافتراضية
        setup_default_environment()
        
        # تشغيل خوادم الويب
        if not start_web_servers():
            logger.error("❌ Failed to start web servers")
            sys.exit(1)
        
        # اختبار خوادم الويب
        test_web_servers()
        
        # عرض معلومات بدء التشغيل
        display_startup_info()
        
        # تشغيل بوت تيليجرام
        start_telegram_bot()
        
    except KeyboardInterrupt:
        logger.info("🛑 Shutting down...")
    except Exception as e:
        logger.error(f"💥 Critical error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
