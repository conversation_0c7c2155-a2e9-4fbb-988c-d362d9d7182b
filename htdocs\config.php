<?php
/**
 * ملف الإعدادات الرئيسي
 * Main Configuration File
 */

// منع الوصول المباشر
if (!defined('INCLUDED')) {
    die('Direct access not allowed');
}

// إعدادات قاعدة البيانات Supabase
define('SUPABASE_URL', 'https://ytqxxodyecdeosnqoure.supabase.co');
define('SUPABASE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4');
define('SUPABASE_SERVICE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTI2MTEwNSwiZXhwIjoyMDYwODM3MTA1fQ._BQpMA9YZpXCjvpoNRK2QdoecsE5VQsr3AN2DJhj2rw'); // تم التحديث بالمفتاح الفعلي

// إعدادات الموقع
define('SITE_NAME', 'Modetaris');
define('SITE_DESCRIPTION', 'موقع مودات ماين كرافت');
define('SITE_KEYWORDS', 'minecraft, mods, addons, texture packs, maps');
define('SITE_AUTHOR', 'Modetaris Team');

// إعدادات اللغة
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);

// إعدادات التحميل
define('DEFAULT_FILE_SIZE_MB', 25.5);
define('MAX_DOWNLOAD_SPEED_MBPS', 7);
define('MIN_DOWNLOAD_SPEED_MBPS', 2);

// إعدادات الإعلانات
define('DEFAULT_AD_DELAY', 5);
define('DEFAULT_AD_DISPLAY_MODE', 'on_download');

// إعدادات الأمان
define('ENABLE_RATE_LIMITING', true);
define('MAX_REQUESTS_PER_MINUTE', 60);
define('ENABLE_CSRF_PROTECTION', true);

// إعدادات التخزين المؤقت
define('ENABLE_CACHING', true);
define('CACHE_DURATION', 3600); // ساعة واحدة

// إعدادات السجلات
define('ENABLE_LOGGING', true);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_FILE', 'logs/app.log');

// إعدادات الأداء
define('ENABLE_COMPRESSION', true);
define('ENABLE_MINIFICATION', false);

// إعدادات قاعدة البيانات
$database_config = [
    'supabase' => [
        'url' => SUPABASE_URL,
        'key' => SUPABASE_KEY,
        'service_key' => SUPABASE_SERVICE_KEY, // تأكد من إضافة هذا السطر
        'timeout' => 30,
        'retry_attempts' => 3
    ]
];

// إعدادات الجداول - تم تصحيح اسم جدول المودات
$table_config = [
    'mods' => 'mods', // تم تصحيحه من 'minemods' إلى 'mods'
    'ads_settings' => 'user_ads_settings',
    'tasks' => 'available_tasks',
    'completed_tasks' => 'user_task_completions',
    'ad_clicks' => 'ad_clicks',
    'custom_download_links' => 'custom_download_links',
    'url_shortener_settings' => 'user_url_shortener_settings'
];

// إعدادات API
$api_config = [
    'version' => '1.0',
    'rate_limit' => [
        'enabled' => ENABLE_RATE_LIMITING,
        'max_requests' => MAX_REQUESTS_PER_MINUTE,
        'window' => 60 // ثانية
    ],
    'cors' => [
        'enabled' => true,
        'origins' => ['*'],
        'methods' => ['GET', 'POST', 'OPTIONS'],
        'headers' => ['Content-Type', 'Authorization']
    ]
];

// إعدادات الأخطاء
$error_config = [
    'display_errors' => false,
    'log_errors' => true,
    'error_reporting' => E_ALL & ~E_NOTICE,
    'custom_error_pages' => [
        404 => '404.html',
        500 => '500.html'
    ]
];

// إعدادات الأمان
$security_config = [
    'csrf_protection' => ENABLE_CSRF_PROTECTION,
    'xss_protection' => true,
    'content_type_nosniff' => true,
    'frame_options' => 'DENY',
    'https_only' => false, // تغيير إلى true عند استخدام HTTPS
    'secure_headers' => [
        'X-Content-Type-Options' => 'nosniff',
        'X-Frame-Options' => 'DENY',
        'X-XSS-Protection' => '1; mode=block',
        'Referrer-Policy' => 'strict-origin-when-cross-origin'
    ]
];

// إعدادات التخزين المؤقت
$cache_config = [
    'enabled' => ENABLE_CACHING,
    'duration' => CACHE_DURATION,
    'types' => [
        'mod_data' => 1800, // 30 دقيقة
        'user_settings' => 3600, // ساعة واحدة
        'tasks' => 7200 // ساعتان
    ]
];

// دالة الحصول على الإعدادات
function getConfig($section = null) {
    global $database_config, $table_config, $api_config, $error_config, $security_config, $cache_config;
    
    $config = [
        'database' => $database_config,
        'tables' => $table_config,
        'api' => $api_config,
        'errors' => $error_config,
        'security' => $security_config,
        'cache' => $cache_config
    ];
    
    if ($section) {
        return $config[$section] ?? null;
    }
    
    return $config;
}

// دالة تطبيق إعدادات الأمان
function applySecurityHeaders() {
    $security = getConfig('security');
    
    if ($security['secure_headers']) {
        foreach ($security['secure_headers'] as $header => $value) {
            header("$header: $value");
        }
    }
}

// دالة تطبيق إعدادات PHP
function applyPhpSettings() {
    $errors = getConfig('errors');
    
    ini_set('display_errors', $errors['display_errors'] ? 1 : 0);
    ini_set('log_errors', $errors['log_errors'] ? 1 : 0);
    error_reporting($errors['error_reporting']);
    
    // إعدادات أخرى
    ini_set('max_execution_time', 300);
    ini_set('memory_limit', '128M');
    ini_set('upload_max_filesize', '50M');
    ini_set('post_max_size', '50M');
}

// دالة التحقق من البيئة
function checkEnvironment() {
    $requirements = [
        'php_version' => '7.4.0',
        'extensions' => ['curl', 'json', 'mbstring'],
        'functions' => ['file_get_contents', 'json_encode', 'json_decode']
    ];
    
    $errors = [];
    
    // فحص إصدار PHP
    if (version_compare(PHP_VERSION, $requirements['php_version'], '<')) {
        $errors[] = "PHP version {$requirements['php_version']} or higher required";
    }
    
    // فحص الإضافات
    foreach ($requirements['extensions'] as $extension) {
        if (!extension_loaded($extension)) {
            $errors[] = "PHP extension '$extension' is required";
        }
    }
    
    // فحص الدوال
    foreach ($requirements['functions'] as $function) {
        if (!function_exists($function)) {
            $errors[] = "PHP function '$function' is required";
        }
    }
    
    return empty($errors) ? true : $errors;
}

// دالة إنشاء مجلدات السجلات
function createLogDirectories() {
    $log_dir = dirname(LOG_FILE);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
}

// دالة تسجيل الأخطاء
function logError($message, $level = 'ERROR') {
    if (!ENABLE_LOGGING) return;
    
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] [$level] $message" . PHP_EOL;
    
    createLogDirectories();
    file_put_contents(LOG_FILE, $log_entry, FILE_APPEND | LOCK_EX);
}

// دالة تسجيل المعلومات
function logInfo($message) {
    logError($message, 'INFO');
}

// دالة تسجيل التحذيرات
function logWarning($message) {
    logError($message, 'WARNING');
}

// تطبيق الإعدادات عند تحميل الملف
if (!defined('SKIP_AUTO_CONFIG')) {
    applyPhpSettings();
    applySecurityHeaders();
    
    // فحص البيئة
    $env_check = checkEnvironment();
    if ($env_check !== true) {
        foreach ($env_check as $error) {
            logError($error);
        }
    }
}

// إعدادات إضافية للتطوير
if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
    define('LOG_LEVEL', 'DEBUG');
}
?>
