# إصلاح مشكلة Query is too old and response timeout expired

## المشكلة الأصلية

كانت تحدث مشكلة في دالة `user_page_customization_menu` والدوال المشابهة:

```
Query is too old and response timeout expired or query id is invalid        
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\bot telegram\main.py", line 3544, in user_page_customization_menu
    await query.answer()
```

## السبب

المشكلة تحدث عندما يكون callback query قديماً (أكثر من 30 ثانية) أو منتهي الصلاحية، مما يسبب خطأ عند محاولة الرد عليه باستخدام `await query.answer()`.

## الحل المطبق

تم استبدال جميع استدعاءات `await query.answer()` بـ `await safe_answer_callback_query(query)` في الدوال التالية:

### دوال نظام تخصيص الصفحات المُصلحة:

1. **user_page_customization_menu** - القائمة الرئيسية لتخصيص الصفحة
2. **user_customize_settings** - قائمة تخصيص الإعدادات
3. **user_preview_page** - معاينة الصفحة المخصصة
4. **user_reset_customization** - إعادة تعيين التخصيص
5. **user_confirm_reset** - تأكيد إعادة التعيين
6. **user_set_site_name** - تعيين اسم الموقع
7. **user_image_settings** - إعدادات عرض الصور
8. **user_button_texts** - تخصيص نصوص الأزرار
9. **user_mod_opening** - إعدادات فتح المود
10. **user_channel_logo** - إعدادات صورة القناة
11. **user_page_theme** - إعدادات ألوان الصفحة

### دوال إعدادات صورة القناة المُصلحة:

12. **user_upload_logo** - رفع صورة القناة
13. **user_logo_position_right** - موضع اللوجو على اليمين
14. **user_logo_position_left** - موضع اللوجو على اليسار
15. **user_remove_logo** - إزالة صورة القناة

### دوال إعدادات الثيمات المُصلحة:

16. **user_theme_default** - الثيم الافتراضي
17. **user_theme_telegram** - ثيم تيليجرام
18. **user_theme_dark** - الثيم الداكن
19. **user_theme_light** - الثيم الفاتح
20. **user_theme_custom** - الألوان المخصصة

### دوال إعدادات الصور المُصلحة:

21. **user_set_images_all** - عرض جميع الصور
22. **user_set_images_main** - عرض الصورة الرئيسية فقط

### دوال نصوص الأزرار المُصلحة:

23. **user_set_download_text** - نص زر التحميل
24. **user_set_open_text** - نص زر الفتح

### دوال إعدادات فتح المود المُصلحة:

25. **user_set_opening_true** - تفعيل فتح المود
26. **user_set_opening_false** - إيقاف فتح المود

## دالة safe_answer_callback_query

الدالة الموجودة بالفعل في الكود تتعامل مع timeout errors بشكل آمن:

```python
async def safe_answer_callback_query(query, text=None, show_alert=False):
    """معالجة آمنة لـ callback query مع تجنب timeout errors"""
    try:
        await query.answer(text=text, show_alert=show_alert)
        return True
    except Exception as e:
        error_msg = str(e).lower()
        if any(keyword in error_msg for keyword in ["query is too old", "timeout expired", "query id is invalid"]):
            logger.warning(f"Callback query timeout/expired for query {query.id}: {e}")
            return False  # فشل بسبب timeout ولكن ليس خطأ خطير
        else:
            logger.error(f"Error answering callback query {query.id}: {e}")
            return False
```

## النتيجة

- ✅ لن تتوقف دوال تخصيص الصفحات بسبب callback query timeout
- ✅ سيتم تسجيل تحذيرات بدلاً من أخطاء فادحة
- ✅ ستستمر العمليات حتى مع وجود timeout
- ✅ تحسين تجربة المستخدم وعدم انقطاع الخدمة

## ملاحظات

- تم إصلاح 26 دالة في نظام تخصيص الصفحات
- لا تزال هناك دوال أخرى في البوت تحتاج نفس الإصلاح
- يُنصح بتطبيق نفس الإصلاح على جميع دوال callback query في البوت

## اختبار الإصلاح

لاختبار الإصلاح:
1. شغل البوت
2. اذهب لقائمة تخصيص الصفحة
3. انتظر أكثر من 30 ثانية
4. اضغط على أي زر
5. يجب أن يعمل البوت بدون أخطاء

تم الإصلاح بنجاح! 🎉
