# دليل التثبيت - صفحة عرض مودات ماين كرافت

## 🎯 نظرة عامة

هذا الدليل يوضح كيفية رفع ونشر صفحة عرض مودات ماين كرافت على استضافة InfinityFree المجانية.

## 📋 المتطلبات

### متطلبات الاستضافة
- ✅ استضافة تدعم PHP 7.4+
- ✅ دعم cURL
- ✅ دعم ملفات .htaccess
- ✅ مساحة تخزين 100MB على الأقل

### متطلبات قاعدة البيانات
- ✅ حساب Supabase نشط
- ✅ قاعدة بيانات تحتوي على جدول `minemods`
- ✅ مفاتيح API صحيحة

## 🚀 خطوات التثبيت

### الخطوة 1: إعداد الاستضافة

1. **إنشاء حساب InfinityFree**
   ```
   - اذه<PERSON> إلى https://www.infinityfree.com
   - سجل حساب جديد
   - أنشئ موقع جديد
   - احصل على تفاصيل FTP
   ```

2. **الحصول على تفاصيل FTP**
   ```
   - اسم الخادم: ftpupload.net
   - اسم المستخدم: epiz_xxxxx
   - كلمة المرور: [كلمة المرور الخاصة بك]
   - المنفذ: 21
   ```

### الخطوة 2: رفع الملفات

1. **استخدام FTP Client**
   ```bash
   # باستخدام FileZilla أو أي برنامج FTP آخر
   # ارفع جميع الملفات إلى مجلد htdocs
   ```

2. **الملفات المطلوبة**
   ```
   ✅ index.php          - الصفحة الرئيسية
   ✅ api.php           - واجهة برمجة التطبيقات
   ✅ config.php        - ملف الإعدادات
   ✅ style.css         - ملف التصميم
   ✅ script.js         - ملف الجافاسكريبت
   ✅ .htaccess         - إعدادات الخادم
   ✅ 404.html          - صفحة خطأ 404
   ✅ 500.html          - صفحة خطأ 500
   ✅ robots.txt        - ملف محركات البحث
   ✅ sitemap.xml       - خريطة الموقع
   ```

### الخطوة 3: تحديث الإعدادات

1. **تحديث بيانات Supabase**
   ```php
   // في ملف config.php
   define('SUPABASE_URL', 'رابط_قاعدة_البيانات_الخاصة_بك');
   define('SUPABASE_KEY', 'مفتاح_API_الخاص_بك');
   ```

2. **تحديث إعدادات الأمان**
   ```php
   // في ملف logs.php و clear_log.php
   $required_key = 'كلمة_مرور_قوية_جديدة';
   ```

### الخطوة 4: اختبار التثبيت

1. **تشغيل صفحة الإعداد**
   ```
   https://your-domain.com/deploy.php?setup=true
   ```

2. **فحص الوظائف الأساسية**
   ```
   ✅ الاتصال بقاعدة البيانات
   ✅ عرض صفحة المود
   ✅ تحميل الصور
   ✅ عمل API
   ```

### الخطوة 5: تكامل مع البوت

1. **تحديث رابط الموقع في البوت**
   ```python
   # في ملف main.py
   WEB_SERVER_URL = "https://your-domain.com"
   ```

2. **اختبار الروابط**
   ```
   https://your-domain.com/?id=1&lang=ar&user_id=123&channel=456
   ```

## 🔧 إعدادات متقدمة

### تخصيص التصميم

1. **تغيير الألوان**
   ```css
   /* في ملف style.css */
   :root {
       --primary-color: #FFA500;
       --secondary-color: #FFD700;
   }
   ```

2. **تغيير الخطوط**
   ```css
   body {
       font-family: 'Arial', sans-serif;
   }
   ```

### إعدادات الأداء

1. **تفعيل الضغط**
   ```apache
   # في ملف .htaccess
   <IfModule mod_deflate.c>
       AddOutputFilterByType DEFLATE text/css
       AddOutputFilterByType DEFLATE application/javascript
   </IfModule>
   ```

2. **تحسين التخزين المؤقت**
   ```apache
   <IfModule mod_expires.c>
       ExpiresActive On
       ExpiresByType text/css "access plus 1 month"
   </IfModule>
   ```

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **الصفحة لا تظهر**
   ```
   ❌ المشكلة: خطأ 500
   ✅ الحل: تحقق من أخطاء PHP في لوحة التحكم
   ```

2. **بيانات المود لا تظهر**
   ```
   ❌ المشكلة: صفحة فارغة
   ✅ الحل: تحقق من إعدادات Supabase
   ```

3. **الصور لا تظهر**
   ```
   ❌ المشكلة: صور مكسورة
   ✅ الحل: تحقق من روابط الصور في قاعدة البيانات
   ```

### فحص السجلات

1. **عرض السجلات**
   ```
   https://your-domain.com/logs.php?key=admin123
   ```

2. **مسح السجلات**
   ```
   استخدم زر "مسح السجل" في صفحة السجلات
   ```

## 🔒 الأمان

### إعدادات الأمان المطلوبة

1. **تغيير كلمات المرور**
   ```php
   // في logs.php
   $required_key = 'كلمة_مرور_قوية_جديدة';
   ```

2. **حماية الملفات الحساسة**
   ```apache
   # في .htaccess
   <Files "config.php">
       Order allow,deny
       Deny from all
   </Files>
   ```

3. **تفعيل HTTPS**
   ```apache
   # إضافة في .htaccess
   RewriteCond %{HTTPS} off
   RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
   ```

## 📊 المراقبة والصيانة

### مراقبة الأداء

1. **فحص دوري للموقع**
   ```
   - تحقق من سرعة التحميل
   - راقب استخدام الموارد
   - تحقق من السجلات
   ```

2. **نسخ احتياطية**
   ```
   - احتفظ بنسخة من الملفات
   - احتفظ بنسخة من قاعدة البيانات
   ```

### التحديثات

1. **تحديث الملفات**
   ```
   - راجع التحديثات الجديدة
   - اختبر التحديثات في بيئة تطوير
   - طبق التحديثات على الموقع الرئيسي
   ```

## 📞 الدعم

### الحصول على المساعدة

1. **مراجعة الوثائق**
   - README.md
   - هذا الملف
   - تعليقات الكود

2. **فحص السجلات**
   - سجلات الخادم
   - سجلات PHP
   - سجلات التطبيق

3. **اختبار المكونات**
   - قاعدة البيانات
   - API
   - الملفات

## ✅ قائمة التحقق النهائية

```
□ تم رفع جميع الملفات
□ تم تحديث إعدادات Supabase
□ تم تغيير كلمات المرور
□ تم اختبار الموقع
□ تم تحديث البوت
□ تم فحص السجلات
□ تم إعداد النسخ الاحتياطية
```

---

**تهانينا! 🎉**

تم تثبيت صفحة عرض مودات ماين كرافت بنجاح. الموقع الآن جاهز للاستخدام مع البوت.

**روابط مهمة:**
- الموقع الرئيسي: `https://your-domain.com`
- صفحة الإعداد: `https://your-domain.com/deploy.php?setup=true`
- عارض السجلات: `https://your-domain.com/logs.php?key=admin123`
