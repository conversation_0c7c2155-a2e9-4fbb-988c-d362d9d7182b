#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح خادم الويب وصفحات تفاصيل المودات
"""

import requests
import json
import time
from datetime import datetime

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:5001"
TEST_MOD_ID = "4e41e396-43b1-4f61-875d-9d3365a6083b"  # المود الذي كان يعطي خطأ
TEST_USER_ID = "7513880877"
TEST_CHANNEL_ID = "-1002433545184"

def test_web_server_status():
    """اختبار حالة الخادم الأساسية"""
    print("🔍 اختبار حالة الخادم...")
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ الخادم يعمل بشكل طبيعي")
            return True
        else:
            print(f"❌ الخادم يعطي خطأ: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ فشل الاتصال بالخادم: {e}")
        return False

def test_api_mod_endpoint():
    """اختبار API endpoint الجديد للمودات"""
    print(f"\n🔍 اختبار API endpoint: /api/mod/{TEST_MOD_ID}")
    try:
        url = f"{BASE_URL}/api/mod/{TEST_MOD_ID}"
        params = {
            'lang': 'ar',
            'user_id': TEST_USER_ID,
            'channel': TEST_CHANNEL_ID
        }
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API endpoint يعمل بشكل طبيعي")
            print(f"   📋 عنوان المود: {data.get('title', 'N/A')}")
            print(f"   🔗 رابط التحميل: {data.get('download_link', 'N/A')[:50]}...")
            print(f"   🖼️ عدد الصور: {len(data.get('image_urls', []))}")
            return True
        elif response.status_code == 404:
            print("❌ المود غير موجود في قاعدة البيانات")
            return False
        else:
            print(f"❌ API endpoint يعطي خطأ: {response.status_code}")
            print(f"   📄 الرد: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ فشل اختبار API endpoint: {e}")
        return False

def test_mod_details_page():
    """اختبار صفحة تفاصيل المود"""
    print(f"\n🔍 اختبار صفحة تفاصيل المود...")
    try:
        url = f"{BASE_URL}/telegram-mod-details"
        params = {
            'id': TEST_MOD_ID,
            'lang': 'ar',
            'user_id': TEST_USER_ID,
            'channel': TEST_CHANNEL_ID
        }
        
        response = requests.get(url, params=params, timeout=15)
        
        if response.status_code == 200:
            html_content = response.text
            
            # فحص وجود عناصر مهمة في الصفحة
            checks = [
                ("عنوان المود", "mod-title" in html_content or "mod_title" in html_content),
                ("صورة المود", "mod-image" in html_content or "main-mod-image" in html_content),
                ("زر التحميل", "download-button" in html_content),
                ("وصف المود", "mod-description" in html_content),
                ("معرف المود", TEST_MOD_ID in html_content)
            ]
            
            all_passed = True
            print("✅ صفحة تفاصيل المود تحمل بنجاح")
            
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name}: موجود")
                else:
                    print(f"   ❌ {check_name}: مفقود")
                    all_passed = False
            
            return all_passed
            
        elif response.status_code == 404:
            print("❌ المود غير موجود")
            return False
        else:
            print(f"❌ صفحة تفاصيل المود تعطي خطأ: {response.status_code}")
            print(f"   📄 الرد: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ فشل اختبار صفحة تفاصيل المود: {e}")
        return False

def test_file_info_endpoint():
    """اختبار endpoint معلومات الملف"""
    print(f"\n🔍 اختبار endpoint معلومات الملف...")
    try:
        url = f"{BASE_URL}/get-file-info"
        params = {'mod_id': TEST_MOD_ID}
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ endpoint معلومات الملف يعمل بشكل طبيعي")
                print(f"   📁 اسم الملف: {data.get('filename', 'N/A')}")
                print(f"   📏 حجم الملف: {data.get('size_mb', 'N/A')} MB")
                print(f"   🔗 رابط التحميل: {data.get('download_url', 'N/A')[:50]}...")
                return True
            else:
                print(f"❌ endpoint معلومات الملف يعطي خطأ: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ endpoint معلومات الملف يعطي خطأ HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ فشل اختبار endpoint معلومات الملف: {e}")
        return False

def test_invalid_mod_id():
    """اختبار معالجة معرف مود غير صالح"""
    print(f"\n🔍 اختبار معالجة معرف مود غير صالح...")
    try:
        invalid_ids = [
            "invalid-id",
            "12345",
            "not-a-uuid",
            ""
        ]
        
        for invalid_id in invalid_ids:
            url = f"{BASE_URL}/api/mod/{invalid_id}" if invalid_id else f"{BASE_URL}/telegram-mod-details"
            params = {'id': invalid_id} if not invalid_id else {}
            
            response = requests.get(url, params=params, timeout=5)
            
            if response.status_code == 400:
                print(f"   ✅ معرف غير صالح '{invalid_id}': تم رفضه بشكل صحيح")
            else:
                print(f"   ❌ معرف غير صالح '{invalid_id}': لم يتم رفضه ({response.status_code})")
                return False
        
        print("✅ معالجة المعرفات غير الصالحة تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ فشل اختبار المعرفات غير الصالحة: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار إصلاحات خادم الويب...")
    print("=" * 60)
    
    tests = [
        ("حالة الخادم", test_web_server_status),
        ("API endpoint للمودات", test_api_mod_endpoint),
        ("صفحة تفاصيل المود", test_mod_details_page),
        ("endpoint معلومات الملف", test_file_info_endpoint),
        ("معالجة المعرفات غير الصالحة", test_invalid_mod_id)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # فترة راحة بين الاختبارات
    
    # عرض النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبارات:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 الإجمالي: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت! الخادم يعمل بشكل مثالي.")
        print("\n💡 يمكنك الآن:")
        print("   1. الضغط على أزرار صفحات المودات في Telegram")
        print("   2. تصفح تفاصيل المودات بدون أخطاء")
        print("   3. تحميل المودات بشكل طبيعي")
    else:
        print(f"\n⚠️ {total - passed} اختبار فشل. راجع الأخطاء أعلاه.")
        print("\n🔧 خطوات الإصلاح:")
        print("   1. تأكد من تشغيل البوت والخادم")
        print("   2. تحقق من اتصال قاعدة البيانات")
        print("   3. راجع سجلات الأخطاء")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = run_all_tests()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم.")
        exit(1)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        exit(1)
