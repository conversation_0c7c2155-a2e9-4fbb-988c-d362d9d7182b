#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات التحسين للاستضافة المجانية
Optimization Configuration for Free Hosting

هذا الملف يحتوي على إعدادات محسنة لتشغيل البوت على الخطط المجانية
This file contains optimized settings for running the bot on free hosting plans
"""

import os
import logging
import gc
import threading
import time
from typing import Dict, Any

# إعدادات التحسين للذاكرة
MEMORY_OPTIMIZATION = {
    'enable_garbage_collection': True,
    'gc_interval_seconds': 300,  # تشغيل garbage collection كل 5 دقائق
    'max_cache_size': 50,  # حد أقصى للعناصر المخزنة مؤقتاً
    'clear_logs_interval_hours': 24,  # مسح السجلات القديمة كل 24 ساعة
    'max_log_file_size_mb': 10,  # حد أقصى لحجم ملف السجل
}

# إعدادات التحسين للمعالج
CPU_OPTIMIZATION = {
    'reduce_polling_frequency': True,
    'telegram_timeout': 30,  # زيادة timeout لتقليل الطلبات
    'max_concurrent_requests': 5,  # حد أقصى للطلبات المتزامنة
    'sleep_between_operations': 0.1,  # توقف قصير بين العمليات
    'batch_processing_size': 10,  # معالجة البيانات في مجموعات صغيرة
}

# إعدادات التحسين للشبكة
NETWORK_OPTIMIZATION = {
    'connection_pool_size': 5,  # حجم مجموعة الاتصالات
    'request_timeout': 15,  # timeout للطلبات
    'retry_attempts': 2,  # عدد محاولات إعادة الطلب
    'cache_responses': True,  # تخزين الاستجابات مؤقتاً
    'compress_responses': True,  # ضغط الاستجابات
}

# إعدادات قاعدة البيانات
DATABASE_OPTIMIZATION = {
    'connection_timeout': 10,
    'max_connections': 3,  # حد أقصى للاتصالات المتزامنة
    'query_timeout': 5,
    'batch_insert_size': 20,  # إدراج البيانات في مجموعات
    'cache_query_results': True,
}

# إعدادات خوادم الويب
WEB_SERVER_OPTIMIZATION = {
    'threaded': True,
    'processes': 1,  # عملية واحدة فقط لتوفير الذاكرة
    'max_content_length': 5 * 1024 * 1024,  # 5MB حد أقصى للمحتوى
    'static_cache_timeout': 3600,  # تخزين الملفات الثابتة لساعة
    'gzip_compression': True,
}

class ResourceOptimizer:
    """مُحسن الموارد للاستضافة المجانية"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.gc_thread = None
        self.log_cleaner_thread = None
        self.running = False
        
    def start_optimization(self):
        """بدء تحسين الموارد"""
        self.running = True
        
        if MEMORY_OPTIMIZATION['enable_garbage_collection']:
            self._start_garbage_collection()
            
        self._start_log_cleaner()
        self._optimize_logging()
        
        self.logger.info("🚀 Resource optimization started for free hosting")
        
    def stop_optimization(self):
        """إيقاف تحسين الموارد"""
        self.running = False
        self.logger.info("🛑 Resource optimization stopped")
        
    def _start_garbage_collection(self):
        """بدء تشغيل garbage collection الدوري"""
        def gc_worker():
            while self.running:
                try:
                    # تشغيل garbage collection
                    collected = gc.collect()
                    if collected > 0:
                        self.logger.debug(f"🧹 Garbage collection: freed {collected} objects")
                    
                    time.sleep(MEMORY_OPTIMIZATION['gc_interval_seconds'])
                except Exception as e:
                    self.logger.error(f"Error in garbage collection: {e}")
                    time.sleep(60)  # انتظار دقيقة في حالة الخطأ
        
        self.gc_thread = threading.Thread(target=gc_worker, daemon=True)
        self.gc_thread.start()
        
    def _start_log_cleaner(self):
        """بدء تنظيف السجلات القديمة"""
        def log_cleaner():
            while self.running:
                try:
                    self._clean_old_logs()
                    # انتظار حسب الفترة المحددة
                    time.sleep(MEMORY_OPTIMIZATION['clear_logs_interval_hours'] * 3600)
                except Exception as e:
                    self.logger.error(f"Error in log cleaner: {e}")
                    time.sleep(3600)  # انتظار ساعة في حالة الخطأ
        
        self.log_cleaner_thread = threading.Thread(target=log_cleaner, daemon=True)
        self.log_cleaner_thread.start()
        
    def _clean_old_logs(self):
        """تنظيف السجلات القديمة"""
        try:
            log_files = ['bot_enhanced.log', 'security.log']
            max_size = MEMORY_OPTIMIZATION['max_log_file_size_mb'] * 1024 * 1024
            
            for log_file in log_files:
                if os.path.exists(log_file):
                    file_size = os.path.getsize(log_file)
                    if file_size > max_size:
                        # قراءة آخر نصف الملف والاحتفاظ به
                        with open(log_file, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                        
                        # الاحتفاظ بآخر نصف السطور
                        keep_lines = lines[len(lines)//2:]
                        
                        with open(log_file, 'w', encoding='utf-8') as f:
                            f.writelines(keep_lines)
                        
                        self.logger.info(f"🧹 Cleaned log file: {log_file}")
                        
        except Exception as e:
            self.logger.error(f"Error cleaning logs: {e}")
            
    def _optimize_logging(self):
        """تحسين إعدادات التسجيل"""
        # تقليل مستوى التسجيل للمكتبات الخارجية
        logging.getLogger("httpx").setLevel(logging.WARNING)
        logging.getLogger("telegram").setLevel(logging.WARNING)
        logging.getLogger("urllib3").setLevel(logging.WARNING)
        logging.getLogger("requests").setLevel(logging.WARNING)
        
    def get_memory_usage(self) -> Dict[str, Any]:
        """الحصول على معلومات استخدام الذاكرة"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
                'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
                'percent': process.memory_percent(),
                'available_mb': psutil.virtual_memory().available / 1024 / 1024
            }
        except ImportError:
            return {'error': 'psutil not available'}
        except Exception as e:
            return {'error': str(e)}
            
    def get_cpu_usage(self) -> Dict[str, Any]:
        """الحصول على معلومات استخدام المعالج"""
        try:
            import psutil
            process = psutil.Process()
            
            return {
                'percent': process.cpu_percent(),
                'system_percent': psutil.cpu_percent(),
                'threads': process.num_threads()
            }
        except ImportError:
            return {'error': 'psutil not available'}
        except Exception as e:
            return {'error': str(e)}

# إنشاء مثيل عام للمُحسن
optimizer = ResourceOptimizer()

def apply_flask_optimizations(app):
    """تطبيق تحسينات Flask"""
    app.config.update({
        'MAX_CONTENT_LENGTH': WEB_SERVER_OPTIMIZATION['max_content_length'],
        'SEND_FILE_MAX_AGE_DEFAULT': WEB_SERVER_OPTIMIZATION['static_cache_timeout'],
        'JSONIFY_PRETTYPRINT_REGULAR': False,  # تقليل حجم JSON
    })
    
    # إضافة ضغط gzip إذا كان متوفراً
    try:
        from flask_compress import Compress
        Compress(app)
    except ImportError:
        pass

def get_optimized_telegram_settings() -> Dict[str, Any]:
    """الحصول على إعدادات تيليجرام المحسنة"""
    return {
        'read_timeout': 60,  # زيادة timeout للقراءة
        'write_timeout': 60,  # زيادة timeout للكتابة
        'connect_timeout': 60,  # زيادة timeout للاتصال
        'pool_timeout': 60,  # زيادة timeout للمجموعة
        'connection_pool_size': 8,  # حجم مجموعة الاتصالات
    }

def get_optimized_supabase_settings() -> Dict[str, Any]:
    """الحصول على إعدادات Supabase المحسنة"""
    return {
        'timeout': DATABASE_OPTIMIZATION['connection_timeout'],
        'max_connections': DATABASE_OPTIMIZATION['max_connections'],
    }

# دالة للتحقق من التوافق مع الخطة المجانية
def check_free_hosting_compatibility() -> Dict[str, Any]:
    """التحقق من التوافق مع الاستضافة المجانية"""
    compatibility = {
        'memory_usage': 'unknown',
        'cpu_usage': 'unknown',
        'disk_usage': 'unknown',
        'recommendations': []
    }
    
    try:
        # فحص استخدام الذاكرة
        memory_info = optimizer.get_memory_usage()
        if 'rss_mb' in memory_info:
            if memory_info['rss_mb'] < 80:
                compatibility['memory_usage'] = 'excellent'
            elif memory_info['rss_mb'] < 100:
                compatibility['memory_usage'] = 'good'
            else:
                compatibility['memory_usage'] = 'high'
                compatibility['recommendations'].append('Consider reducing memory usage')
        
        # فحص استخدام المعالج
        cpu_info = optimizer.get_cpu_usage()
        if 'percent' in cpu_info:
            if cpu_info['percent'] < 10:
                compatibility['cpu_usage'] = 'excellent'
            elif cpu_info['percent'] < 20:
                compatibility['cpu_usage'] = 'good'
            else:
                compatibility['cpu_usage'] = 'high'
                compatibility['recommendations'].append('Consider optimizing CPU usage')
        
        # فحص استخدام القرص
        try:
            import shutil
            disk_usage = shutil.disk_usage('.')
            used_gb = (disk_usage.total - disk_usage.free) / (1024**3)
            
            if used_gb < 1:
                compatibility['disk_usage'] = 'excellent'
            elif used_gb < 3:
                compatibility['disk_usage'] = 'good'
            else:
                compatibility['disk_usage'] = 'high'
                compatibility['recommendations'].append('Consider cleaning up disk space')
        except:
            pass
            
    except Exception as e:
        compatibility['error'] = str(e)
    
    return compatibility

if __name__ == "__main__":
    # اختبار المُحسن
    print("🔧 Testing Resource Optimizer...")
    
    optimizer.start_optimization()
    
    print("📊 Memory usage:", optimizer.get_memory_usage())
    print("⚡ CPU usage:", optimizer.get_cpu_usage())
    print("🎯 Compatibility:", check_free_hosting_compatibility())
    
    time.sleep(5)
    optimizer.stop_optimization()
    
    print("✅ Test completed")
