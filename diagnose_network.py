#!/usr/bin/env python3
"""
أداة تشخيص سريع لمشاكل الشبكة والـ DNS
Quick Network and DNS Diagnostic Tool
"""

import socket
import subprocess
import platform
import requests
import time
import sys

def print_header():
    print("=" * 60)
    print("🔍 أداة تشخيص مشاكل الشبكة والـ DNS")
    print("   Network and DNS Diagnostic Tool")
    print("=" * 60)

def test_basic_connectivity():
    """اختبار الاتصال الأساسي"""
    print("\n🌐 اختبار الاتصال الأساسي...")
    
    # اختبار DNS servers
    dns_servers = ["*******", "1.1.1.1", "208.67.222.222"]
    
    for dns in dns_servers:
        try:
            socket.create_connection((dns, 53), timeout=5)
            print(f"✅ DNS {dns} متاح")
            return True
        except Exception as e:
            print(f"❌ DNS {dns} غير متاح: {e}")
    
    print("❌ جميع خوادم DNS غير متاحة")
    return False

def test_dns_resolution():
    """اختبار حل أسماء النطاقات"""
    print("\n🔍 اختبار حل أسماء النطاقات...")
    
    domains = [
        "google.com",
        "api.telegram.org",
        "telegram.org",
        "github.com"
    ]
    
    success_count = 0
    
    for domain in domains:
        try:
            ip = socket.gethostbyname(domain)
            print(f"✅ {domain} -> {ip}")
            success_count += 1
        except Exception as e:
            print(f"❌ {domain} فشل: {e}")
    
    success_rate = (success_count / len(domains)) * 100
    print(f"\n📊 معدل نجاح DNS: {success_rate:.1f}%")
    return success_rate > 75

def test_telegram_api():
    """اختبار الوصول لـ Telegram API"""
    print("\n📱 اختبار الوصول لـ Telegram API...")
    
    try:
        # اختبار بسيط للوصول لـ Telegram
        response = requests.get("https://api.telegram.org/bot123:test/getMe", timeout=10)
        
        if response.status_code in [200, 401, 404]:
            print("✅ Telegram API قابل للوصول")
            return True
        else:
            print(f"⚠️ Telegram API استجاب بكود: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError as e:
        print(f"❌ فشل الاتصال بـ Telegram API: {e}")
        return False
    except requests.exceptions.Timeout:
        print("❌ انتهت مهلة الاتصال بـ Telegram API")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار Telegram API: {e}")
        return False

def check_system_dns():
    """فحص إعدادات DNS في النظام"""
    print("\n🔧 فحص إعدادات DNS في النظام...")
    
    system = platform.system().lower()
    
    if system == "windows":
        try:
            result = subprocess.run(["nslookup", "google.com"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ nslookup يعمل بشكل صحيح")
                print(f"📋 النتيجة: {result.stdout[:200]}...")
                return True
            else:
                print(f"❌ nslookup فشل: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تشغيل nslookup: {e}")
            return False
    
    return True

def check_firewall_ports():
    """فحص المنافذ المطلوبة"""
    print("\n🛡️ فحص المنافذ المطلوبة...")
    
    required_ports = [
        ("api.telegram.org", 443),
        ("api.telegram.org", 80),
        ("google.com", 443),
        ("*******", 53)
    ]
    
    success_count = 0
    
    for host, port in required_ports:
        try:
            socket.create_connection((host, port), timeout=5)
            print(f"✅ {host}:{port} متاح")
            success_count += 1
        except Exception as e:
            print(f"❌ {host}:{port} غير متاح: {e}")
    
    success_rate = (success_count / len(required_ports)) * 100
    print(f"\n📊 معدل نجاح المنافذ: {success_rate:.1f}%")
    return success_rate > 75

def suggest_solutions():
    """اقتراح حلول للمشاكل"""
    print("\n" + "=" * 60)
    print("💡 حلول مقترحة:")
    print("=" * 60)
    
    print("\n1. 🌐 مشاكل الاتصال:")
    print("   - تحقق من كابل الإنترنت")
    print("   - أعد تشغيل الراوتر")
    print("   - جرب شبكة أخرى (هاتف محمول)")
    
    print("\n2. 🔧 إصلاح DNS:")
    print("   - تغيير DNS إلى ******* و *******")
    print("   - تشغيل: ipconfig /flushdns")
    print("   - إعادة تشغيل الكمبيوتر")
    
    print("\n3. 🛡️ الجدار الناري:")
    print("   - إضافة Python للاستثناءات")
    print("   - السماح للمنافذ 80, 443, 53")
    print("   - تعطيل مضاد الفيروسات مؤقتاً")
    
    print("\n4. 🔄 أوامر إصلاح Windows:")
    print("   ipconfig /flushdns")
    print("   ipconfig /release")
    print("   ipconfig /renew")
    print("   netsh winsock reset")
    
    print("\n5. 🐍 إصلاح Python:")
    print("   pip install --upgrade requests")
    print("   pip install --upgrade python-telegram-bot")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    tests = [
        ("الاتصال الأساسي", test_basic_connectivity),
        ("حل أسماء النطاقات", test_dns_resolution),
        ("Telegram API", test_telegram_api),
        ("إعدادات DNS", check_system_dns),
        ("المنافذ المطلوبة", check_firewall_ports)
    ]
    
    print(f"\n🚀 بدء تشغيل {len(tests)} اختبارات...\n")
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        print(f"\n[{tests.index((test_name, test_func)) + 1}/{len(tests)}] {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ نجح: {test_name}")
            else:
                print(f"❌ فشل: {test_name}")
        except Exception as e:
            print(f"💥 خطأ في {test_name}: {e}")
        
        time.sleep(1)
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 النتائج النهائية")
    print("=" * 60)
    
    success_rate = (passed_tests / len(tests)) * 100
    print(f"✅ الاختبارات الناجحة: {passed_tests}/{len(tests)} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 الشبكة تعمل بشكل جيد!")
        print("💡 المشكلة قد تكون في إعدادات البوت أو Token")
    elif success_rate >= 50:
        print("⚠️ هناك مشاكل جزئية في الشبكة")
        print("🔧 راجع الحلول المقترحة أدناه")
    else:
        print("❌ مشاكل خطيرة في الشبكة")
        print("🆘 تحتاج لمساعدة تقنية متخصصة")
    
    suggest_solutions()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التشخيص بواسطة المستخدم")
    except Exception as e:
        print(f"\n💥 خطأ في التشخيص: {e}")
        sys.exit(1)
