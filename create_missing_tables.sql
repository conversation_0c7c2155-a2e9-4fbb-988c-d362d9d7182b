-- جدول إعدادات اختصار الروابط للمستخدمين
-- User URL Shortener Settings Table for Supabase (PostgreSQL)

CREATE TABLE IF NOT EXISTS user_url_shortener_settings (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL UNIQUE,
    channel_id VARCHAR(255) NOT NULL,

    -- إعدادات اختصار الروابط الأساسية
    shortener_enabled BOOLEAN DEFAULT FALSE,
    api_url TEXT NOT NULL,
    api_key TEXT NOT NULL,
    service_name VARCHAR(100) DEFAULT 'custom', -- 'linkjust', 'shortlink', 'tinyurl', 'bitly', 'custom'

    -- إعدادات إضافية
    use_custom_alias BOOLEAN DEFAULT TRUE, -- استخدام اسم مستعار مخصص
    alias_prefix VARCHAR(50) DEFAULT 'mod', -- بادئة الاسم المستعار

    -- إحصائيات
    total_urls_shortened INTEGER DEFAULT 0,
    total_clicks INTEGER DEFAULT 0,
    last_shortened_at TIMESTAMP WITH TIME ZONE NULL,
    
    -- تواريخ الإنشاء والتحديث
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_url_shortener_settings_user_id ON user_url_shortener_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_url_shortener_settings_channel_id ON user_url_shortener_settings(channel_id);
CREATE INDEX IF NOT EXISTS idx_user_url_shortener_settings_enabled ON user_url_shortener_settings(shortener_enabled);
CREATE INDEX IF NOT EXISTS idx_user_url_shortener_settings_service ON user_url_shortener_settings(service_name);
CREATE INDEX IF NOT EXISTS idx_user_url_shortener_settings_created_at ON user_url_shortener_settings(created_at);

-- إضافة تعليقات للجدول (PostgreSQL syntax)
COMMENT ON TABLE user_url_shortener_settings IS 'جدول لحفظ إعدادات اختصار الروابط لكل مستخدم';
COMMENT ON COLUMN user_url_shortener_settings.user_id IS 'معرف المستخدم الفريد';
COMMENT ON COLUMN user_url_shortener_settings.channel_id IS 'معرف القناة المرتبطة';
COMMENT ON COLUMN user_url_shortener_settings.shortener_enabled IS 'تفعيل/إيقاف ميزة اختصار الروابط';
COMMENT ON COLUMN user_url_shortener_settings.api_url IS 'رابط API الخاص بموقع الاختصار';
COMMENT ON COLUMN user_url_shortener_settings.api_key IS 'مفتاح API للموقع';
COMMENT ON COLUMN user_url_shortener_settings.service_name IS 'اسم خدمة الاختصار المستخدمة';
COMMENT ON COLUMN user_url_shortener_settings.use_custom_alias IS 'استخدام اسم مستعار مخصص للروابط';
COMMENT ON COLUMN user_url_shortener_settings.alias_prefix IS 'بادئة الاسم المستعار';
COMMENT ON COLUMN user_url_shortener_settings.total_urls_shortened IS 'إجمالي عدد الروابط المختصرة';
COMMENT ON COLUMN user_url_shortener_settings.total_clicks IS 'إجمالي عدد النقرات على الروابط المختصرة';
COMMENT ON COLUMN user_url_shortener_settings.last_shortened_at IS 'تاريخ آخر اختصار رابط';

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_user_url_shortener_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث updated_at
DROP TRIGGER IF EXISTS trigger_update_user_url_shortener_settings_updated_at ON user_url_shortener_settings;
CREATE TRIGGER trigger_update_user_url_shortener_settings_updated_at
    BEFORE UPDATE ON user_url_shortener_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_user_url_shortener_settings_updated_at();

-- إضافة قيود إضافية للتحقق من صحة البيانات
ALTER TABLE user_url_shortener_settings 
ADD CONSTRAINT check_api_url_format 
CHECK (api_url ~ '^https?://.*');

ALTER TABLE user_url_shortener_settings 
ADD CONSTRAINT check_api_key_length 
CHECK (LENGTH(api_key) >= 10);

ALTER TABLE user_url_shortener_settings 
ADD CONSTRAINT check_service_name_valid 
CHECK (service_name IN ('linkjust', 'shortlink', 'tinyurl', 'bitly', 'custom'));

-- جدول إعدادات الإعلانات للمستخدمين
-- User Ads Settings Table for Supabase (PostgreSQL)

CREATE TABLE IF NOT EXISTS user_ads_settings (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL UNIQUE,
    channel_id VARCHAR(255) NOT NULL,

    -- إعدادات الإعلان الأساسية
    ads_enabled BOOLEAN DEFAULT FALSE,
    ad_direct_link TEXT NULL,
    ad_network VARCHAR(100) DEFAULT 'custom', -- 'adstera', 'monetag', 'propellerads', 'hilltopads', 'custom'

    -- إعدادات عرض الإعلان
    ad_display_mode VARCHAR(50) DEFAULT 'on_download', -- 'on_download', 'after_page_load', 'after_delay'
    ad_delay_seconds INTEGER DEFAULT 3, -- للوضع after_delay

    -- إعدادات زر الإغلاق
    close_button_enabled BOOLEAN DEFAULT TRUE,
    close_button_delay INTEGER DEFAULT 5, -- بالثواني

    -- إحصائيات
    total_ad_views INTEGER DEFAULT 0,
    total_ad_clicks INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_ads_settings_user_id ON user_ads_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_ads_settings_channel_id ON user_ads_settings(channel_id);
CREATE INDEX IF NOT EXISTS idx_user_ads_settings_ads_enabled ON user_ads_settings(ads_enabled);
CREATE INDEX IF NOT EXISTS idx_user_ads_settings_created_at ON user_ads_settings(created_at);

-- إضافة تعليقات للجدول (PostgreSQL syntax)
COMMENT ON TABLE user_ads_settings IS 'جدول لحفظ إعدادات الإعلانات لكل مستخدم';

-- تعليقات الأعمدة (PostgreSQL syntax)
COMMENT ON COLUMN user_ads_settings.user_id IS 'معرف المستخدم في تليجرام';
COMMENT ON COLUMN user_ads_settings.channel_id IS 'معرف القناة المرتبطة';
COMMENT ON COLUMN user_ads_settings.ads_enabled IS 'هل الإعلانات مفعلة';
COMMENT ON COLUMN user_ads_settings.ad_direct_link IS 'رابط الإعلان المباشر';
COMMENT ON COLUMN user_ads_settings.ad_network IS 'شبكة الإعلانات المستخدمة';
COMMENT ON COLUMN user_ads_settings.ad_display_mode IS 'طريقة عرض الإعلان';
COMMENT ON COLUMN user_ads_settings.ad_delay_seconds IS 'تأخير عرض الإعلان بالثواني';
COMMENT ON COLUMN user_ads_settings.close_button_enabled IS 'هل زر الإغلاق مفعل';
COMMENT ON COLUMN user_ads_settings.close_button_delay IS 'تأخير ظهور زر الإغلاق بالثواني';
COMMENT ON COLUMN user_ads_settings.total_ad_views IS 'إجمالي مشاهدات الإعلانات';
COMMENT ON COLUMN user_ads_settings.total_ad_clicks IS 'إجمالي نقرات الإعلانات';

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء trigger لتحديث updated_at
CREATE TRIGGER update_user_ads_settings_updated_at
    BEFORE UPDATE ON user_ads_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
