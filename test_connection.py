#!/usr/bin/env python3
import socket
import requests

def test_connection():
    try:
        # اختبار DNS
        socket.gethostbyname("google.com")
        print("✅ DNS يعمل")
        
        # اختبار HTTP
        requests.get("https://httpbin.org/ip", timeout=10)
        print("✅ HTTP يعمل")
        
        # اختبار Telegram
        requests.get("https://api.telegram.org/bot123:test/getMe", timeout=10)
        print("✅ Telegram API قابل للوصول")
        
        return True
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        return False

if __name__ == "__main__":
    if test_connection():
        print("🎉 الشبكة تعمل بشكل جيد!")
    else:
        print("❌ لا تزال هناك مشاكل في الشبكة")
