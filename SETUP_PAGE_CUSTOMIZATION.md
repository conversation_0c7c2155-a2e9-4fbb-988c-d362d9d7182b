# إعداد ميزة تخصيص الصفحة

## 🎯 المشكلة الحالية
البوت يحفظ إعدادات التخصيص في قاعدة بيانات مختلفة عن قاعدة البيانات التي تحتوي على المودات.

## ✅ الحل المطبق

### 1. تحديث إعدادات قاعدة البيانات
تم تحديث ملف `.env` ليستخدم قاعدة البيانات الصحيحة:
```
SUPABASE_URL=https://ytqxxodyecdeosnqoure.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4
```

### 2. إن<PERSON>اء جدول إعدادات التخصيص

**خطوات إنشاء الجدول:**

1. **اذهب إلى لوحة تحكم Supabase:**
   - الرابط: https://supabase.com/dashboard/project/ytqxxodyecdeosnqoure

2. **اذهب إلى SQL Editor:**
   - من القائمة الجانبية، اختر "SQL Editor"

3. **انسخ والصق الكود التالي:**
```sql
-- إنشاء جدول page_customization_settings
CREATE TABLE IF NOT EXISTS page_customization_settings (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL UNIQUE,
    site_name TEXT DEFAULT 'Modetaris',
    channel_logo_url TEXT,
    logo_position TEXT DEFAULT 'right',
    page_theme TEXT DEFAULT 'default',
    custom_bg_color TEXT,
    custom_header_color TEXT,
    custom_text_color TEXT,
    custom_button_color TEXT,
    custom_border_color TEXT,
    show_all_images BOOLEAN DEFAULT true,
    enable_mod_opening BOOLEAN DEFAULT true,
    download_button_text_ar TEXT DEFAULT 'تحميل',
    download_button_text_en TEXT DEFAULT 'Download',
    open_button_text_ar TEXT DEFAULT 'فتح',
    open_button_text_en TEXT DEFAULT 'Open',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهرس على user_id لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_page_customization_settings_user_id ON page_customization_settings(user_id);

-- تعطيل RLS مؤقتاً للاختبار
ALTER TABLE page_customization_settings DISABLE ROW LEVEL SECURITY;
```

4. **اضغط "Run" لتنفيذ الكود**

### 3. التحقق من نجاح الإنشاء

بعد تنفيذ الكود، يجب أن ترى رسالة نجاح. يمكنك التحقق من إنشاء الجدول بتشغيل:

```sql
SELECT * FROM page_customization_settings LIMIT 1;
```

## 🧪 اختبار الميزة

بعد إنشاء الجدول:

1. **اذهب إلى البوت في تيليجرام**
2. **اختر: القائمة الرئيسية → إعدادات الصفحة → تخصيص الصفحة**
3. **جرب تغيير اسم الموقع**
4. **جرب رفع صورة القناة**
5. **اختر "👀 معاينة الصفحة"**

## 📋 الميزات المتاحة

- ✅ **تغيير اسم الموقع** (بدلاً من "Modetaris")
- ✅ **رفع صورة القناة** مع اختيار موضعها (يمين/يسار)
- ✅ **تخصيص نصوص الأزرار** (عربي/إنجليزي)
- ✅ **إعدادات عرض الصور** (جميع الصور أو الرئيسية فقط)
- ✅ **معاينة الصفحة** مع التخصيصات

## 🔧 استكشاف الأخطاء

إذا لم تعمل الميزة:

1. **تأكد من إنشاء الجدول** في قاعدة البيانات الصحيحة
2. **تأكد من إعادة تشغيل البوت** بعد تحديث `.env`
3. **تحقق من اللوج** للتأكد من عدم وجود أخطاء

## 📝 ملاحظات مهمة

- قاعدة البيانات `https://ytqxxodyecdeosnqoure.supabase.co` تحتوي على المودات
- ملف `htdocs/index.php` يستخدم نفس قاعدة البيانات
- البوت الآن يحفظ إعدادات التخصيص في نفس قاعدة البيانات
