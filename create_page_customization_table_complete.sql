-- إنشاء جدول إعدادات تخصيص الصفحة مع الأساليب المتقدمة
-- Create page customization settings table with advanced styles
-- تشغيل هذا الكود في SQL Editor في Supabase
-- Run this code in SQL Editor in Supabase
-- قاعدة البيانات: https://ytqxxodyecdeosnqoure.supabase.co

-- حذف الجدول إذا كان موجوداً (اختياري - احذف هذا السطر إذا كنت تريد الاحتفاظ بالبيانات الموجودة)
DROP TABLE IF EXISTS page_customization_settings CASCADE;

-- إن<PERSON><PERSON>ء الجدول الكامل مع جميع الحقول
CREATE TABLE IF NOT EXISTS page_customization_settings (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL UNIQUE,
    
    -- الإعدادات الأساسية
    site_name TEXT DEFAULT 'Modetaris',
    channel_logo_url TEXT,
    logo_position TEXT DEFAULT 'right' CHECK (logo_position IN ('left', 'right')),
    
    -- إعدادات الثيم والستايل
    page_theme TEXT DEFAULT 'default' CHECK (page_theme IN ('default', 'telegram', 'tiktok', 'classic', 'professional', 'dark', 'light', 'custom')),
    style_template TEXT DEFAULT 'default' CHECK (style_template IN ('default', 'telegram', 'tiktok', 'classic', 'professional', 'custom')),
    
    -- الألوان الأساسية
    custom_bg_color TEXT,
    custom_header_color TEXT,
    custom_text_color TEXT,
    custom_button_color TEXT,
    custom_border_color TEXT,
    
    -- الألوان المتقدمة
    custom_accent_color TEXT,
    custom_card_color TEXT,
    custom_shadow_color TEXT,
    
    -- إعدادات الخطوط
    custom_font_family TEXT DEFAULT 'Press Start 2P',
    custom_font_size TEXT DEFAULT 'medium' CHECK (custom_font_size IN ('small', 'medium', 'large', 'extra-large')),
    
    -- إعدادات التأثيرات
    enable_animations BOOLEAN DEFAULT true,
    enable_gradients BOOLEAN DEFAULT true,
    enable_shadows BOOLEAN DEFAULT true,
    
    -- إعدادات التخطيط
    layout_style TEXT DEFAULT 'modern' CHECK (layout_style IN ('modern', 'compact', 'spacious', 'minimal')),
    border_radius TEXT DEFAULT 'medium' CHECK (border_radius IN ('none', 'small', 'medium', 'large', 'round')),
    
    -- إعدادات العرض
    show_all_images BOOLEAN DEFAULT true,
    enable_mod_opening BOOLEAN DEFAULT true,
    
    -- نصوص الأزرار
    download_button_text_ar TEXT DEFAULT 'تحميل',
    download_button_text_en TEXT DEFAULT 'Download',
    open_button_text_ar TEXT DEFAULT 'فتح',
    open_button_text_en TEXT DEFAULT 'Open',
    
    -- نصوص التسميات
    version_label_ar TEXT DEFAULT 'الإصدار',
    version_label_en TEXT DEFAULT 'Version',
    category_label_ar TEXT DEFAULT 'تصنيف المود',
    category_label_en TEXT DEFAULT 'Mod Category',
    description_label_ar TEXT DEFAULT 'الوصف',
    description_label_en TEXT DEFAULT 'Description',
    
    -- الطوابع الزمنية
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_page_customization_user_id ON page_customization_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_page_customization_style_template ON page_customization_settings(style_template);
CREATE INDEX IF NOT EXISTS idx_page_customization_page_theme ON page_customization_settings(page_theme);
CREATE INDEX IF NOT EXISTS idx_page_customization_layout_style ON page_customization_settings(layout_style);
CREATE INDEX IF NOT EXISTS idx_page_customization_created_at ON page_customization_settings(created_at);

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_page_customization_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث updated_at
DROP TRIGGER IF EXISTS trigger_update_page_customization_updated_at ON page_customization_settings;
CREATE TRIGGER trigger_update_page_customization_updated_at
    BEFORE UPDATE ON page_customization_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_page_customization_updated_at();

-- إدراج قوالب افتراضية للأساليب المختلفة
INSERT INTO page_customization_settings (
    user_id, site_name, style_template, page_theme,
    custom_bg_color, custom_header_color, custom_text_color, 
    custom_button_color, custom_accent_color, custom_font_family,
    layout_style, enable_gradients, enable_animations
) VALUES 
-- قالب ستايل تيليجرام
('template_telegram', 'Telegram Style', 'telegram', 'telegram',
 '#0088cc', '#0088cc', '#ffffff', '#40a7e3', '#64b5f6', 'Roboto',
 'modern', true, true),

-- قالب ستايل تيك توك
('template_tiktok', 'TikTok Style', 'tiktok', 'custom',
 '#000000', '#ff0050', '#ffffff', '#ff0050', '#25f4ee', 'Poppins',
 'modern', true, true),

-- قالب ستايل كلاسيكي
('template_classic', 'Classic Style', 'classic', 'custom',
 '#f5f5dc', '#8b4513', '#2f4f4f', '#cd853f', '#daa520', 'Georgia',
 'spacious', false, false),

-- قالب ستايل احترافي
('template_professional', 'Professional Style', 'professional', 'custom',
 '#f8f9fa', '#2c3e50', '#2c3e50', '#3498db', '#e74c3c', 'Inter',
 'minimal', false, false)

ON CONFLICT (user_id) DO NOTHING;

-- إنشاء دالة مساعدة لتطبيق ستايل على مستخدم
CREATE OR REPLACE FUNCTION apply_style_to_user(
    target_user_id TEXT,
    template_name TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    template_data RECORD;
    result BOOLEAN := FALSE;
BEGIN
    -- جلب بيانات القالب
    SELECT * INTO template_data 
    FROM page_customization_settings 
    WHERE user_id = 'template_' || template_name;
    
    IF FOUND THEN
        -- تطبيق القالب على المستخدم
        INSERT INTO page_customization_settings (
            user_id, style_template, page_theme,
            custom_bg_color, custom_header_color, custom_text_color,
            custom_button_color, custom_accent_color, custom_font_family,
            layout_style, enable_gradients, enable_animations
        ) VALUES (
            target_user_id, template_data.style_template, template_data.page_theme,
            template_data.custom_bg_color, template_data.custom_header_color, template_data.custom_text_color,
            template_data.custom_button_color, template_data.custom_accent_color, template_data.custom_font_family,
            template_data.layout_style, template_data.enable_gradients, template_data.enable_animations
        )
        ON CONFLICT (user_id) DO UPDATE SET
            style_template = EXCLUDED.style_template,
            page_theme = EXCLUDED.page_theme,
            custom_bg_color = EXCLUDED.custom_bg_color,
            custom_header_color = EXCLUDED.custom_header_color,
            custom_text_color = EXCLUDED.custom_text_color,
            custom_button_color = EXCLUDED.custom_button_color,
            custom_accent_color = EXCLUDED.custom_accent_color,
            custom_font_family = EXCLUDED.custom_font_family,
            layout_style = EXCLUDED.layout_style,
            enable_gradients = EXCLUDED.enable_gradients,
            enable_animations = EXCLUDED.enable_animations,
            updated_at = NOW();
        
        result := TRUE;
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة للحصول على إحصائيات الأساليب
CREATE OR REPLACE FUNCTION get_style_statistics()
RETURNS TABLE(
    style_name TEXT,
    user_count BIGINT,
    percentage NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    WITH style_counts AS (
        SELECT 
            COALESCE(style_template, 'default') as style,
            COUNT(*) as count
        FROM page_customization_settings 
        WHERE user_id NOT LIKE 'template_%'
        GROUP BY COALESCE(style_template, 'default')
    ),
    total_users AS (
        SELECT SUM(count) as total FROM style_counts
    )
    SELECT 
        sc.style,
        sc.count,
        ROUND((sc.count::NUMERIC / tu.total::NUMERIC) * 100, 2) as pct
    FROM style_counts sc, total_users tu
    ORDER BY sc.count DESC;
END;
$$ LANGUAGE plpgsql;

-- التحقق من نجاح العملية
SELECT 'Page customization table created successfully!' as status;

-- عرض الجدول المُنشأ
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'page_customization_settings'
ORDER BY ordinal_position;

-- عرض القوالب المُدرجة
SELECT user_id, style_template, custom_bg_color, custom_header_color 
FROM page_customization_settings 
WHERE user_id LIKE 'template_%';

-- عرض إحصائيات الأساليب
SELECT * FROM get_style_statistics();

-- رسالة نهائية
SELECT 'Setup completed! You can now use the style templates feature.' as message;
