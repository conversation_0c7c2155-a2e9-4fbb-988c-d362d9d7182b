/* 
 * Advanced Style Templates for Mod Display Pages
 * أساليب التصميم المتقدمة لصفحات عرض المودات
 */

/* ===== TELEGRAM STYLE ===== */
.style-template-telegram {
    --telegram-blue: #0088cc;
    --telegram-light-blue: #40a7e3;
    --telegram-accent: #64b5f6;
}

.style-template-telegram .mod-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(64, 167, 227, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.style-template-telegram .mod-container:hover {
    background: rgba(255, 255, 255, 0.98);
    border-color: var(--telegram-light-blue);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 136, 204, 0.25);
}

.style-template-telegram .pixel-button {
    background: linear-gradient(135deg, var(--telegram-blue) 0%, var(--telegram-light-blue) 50%, var(--telegram-accent) 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(0, 136, 204, 0.3);
    transition: all 0.3s ease;
}

.style-template-telegram .pixel-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 136, 204, 0.4);
}

.style-template-telegram .pixel-button:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 136, 204, 0.3);
}

/* ===== TIKTOK STYLE ===== */
.style-template-tiktok {
    --tiktok-pink: #ff0050;
    --tiktok-cyan: #25f4ee;
    --tiktok-dark: #000000;
    --tiktok-gray: #1a1a1a;
}

.style-template-tiktok .mod-container {
    background: linear-gradient(135deg, var(--tiktok-gray) 0%, #2d2d2d 100%);
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
    overflow: hidden;
}

.style-template-tiktok .mod-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, var(--tiktok-pink), var(--tiktok-cyan), var(--tiktok-pink));
    z-index: -1;
    margin: -2px;
    border-radius: inherit;
}

.style-template-tiktok .mod-container:hover {
    transform: scale(1.02);
    box-shadow: 0 0 30px rgba(255, 0, 80, 0.4);
}

.style-template-tiktok .pixel-button {
    background: linear-gradient(45deg, var(--tiktok-pink) 0%, var(--tiktok-cyan) 50%, var(--tiktok-pink) 100%);
    background-size: 200% 200%;
    animation: tiktok-gradient 3s ease infinite;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    overflow: hidden;
}

.style-template-tiktok .pixel-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.style-template-tiktok .pixel-button:hover::before {
    left: 100%;
}

@keyframes tiktok-gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* ===== CLASSIC STYLE ===== */
.style-template-classic {
    --classic-brown: #8b4513;
    --classic-gold: #daa520;
    --classic-bronze: #cd853f;
    --classic-cream: #f5f5dc;
}

.style-template-classic .mod-container {
    background: #ffffff;
    border: 3px solid var(--classic-brown);
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        0 4px 8px rgba(139, 69, 19, 0.3);
    position: relative;
}

.style-template-classic .mod-container::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    right: 3px;
    bottom: 3px;
    border: 1px solid rgba(218, 165, 32, 0.3);
    pointer-events: none;
}

.style-template-classic .mod-container:hover {
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        0 6px 12px rgba(139, 69, 19, 0.4);
}

.style-template-classic .pixel-button {
    background: linear-gradient(to bottom, var(--classic-bronze) 0%, var(--classic-gold) 50%, var(--classic-bronze) 100%);
    border: 2px solid var(--classic-brown);
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 2px 4px rgba(139, 69, 19, 0.3);
    font-family: 'Georgia', serif;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(139, 69, 19, 0.3);
}

.style-template-classic .pixel-button:hover {
    background: linear-gradient(to bottom, var(--classic-gold) 0%, var(--classic-bronze) 50%, var(--classic-gold) 100%);
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 4px 8px rgba(139, 69, 19, 0.4);
}

/* ===== PROFESSIONAL STYLE ===== */
.style-template-professional {
    --pro-blue: #3498db;
    --pro-dark-blue: #2980b9;
    --pro-gray: #2c3e50;
    --pro-light-gray: #ecf0f1;
    --pro-red: #e74c3c;
}

.style-template-professional .mod-container {
    background: #ffffff;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 10px rgba(44, 62, 80, 0.08);
    transition: all 0.2s ease;
}

.style-template-professional .mod-container:hover {
    border-color: var(--pro-blue);
    box-shadow: 0 4px 20px rgba(52, 152, 219, 0.15);
    transform: translateY(-2px);
}

.style-template-professional .pixel-button {
    background: linear-gradient(135deg, var(--pro-blue) 0%, var(--pro-dark-blue) 100%);
    border: none;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.style-template-professional .pixel-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transition: width 0.3s ease;
}

.style-template-professional .pixel-button:hover::after {
    width: 100%;
}

.style-template-professional .pixel-button:hover {
    background: linear-gradient(135deg, var(--pro-dark-blue) 0%, var(--pro-blue) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

/* ===== LAYOUT STYLES ===== */

/* Compact Layout */
.style-template-compact .container {
    padding: 0.5rem;
    max-width: 600px;
}

.style-template-compact .mod-container {
    margin-bottom: 0.75rem;
    padding: 1rem;
}

.style-template-compact .mod-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

/* Spacious Layout */
.style-template-spacious .container {
    padding: 2rem;
    max-width: 1000px;
}

.style-template-spacious .mod-container {
    margin-bottom: 2.5rem;
    padding: 2.5rem;
}

.style-template-spacious .mod-title {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

/* Minimal Layout */
.style-template-minimal .mod-container {
    border: none;
    box-shadow: none;
    background: transparent;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0;
}

.style-template-minimal .pixel-button {
    border-radius: 0;
    border: 2px solid currentColor;
    background: transparent;
    color: inherit;
}

.style-template-minimal .pixel-button:hover {
    background: currentColor;
    color: var(--bg-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .style-template-spacious .container {
        padding: 1rem;
    }
    
    .style-template-spacious .mod-container {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .style-template-spacious .mod-title {
        font-size: 1.75rem;
    }
}

/* ===== ANIMATION ENHANCEMENTS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mod-container {
    animation: fadeInUp 0.6s ease-out;
}

.mod-container:nth-child(2) { animation-delay: 0.1s; }
.mod-container:nth-child(3) { animation-delay: 0.2s; }
.mod-container:nth-child(4) { animation-delay: 0.3s; }

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
.pixel-button:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
    .style-template-professional {
        --pro-light-gray: #34495e;
    }
    
    .style-template-professional .mod-container {
        background: #2c3e50;
        color: #ecf0f1;
        border-color: #34495e;
    }
}
