#!/usr/bin/env python3
"""
حل سريع لمشكلة HTTPS
Quick HTTPS Fix

حل مؤقت لمشكلة عدم قبول تيليجرام لروابط HTTP
Temporary fix for Telegram not accepting HTTP links
"""

import os
import sys
import socket
from pathlib import Path

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

def update_env_with_local_ip():
    """تحديث ملف .env بـ IP المحلي"""
    local_ip = get_local_ip()
    local_url = f"http://{local_ip}:5001"
    
    print(f"🔍 Local IP detected: {local_ip}")
    print(f"🌐 Local URL: {local_url}")
    
    env_file = Path(".env")
    env_lines = []
    updated = False
    
    # قراءة الملف الموجود
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('WEB_SERVER_URL='):
                        env_lines.append(f"WEB_SERVER_URL={local_url}")
                        updated = True
                        print(f"✅ Updated WEB_SERVER_URL to {local_url}")
                    else:
                        env_lines.append(line)
        except Exception as e:
            print(f"⚠️ Error reading .env file: {e}")
    
    # إضافة WEB_SERVER_URL إذا لم يكن موجوداً
    if not updated:
        env_lines.append(f"WEB_SERVER_URL={local_url}")
        print(f"✅ Added WEB_SERVER_URL={local_url}")
    
    # حفظ الملف
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            for line in env_lines:
                f.write(line + '\n')
        print("✅ .env file updated successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to update .env file: {e}")
        return False

def show_ngrok_instructions():
    """عرض تعليمات ngrok"""
    print("\n" + "="*60)
    print("🔒 للحصول على HTTPS (مطلوب لتيليجرام)")
    print("="*60)
    print("\n📥 تحميل ngrok:")
    print("1. اذهب إلى: https://ngrok.com/download")
    print("2. حمل ngrok لـ Windows")
    print("3. فك الضغط وضع ngrok.exe في هذا المجلد")
    print("4. قم بتشغيل الأمر التالي:")
    print("   ngrok http 5001")
    print("5. انسخ الرابط HTTPS المعطى")
    print("6. أضفه إلى ملف .env:")
    print("   WEB_SERVER_URL=https://your-ngrok-url.ngrok.io")
    
    print("\n🔄 أو استخدم الحل المؤقت:")
    print("• تم تحديث .env بـ IP المحلي")
    print("• سيعمل فقط للأجهزة في نفس الشبكة")
    print("• لن يعمل مع Telegram Web App (يتطلب HTTPS)")
    print("="*60)

def main():
    print("\n" + "="*60)
    print("🔧 حل سريع لمشكلة HTTPS")
    print("="*60)
    
    print("\n🚨 المشكلة المكتشفة:")
    print("تيليجرام يرفض روابط HTTP ويتطلب HTTPS فقط")
    print("Telegram rejects HTTP links and requires HTTPS only")
    
    # تحديث ملف .env بـ IP المحلي كحل مؤقت
    print("\n🔧 تطبيق حل مؤقت...")
    update_env_with_local_ip()
    
    # عرض تعليمات ngrok للحل الدائم
    show_ngrok_instructions()
    
    print("\n⚠️ ملاحظة مهمة:")
    print("الحل المؤقت لن يعمل مع Telegram Web App")
    print("لحل المشكلة نهائياً، استخدم ngrok للحصول على HTTPS")

if __name__ == "__main__":
    main()
