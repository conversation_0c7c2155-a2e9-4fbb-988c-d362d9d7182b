#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل البوت المحسن مع معالجة أخطاء الشبكة
Enhanced Bot Runner with Network Error Handling
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# إعداد التسجيل
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)

# تقليل مستوى التسجيل للمكتبات الخارجية
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("telegram.vendor.ptb_urllib3.urllib3").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)

logger = logging.getLogger(__name__)

def check_requirements():
    """فحص المتطلبات الأساسية"""
    logger.info("🔍 فحص المتطلبات الأساسية...")
    
    required_files = [
        "main.py",
        "supabase_client.py",
        "web_server.py",
        ".env"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    logger.info("✅ جميع الملفات المطلوبة موجودة")
    return True

def check_environment_variables():
    """فحص متغيرات البيئة المطلوبة"""
    logger.info("🔍 فحص متغيرات البيئة...")
    
    # تحميل متغيرات البيئة من ملف .env
    try:
        from dotenv import load_dotenv
        load_dotenv()
        logger.info("✅ تم تحميل متغيرات البيئة من ملف .env")
    except ImportError:
        logger.warning("⚠️ مكتبة python-dotenv غير مثبتة، سيتم استخدام متغيرات البيئة الافتراضية")
    except Exception as e:
        logger.warning(f"⚠️ فشل في تحميل ملف .env: {e}")
    
    required_vars = {
        "BOT_TOKEN": "رمز البوت من BotFather",
        "ADMIN_CHAT_ID": "معرف المسؤول",
        "SUPABASE_URL": "رابط قاعدة البيانات Supabase",
        "SUPABASE_KEY": "مفتاح قاعدة البيانات Supabase"
    }
    
    missing_vars = []
    for var, description in required_vars.items():
        if not os.environ.get(var):
            missing_vars.append(f"{var} ({description})")
    
    if missing_vars:
        logger.error("❌ متغيرات البيئة المفقودة:")
        for var in missing_vars:
            logger.error(f"   - {var}")
        logger.error("يرجى إضافة هذه المتغيرات إلى ملف .env")
        return False
    
    logger.info("✅ جميع متغيرات البيئة المطلوبة موجودة")
    return True

def check_network_connection():
    """فحص الاتصال بالشبكة"""
    logger.info("🌐 فحص الاتصال بالشبكة...")
    
    try:
        import socket
        # محاولة الاتصال بـ Google DNS
        socket.create_connection(("*******", 53), timeout=10)
        logger.info("✅ الاتصال بالإنترنت متاح")
        return True
    except OSError:
        try:
            # محاولة الاتصال بـ Cloudflare DNS
            socket.create_connection(("*******", 53), timeout=10)
            logger.info("✅ الاتصال بالإنترنت متاح")
            return True
        except OSError:
            logger.error("❌ لا يوجد اتصال بالإنترنت")
            return False

def check_telegram_api():
    """فحص الاتصال مع Telegram API"""
    logger.info("🔗 فحص الاتصال مع Telegram API...")
    
    try:
        import requests
        response = requests.get("https://api.telegram.org", timeout=30)
        if response.status_code == 200:
            logger.info("✅ الاتصال مع Telegram API متاح")
            return True
        else:
            logger.warning(f"⚠️ استجابة غير متوقعة من Telegram API: {response.status_code}")
            return False
    except Exception as e:
        logger.warning(f"⚠️ فشل في الاتصال مع Telegram API: {e}")
        return False

async def run_bot_with_retry():
    """تشغيل البوت مع إعادة المحاولة عند الفشل"""
    max_retries = 3
    retry_delay = 10
    
    for attempt in range(max_retries):
        try:
            logger.info(f"🚀 محاولة تشغيل البوت #{attempt + 1}")
            
            # استيراد وتشغيل البوت
            from main import main
            await main()
            
            # إذا وصلنا هنا، فقد نجح التشغيل
            break
            
        except KeyboardInterrupt:
            logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
            break
            
        except Exception as e:
            logger.error(f"❌ فشل في تشغيل البوت (المحاولة {attempt + 1}): {e}")
            
            if attempt < max_retries - 1:
                logger.info(f"⏳ انتظار {retry_delay} ثانية قبل المحاولة التالية...")
                await asyncio.sleep(retry_delay)
                retry_delay += 5  # زيادة وقت الانتظار تدريجياً
            else:
                logger.error("❌ فشل في جميع محاولات تشغيل البوت")
                raise

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🤖 بوت نشر مودات ماين كرافت - إصدار محسن")
    print("🔧 Minecraft Mods Bot - Enhanced Edition")
    print("=" * 60)
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات!")
        sys.exit(1)
    
    if not check_environment_variables():
        print("\n❌ فشل في فحص متغيرات البيئة!")
        sys.exit(1)
    
    if not check_network_connection():
        print("\n❌ فشل في فحص الاتصال بالشبكة!")
        print("🔧 يرجى التحقق من:")
        print("   - الاتصال بالإنترنت")
        print("   - إعدادات الجدار الناري")
        print("   - إعدادات البروكسي (إن وجدت)")
        sys.exit(1)
    
    if not check_telegram_api():
        print("\n⚠️ تحذير: قد تكون هناك مشكلة في الاتصال مع Telegram API")
        print("🔄 سيتم المحاولة مع إعدادات timeout محسنة...")
    
    print("\n🚀 بدء تشغيل البوت...")
    print("📋 البوت سيعمل في الخلفية...")
    print("⏹️ اضغط Ctrl+C لإيقاف البوت")
    print("=" * 60)
    
    try:
        # تشغيل البوت
        asyncio.run(run_bot_with_retry())
        
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف البوت بواسطة المستخدم")
        print("✅ البوت توقف بأمان")
        
    except Exception as e:
        print(f"\n\n❌ خطأ في تشغيل البوت: {e}")
        print("🔧 يرجى التحقق من:")
        print("   - إعدادات البوت في ملف .env")
        print("   - الاتصال بالإنترنت")
        print("   - صحة رمز البوت")
        sys.exit(1)

if __name__ == "__main__":
    main()
