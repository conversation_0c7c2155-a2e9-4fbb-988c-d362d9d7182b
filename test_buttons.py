#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة اختبار أزرار Telegram للتأكد من عمل الحل الجديد
"""

import asyncio
import logging
from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application
import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# إعدادات البوت
BOT_TOKEN = os.getenv('BOT_TOKEN')
TEST_CHAT_ID = os.getenv('ADMIN_USER_ID')  # معرف المسؤول للاختبار

async def test_webapp_buttons():
    """اختبار أزرار WebApp"""
    try:
        app = Application.builder().token(BOT_TOKEN).build()
        
        # إنشاء أزرار WebApp
        webapp_buttons = [
            [InlineKeyboardButton("🌐 WebApp Test", web_app={"url": "https://example.com"})],
            [InlineKeyboardButton("📱 Another WebApp", web_app={"url": "https://google.com"})]
        ]
        webapp_markup = InlineKeyboardMarkup(webapp_buttons)
        
        # إرسال رسالة اختبار
        await app.bot.send_message(
            chat_id=TEST_CHAT_ID,
            text="🧪 <b>اختبار أزرار WebApp</b>\n\nإذا ظهرت الأزرار أدناه، فإن WebApp يعمل بشكل طبيعي.",
            parse_mode="HTML",
            reply_markup=webapp_markup
        )
        
        logger.info("✅ تم إرسال اختبار WebApp بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل اختبار WebApp: {e}")
        return False

async def test_url_buttons():
    """اختبار أزرار URL العادية (البديلة)"""
    try:
        app = Application.builder().token(BOT_TOKEN).build()
        
        # إنشاء أزرار URL عادية
        url_buttons = [
            [InlineKeyboardButton("🔗 URL Test", url="https://example.com")],
            [InlineKeyboardButton("🌐 Another URL", url="https://google.com")]
        ]
        url_markup = InlineKeyboardMarkup(url_buttons)
        
        # إرسال رسالة اختبار
        await app.bot.send_message(
            chat_id=TEST_CHAT_ID,
            text="🧪 <b>اختبار أزرار URL البديلة</b>\n\nهذه الأزرار تعمل كبديل عند فشل WebApp.",
            parse_mode="HTML",
            reply_markup=url_markup
        )
        
        logger.info("✅ تم إرسال اختبار URL بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل اختبار URL: {e}")
        return False

async def test_mixed_buttons():
    """اختبار أزرار مختلطة"""
    try:
        app = Application.builder().token(BOT_TOKEN).build()
        
        # إنشاء أزرار مختلطة
        mixed_buttons = [
            [InlineKeyboardButton("🌐 WebApp", web_app={"url": "https://example.com"})],
            [InlineKeyboardButton("🔗 URL", url="https://google.com")],
            [InlineKeyboardButton("📞 Callback", callback_data="test_callback")]
        ]
        mixed_markup = InlineKeyboardMarkup(mixed_buttons)
        
        # إرسال رسالة اختبار
        await app.bot.send_message(
            chat_id=TEST_CHAT_ID,
            text="🧪 <b>اختبار أزرار مختلطة</b>\n\nمزيج من WebApp و URL و Callback.",
            parse_mode="HTML",
            reply_markup=mixed_markup
        )
        
        logger.info("✅ تم إرسال اختبار الأزرار المختلطة بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"❌ فشل اختبار الأزرار المختلطة: {e}")
        return False

async def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار أزرار Telegram...")
    print("=" * 50)
    
    if not BOT_TOKEN:
        print("❌ خطأ: BOT_TOKEN غير موجود في ملف .env")
        return
    
    if not TEST_CHAT_ID:
        print("❌ خطأ: ADMIN_USER_ID غير موجود في ملف .env")
        return
    
    # اختبار WebApp
    print("1️⃣ اختبار أزرار WebApp...")
    webapp_result = await test_webapp_buttons()
    await asyncio.sleep(2)
    
    # اختبار URL
    print("2️⃣ اختبار أزرار URL البديلة...")
    url_result = await test_url_buttons()
    await asyncio.sleep(2)
    
    # اختبار مختلط
    print("3️⃣ اختبار أزرار مختلطة...")
    mixed_result = await test_mixed_buttons()
    
    # النتائج
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبار:")
    print(f"   WebApp: {'✅ نجح' if webapp_result else '❌ فشل'}")
    print(f"   URL: {'✅ نجح' if url_result else '❌ فشل'}")
    print(f"   Mixed: {'✅ نجح' if mixed_result else '❌ فشل'}")
    
    if all([webapp_result, url_result, mixed_result]):
        print("\n🎉 جميع الاختبارات نجحت! النظام يعمل بشكل طبيعي.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. راجع السجلات للتفاصيل.")
    
    print("\n💡 تحقق من رسائل البوت في Telegram للتأكد من ظهور الأزرار.")

if __name__ == "__main__":
    try:
        asyncio.run(run_all_tests())
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم.")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الاختبار: {e}")
