#!/usr/bin/env python3
"""
اختبار النظام المحسن
Test Enhanced System

هذا الملف لاختبار النظام المحسن لعرض تفاصيل المودات
This file tests the enhanced mod details display system
"""

import os
import sys
import time
import requests
import threading
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_web_servers():
    """اختبار خوادم الويب"""
    print("🧪 Testing web servers...")
    
    # اختبار الخادم الأساسي
    try:
        print("📡 Testing Flask server...")
        response = requests.get("http://localhost:5000/", timeout=5)
        if response.status_code == 200:
            print("✅ Flask server is working")
        else:
            print(f"⚠️ Flask server returned status {response.status_code}")
    except Exception as e:
        print(f"❌ Flask server test failed: {e}")
    
    # اختبار خادم Telegram Web App
    try:
        print("📱 Testing Telegram Web App server...")
        response = requests.get("http://localhost:5001/telegram-mod-details?id=1&lang=ar", timeout=5)
        if response.status_code in [200, 404]:  # 404 مقبول إذا لم يكن هناك مود
            print("✅ Telegram Web App server is working")
        else:
            print(f"⚠️ Telegram Web App server returned status {response.status_code}")
    except Exception as e:
        print(f"❌ Telegram Web App server test failed: {e}")
    
    # اختبار API
    try:
        print("🔌 Testing API endpoint...")
        response = requests.get("http://localhost:5001/api/mod/1?lang=ar", timeout=5)
        if response.status_code in [200, 404]:
            print("✅ API endpoint is working")
            if response.status_code == 200:
                data = response.json()
                print(f"   API response: {data.get('success', 'Unknown')}")
        else:
            print(f"⚠️ API returned status {response.status_code}")
    except Exception as e:
        print(f"❌ API test failed: {e}")

def start_test_servers():
    """تشغيل الخوادم للاختبار"""
    print("🚀 Starting test servers...")
    
    try:
        # تشغيل خادم Telegram Web App
        from telegram_web_app import run_telegram_web_app
        
        web_app_thread = threading.Thread(
            target=run_telegram_web_app,
            args=(5001,),
            daemon=True,
            name="TestTelegramWebApp"
        )
        web_app_thread.start()
        print("✅ Telegram Web App server started on port 5001")
        
        # تشغيل الخادم الأساسي
        from web_server import run_web_server
        
        flask_thread = threading.Thread(
            target=run_web_server,
            args=(5000,),
            daemon=True,
            name="TestFlaskServer"
        )
        flask_thread.start()
        print("✅ Flask server started on port 5000")
        
        # انتظار قصير للتأكد من تشغيل الخوادم
        time.sleep(3)
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to start test servers: {e}")
        return False

def test_mod_data():
    """اختبار بيانات المود"""
    print("\n📊 Testing mod data...")
    
    try:
        import supabase_client
        
        # اختبار الاتصال بقاعدة البيانات
        print("🗄️ Testing database connection...")
        
        # محاولة جلب مود تجريبي
        mod_data = supabase_client.get_mod_by_id(1)
        if mod_data:
            print("✅ Database connection working")
            print(f"   Found mod: {mod_data.get('title', 'Unknown')}")
        else:
            print("⚠️ No mod found with ID 1 (this is normal for new databases)")
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")

def create_test_html():
    """إنشاء صفحة اختبار HTML"""
    print("\n📄 Creating test HTML page...")
    
    test_html = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام المحسن</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background: #f1f8e9; }
        .error { border-color: #f44336; background: #ffebee; }
        .warning { border-color: #ff9800; background: #fff3e0; }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1976D2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار النظام المحسن</h1>
        
        <div class="test-item">
            <h3>🌐 اختبار الخوادم</h3>
            <button onclick="testServers()">اختبار الخوادم</button>
            <div id="server-results"></div>
        </div>
        
        <div class="test-item">
            <h3>📱 اختبار Telegram Web App</h3>
            <button onclick="testWebApp()">اختبار صفحة المود</button>
            <div id="webapp-results"></div>
        </div>
        
        <div class="test-item">
            <h3>🔌 اختبار API</h3>
            <button onclick="testAPI()">اختبار API</button>
            <div id="api-results"></div>
        </div>
        
        <div class="test-item">
            <h3>📊 معلومات النظام</h3>
            <p><strong>Flask Server:</strong> http://localhost:5000</p>
            <p><strong>Telegram Web App:</strong> http://localhost:5001</p>
            <p><strong>API Endpoint:</strong> http://localhost:5001/api/mod/1</p>
        </div>
    </div>

    <script>
        async function testServers() {
            const results = document.getElementById('server-results');
            results.innerHTML = '<p>جاري الاختبار...</p>';
            
            try {
                // اختبار Flask
                const flaskResponse = await fetch('http://localhost:5000/');
                const flaskStatus = flaskResponse.ok ? '✅ يعمل' : '❌ خطأ';
                
                // اختبار Telegram Web App
                const webappResponse = await fetch('http://localhost:5001/telegram-mod-details?id=1&lang=ar');
                const webappStatus = webappResponse.ok ? '✅ يعمل' : '❌ خطأ';
                
                results.innerHTML = `
                    <p>Flask Server: ${flaskStatus}</p>
                    <p>Telegram Web App: ${webappStatus}</p>
                `;
                results.className = 'success';
            } catch (error) {
                results.innerHTML = `<p>❌ خطأ في الاختبار: ${error.message}</p>`;
                results.className = 'error';
            }
        }
        
        async function testWebApp() {
            const results = document.getElementById('webapp-results');
            results.innerHTML = '<p>جاري فتح صفحة المود...</p>';
            
            // فتح صفحة المود في نافذة جديدة
            window.open('http://localhost:5001/telegram-mod-details?id=1&lang=ar', '_blank');
            
            results.innerHTML = '<p>✅ تم فتح صفحة المود في نافذة جديدة</p>';
            results.className = 'success';
        }
        
        async function testAPI() {
            const results = document.getElementById('api-results');
            results.innerHTML = '<p>جاري اختبار API...</p>';
            
            try {
                const response = await fetch('http://localhost:5001/api/mod/1?lang=ar');
                const data = await response.json();
                
                if (data.success) {
                    results.innerHTML = `
                        <p>✅ API يعمل بشكل صحيح</p>
                        <p>عنوان المود: ${data.mod.title || 'غير محدد'}</p>
                    `;
                    results.className = 'success';
                } else {
                    results.innerHTML = `<p>⚠️ API يعمل لكن لا توجد بيانات: ${data.error}</p>`;
                    results.className = 'warning';
                }
            } catch (error) {
                results.innerHTML = `<p>❌ خطأ في API: ${error.message}</p>`;
                results.className = 'error';
            }
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            setTimeout(testServers, 1000);
        };
    </script>
</body>
</html>
"""
    
    try:
        with open("test_page.html", "w", encoding='utf-8') as f:
            f.write(test_html)
        print("✅ Created test_page.html")
        print("   Open http://localhost:5000/test_page.html to test")
    except Exception as e:
        print(f"❌ Failed to create test page: {e}")

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 Enhanced System Test Suite")
    print("="*50)
    
    # تشغيل الخوادم
    if not start_test_servers():
        print("❌ Failed to start servers")
        return
    
    # اختبار بيانات المود
    test_mod_data()
    
    # إنشاء صفحة اختبار
    create_test_html()
    
    # اختبار الخوادم
    print("\n" + "="*50)
    test_web_servers()
    
    print("\n" + "="*50)
    print("🎉 Test completed!")
    print("\n📋 Next steps:")
    print("1. Open http://localhost:5001/telegram-mod-details?id=1&lang=ar")
    print("2. Check if the page loads correctly")
    print("3. Test on different devices")
    print("4. Open test_page.html for interactive testing")
    print("\n🔄 Servers will keep running...")
    print("Press Ctrl+C to stop")
    
    try:
        # إبقاء البرنامج يعمل
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Test stopped by user")

if __name__ == "__main__":
    main()
