#!/usr/bin/env python3
"""
🚂 دليل نشر البوت على Railway
🌐 Railway Bot Deployment Guide

دليل شامل لنشر البوت على Railway مع HTTPS دائم
Complete guide to deploy bot on Railway with permanent HTTPS
"""

import os
import json
import webbrowser
from pathlib import Path

def print_header():
    """طباعة الرأس"""
    print("\n" + "="*70)
    print("🚂 دليل نشر البوت على Railway")
    print("🌐 Railway Bot Deployment Guide")
    print("="*70)
    print("🎯 الهدف: نشر البوت ليعمل 24/7 مع HTTPS دائم بدون ngrok")
    print("🎯 Goal: Deploy bot for 24/7 operation with permanent HTTPS without ngrok")
    print("="*70)

def show_railway_advantages():
    """عرض مميزات Railway"""
    print("\n🌟 لماذا Railway هو الأفضل للبوت:")
    print("✅ مجاني تماماً (500 ساعة شهرياً)")
    print("✅ HTTPS تلقائي وآمن")
    print("✅ لا ينام أبداً (على عكس Heroku)")
    print("✅ نشر سريع (أقل من 3 دقائق)")
    print("✅ رابط ثابت لا يتغير")
    print("✅ دعم قواعد البيانات مجاناً")
    print("✅ مراقبة وإحصائيات مجانية")
    print("✅ نسخ احتياطي تلقائي")
    print("✅ دعم فني ممتاز")

def create_railway_files():
    """إنشاء ملفات Railway"""
    print("\n🔧 إنشاء ملفات التكوين...")
    
    # إنشاء railway.json
    railway_config = {
        "build": {
            "builder": "NIXPACKS"
        },
        "deploy": {
            "startCommand": "python main.py",
            "healthcheckPath": "/health",
            "healthcheckTimeout": 100,
            "restartPolicyType": "ON_FAILURE",
            "restartPolicyMaxRetries": 10
        }
    }
    
    with open("railway.json", "w", encoding="utf-8") as f:
        json.dump(railway_config, f, indent=2)
    print("✅ تم إنشاء railway.json")
    
    # إنشاء nixpacks.toml
    nixpacks_config = """[phases.setup]
nixPkgs = ["python311", "pip"]

[phases.install]
cmds = ["pip install -r requirements.txt"]

[start]
cmd = "python main.py"
"""
    
    with open("nixpacks.toml", "w", encoding="utf-8") as f:
        f.write(nixpacks_config)
    print("✅ تم إنشاء nixpacks.toml")
    
    # إنشاء Procfile (احتياطي)
    with open("Procfile", "w", encoding="utf-8") as f:
        f.write("web: python main.py\n")
    print("✅ تم إنشاء Procfile")

def update_main_for_hosting():
    """تحديث main.py للاستضافة"""
    print("🔧 إنشاء main محسن للاستضافة...")
    
    main_hosting = '''#!/usr/bin/env python3
"""
البوت الرئيسي محسن للاستضافة
Main bot optimized for hosting
"""

import os
import sys
import logging
from pathlib import Path

# إعداد المسارات
sys.path.insert(0, str(Path(__file__).parent))

# إعداد متغيرات البيئة للاستضافة
os.environ.setdefault("ENVIRONMENT", "production")
os.environ.setdefault("DEBUG", "false")

# تحديد رابط الخادم تلقائياً
if not os.environ.get("WEB_SERVER_URL"):
    # للاستضافة على Railway
    railway_url = os.environ.get("RAILWAY_STATIC_URL")
    if railway_url:
        os.environ["WEB_SERVER_URL"] = railway_url
        print(f"🚂 Railway URL: {railway_url}")
    else:
        # للاستضافة على خدمات أخرى
        port = os.environ.get("PORT", "5000")
        app_name = os.environ.get("APP_NAME", "minecraft-mods-bot")
        
        # تحديد الخدمة
        if "railway" in os.environ.get("RAILWAY_ENVIRONMENT", ""):
            os.environ["WEB_SERVER_URL"] = f"https://{app_name}.up.railway.app"
        elif "render" in os.environ.get("RENDER", ""):
            os.environ["WEB_SERVER_URL"] = f"https://{app_name}.onrender.com"
        elif "herokuapp" in os.environ.get("DYNO", ""):
            os.environ["WEB_SERVER_URL"] = f"https://{app_name}.herokuapp.com"
        else:
            os.environ["WEB_SERVER_URL"] = f"http://localhost:{port}"

# إعداد الـ logging للاستضافة
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    """تشغيل البوت"""
    try:
        logger.info("🚀 Starting Minecraft Mods Bot on hosting platform...")
        logger.info(f"🌐 Web server URL: {os.environ.get('WEB_SERVER_URL')}")
        logger.info(f"🔧 Environment: {os.environ.get('ENVIRONMENT')}")
        
        # استيراد وتشغيل البوت الأصلي
        from main import *
        logger.info("✅ Bot started successfully!")
        
    except Exception as e:
        logger.error(f"❌ Error starting bot: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
    
    with open("main_hosting.py", "w", encoding="utf-8") as f:
        f.write(main_hosting)
    print("✅ تم إنشاء main_hosting.py")

def show_deployment_steps():
    """عرض خطوات النشر"""
    print("\n" + "="*70)
    print("📋 خطوات النشر على Railway (خطوة بخطوة)")
    print("="*70)
    
    print("\n🔗 الخطوة 1: إنشاء حساب Railway")
    print("1. اذهب إلى: https://railway.app")
    print("2. اضغط 'Login' ثم 'Login with GitHub'")
    print("3. سجل دخول بحساب GitHub الخاص بك")
    print("4. اقبل الأذونات المطلوبة")
    
    print("\n📁 الخطوة 2: رفع الكود إلى GitHub")
    print("1. أنشئ repository جديد على GitHub")
    print("2. ارفع جميع ملفات البوت")
    print("3. تأكد من وجود:")
    print("   • main.py")
    print("   • requirements.txt")
    print("   • railway.json")
    print("   • .env (لا ترفعه - سنضيف المتغيرات يدوياً)")
    
    print("\n🚀 الخطوة 3: النشر على Railway")
    print("1. في Railway، اضغط 'New Project'")
    print("2. اختر 'Deploy from GitHub repo'")
    print("3. اختر repository البوت")
    print("4. انتظر البناء التلقائي (2-3 دقائق)")
    
    print("\n⚙️ الخطوة 4: إضافة متغيرات البيئة")
    print("1. اذهب إلى تبويب 'Variables'")
    print("2. أضف المتغيرات التالية:")
    
    # عرض متغيرات البيئة من ملف .env
    env_file = Path(".env")
    if env_file.exists():
        print("\n📋 متغيرات البيئة المطلوبة:")
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                important_vars = ['BOT_TOKEN', 'ADMIN_CHAT_ID', 'SUPABASE_URL', 'SUPABASE_KEY', 'GEMINI_API_KEY']
                for line in f:
                    line = line.strip()
                    if '=' in line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        if key in important_vars:
                            # إخفاء القيم الحساسة جزئياً
                            if len(value) > 20:
                                display_value = value[:10] + "..." + value[-5:]
                            else:
                                display_value = value
                            print(f"   • {key} = {display_value}")
        except Exception:
            print("   • BOT_TOKEN = (توكن البوت من @BotFather)")
            print("   • ADMIN_CHAT_ID = (معرف المدير)")
            print("   • SUPABASE_URL = (رابط قاعدة البيانات)")
            print("   • SUPABASE_KEY = (مفتاح قاعدة البيانات)")
    
    print("\n🌐 الخطوة 5: الحصول على الرابط")
    print("1. انتظر انتهاء النشر (ستظهر رسالة 'Deployed')")
    print("2. اذهب إلى تبويب 'Settings'")
    print("3. انسخ 'Public Domain' (مثل: https://your-app.up.railway.app)")
    print("4. هذا هو رابط البوت الدائم!")
    
    print("\n🧪 الخطوة 6: اختبار البوت")
    print("1. اذهب إلى البوت في تيليجرام")
    print("2. أرسل /start")
    print("3. أرسل مود للبوت")
    print("4. اضغط على 'عرض التفاصيل'")
    print("5. يجب أن تفتح صفحة ويب من رابط Railway!")

def show_final_benefits():
    """عرض الفوائد النهائية"""
    print("\n" + "="*70)
    print("🎉 مبروك! البوت الآن يعمل على Railway")
    print("="*70)
    
    print("\n✅ ما حققته:")
    print("🌍 البوت يعمل 24/7 من أي مكان في العالم")
    print("🔒 HTTPS دائم وآمن (بدون ngrok)")
    print("📱 صفحات المودات تعمل على جميع الأجهزة")
    print("🚀 رابط ثابت لا يتغير أبداً")
    print("💰 مجاني تماماً (500 ساعة شهرياً)")
    print("🔄 نسخ احتياطي تلقائي")
    print("📊 مراقبة وإحصائيات")
    print("⚡ أداء عالي وسرعة ممتازة")
    
    print("\n🎯 الآن يمكنك:")
    print("• إغلاق الكمبيوتر - البوت سيستمر في العمل")
    print("• مشاركة رابط البوت مع المستخدمين")
    print("• الوصول لصفحات المودات من أي شبكة")
    print("• الاستمتاع بخدمة مجانية وموثوقة")

def main():
    """الدالة الرئيسية"""
    print_header()
    show_railway_advantages()
    
    print("\n🔧 إعداد ملفات النشر...")
    create_railway_files()
    update_main_for_hosting()
    
    show_deployment_steps()
    
    # فتح Railway
    choice = input("\n❓ هل تريد فتح موقع Railway الآن؟ (y/n): ").lower()
    if choice == 'y':
        webbrowser.open("https://railway.app")
        print("✅ تم فتح موقع Railway في المتصفح")
    
    show_final_benefits()
    
    print("\n📞 للدعم والمساعدة: @Kim880198")

if __name__ == "__main__":
    main()
